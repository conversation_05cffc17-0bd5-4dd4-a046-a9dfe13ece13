#!/usr/bin/env python3
"""
멀티스포츠 시스템 통합 테스트 - 간단 버전
"""
import asyncio
import sys
import os
from datetime import datetime

# 프로젝트 루트를 Python path에 추가
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def test_sport_configs():
    """스포츠 설정 테스트"""
    print("🔧 스포츠 설정 테스트")
    
    try:
        from core.config.sport_config import SportConfig
        
        # 축구 설정 테스트
        soccer_config = SportConfig(
            sport_name="soccer",
            sport_code="SC",
            leagues={'SC001': 'K리그1', 'SC003': 'K리그2'},
            team_url_pattern="https://example.com/team/{teamId}",
            player_url_pattern="https://example.com/player/{playerId}",
            time_slots=[{"start": "10:00", "end": "12:00"}],
            max_concurrent=3,
            delay_between_requests=2.0
        )
        
        assert soccer_config.sport_name == "soccer"
        assert len(soccer_config.leagues) == 2
        print("   ✅ 축구 설정 테스트 통과")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 스포츠 설정 테스트 실패: {e}")
        return False


async def test_time_slot_scheduler():
    """시간대 스케줄러 테스트"""
    print("🔧 시간대 스케줄러 테스트")
    
    try:
        from core.scheduler.time_slot_scheduler import TimeSlotScheduler
        from core.config.sport_config import SportConfig
        
        scheduler = TimeSlotScheduler()
        
        # 테스트용 설정
        config = SportConfig(
            sport_name="test",
            sport_code="TS",
            leagues={'TS001': 'Test League'},
            team_url_pattern="https://example.com/team/{teamId}",
            player_url_pattern="https://example.com/player/{playerId}",
            time_slots=[{"start": "00:00", "end": "23:59"}],  # 항상 실행 가능
            max_concurrent=1,
            delay_between_requests=1.0
        )
        
        # 현재 시간에 실행 가능한지 확인
        should_run = scheduler.should_run_now(config, datetime.now())
        assert should_run is True
        print("   ✅ 시간대 스케줄러 테스트 통과")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 시간대 스케줄러 테스트 실패: {e}")
        return False


async def test_team_matcher():
    """팀 매처 테스트"""
    print("🔧 팀 매처 테스트")
    
    try:
        from core.matcher.universal_team_matcher import UniversalTeamMatcher
        
        matcher = UniversalTeamMatcher()
        
        # 테스트 매핑 추가
        test_mappings = {
            'soccer': {
                'K01': '울산 HD FC',
                'K02': '포항 스틸러스'
            },
            'basketball': {
                'KBL001': '서울 SK 나이츠',
                'KBL002': '부산 KT 소닉붐'
            }
        }
        
        for sport, mappings in test_mappings.items():
            for team_id, team_name in mappings.items():
                matcher.add_team_mapping(sport, team_id, team_name)
        
        # 매핑 확인
        soccer_team = matcher.get_team_name('soccer', 'K01')
        assert soccer_team == '울산 HD FC'
        
        basketball_team = matcher.get_team_name('basketball', 'KBL001')
        assert basketball_team == '서울 SK 나이츠'
        
        print("   ✅ 팀 매처 테스트 통과")
        return True
        
    except Exception as e:
        print(f"   ❌ 팀 매처 테스트 실패: {e}")
        return False


async def test_soccer_parser():
    """축구 파서 테스트"""
    print("🔧 축구 파서 테스트")
    
    try:
        from sports.soccer.parsers.soccer_parser import SoccerParser
        
        # 샘플 HTML
        sample_html = """
        <html>
            <body>
                <h2>울산 HD FC</h2>
                <table>
                    <caption>시즌 전체성적</caption>
                    <tr><th>순위</th><th>경기수</th><th>승</th><th>무</th><th>패</th></tr>
                    <tr><td>1</td><td>30</td><td>20</td><td>5</td><td>5</td></tr>
                </table>
                <a href="scPlayerDetail.do?playerId=123">김선수</a>
                <a href="scPlayerDetail.do?playerId=456">박선수</a>
            </body>
        </html>
        """
        
        parser = SoccerParser()
        data = parser.parse_team_data(sample_html, "울산 HD FC", league_id="SC001", league="K리그1")
        
        assert data.team_name == "울산 HD FC"
        assert data.league == "K리그1"
        assert data.league_id == "SC001"
        
        print("   ✅ 축구 파서 테스트 통과")
        return True
        
    except Exception as e:
        print(f"   ❌ 축구 파서 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_basketball_parser():
    """농구 파서 테스트"""
    print("🔧 농구 파서 테스트")
    
    try:
        from sports.basketball.parsers.basketball_parser import BasketballParser
        
        # 샘플 HTML
        sample_html = """
        <html>
            <body>
                <h2>서울 SK 나이츠</h2>
                <table>
                    <caption>시즌 전체성적</caption>
                    <tr><th>순위</th><th>경기수</th><th>승</th><th>패</th></tr>
                    <tr><td>1</td><td>30</td><td>20</td><td>10</td></tr>
                </table>
            </body>
        </html>
        """
        
        parser = BasketballParser()
        data = parser.parse_team_data(sample_html, "서울 SK 나이츠", league_id="BK001", league="KBL")
        
        assert data.team_name == "서울 SK 나이츠"
        assert data.league == "KBL"
        assert data.league_id == "BK001"
        
        print("   ✅ 농구 파서 테스트 통과")
        return True
        
    except Exception as e:
        print(f"   ❌ 농구 파서 테스트 실패: {e}")
        return False


async def test_volleyball_parser():
    """배구 파서 테스트"""
    print("🔧 배구 파서 테스트")
    
    try:
        from sports.volleyball.parsers.volleyball_parser import VolleyballParser
        
        # 샘플 HTML
        sample_html = """
        <html>
            <body>
                <h2>현대캐피탈 스카이워커스</h2>
                <table>
                    <caption>시즌 전체성적</caption>
                    <tr><th>순위</th><th>경기수</th><th>승</th><th>패</th></tr>
                    <tr><td>1</td><td>30</td><td>20</td><td>10</td></tr>
                </table>
            </body>
        </html>
        """
        
        parser = VolleyballParser()
        data = parser.parse_team_data(sample_html, "현대캐피탈 스카이워커스", league_id="VL001", league="KOVO남")
        
        assert data.team_name == "현대캐피탈 스카이워커스"
        assert data.league == "KOVO남"
        assert data.league_id == "VL001"
        
        print("   ✅ 배구 파서 테스트 통과")
        return True
        
    except Exception as e:
        print(f"   ❌ 배구 파서 테스트 실패: {e}")
        return False


async def test_plugins():
    """플러그인 테스트"""
    print("🔧 플러그인 테스트")
    
    try:
        # 축구 플러그인
        from sports.soccer.plugin import SoccerPlugin
        soccer_plugin = SoccerPlugin()
        soccer_config = soccer_plugin.get_sport_config()
        assert soccer_config.sport_name == "soccer"
        print("   ✅ 축구 플러그인 테스트 통과")
        
        # 농구 플러그인
        from sports.basketball.plugin import BasketballPlugin
        basketball_plugin = BasketballPlugin()
        basketball_config = basketball_plugin.get_sport_config()
        assert basketball_config.sport_name == "basketball"
        print("   ✅ 농구 플러그인 테스트 통과")
        
        # 배구 플러그인
        from sports.volleyball.plugin import VolleyballPlugin
        volleyball_plugin = VolleyballPlugin()
        volleyball_config = volleyball_plugin.get_sport_config()
        assert volleyball_config.sport_name == "volleyball"
        print("   ✅ 배구 플러그인 테스트 통과")
        
        # 야구 플러그인
        from sports.baseball.plugin import BaseballPlugin
        baseball_plugin = BaseballPlugin()
        baseball_config = baseball_plugin.get_sport_config()
        assert baseball_config.sport_name == "baseball"
        print("   ✅ 야구 플러그인 테스트 통과")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 플러그인 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """메인 테스트 함수"""
    print("🚀 멀티스포츠 시스템 통합 테스트 시작")
    print("=" * 80)
    
    tests = [
        ("스포츠 설정", test_sport_configs),
        ("시간대 스케줄러", test_time_slot_scheduler),
        ("팀 매처", test_team_matcher),
        ("축구 파서", test_soccer_parser),
        ("농구 파서", test_basketball_parser),
        ("배구 파서", test_volleyball_parser),
        ("플러그인", test_plugins),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 {test_name} 테스트 실행 중...")
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 테스트 통과")
            else:
                print(f"❌ {test_name} 테스트 실패")
        except Exception as e:
            print(f"❌ {test_name} 테스트 실행 실패: {e}")
    
    print("\n" + "=" * 80)
    print(f"🎉 멀티스포츠 시스템 통합 테스트 완료: {passed}/{total} 통과")
    print("=" * 80)
    
    if passed == total:
        print("✅ 모든 테스트 통과! 멀티스포츠 시스템 준비 완료")
        return True
    else:
        print(f"⚠️ {total - passed}개 테스트 실패")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
