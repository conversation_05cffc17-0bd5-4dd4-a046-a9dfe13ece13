# 데이터베이스 관계 설계 및 외래키 매핑 가이드

---

## 1. 기준 데이터(Reference Data)의 중요성

- **기준 테이블**은 어떤 데이터를 처리할지 결정하는 **필터** 역할을 합니다.
- 메인 데이터 테이블에 저장할 데이터의 기준이 되는 것이 바로 이 기준 테이블입니다.
- 기준 데이터가 없으면, 어떤 엔티티/이벤트/날짜의 데이터를 저장해야 할지 기준이 모호해집니다.
- **실제 저장 로직:**
    - 기준 테이블의 `id`와 `date`를 기준으로, 해당 엔티티의 가장 최근 데이터를 외부 API에서 가져와서 저장합니다.
    - 기준 데이터가 없으면 어떤 데이터를 저장해야 할지 결정할 수 없습니다.

**결론:**
> 기준 데이터 없이 메인 테이블에 저장하는 것은 설계상 불가능합니다.

---

## 2. 메인 테이블 컬럼 설계 원칙

- 메인 테이블의 주요 컬럼들은 **기준 데이터의 엔티티/날짜**를 기준으로, 해당 시점까지의 누적/최신 통계를 저장하는 용도입니다.
- **외래키 매핑**:
    - 외부 시스템의 ID와 내부 시스템 ID를 매핑해서, 올바른 엔티티의 데이터를 저장합니다.
    - 이 매핑이 없으면, 잘못된 엔티티에 데이터가 들어갈 수 있습니다.
- **JSONB 컬럼 활용**:
    - 복잡한 통계 데이터는 JSONB 컬럼에 구조화해서 저장
    - 각 컬럼은 특정 카테고리(예: 성과지표, 활동지표, 히스토리)별로 분리
    - 모든 데이터는 **기준 날짜**를 기준으로 추출됩니다.

**결론:**
> 외래키 매핑은 반드시 필요하며, 기준 데이터의 정보(특히 날짜)는 각 컬럼의 데이터 추출 기준이 되므로, 컬럼에서 적극적으로 활용되고 있습니다.

---

## 3. 시간 기반 데이터 처리 로직

- 실시간 데이터 처리 시, 현재 시점을 기준으로 적절한 시간 범위를 설정해야 합니다.
- **처리 로직:**
    1. 메인 테이블에 존재하는 엔티티(또는 기준으로 지정된 엔티티)만 처리합니다.
    2. 외부 API에서 데이터를 가져올 때, 종료일은 현재 날짜 - 1로 설정합니다.
    3. 어제까지의 데이터만 가져와서, 오늘의 기준 데이터로 사용합니다.
    4. 만약 어제까지 데이터가 없으면, 그 전 마지막 데이터까지를 사용합니다.

**예시 플로우:**
- 기준 데이터: 오늘 날짜, entity_id: X, reference_id: Y
- 외부 API에서 entity_id Y의 어제까지 데이터를 가져옴
- 가장 최근 데이터의 누적/평균 통계를 추출
- 이 데이터를 오늘의 기준 데이터와 연결하여 메인 테이블에 저장

---

## 4. 데이터베이스 스키마 설계 패턴

### 기본 구조
```sql
-- 기준 테이블 (Reference Table)
CREATE TABLE reference_events (
    id SERIAL PRIMARY KEY,
    external_id VARCHAR(50) NOT NULL,
    event_date DATE NOT NULL,
    entity_id INTEGER REFERENCES entities(id),
    status VARCHAR(20) DEFAULT 'active'
);

-- 메인 데이터 테이블
CREATE TABLE main_statistics (
    id SERIAL PRIMARY KEY,
    reference_event_id INTEGER REFERENCES reference_events(id),
    entity_id INTEGER REFERENCES entities(id),
    performance_data JSONB,           -- 성과 지표
    activity_data JSONB,              -- 활동 지표  
    historical_data JSONB,            -- 히스토리 데이터
    created_at TIMESTAMP DEFAULT NOW()
);

-- 엔티티 매핑 테이블
CREATE TABLE entity_mappings (
    internal_id INTEGER PRIMARY KEY,
    external_id VARCHAR(50) UNIQUE NOT NULL,
    entity_name VARCHAR(100),
    entity_type VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE
);
```

### JSONB 구조 예시
```json
{
  "performance_data": {
    "metrics": {
      "score": 85.5,
      "efficiency": 92.3
    },
    "period": "2024-01-01 to 2024-01-31"
  },
  "activity_data": {
    "events": [
      {"date": "2024-01-15", "type": "action", "value": 10},
      {"date": "2024-01-20", "type": "action", "value": 15}
    ],
    "summary": {"total": 25, "average": 12.5}
  }
}
```

---

## 5. 프로그래밍 구현 원칙

### (1) 데이터 저장 시 키 래핑
```python
# ✅ 올바른 방법: 키로 감싸서 저장
update_data = {
    "performance_data": {"performance": raw_performance_data},
    "activity_data": {"activity": raw_activity_data}, 
    "historical_data": {"history": raw_history_data}
}

# ❌ 잘못된 방법: 평평한 구조로 저장
update_data = {
    "performance_data": raw_performance_data,  # 구조 깨짐
    "activity_data": raw_activity_data
}
```

### (2) 매핑 테이블 활용
```python
def get_internal_entity_id(external_id: str) -> int:
    """외부 ID를 내부 ID로 변환"""
    mapping = db.query(EntityMapping).filter_by(external_id=external_id).first()
    if not mapping:
        raise ValueError(f"Unknown external_id: {external_id}")
    return mapping.internal_id
```

### (3) 시간 기반 데이터 필터링
```python
def get_reference_date_range(target_date: date) -> tuple[date, date]:
    """기준 날짜 범위 계산"""
    end_date = target_date - timedelta(days=1)  # 어제까지
    start_date = end_date - timedelta(days=30)  # 30일 범위
    return start_date, end_date
```

---

## 6. 모니터링 및 검증

### 데이터 품질 검증
```sql
-- 누락된 매핑 확인
SELECT DISTINCT external_id 
FROM raw_data 
WHERE external_id NOT IN (SELECT external_id FROM entity_mappings);

-- 기준 데이터 없는 메인 데이터 확인  
SELECT * FROM main_statistics 
WHERE reference_event_id NOT IN (SELECT id FROM reference_events);

-- JSONB 구조 검증
SELECT id, performance_data 
FROM main_statistics 
WHERE NOT (performance_data ? 'performance');
```

### 성능 모니터링
```sql
-- 인덱스 생성
CREATE INDEX idx_main_stats_entity_date ON main_statistics(entity_id, created_at);
CREATE INDEX idx_reference_events_date ON reference_events(event_date);
CREATE INDEX idx_jsonb_performance ON main_statistics USING GIN (performance_data);
```

이 가이드는 어떤 도메인(스포츠, 금융, IoT 등)에서도 적용할 수 있는 범용적인 데이터베이스 관계 설계 패턴을 제공합니다. 