# 범용 명명 규칙 가이드 (Universal Naming Conventions Guide)

이 문서는 모든 Python 프로젝트에서 적용할 수 있는 범용적인 명명 규칙을 정의합니다.

## 파일 및 폴더 명명 규칙

### 1. 폴더 명명 규칙
- **짧고 설명적**: 가능한 한 단일 단어 사용
- **소문자와 언더스코어**: `src/utils/`, `src/workers/`
- **명확한 목적**: 폴더명이 내용 유형을 나타내야 함

#### 범용 폴더 구조 예시:
```
src/
├── processors/     # 데이터 처리 모듈
├── services/       # 비즈니스 로직 서비스
├── utils/          # 유틸리티 함수들
├── workers/        # 백그라운드 처리
└── aggregators/    # 데이터 집계 로직
```

### 2. 파일 명명 규칙
- **최대 12글자** (.py 확장자 제외)
- **설명적 약어** 필요시 사용
- **중복 단어 제거** (파일명에 "processor", "service", "manager" 피하기)

#### 길이 최적화 예시:
```
user_profile_processor.py      → user_profile.py
payment_transaction_handler.py → payment.py
inventory_management_service.py → inventory.py
statistics_calculator.py       → stats_calc.py
external_api_client.py         → api_client.py
```

#### 도메인별 파일 구조 예시:

##### 전자상거래 프로젝트
```
processors/
├── order.py           # 주문 데이터 처리
├── payment.py         # 결제 처리
└── inventory.py       # 재고 관리

services/
├── api/               # 외부 API 통신
│   ├── payment.py     # 결제 API 클라이언트
│   └── shipping.py    # 배송 API 클라이언트
├── db/                # 데이터베이스 작업
│   ├── client.py      # DB 클라이언트
│   ├── orders.py      # 주문 데이터 저장
│   └── users.py       # 사용자 데이터 관리
└── calc/              # 계산 및 처리
    └── pricing.py     # 가격 계산

utils/
├── date_mgr.py        # 날짜 관리
├── validator.py       # 데이터 검증
├── memory_mon.py      # 메모리 모니터링
└── logger.py          # 로깅 유틸리티

workers/
├── order.py           # 주문 처리 워커
├── inventory.py       # 재고 업데이트 워커
└── analytics.py       # 분석 워커
```

##### IoT/센서 프로젝트
```
processors/
├── sensor.py          # 센서 데이터 처리
├── telemetry.py       # 원격 측정 데이터
└── device.py          # 디바이스 상태 처리

services/
├── api/               # 외부 서비스 통신
│   ├── cloud.py       # 클라우드 API
│   └── mqtt.py        # MQTT 브로커
├── db/                # 데이터베이스 작업
│   ├── timeseries.py  # 시계열 데이터
│   └── metadata.py    # 디바이스 메타데이터
└── calc/              # 계산 및 분석
    └── anomaly.py     # 이상 감지

workers/
├── collector.py       # 데이터 수집 워커
├── processor.py       # 실시간 처리 워커
└── alert.py          # 알림 워커
```

## 함수 명명 규칙

### 1. 함수명
- **최대 20글자**
- **동사 기반**: 액션 단어로 시작
- **명확하고 간결**: 중복 단어 피하기

#### 길이 최적화 예시:
```python
# 너무 길음 → 개선됨
collect_user_data_with_validation()     → collect_user_data()
fetch_product_information_from_api()    → fetch_product_info()
calculate_order_total_with_discounts()  → calculate_total()
get_date_range_from_parameters()        → get_date_range()
process_payment_transaction_data()      → process_payment()
```

### 2. 변수명
- **설명적이지만 간결**
- **약어 피하기** (파일명과 달리 변수명은 완전한 단어 사용)
- **가독성을 위한 완전한 단어** 사용

```python
# 좋은 예
user_profile = fetch_user_data()
order_items = calculate_items()
payment_status = process_payment()

# 피할 예
usr_prof = fetch_usr()
ord_itms = calc_itms()
pay_stat = proc_pay()
```

## 클래스 명명 규칙

### 1. 클래스명
- **PascalCase**: `UserProfileProcessor`
- **설명적이지만 장황하지 않게**: 중복 접미사 피하기
- **명확한 목적**: 클래스명이 기능을 나타내야 함

#### 도메인별 클래스명 예시:
```python
# 전자상거래
class OrderProcessor:           # 명확하고 간결
class PaymentGateway:          # 설명적
class InventoryManager:        # 목적이 분명

# IoT/센서
class SensorDataCollector:     # 역할이 명확
class DeviceMonitor:          # 간결하면서 설명적
class TelemetryProcessor:     # 기능 중심

# 금융
class TransactionValidator:    # 목적 기반
class RiskCalculator:         # 명확한 기능
class PortfolioAnalyzer:      # 분석 도구

# 너무 장황함 (피해야 할 예)
class OrderProcessingService:
class PaymentTransactionDataProcessor:
class InventoryManagementSystemService:
```

## Import 규칙

### 1. Import 별칭
공통적으로 사용되는 모듈에 대한 일관된 짧은 별칭 사용:

```python
# 표준 별칭 (도메인 무관)
from src.processors.data import DataProcessor as DataProc
from src.services.api import ApiClient as ApiClient
from src.utils.date_mgr import DateManager as DateMgr
from src.utils.validator import DataValidator as Validator

# 도메인별 별칭 예시
# 전자상거래
from src.processors.order import OrderProcessor as OrderProc
from src.services.payment import PaymentService as PaymentSvc

# IoT
from src.processors.sensor import SensorProcessor as SensorProc
from src.services.telemetry import TelemetryService as TelemetrySvc
```

### 2. 모듈 구조
Import를 체계적으로 그룹화:

```python
# 표준 라이브러리
import json
import gc
from typing import Dict, Any, List

# 서드파티
import requests
import pandas as pd

# 로컬 imports - 카테고리별 그룹화
from src.processors.data import DataProcessor
from src.services.api import ApiClient
from src.utils.date_mgr import DateManager
```

## 데이터베이스 명명 규칙

### 테이블 및 컬럼
```sql
-- 테이블: snake_case (복수형)
users, orders, order_items, sensor_readings

-- 컬럼: snake_case (단수형)  
user_id, created_at, order_status, sensor_value

-- 인덱스: {table}_{column}_idx
idx_users_email, idx_orders_created_at, idx_sensors_timestamp

-- 외래키: fk_{from_table}_{to_table}
fk_orders_users, fk_order_items_orders, fk_readings_sensors
```

## 환경별 적응형 규칙

### 프로젝트 규모별 명명

#### 소형 프로젝트 (10파일 미만)
- **파일명**: 짧고 직관적 (`user.py`, `api.py`)
- **함수명**: 동사_명사 (`get_user`, `save_data`)
- **클래스명**: 단순한 PascalCase (`UserService`, `DataModel`)

#### 중형 프로젝트 (10-50파일)
- **파일명**: 도메인 접두사 (`user_service.py`, `order_api.py`)
- **모듈명**: 도메인별 그룹화 (`user/`, `order/`)
- **함수명**: 도메인_동작 (`user_get_profile`, `order_calculate`)

#### 대형 프로젝트 (50파일 이상)
- **네임스페이스**: 완전한 경로 (`domains.user.services.profile`)
- **인터페이스**: 명시적 계약 (`IUserRepository`, `IOrderService`)
- **이벤트**: 도메인_이벤트 (`UserRegistered`, `OrderPlaced`)

## 구현 가이드라인

### 1. 파일 크기 제한
- **최대 200라인** (imports와 docstring 제외)
- **큰 파일은 분할** 집중된 모듈로 나누기
- **단일 책임** 파일당 하나의 목적

### 2. 함수 크기 제한  
- **최대 30라인** 
- **복잡한 로직 추출** 헬퍼 함수로 분리
- **명확한 함수 목적**

### 3. 문서화
- **간결한 docstring**: 목적과 매개변수에 집중
- **당연한 주석 피하기**: 코드 자체가 설명적이어야 함
- **복잡한 함수에는 예시** 포함

## 이 규칙의 이점

1. **개발 속도 향상**: 짧은 이름 = 빠른 타이핑과 읽기
2. **향상된 구조화**: 명확한 계층과 목적
3. **쉬운 탐색**: 예측 가능한 파일 위치
4. **인지 부하 감소**: 더 적은 정신적 오버헤드
5. **일관된 코드베이스**: 모든 모듈에서 균일한 명명

## 마이그레이션 체크리스트

파일명 변경 시:
1. ✅ 모든 import 문 업데이트
2. ✅ 테스트 파일 업데이트
3. ✅ 문서 참조 업데이트
4. ✅ 변경 후 기능 테스트
5. ✅ 이 문서를 새로운 규칙으로 업데이트