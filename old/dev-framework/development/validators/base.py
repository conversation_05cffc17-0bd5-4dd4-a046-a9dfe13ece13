"""
기본 검증기 클래스
"""

import importlib.util
# validator 모듈에서 직접 import
import sys
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List

validator_path = Path(__file__).parent.parent / "validator.py"
spec = importlib.util.spec_from_file_location("validator", validator_path)
validator_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(validator_module)

ValidationResult = validator_module.ValidationResult
SeverityLevel = validator_module.SeverityLevel


class BaseValidator(ABC):
    """기본 검증기"""
    
    def __init__(self, framework_path: Path):
        self.framework_path = framework_path
        self.results: List[ValidationResult] = []
        
    @abstractmethod
    def validate(self) -> List[ValidationResult]:
        """검증 실행 (서브클래스에서 구현)"""
        pass
        
    def add_result(self, rule: str, severity: SeverityLevel, 
                   message: str, file_path: str, suggestion: str, 
                   line_number: int = 0):
        """검증 결과 추가"""
        self.results.append(ValidationResult(
            rule=rule,
            severity=severity.value,
            message=message,
            file_path=file_path,
            suggestion=suggestion,
            line_number=line_number
        ))
        
    def get_relative_path(self, file_path: Path) -> str:
        """상대 경로 변환"""
        return str(file_path.relative_to(self.framework_path)) 