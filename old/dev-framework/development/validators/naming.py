"""
명명 규칙 검증기
"""

from pathlib import Path

from .base import BaseValidator, SeverityLevel


class NamingValidator(BaseValidator):
    """명명 규칙 검증"""
    
    def validate(self):
        """명명 규칙 검증 실행"""
        print("📝 명명 규칙 검증 중...")
        
        self._check_python_files()
        self._check_markdown_files()
        
        return self.results
        
    def _check_python_files(self):
        """Python 파일명 검증"""
        for py_file in self.framework_path.rglob("*.py"):
            filename = py_file.stem
            
            if len(filename) > 12:
                self.add_result(
                    "NAMING_PYTHON_FILE_LENGTH", SeverityLevel.ERROR,
                    f"Python 파일명이 너무 깁니다: {filename} ({len(filename)}자)",
                    self.get_relative_path(py_file), "파일명을 12자 이하로 줄이세요"
                )
                
    def _check_markdown_files(self):
        """Markdown 파일명 검증"""
        for md_file in self.framework_path.rglob("*.md"):
            filename = md_file.stem
            
            if "_" in filename and filename.isupper():
                continue  # 대문자_언더스코어는 허용
                
            if len(filename) > 20:
                self.add_result(
                    "NAMING_MD_FILE_LENGTH", SeverityLevel.WARNING,
                    f"Markdown 파일명이 너무 깁니다: {filename}",
                    self.get_relative_path(md_file), "파일명을 줄이거나 분할하세요"
                ) 