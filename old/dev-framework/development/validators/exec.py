"""
실행 가능성 검증기
"""

from pathlib import Path
from typing import Dict, List

from .base import BaseValidator


class PythonExecutabilityChecker:
    """Python 파일 실행 가능성 검사"""
    
    @staticmethod
    def check_syntax(file_path: Path) -> List[str]:
        """Python 구문 검사"""
        issues = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            compile(content, str(file_path), 'exec')
        except SyntaxError as e:
            issues.append(f"구문 오류: {e}")
        except (OSError, UnicodeDecodeError):
            issues.append("파일 읽기 오류")
        return issues
    
    @staticmethod
    def check_imports(file_path: Path) -> List[str]:
        """import 구문 검사"""
        issues = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip().startswith(('import ', 'from ')):
                        if 'natstat' in line.lower():
                            issues.append(f"라인 {line_num}: 프로젝트별 import")
        except (OSError, UnicodeDecodeError):
            issues.append("파일 읽기 오류")
        return issues


class ExecutabilityValidator(BaseValidator):
    """실행 가능성 검증기"""
    
    def __init__(self, framework_path: Path):
        super().__init__(framework_path)
        self.checker = PythonExecutabilityChecker()
    
    def validate(self) -> List[Dict]:
        """실행 가능성 검증"""
        for py_file in self.framework_path.rglob("*.py"):
            # 구문 검사
            syntax_issues = self.checker.check_syntax(py_file)
            for issue in syntax_issues:
                self.add_issue("EXEC_SYNTAX", py_file, issue)
            
            # import 검사
            import_issues = self.checker.check_imports(py_file)
            for issue in import_issues:
                self.add_issue("EXEC_IMPORT", py_file, issue)
        
        return self.issues 