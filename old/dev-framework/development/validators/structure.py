"""
파일 구조 검증기
"""

import importlib.util
from pathlib import Path
from typing import List

# 부모 디렉토리의 validator 모듈 import
validator_path = Path(__file__).parent.parent / "validator.py"
spec = importlib.util.spec_from_file_location("validator", validator_path)
validator_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(validator_module)

SeverityLevel = validator_module.SeverityLevel
ValidationResult = validator_module.ValidationResult

class StructureValidator:
    """파일 구조 검증"""
    
    def __init__(self, framework_path: Path):
        self.framework_path = framework_path
        self.results: List[ValidationResult] = []
        
    def validate(self) -> List[ValidationResult]:
        """파일 구조 검증 실행"""
        print("📁 파일 구조 검증 중...")
        
        self._check_required_dirs()
        self._check_required_files()
        self._check_empty_dirs()
        
        return self.results
        
    def _check_required_dirs(self):
        """필수 디렉토리 확인"""
        required_dirs = ["development", "database", "guides"]
        
        for dir_name in required_dirs:
            dir_path = self.framework_path / dir_name
            if not dir_path.exists():
                self.add_result(
                    "STRUCTURE_MISSING_DIR", SeverityLevel.ERROR,
                    f"필수 디렉토리가 없습니다: {dir_name}",
                    dir_name, "디렉토리를 생성하세요"
                )
                
    def _check_required_files(self):
        """필수 파일 확인"""
        required_files = [
            "README.md",
            "development/validator.py", 
            "development/CODE_QUALITY_STANDARDS.md"
        ]
        
        for file_path in required_files:
            full_path = self.framework_path / file_path
            if not full_path.exists():
                self.add_result(
                    "STRUCTURE_MISSING_FILE", SeverityLevel.ERROR,
                    f"필수 파일이 없습니다: {file_path}",
                    file_path, "파일을 생성하세요"
                )
                
    def _check_empty_dirs(self):
        """빈 디렉토리 확인"""
        for dir_path in self.framework_path.rglob("*"):
            if dir_path.is_dir() and not any(dir_path.iterdir()):
                self.add_result(
                    "STRUCTURE_EMPTY_DIR", SeverityLevel.WARNING,
                    f"빈 디렉토리가 있습니다: {self.get_relative_path(dir_path)}",
                    self.get_relative_path(dir_path), "내용을 추가하거나 제거하세요"
                ) 
                
    def add_result(self, rule: str, severity: SeverityLevel, 
                  message: str, file_path: str, suggestion: str, 
                  line_number: int = 0):
        """검증 결과 추가"""
        self.results.append(ValidationResult(
            rule=rule,
            severity=severity.value,
            message=message,
            file_path=file_path,
            suggestion=suggestion,
            line_number=line_number
        ))
        
    def get_relative_path(self, file_path: Path) -> str:
        """상대 경로 변환"""
        return str(file_path.relative_to(self.framework_path)) 