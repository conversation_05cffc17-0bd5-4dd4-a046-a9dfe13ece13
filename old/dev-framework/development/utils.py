"""
개발 어시스턴트 유틸리티 함수들
"""

import os
from pathlib import Path
from typing import List


def has_supabase_config() -> bool:
    """Supabase 설정 존재 여부 확인"""
    return bool(os.getenv("SUPABASE_URL") and os.getenv("SUPABASE_KEY"))

def has_mcp_tools() -> bool:
    """MCP 도구 사용 가능 여부 확인"""
    mcp_vars = ["TAVILY_API_KEY", "OPENROUTER_API_KEY", "BRAVE_API_KEY"]
    return any(os.getenv(var) for var in mcp_vars)

def has_async_code(project_root: Path) -> bool:
    """비동기 코드 사용 여부 확인"""
    for py_file in project_root.rglob("*.py"):
        try:
            content = py_file.read_text()
            if "async def" in content or "await " in content:
                return True
        except Exception:
            continue
    return False

def analyze_requirements(project_root: Path) -> List[str]:
    """requirements.txt 분석"""
    req_file = project_root / "requirements.txt"
    if req_file.exists():
        return req_file.read_text().strip().split('\n')
    return []

def get_team_size() -> int:
    """팀 크기 입력받기"""
    try:
        size = input("팀 크기를 입력하세요 (1-10): ")
        return min(max(int(size), 1), 10)
    except Exception:
        return 1

def get_project_domain() -> str:
    """프로젝트 도메인 입력받기"""
    prompt = "프로젝트 도메인을 입력하세요 (예: ecommerce, iot, finance): "
    domain = input(prompt)
    return domain or "general" 