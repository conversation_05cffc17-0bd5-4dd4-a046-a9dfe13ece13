# 코드 품질 표준 (Code Quality Standards)

## 🎯 핵심 철학: 극단적 간결성 (Extreme Simplicity)

**"가장 간단한 해결책이 최고의 해결책이다"**

### 금지 사항 (FORBIDDEN)
- ❌ **중복 코드** (DRY 원칙 위반)
- ❌ **과도한 엔지니어링** (YAGNI 위반)  
- ❌ **불필요한 추상화** (KISS 원칙 위반)
- ❌ **신의 클래스** (God Object)
- ❌ **긴 함수** (20라인 초과)
- ❌ **복잡한 상속** (3단계 초과)

## 🏗️ SOLID 원칙 강제 적용

### 1. SRP (Single Responsibility Principle)
**하나의 클래스는 하나의 이유로만 변경되어야 함**

#### ✅ 올바른 예시
```python
# 단일 책임: 사용자 데이터만 처리
class UserProcessor:
    def process_user_data(self, user_data: dict) -> dict:
        return self._normalize_data(user_data)
    
    def _normalize_data(self, data: dict) -> dict:
        return {k.lower(): v for k, v in data.items()}

# 단일 책임: 사용자 저장만 처리
class UserSaver:
    def __init__(self, db_client):
        self.db = db_client
    
    def save_user(self, user_data: dict) -> bool:
        return self.db.insert('users', user_data)
```

#### ❌ 잘못된 예시 (<PERSON> Object)
```python
# 여러 책임이 혼재된 잘못된 클래스
class UserManager:  # ❌ 너무 많은 책임
    def process_data(self, data): pass      # 데이터 처리
    def save_to_db(self, data): pass        # DB 저장
    def send_email(self, email): pass       # 이메일 발송
    def validate_input(self, data): pass    # 데이터 검증
    def generate_report(self, data): pass   # 리포트 생성
```

### 2. OCP (Open/Closed Principle)
**확장에는 열려있고 수정에는 닫혀있어야 함**

#### ✅ 올바른 예시
```python
from abc import ABC, abstractmethod

# 기본 인터페이스
class DataProcessor(ABC):
    @abstractmethod
    def process(self, data: dict) -> dict:
        pass

# 확장 가능한 구현체들
class JSONProcessor(DataProcessor):
    def process(self, data: dict) -> dict:
        return {"processed": data, "format": "json"}

class XMLProcessor(DataProcessor):
    def process(self, data: dict) -> dict:
        return {"processed": data, "format": "xml"}

class CSVProcessor(DataProcessor):
    def process(self, data: dict) -> dict:
        return {"processed": data, "format": "csv"}
```

### 3. LSP (Liskov Substitution Principle)
**서브클래스는 기본 클래스를 완전히 대체할 수 있어야 함**

#### ✅ 올바른 예시
```python
class DatabaseClient(ABC):
    @abstractmethod
    def save(self, table: str, data: dict) -> bool:
        pass

class PostgreSQLClient(DatabaseClient):
    def save(self, table: str, data: dict) -> bool:
        # PostgreSQL 구현
        return True

class MySQLClient(DatabaseClient):
    def save(self, table: str, data: dict) -> bool:
        # MySQL 구현
        return True

# LSP 준수: 어떤 구현체든 동일하게 동작
def save_user_data(db_client: DatabaseClient, user_data: dict):
    return db_client.save('users', user_data)
```

### 4. ISP (Interface Segregation Principle)
**클라이언트는 사용하지 않는 인터페이스에 의존하면 안됨**

#### ✅ 올바른 예시
```python
# 작고 집중된 인터페이스들
class Readable(ABC):
    @abstractmethod
    def read(self, query: str) -> list:
        pass

class Writable(ABC):
    @abstractmethod
    def write(self, table: str, data: dict) -> bool:
        pass

class Deletable(ABC):
    @abstractmethod
    def delete(self, table: str, id: int) -> bool:
        pass

# 필요한 인터페이스만 구현
class ReadOnlyService(Readable):
    def read(self, query: str) -> list: pass

class FullService(Readable, Writable, Deletable):
    def read(self, query: str) -> list: pass
    def write(self, table: str, data: dict) -> bool: pass
    def delete(self, table: str, id: int) -> bool: pass
```

### 5. DIP (Dependency Inversion Principle)
**고수준 모듈은 저수준 모듈에 의존하면 안됨**

#### ✅ 올바른 예시
```python
# 추상화에 의존하는 고수준 모듈
class UserService:
    def __init__(self, data_processor: DataProcessor, 
                 db_client: DatabaseClient):
        self.processor = data_processor  # 구체 클래스가 아닌 추상화에 의존
        self.db = db_client

    def create_user(self, raw_data: dict) -> bool:
        processed_data = self.processor.process(raw_data)
        return self.db.save('users', processed_data)

# 의존성 주입으로 유연한 구성
def create_user_service():
    processor = JSONProcessor()
    db_client = PostgreSQLClient()
    return UserService(processor, db_client)
```

## 🎨 필수 디자인 패턴

### 1. Factory Pattern (객체 생성)
```python
class ServiceFactory:
    @staticmethod
    def create_data_service(service_type: str):
        if service_type == "ecommerce":
            return EcommerceDataService(
                processor=OrderProcessor(),
                db_client=PostgreSQLClient()
            )
        elif service_type == "iot":
            return IoTDataService(
                processor=SensorProcessor(),
                db_client=TimeSeriesClient()
            )
        else:
            raise ValueError(f"Unknown service type: {service_type}")
```

### 2. Strategy Pattern (알고리즘 선택)
```python
class DataValidationStrategy(ABC):
    @abstractmethod
    def validate(self, data: dict) -> bool:
        pass

class StrictValidation(DataValidationStrategy):
    def validate(self, data: dict) -> bool:
        return all(key in data for key in ['id', 'name', 'email'])

class LenientValidation(DataValidationStrategy):
    def validate(self, data: dict) -> bool:
        return 'id' in data

class DataValidator:
    def __init__(self, strategy: DataValidationStrategy):
        self.strategy = strategy
    
    def validate(self, data: dict) -> bool:
        return self.strategy.validate(data)
```

### 3. Observer Pattern (이벤트 처리)
```python
class EventManager:
    def __init__(self):
        self.observers = []
    
    def subscribe(self, observer):
        self.observers.append(observer)
    
    def notify(self, event_data: dict):
        for observer in self.observers:
            observer.handle_event(event_data)

class EmailNotifier:
    def handle_event(self, event_data: dict):
        print(f"Sending email for: {event_data}")

class LoggingObserver:
    def handle_event(self, event_data: dict):
        print(f"Logging event: {event_data}")
```

## 📏 크기 제한 (엄격한 규칙)

### 파일 크기 제한
```python
# ✅ 올바른 파일 크기 (200라인 이하)
class OrderProcessor:
    """주문 처리기 (200라인 제한)"""
    
    def __init__(self, validator, calculator):
        self.validator = validator
        self.calculator = calculator
    
    def process_order(self, order_data: dict) -> dict:
        validated_data = self.validator.validate(order_data)
        calculated_data = self.calculator.calculate_totals(validated_data)
        return self._format_order(calculated_data)
    
    def _format_order(self, data: dict) -> dict:
        return {
            'order_id': data['id'],
            'total': data['total'],
            'status': 'processed'
        }

# ❌ 큰 파일은 분할 필수 (200라인 초과 시)
```

### 클래스 크기 제한
```python
# ✅ 올바른 클래스 크기 (50라인 이하)
class UserProcessor:
    """사용자 데이터 처리기 (최대 50라인)"""
    
    def __init__(self, validator: DataValidator):
        self.validator = validator
    
    def process(self, raw_data: dict) -> dict:
        if not self.validator.validate(raw_data):
            raise ValueError("Invalid user data")
        
        return self._normalize_data(raw_data)
    
    def _normalize_data(self, data: dict) -> dict:
        return {
            'id': str(data['id']).strip(),
            'name': data['name'].strip().title(),
            'email': data['email'].strip().lower()
        }

# ❌ 큰 클래스는 분할 필수 (50라인 초과 시)
```

### 함수 크기 제한
```python
# ✅ 올바른 함수 크기 (15라인 이하)
def calculate_order_total(items: list, tax_rate: float) -> float:
    """주문 총액 계산 (최대 15라인)"""
    
    if not items:
        return 0.0
    
    subtotal = sum(item['price'] * item['quantity'] for item in items)
    tax_amount = subtotal * tax_rate
    total = subtotal + tax_amount
    
    return round(total, 2)

# ❌ 긴 함수는 분할 필수 (15라인 초과 시)
```

## 📂 폴더 및 파일 명명 규칙

### 폴더 구조 규칙
```
src/
├── core/              # 핵심 비즈니스 로직 (10-15 파일)
│   ├── models/        # 데이터 모델 (5-8 파일)
│   ├── services/      # 비즈니스 서비스 (8-12 파일)
│   └── processors/    # 데이터 처리 (5-10 파일)
├── infrastructure/    # 외부 연결 (8-12 파일)
│   ├── api/          # API 클라이언트 (3-5 파일)
│   ├── db/           # 데이터베이스 (3-5 파일)
│   └── cache/        # 캐싱 (2-3 파일)
├── utils/            # 유틸리티 (5-8 파일)
│   ├── validators/   # 검증 로직 (3-5 파일)
│   ├── formatters/   # 포맷팅 (2-4 파일)
│   └── helpers/      # 헬퍼 함수 (3-5 파일)
└── interfaces/       # 추상 인터페이스 (5-8 파일)
```

### 파일 명명 표준
```python
# ✅ 올바른 파일명 (동사_명사 패턴)
process_user.py        # 사용자 처리
validate_data.py       # 데이터 검증
calculate_score.py     # 점수 계산
format_output.py       # 출력 포맷팅
manage_cache.py        # 캐시 관리

# ✅ 올바른 클래스/서비스명 (명사 패턴)
user_service.py        # UserService 클래스
data_processor.py      # DataProcessor 클래스
api_client.py         # ApiClient 클래스
db_connection.py      # DbConnection 클래스

# ❌ 피해야 할 패턴
user_data_processing_service.py  # 너무 길음
utils.py                        # 너무 일반적
manager.py                      # 의미 불분명
helper.py                       # 구체적이지 않음
```

### 함수 명명 표준
```python
# ✅ 올바른 함수명 (동사로 시작, 15글자 이하)
def get_user(id: int) -> dict:
def save_data(data: dict) -> bool:
def calc_score(values: list) -> float:
def format_name(name: str) -> str:
def validate_email(email: str) -> bool:

# ❌ 잘못된 함수명
def getUserDataFromDatabaseById():  # 너무 길음
def data():                         # 동사 없음
def process():                      # 너무 일반적
def doSomething():                  # 의미 불분명
```

## 🔍 중복 방지 규칙

### 1. 코드 중복 탐지
```python
# ❌ 중복 코드 (DRY 위반)
class EcommerceAPIClient:
    def fetch_data(self, url):
        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
        return None

class IoTAPIClient:
    def fetch_data(self, url):  # 중복!
        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
        return None

# ✅ 중복 제거 (공통 기능 추출)
class BaseAPIClient:
    def fetch_data(self, url: str) -> dict:
        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
        return None

class EcommerceAPIClient(BaseAPIClient):
    def get_orders(self, date: str) -> list:
        url = f"/ecommerce/orders?date={date}"
        return self.fetch_data(url)

class IoTAPIClient(BaseAPIClient):
    def get_sensor_data(self, sensor_id: str) -> dict:
        url = f"/iot/sensors/{sensor_id}/data"
        return self.fetch_data(url)
```

### 2. 과도한 엔지니어링 방지
```python
# ❌ 과도한 엔지니어링 (YAGNI 위반)
class GenericAbstractFactoryBuilderManagerService:
    """불필요하게 복잡한 추상화"""
    pass

# ✅ 간단하고 직접적인 해결책
class OrderService:
    """주문 서비스 - 단순하고 명확"""
    
    def create_order(self, order_data: dict) -> dict:
        return {'order_id': 123, 'status': 'created'}
```

## 💡 성능 최적화 원칙

### 메모리 효율성
```python
# ✅ 메모리 효율적인 코드
def process_large_dataset(data: list) -> list:
    """큰 데이터셋을 배치로 처리"""
    
    batch_size = 1000
    results = []
    
    for i in range(0, len(data), batch_size):
        batch = data[i:i + batch_size]
        batch_result = process_batch(batch)
        results.extend(batch_result)
        
        # 메모리 정리
        del batch
        gc.collect()
    
    return results

# ❌ 메모리 비효율적인 코드
def process_all_at_once(data: list) -> list:
    """전체 데이터를 한번에 처리 (메모리 문제 발생 가능)"""
    return [expensive_operation(item) for item in data]
```

### 시간 복잡도 최적화
```python
# ✅ O(1) 룩업 사용
class UserLookup:
    def __init__(self, users: list):
        self.user_map = {user['id']: user for user in users}
    
    def find_user(self, user_id: int) -> dict:
        return self.user_map.get(user_id)

# ❌ O(n) 순차 검색
class SlowUserLookup:
    def __init__(self, users: list):
        self.users = users
    
    def find_user(self, user_id: int) -> dict:
        for user in self.users:  # 비효율적
            if user['id'] == user_id:
                return user
        return None
```

이 표준을 통해 **극단적으로 간결하면서도 확장 가능한 코드**를 작성할 수 있습니다. 