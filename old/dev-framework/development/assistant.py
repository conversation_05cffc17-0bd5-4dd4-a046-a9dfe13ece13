"""
개발 어시스턴트 - 프로젝트 자동 설정 도구
"""

import os
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List


@dataclass
class ProjectContext:
    size: str
    team_size: int
    has_supabase: bool
    has_mcp_tools: bool
    has_async: bool
    domain: str
    requirements: List[str]

class ProjectAnalyzer:
    """프로젝트 분석기"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        
    def analyze(self) -> ProjectContext:
        """프로젝트 분석 및 컨텍스트 생성"""
        py_files = list(self.project_root.rglob("*.py"))
        file_count = len(py_files)
        
        size = self._determine_size(file_count)
        team_size = self._get_team_size()
        domain = self._get_project_domain()
        
        return ProjectContext(
            size=size,
            team_size=team_size,
            has_supabase=self._has_supabase_config(),
            has_mcp_tools=self._has_mcp_tools(),
            has_async=self._has_async_code(),
            domain=domain,
            requirements=self._analyze_requirements()
        )
        
    def _determine_size(self, file_count: int) -> str:
        """파일 수에 따른 프로젝트 크기 결정"""
        if file_count < 10:
            return "small"
        elif file_count < 50:
            return "medium"
        else:
            return "large" 