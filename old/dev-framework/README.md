# 🏗️ Universal Development Framework (범용 개발 프레임워크)

**검증된 개발 프레임워크 - 모든 Python 프로젝트에서 재사용 가능**

이 프레임워크는 **완전 범용적**이며 어떤 도메인(전자상거래, IoT, 금융, 헬스케어, AI 등)의 Python 프로젝트든 적용할 수 있도록 설계되었습니다.

## 🎯 핵심 특징

- ✅ **완전 검증된 프레임워크**: 자동 품질 검증 시스템 내장
- ✅ **SOLID 원칙 강제 적용**: 코드 품질 표준 자동 검사
- ✅ **중복 방지 시스템**: 자동 중복 코드 탐지 및 제거
- ✅ **적응형 구조**: 프로젝트 규모에 따라 자동 조정
- ✅ **MCP 도구 연동**: AI 기반 연구 및 개발 지원
- ✅ **실행 가능한 자동화**: 문서가 아닌 실제 동작하는 도구들

## 📁 프레임워크 구조

### 📁 development/ - 핵심 개발 규칙과 도구
- `DEVELOPMENT_RULES.md` - 적응형 개발 프레임워크 규칙
- `CODE_QUALITY_STANDARDS.md` - **엄격한 코드 품질 표준 (필수)**
- `NAMING_CONVENTIONS.md` - 범용 명명 규칙 가이드
- `MCP_TOOLS_GUIDE.md` - 검증된 MCP 도구 사용법
- `simple_validator.py` - **프레임워크 품질 검증기 (핵심)**
- `assistant.py` - 프로젝트 자동 설정 도구 (모듈화됨)

### 📁 database/ - 범용 데이터베이스 설계
- `DATABASE_ARCHITECTURE.md` - 모든 도메인에 적용 가능한 DB 아키텍처

### 📁 guides/ - 실용적 개발 가이드
- `CLAUDE.md` - AI 기반 개발 워크플로우
- `patch.md` - JSONB 데이터 일관성 보장 가이드

## 🚀 새 프로젝트에서 사용하는 방법

### 1. 프레임워크 복사 및 검증
```bash
# 새 프로젝트에 프레임워크 복사
cp -r /path/to/dev-framework /your/new/project/dev-framework
cd /your/new/project/dev-framework

# 프레임워크 무결성 검증
cd development
python simple_validator.py
```

### 2. 프로젝트 자동 설정
```bash
# 개발 어시스턴트로 프로젝트 분석 및 설정 (개발 중)
# python assistant.py analyze      # 프로젝트 자동 분석
# python assistant.py setup        # 구조 자동 생성
# python assistant.py validate     # 아키텍처 검증
```

### 3. MCP 도구 활성화 (선택사항)
```bash
# 환경변수 설정으로 AI 도구 활성화
export TAVILY_API_KEY="your_key"           # 웹 연구 도구
export OPENROUTER_API_KEY="your_key"       # AI 코드 분석
export SUPABASE_URL="your_url"             # 데이터베이스 연동
export SUPABASE_KEY="your_key"

# AI 연구 수행 (개발 중)
# python assistant.py research "best python libraries for my project"
```

## 🛡️ 품질 보장 시스템

### 자동 검증 도구
이 프레임워크는 **스스로를 검증**하는 시스템을 내장하고 있습니다:

```bash
# 전체 프레임워크 품질 검증
cd dev-framework/development
python simple_validator.py

# 검증 결과는 즉시 콘솔에 출력됩니다
```

### 강제 적용되는 품질 표준
- **함수 크기**: 15라인 이하
- **클래스 크기**: 50라인 이하  
- **파일명 길이**: 12자 이하
- **파일 크기**: 200라인 이하
- **중복도**: 3% 이하
- **프로젝트별 용어 금지**: 특정 도메인 용어 사용 금지

## 🎨 적응형 특징

### 프로젝트 규모별 자동 적응
- **소규모** (< 10 파일): 단순한 구조, 빠른 개발
- **중규모** (10-50 파일): 모듈화된 구조, 도메인 분리
- **대규모** (50+ 파일): DDD 아키텍처, 완전한 분리

### 도메인별 최적화 지원
- **전자상거래**: 주문/결제/재고 패턴
- **IoT/센서**: 시계열/텔레메트리 패턴  
- **금융**: 트랜잭션/리스크 분석 패턴
- **AI/ML**: 데이터 파이프라인 패턴
- **헬스케어**: 환자/치료/기록 패턴

### 팀 크기별 규칙 조정
- **1-2명**: 간결성 우선, 빠른 프로토타이핑
- **3-5명**: 명확성과 효율성 균형
- **6명+**: 완전한 문서화, 엄격한 코드 리뷰

## 🔧 고급 기능

### 모듈화된 아키텍처
- **작은 파일들**: 모든 파일이 품질 표준을 준수
- **명확한 책임 분리**: 각 모듈이 단일 책임
- **확장 가능**: 새로운 도메인 쉽게 추가

### 자동 검증 시스템
```bash
# 실시간 품질 모니터링
python development/simple_validator.py

# 검증 항목:
# - 파일 크기 (함수 15라인, 클래스 50라인)
# - 명명 규칙 (파일명 12자 이하)
# - 프로젝트별 용어 제거 확인
```

## 📚 학습 경로

### 1단계: 핵심 이해
1. **`development/CODE_QUALITY_STANDARDS.md`** - 품질 표준 완전 숙지 (🔥 필수)
2. **`development/DEVELOPMENT_RULES.md`** - 프레임워크 철학 이해
3. **`development/simple_validator.py`** - 검증 도구 활용법

### 2단계: 도구 활용
1. **`development/MCP_TOOLS_GUIDE.md`** - AI 도구 연동 마스터
2. **`database/DATABASE_ARCHITECTURE.md`** - 범용 DB 설계
3. **`guides/patch.md`** - JSONB 데이터 일관성

### 3단계: 실전 적용
1. **작은 프로젝트부터 시작** - 개인 프로젝트 적용
2. **점진적 확장** - 팀 프로젝트로 확장
3. **커스터마이징** - 도메인별 규칙 추가

## 🎯 사용 사례

### 실제 적용 예시

#### 전자상거래 플랫폼
```bash
# 프레임워크 적용 후 구조 예시
src/
├── processors/order.py       # 주문 처리 (15라인 이하 함수들)
├── services/payment.py       # 결제 서비스 (50라인 이하 클래스)
└── utils/validator.py        # 데이터 검증 (12자 이하 파일명)
```

#### IoT 데이터 수집 시스템  
```bash
# 프레임워크 적용 후 구조 예시  
src/
├── processors/sensor.py      # 센서 데이터 처리
├── services/telemetry.py     # 원격 측정 서비스
└── utils/date_mgr.py         # 날짜 관리 유틸리티
```

#### AI/ML 파이프라인
```bash
# 프레임워크 적용 후 구조 예시
src/
├── processors/data.py        # 데이터 전처리
├── services/model.py         # 모델 서비스
└── utils/metrics.py          # 성능 지표 계산
```

## 🔄 지속적 개선

### 자동 업데이트 시스템
이 프레임워크는 **살아있는 시스템**입니다:

1. **품질 표준 자동 검증**: 매번 실행 시 품질 확인
2. **점진적 개선**: 새로운 패턴과 모범 사례 지속 통합
3. **커뮤니티 기반**: 다양한 도메인 적용 사례 수집
4. **AI 도구 연동**: 최신 개발 도구와 지속적 통합

### 기여 방법
1. **새 도메인 패턴 추가**: 다른 도메인의 성공 사례 공유
2. **품질 표준 개선**: 더 나은 코드 품질 기준 제안
3. **도구 개선**: 자동화 도구 기능 확장
4. **문서 개선**: 사용법과 가이드 개선

## 📖 추가 리소스

- **기술 문서**: `development/` 폴더의 상세 가이드
- **데이터베이스 설계**: `database/` 폴더의 아키텍처 패턴
- **실용 가이드**: `guides/` 폴더의 워크플로우 예시
- **검증 도구**: `development/simple_validator.py` 소스 코드

---

**이 프레임워크로 더 나은 코드를, 더 빠르게, 더 확실하게 작성하세요! 🚀** 