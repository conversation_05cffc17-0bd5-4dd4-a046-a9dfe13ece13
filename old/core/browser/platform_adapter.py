"""
Platform Adapter for Browser Configuration
OCP: 새로운 플랫폼 지원을 위한 확장 가능한 어댑터 패턴
DIP: 구체적인 플랫폼 구현에 의존하지 않는 추상화
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


class PlatformConfig(ABC):
    """플랫폼 설정 인터페이스"""
    
    @abstractmethod
    def get_browser_args(self) -> List[str]:
        """브라우저 실행 인수"""
        pass
    
    @abstractmethod
    def get_memory_limits(self) -> Dict[str, int]:
        """메모리 제한 설정"""
        pass
    
    @abstractmethod
    def get_performance_settings(self) -> Dict[str, Any]:
        """성능 설정"""
        pass
    
    @abstractmethod
    def get_browser_timeout(self) -> int:
        """브라우저 타임아웃"""
        pass
    
    @abstractmethod
    def get_max_browsers(self) -> int:
        """최대 브라우저 수"""
        pass


class EC2T3MediumConfig(PlatformConfig):
    """EC2 t3.medium 최적화 설정"""
    
    def get_browser_args(self) -> List[str]:
        """EC2 t3.medium용 브라우저 인수"""
        return [
            '--memory-pressure-off',
            '--max-old-space-size=300',
            '--aggressive-cache-discard',
            '--single-process',
            '--disable-background-timers',
            '--disable-renderer-backgrounding',
            '--disable-images',
            '--disable-javascript',
            '--disable-plugins',
            '--disable-extensions',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-networking',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-setuid-sandbox'
        ]
    
    def get_memory_limits(self) -> Dict[str, int]:
        """EC2 t3.medium 메모리 제한"""
        return {
            'max_memory_usage': 1024**3,  # 1GB
            'gc_threshold': 75,
            'emergency_threshold': 85
        }
    
    def get_performance_settings(self) -> Dict[str, Any]:
        """EC2 t3.medium 성능 설정"""
        return {
            'worker_count': 3,
            'batch_size': 5,
            'memory_check_interval': 15,
            'browser_restart_threshold': 50
        }
    
    def get_browser_timeout(self) -> int:
        """브라우저 타임아웃"""
        return 30000
    
    def get_max_browsers(self) -> int:
        """최대 브라우저 수"""
        return 1


class LocalDevelopmentConfig(PlatformConfig):
    """로컬 개발 환경 설정"""
    
    def get_browser_args(self) -> List[str]:
        """로컬 개발용 브라우저 인수"""
        return [
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox',
            '--disable-dev-shm-usage'
        ]
    
    def get_memory_limits(self) -> Dict[str, int]:
        """로컬 개발 환경 메모리 제한"""
        return {
            'max_memory_usage': 2 * 1024**3,  # 2GB
            'gc_threshold': 80,
            'emergency_threshold': 90
        }
    
    def get_performance_settings(self) -> Dict[str, Any]:
        """로컬 개발 환경 성능 설정"""
        return {
            'worker_count': 4,
            'batch_size': 10,
            'memory_check_interval': 30,
            'browser_restart_threshold': 100
        }
    
    def get_browser_timeout(self) -> int:
        """브라우저 타임아웃"""
        return 60000
    
    def get_max_browsers(self) -> int:
        """최대 브라우저 수"""
        return 3


class ProductionConfig(PlatformConfig):
    """운영 환경 설정"""
    
    def get_browser_args(self) -> List[str]:
        """운영 환경용 브라우저 인수"""
        return [
            '--memory-pressure-off',
            '--max-old-space-size=512',
            '--aggressive-cache-discard',
            '--disable-background-timers',
            '--disable-renderer-backgrounding',
            '--disable-images',
            '--disable-javascript',
            '--disable-plugins',
            '--disable-extensions',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-networking',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-setuid-sandbox'
        ]
    
    def get_memory_limits(self) -> Dict[str, int]:
        """운영 환경 메모리 제한"""
        return {
            'max_memory_usage': 2 * 1024**3,  # 2GB
            'gc_threshold': 70,
            'emergency_threshold': 80
        }
    
    def get_performance_settings(self) -> Dict[str, Any]:
        """운영 환경 성능 설정"""
        return {
            'worker_count': 4,
            'batch_size': 8,
            'memory_check_interval': 10,
            'browser_restart_threshold': 30
        }
    
    def get_browser_timeout(self) -> int:
        """브라우저 타임아웃"""
        return 45000
    
    def get_max_browsers(self) -> int:
        """최대 브라우저 수"""
        return 2


class PlatformAdapter:
    """플랫폼 어댑터 - 플랫폼별 설정을 제공"""
    
    def __init__(self, platform_type: str = "auto"):
        self.platform_type = platform_type
        self._platform_config = self._create_platform_config()
    
    def _create_platform_config(self) -> PlatformConfig:
        """플랫폼 설정 생성"""
        if self.platform_type == "auto":
            # 자동 감지
            try:
                # EC2 감지 로직
                from config.ec2_config import apply_t3_medium_optimization
                logger.info("🔧 EC2 환경 감지됨")
                return EC2T3MediumConfig()
            except ImportError:
                # 환경 변수로 판단
                import os
                env_type = os.getenv('ENVIRONMENT', 'development').lower()
                if env_type == 'production':
                    logger.info("🔧 운영 환경 감지됨")
                    return ProductionConfig()
                else:
                    logger.info("🔧 로컬 개발 환경 감지됨")
                    return LocalDevelopmentConfig()
        
        elif self.platform_type == "ec2":
            return EC2T3MediumConfig()
        elif self.platform_type == "local":
            return LocalDevelopmentConfig()
        elif self.platform_type == "production":
            return ProductionConfig()
        else:
            logger.warning(f"알 수 없는 플랫폼 타입: {self.platform_type}, 기본 설정 사용")
            return LocalDevelopmentConfig()
    
    def get_browser_config(self) -> Dict[str, Any]:
        """브라우저 설정 반환"""
        return {
            'args': self._platform_config.get_browser_args(),
            'timeout': self._platform_config.get_browser_timeout(),
            'max_browsers': self._platform_config.get_max_browsers()
        }
    
    def get_memory_config(self) -> Dict[str, Any]:
        """메모리 설정 반환"""
        memory_limits = self._platform_config.get_memory_limits()
        performance_settings = self._platform_config.get_performance_settings()
        
        return {
            **memory_limits,
            'memory_check_interval': performance_settings['memory_check_interval']
        }
    
    def get_performance_config(self) -> Dict[str, Any]:
        """성능 설정 반환"""
        return self._platform_config.get_performance_settings()
    
    def get_full_config(self) -> Dict[str, Any]:
        """전체 설정 반환"""
        return {
            'platform_type': self.platform_type,
            'browser': self.get_browser_config(),
            'memory': self.get_memory_config(),
            'performance': self.get_performance_config()
        }
    
    def log_platform_info(self) -> None:
        """플랫폼 정보 로깅"""
        config = self.get_full_config()
        logger.info(f"🔧 플랫폼 어댑터 초기화: {self.platform_type}")
        logger.info(f"   브라우저: {config['browser']['max_browsers']}개")
        logger.info(f"   메모리 제한: {config['memory']['max_memory_usage'] // 1024 // 1024}MB")
        logger.info(f"   워커 수: {config['performance']['worker_count']}개")