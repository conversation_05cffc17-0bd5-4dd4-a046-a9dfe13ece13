"""
데이터 수집 관련 예외 클래스 정의
크롤링, 파싱, 리소스 관리 등의 예외 타입들
"""
from typing import Optional, Dict, Any
from .base_exceptions import SystemException


class CollectionException(SystemException):
    """데이터 수집 관련 기본 예외 클래스"""
    
    def __init__(self, message: str = "데이터 수집 중 오류가 발생했습니다.", **kwargs):
        kwargs.setdefault("error_code", "COLLECTION_ERROR")
        super().__init__(message, **kwargs)


class NavigationException(CollectionException):
    """페이지 탐색 관련 예외"""
    
    def __init__(
        self, 
        url: str, 
        status_code: Optional[int] = None,
        timeout: Optional[int] = None,
        **kwargs
    ):
        self.url = url
        self.status_code = status_code
        self.timeout = timeout
        
        message = f"페이지 탐색 실패: {url}"
        if status_code:
            message += f" (상태 코드: {status_code})"
        if timeout:
            message += f" (타임아웃: {timeout}초)"
        
        kwargs.setdefault("error_code", "NAVIGATION_ERROR")
        kwargs.setdefault("context", {}).update({
            "url": url,
            "status_code": status_code,
            "timeout": timeout
        })
        
        super().__init__(message, **kwargs)


class ParsingException(CollectionException):
    """HTML/데이터 파싱 관련 예외"""
    
    def __init__(
        self, 
        element: str, 
        parser_type: Optional[str] = None,
        data_source: Optional[str] = None,
        **kwargs
    ):
        self.element = element
        self.parser_type = parser_type
        self.data_source = data_source
        
        message = f"파싱 실패: {element}"
        if parser_type:
            message += f" (파서: {parser_type})"
        if data_source:
            message += f" (소스: {data_source})"
        
        kwargs.setdefault("error_code", "PARSING_ERROR")
        kwargs.setdefault("context", {}).update({
            "element": element,
            "parser_type": parser_type,
            "data_source": data_source
        })
        
        super().__init__(message, **kwargs)


class TimeoutException(CollectionException):
    """타임아웃 관련 예외"""
    
    def __init__(
        self, 
        operation: str, 
        timeout_seconds: int,
        actual_duration: Optional[float] = None,
        **kwargs
    ):
        self.operation = operation
        self.timeout_seconds = timeout_seconds
        self.actual_duration = actual_duration
        
        message = f"작업 타임아웃: {operation} ({timeout_seconds}초 초과)"
        if actual_duration:
            message += f" (실제 소요: {actual_duration:.2f}초)"
        
        kwargs.setdefault("error_code", "TIMEOUT_ERROR")
        kwargs.setdefault("context", {}).update({
            "operation": operation,
            "timeout_seconds": timeout_seconds,
            "actual_duration": actual_duration
        })
        
        super().__init__(message, **kwargs)


class DataExtractionException(CollectionException):
    """데이터 추출 관련 예외"""
    
    def __init__(
        self, 
        data_type: str, 
        extraction_method: Optional[str] = None,
        expected_count: Optional[int] = None,
        actual_count: Optional[int] = None,
        **kwargs
    ):
        self.data_type = data_type
        self.extraction_method = extraction_method
        self.expected_count = expected_count
        self.actual_count = actual_count
        
        message = f"데이터 추출 실패: {data_type}"
        if extraction_method:
            message += f" (추출 방법: {extraction_method})"
        if expected_count is not None and actual_count is not None:
            message += f" (예상: {expected_count}개, 실제: {actual_count}개)"
        
        kwargs.setdefault("error_code", "EXTRACTION_ERROR")
        kwargs.setdefault("context", {}).update({
            "data_type": data_type,
            "extraction_method": extraction_method,
            "expected_count": expected_count,
            "actual_count": actual_count
        })
        
        super().__init__(message, **kwargs)


class ResourceException(CollectionException):
    """리소스 관리 관련 예외"""
    
    def __init__(
        self, 
        resource_type: str, 
        resource_id: Optional[str] = None,
        operation: Optional[str] = None,
        **kwargs
    ):
        self.resource_type = resource_type
        self.resource_id = resource_id
        self.operation = operation
        
        message = f"리소스 오류: {resource_type}"
        if resource_id:
            message += f" (ID: {resource_id})"
        if operation:
            message += f" (작업: {operation})"
        
        kwargs.setdefault("error_code", "RESOURCE_ERROR")
        kwargs.setdefault("context", {}).update({
            "resource_type": resource_type,
            "resource_id": resource_id,
            "operation": operation
        })
        
        super().__init__(message, **kwargs) 