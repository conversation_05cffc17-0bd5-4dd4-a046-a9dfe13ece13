"""
서비스 계층 관련 예외 클래스 정의
비즈니스 로직, 데이터 저장소, 캐시 등의 예외 타입들
"""
from typing import Optional, Dict, Any
from .base_exceptions import SystemException


class ServiceException(SystemException):
    """서비스 계층 기본 예외 클래스"""
    
    def __init__(self, message: str = "서비스 처리 중 오류가 발생했습니다.", **kwargs):
        kwargs.setdefault("error_code", "SERVICE_ERROR")
        super().__init__(message, **kwargs)


class RepositoryException(ServiceException):
    """데이터 저장소 관련 예외"""
    
    def __init__(
        self, 
        operation: str, 
        entity_type: Optional[str] = None,
        entity_id: Optional[str] = None,
        **kwargs
    ):
        self.operation = operation
        self.entity_type = entity_type
        self.entity_id = entity_id
        
        message = f"저장소 오류: {operation}"
        if entity_type:
            message += f" (엔티티: {entity_type})"
        if entity_id:
            message += f" (ID: {entity_id})"
        
        kwargs.setdefault("error_code", "REPOSITORY_ERROR")
        kwargs.setdefault("context", {}).update({
            "operation": operation,
            "entity_type": entity_type,
            "entity_id": entity_id
        })
        
        super().__init__(message, **kwargs)


class CacheException(ServiceException):
    """캐시 관련 예외"""
    
    def __init__(
        self, 
        operation: str, 
        cache_key: Optional[str] = None,
        cache_type: Optional[str] = None,
        **kwargs
    ):
        self.operation = operation
        self.cache_key = cache_key
        self.cache_type = cache_type
        
        message = f"캐시 오류: {operation}"
        if cache_key:
            message += f" (키: {cache_key})"
        if cache_type:
            message += f" (타입: {cache_type})"
        
        kwargs.setdefault("error_code", "CACHE_ERROR")
        kwargs.setdefault("context", {}).update({
            "operation": operation,
            "cache_key": cache_key,
            "cache_type": cache_type
        })
        
        super().__init__(message, **kwargs)


class NotificationException(ServiceException):
    """알림 관련 예외"""
    
    def __init__(
        self, 
        notification_type: str, 
        recipient: Optional[str] = None,
        channel: Optional[str] = None,
        **kwargs
    ):
        self.notification_type = notification_type
        self.recipient = recipient
        self.channel = channel
        
        message = f"알림 오류: {notification_type}"
        if recipient:
            message += f" (수신자: {recipient})"
        if channel:
            message += f" (채널: {channel})"
        
        kwargs.setdefault("error_code", "NOTIFICATION_ERROR")
        kwargs.setdefault("context", {}).update({
            "notification_type": notification_type,
            "recipient": recipient,
            "channel": channel
        })
        
        super().__init__(message, **kwargs) 