"""
Core Exceptions - 시스템 전반에 사용되는 예외 클래스 정의
"""
from .base_exceptions import (
    SystemException,
    ValidationException,
    ConfigurationException
)
from .collection_exceptions import (
    CollectionException,
    NavigationException,
    ParsingException,
    TimeoutException,
    DataExtractionException,
    ResourceException
)
from .service_exceptions import (
    ServiceException,
    RepositoryException,
    CacheException,
    NotificationException
)

__all__ = [
    # Base exceptions
    'SystemException',
    'ValidationException', 
    'ConfigurationException',
    
    # Collection exceptions
    'CollectionException',
    'NavigationException',
    'ParsingException', 
    'TimeoutException',
    'DataExtractionException',
    'ResourceException',
    
    # Service exceptions
    'ServiceException',
    'RepositoryException',
    'CacheException',
    'NotificationException'
] 