"""
통합 팀 매칭 시스템
"""
import logging
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass
import json
import os

logger = logging.getLogger(__name__)


@dataclass
class TeamMatch:
    """팀 매칭 결과"""
    team_id: str
    team_name: str
    league_id: str
    confidence: float = 1.0


class UniversalTeamMatcher:
    """통합 팀 매칭 시스템"""
    
    def __init__(self, mapping_file: Optional[str] = None):
        """
        초기화
        
        Args:
            mapping_file: 팀 매핑 파일 경로
        """
        self.mapping_file = mapping_file or "team_mappings.json"
        self.mappings: Dict[str, Dict[str, str]] = {}
        self.reverse_mappings: Dict[str, Dict[str, str]] = {}
        
        # 기본 매핑 로드
        self._load_default_mappings()
        
        # 파일에서 매핑 로드
        self._load_mappings_from_file()
        
        logger.info("UniversalTeamMatcher 초기화 완료")
    
    def _load_default_mappings(self):
        """기본 팀 매핑 로드"""
        try:
            # 축구 팀 매핑
            self.mappings['soccer'] = {
                'K01': '울산 HD FC',
                'K02': '포항 스틸러스',
                'K03': '대구 FC',
                'K04': '전북 현대 모터스',
                'K05': '광주 FC',
                'K06': '제주 유나이티드',
                'K07': '인천 유나이티드',
                'K08': '수원 FC',
                'K09': '김천 상무',
                'K10': '강원 FC',
                'K11': '서울 FC',
                'K12': '수원 삼성 블루윙즈'
            }
            
            # 농구 팀 매핑
            self.mappings['basketball'] = {
                'KBL001': '서울 SK 나이츠',
                'KBL002': '부산 KT 소닉붐',
                'KBL003': '안양 정관장 레드부스터',
                'KBL004': '창원 LG 세이커스',
                'KBL005': '고양 소노 스카이거너스',
                'KBL006': '원주 DB 프로미',
                'KBL007': '울산 현대모비스 피버스',
                'KBL008': '대구 한국가스공사 페가수스',
                'KBL009': '서울 삼성 썬더스',
                'KBL010': '전주 KCC 이지스',
                # NBA 팀들
                'LAL': 'LA 레이커스',
                'GSW': '골든스테이트 워리어스',
                'BOS': '보스턴 셀틱스',
                'MIA': '마이애미 히트',
                'HOU': '휴스턴 로켓츠'
            }
            
            # 배구 팀 매핑
            self.mappings['volleyball'] = {
                '1001': '현대캐피탈 스카이워커스',
                '1002': '한국전력 빅스톰',
                '1003': 'OK금융그룹 읏맨',
                '1004': '대한항공 점보스',
                '1005': '우리카드 위비',
                '1006': '삼성화재 블루팡스',
                '1007': 'KB손해보험 스타즈',
                # 여자부
                '2001': '현대건설 힐스테이트',
                '2002': '흥국생명 핑크스파이더스',
                '2003': 'GS칼텍스 서울 KIXX',
                '2004': '한국도로공사 하이패스',
                '2005': 'IBK기업은행 알토스',
                '2006': '정관장 레드스파크스',
                '2007': 'KGC인삼공사 인삼공사'
            }
            
            # 야구 팀 매핑
            self.mappings['baseball'] = {
                'LG': 'LG 트윈스',
                'KT': 'KT 위즈',
                'SSG': 'SSG 랜더스',
                'NC': 'NC 다이노스',
                'DOO': '두산 베어스',
                'KIA': 'KIA 타이거즈',
                'LOT': '롯데 자이언츠',
                'SAM': '삼성 라이온즈',
                'HAN': '한화 이글스',
                'KIW': '키움 히어로즈',
                # MLB 팀들
                'NYY': '뉴욕 양키스',
                'BOS': '보스턴 레드삭스',
                'LAD': 'LA 다저스',
                'HOU': '휴스턴 애스트로스',
                # NPB 팀들 (DB team_id -> website teamId 매핑)
                'C1': '한신 타이거스',  # DB: C1, Website: P5
                'B1': '요미우리 자이언츠',
                'B2': '야쿠르트 스왈로즈',
                'B3': '요코하마 베이스타즈',
                'B4': '중일 드래곤즈',
                'B5': '히로시마 카프',
                'P1': '소프트뱅크 호크스',
                'P2': '닛폰햄 파이터스',
                'P3': '라쿠텐 이글스',
                'P4': '세이부 라이온즈',
                'P5': '롯데 마린즈',
                'P6': '오릭스 버팔로즈'
            }
            
            # NPB 팀 ID 변환 매핑 (DB team_id -> website teamId)
            self.npb_team_id_mapping = {
                'C1': 'C1',  # 한신 타이거스: DB=C1, Website=C1 (동일)
                'B1': 'B1',  # 요미우리: 동일
                'B2': 'B2',  # 야쿠르트: 동일  
                'B3': 'B3',  # 요코하마: 동일
                'B4': 'B4',  # 중일: 동일
                'B5': 'B5',  # 히로시마: 동일
                'P1': 'P1',  # 소프트뱅크: 동일
                'P2': 'P2',  # 닛폰햄: 동일
                'P3': 'P3',  # 라쿠텐: 동일
                'P4': 'P4',  # 세이부: 동일
                'P5': 'P5',  # 롯데: 동일
                'P6': 'P6'   # 오릭스: 동일
            }
            
            # 역방향 매핑 생성
            self._build_reverse_mappings()
            
        except Exception as e:
            logger.error(f"기본 매핑 로드 실패: {e}")
    
    def _build_reverse_mappings(self):
        """역방향 매핑 생성 (팀명 -> 팀ID)"""
        try:
            self.reverse_mappings = {}
            for sport, mappings in self.mappings.items():
                self.reverse_mappings[sport] = {
                    team_name: team_id 
                    for team_id, team_name in mappings.items()
                }
        except Exception as e:
            logger.error(f"역방향 매핑 생성 실패: {e}")
    
    def _load_mappings_from_file(self):
        """파일에서 매핑 로드"""
        try:
            if os.path.exists(self.mapping_file):
                with open(self.mapping_file, 'r', encoding='utf-8') as f:
                    file_mappings = json.load(f)
                
                # 파일 매핑을 기본 매핑에 병합
                for sport, mappings in file_mappings.items():
                    if sport not in self.mappings:
                        self.mappings[sport] = {}
                    self.mappings[sport].update(mappings)
                
                # 역방향 매핑 재생성
                self._build_reverse_mappings()
                
                logger.info(f"매핑 파일 로드 완료: {self.mapping_file}")
            
        except Exception as e:
            logger.warning(f"매핑 파일 로드 실패: {e}")
    
    def save_mappings_to_file(self):
        """매핑을 파일에 저장"""
        try:
            with open(self.mapping_file, 'w', encoding='utf-8') as f:
                json.dump(self.mappings, f, ensure_ascii=False, indent=2)
            
            logger.info(f"매핑 파일 저장 완료: {self.mapping_file}")
            
        except Exception as e:
            logger.error(f"매핑 파일 저장 실패: {e}")
    
    def add_team_mapping(self, sport: str, team_id: str, team_name: str):
        """팀 매핑 추가"""
        try:
            if sport not in self.mappings:
                self.mappings[sport] = {}
            
            self.mappings[sport][team_id] = team_name
            
            # 역방향 매핑 업데이트
            if sport not in self.reverse_mappings:
                self.reverse_mappings[sport] = {}
            self.reverse_mappings[sport][team_name] = team_id
            
            logger.debug(f"팀 매핑 추가: {sport} - {team_id}: {team_name}")
            
        except Exception as e:
            logger.error(f"팀 매핑 추가 실패: {e}")
    
    def get_team_name(self, sport: str, team_id: str) -> Optional[str]:
        """팀 ID로 팀명 조회"""
        try:
            return self.mappings.get(sport, {}).get(team_id)
        except Exception as e:
            logger.error(f"팀명 조회 실패 {sport}-{team_id}: {e}")
            return None
    
    def get_team_id(self, sport: str, team_name: str) -> Optional[str]:
        """팀명으로 팀 ID 조회"""
        try:
            return self.reverse_mappings.get(sport, {}).get(team_name)
        except Exception as e:
            logger.error(f"팀 ID 조회 실패 {sport}-{team_name}: {e}")
            return None
    
    def get_website_team_id(self, db_team_id: str) -> str:
        """NPB 팀의 DB team_id를 website teamId로 변환"""
        if hasattr(self, 'npb_team_id_mapping'):
            return self.npb_team_id_mapping.get(db_team_id, db_team_id)
        return db_team_id
    
    def get_all_teams(self, sport: str) -> Dict[str, str]:
        """특정 스포츠의 모든 팀 매핑 조회"""
        try:
            return self.mappings.get(sport, {}).copy()
        except Exception as e:
            logger.error(f"전체 팀 조회 실패 {sport}: {e}")
            return {}
    
    def get_supported_sports(self) -> List[str]:
        """지원하는 스포츠 목록"""
        return list(self.mappings.keys())
    
    def search_teams(self, sport: str, query: str) -> List[Dict[str, str]]:
        """팀 검색"""
        try:
            results = []
            sport_mappings = self.mappings.get(sport, {})
            
            query_lower = query.lower()
            
            for team_id, team_name in sport_mappings.items():
                if (query_lower in team_id.lower() or 
                    query_lower in team_name.lower()):
                    results.append({
                        'team_id': team_id,
                        'team_name': team_name,
                        'sport': sport
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"팀 검색 실패 {sport}-{query}: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """매핑 통계 정보"""
        try:
            stats = {
                'total_sports': len(self.mappings),
                'sports': {},
                'total_teams': 0
            }
            
            for sport, mappings in self.mappings.items():
                team_count = len(mappings)
                stats['sports'][sport] = {
                    'team_count': team_count,
                    'teams': list(mappings.values())
                }
                stats['total_teams'] += team_count
            
            return stats
            
        except Exception as e:
            logger.error(f"통계 생성 실패: {e}")
            return {'error': str(e)}
    
    def validate_mapping(self, sport: str, team_id: str, team_name: str) -> bool:
        """매핑 유효성 검증"""
        try:
            # 기본 검증
            if not sport or not team_id or not team_name:
                return False
            
            # 중복 검증
            existing_name = self.get_team_name(sport, team_id)
            if existing_name and existing_name != team_name:
                logger.warning(f"팀 ID 중복: {sport}-{team_id} ({existing_name} vs {team_name})")
                return False
            
            existing_id = self.get_team_id(sport, team_name)
            if existing_id and existing_id != team_id:
                logger.warning(f"팀명 중복: {sport}-{team_name} ({existing_id} vs {team_id})")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"매핑 검증 실패: {e}")
            return False
    
    async def match_team(self, team_name: str, sport, league_id: str = None) -> Optional[TeamMatch]:
        """팀 매칭"""
        try:
            sport_str = sport.value if hasattr(sport, 'value') else str(sport)
            
            # 팀 ID 검색
            team_id = self.get_team_id(sport_str, team_name)
            if team_id:
                return TeamMatch(
                    team_id=team_id,
                    team_name=team_name,
                    league_id=league_id or '',
                    confidence=1.0
                )
            
            # 부분 매칭 시도
            for tid, tname in self.mappings.get(sport_str, {}).items():
                if team_name in tname or tname in team_name:
                    return TeamMatch(
                        team_id=tid,
                        team_name=tname,
                        league_id=league_id or '',
                        confidence=0.8
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"팀 매칭 실패 {team_name}: {e}")
            return None


# 전역 인스턴스
team_matcher = UniversalTeamMatcher()
