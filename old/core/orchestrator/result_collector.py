"""
Result Collection and Aggregation - 결과 수집 및 집계 전용 클래스
SRP: 결과 수집과 집계만 담당하는 단일 책임 클래스
"""
import logging
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


@dataclass
class OrchestrationResult:
    """조율 결과 데이터"""
    sport: str
    success: bool
    message: str = ""
    data_count: int = 0
    duration: float = 0.0
    errors: List[str] = field(default_factory=list)
    teams_processed: int = 0
    players_processed: int = 0


@dataclass
class OrchestrationSummary:
    """전체 조율 요약"""
    success: bool
    message: str
    total_sports: int
    successful_sports: int
    failed_sports: int
    total_data_count: int
    total_duration: float
    results: List[OrchestrationResult]
    errors: List[str] = field(default_factory=list)


class ResultCollector:
    """결과 수집기 - 오케스트레이션 결과를 수집하고 집계"""
    
    def __init__(self):
        self._results: List[OrchestrationResult] = []
        self._start_time: Optional[datetime] = None
    
    def start_collection(self) -> None:
        """결과 수집 시작"""
        self._start_time = datetime.now()
        self._results.clear()
        logger.info("🎯 결과 수집 시작")
    
    def add_result(self, result: OrchestrationResult) -> None:
        """결과 추가"""
        self._results.append(result)
        status = "✅ 성공" if result.success else "❌ 실패"
        logger.info(f"{status}: {result.sport} - {result.message}")
    
    def add_sport_result(
        self,
        sport: str,
        success: bool,
        message: str = "",
        data_count: int = 0,
        duration: float = 0.0,
        errors: Optional[List[str]] = None,
        teams_processed: int = 0,
        players_processed: int = 0
    ) -> None:
        """스포츠 결과 추가 (편의 메서드)"""
        result = OrchestrationResult(
            sport=sport,
            success=success,
            message=message,
            data_count=data_count,
            duration=duration,
            errors=errors or [],
            teams_processed=teams_processed,
            players_processed=players_processed
        )
        self.add_result(result)
    
    def add_error_result(self, sport: str, error: Exception) -> None:
        """에러 결과 추가"""
        error_msg = f"{sport} 실행 실패: {str(error)}"
        self.add_sport_result(
            sport=sport,
            success=False,
            message=error_msg,
            errors=[str(error)]
        )
    
    def get_results(self) -> List[OrchestrationResult]:
        """모든 결과 반환"""
        return self._results.copy()
    
    def get_summary(self) -> OrchestrationSummary:
        """결과 요약 생성"""
        if not self._start_time:
            raise RuntimeError("결과 수집이 시작되지 않았습니다")
        
        total_duration = (datetime.now() - self._start_time).total_seconds()
        successful_count = sum(1 for r in self._results if r.success)
        failed_count = len(self._results) - successful_count
        total_data_count = sum(r.data_count for r in self._results)
        
        # 모든 에러 수집
        all_errors = []
        for result in self._results:
            all_errors.extend(result.errors)
        
        # 성공 여부 결정
        success = failed_count == 0 and len(self._results) > 0
        
        # 메시지 생성
        if success:
            message = f"{successful_count}개 스포츠 성공, {total_data_count}개 데이터 처리"
        else:
            message = f"{successful_count}개 성공, {failed_count}개 실패"
        
        return OrchestrationSummary(
            success=success,
            message=message,
            total_sports=len(self._results),
            successful_sports=successful_count,
            failed_sports=failed_count,
            total_data_count=total_data_count,
            total_duration=total_duration,
            results=self._results.copy(),
            errors=all_errors
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """결과 통계 반환"""
        if not self._results:
            return {"message": "결과 없음"}
        
        successful_results = [r for r in self._results if r.success]
        failed_results = [r for r in self._results if not r.success]
        
        return {
            "total_sports": len(self._results),
            "successful_sports": len(successful_results),
            "failed_sports": len(failed_results),
            "success_rate": len(successful_results) / len(self._results) * 100,
            "total_data_processed": sum(r.data_count for r in self._results),
            "total_teams_processed": sum(r.teams_processed for r in self._results),
            "total_players_processed": sum(r.players_processed for r in self._results),
            "average_duration": sum(r.duration for r in self._results) / len(self._results),
            "total_errors": sum(len(r.errors) for r in self._results),
            "sports_summary": {
                r.sport: {
                    "success": r.success,
                    "data_count": r.data_count,
                    "duration": r.duration,
                    "error_count": len(r.errors)
                }
                for r in self._results
            }
        }
    
    def get_failed_sports(self) -> List[OrchestrationResult]:
        """실패한 스포츠 결과 반환"""
        return [r for r in self._results if not r.success]
    
    def get_successful_sports(self) -> List[OrchestrationResult]:
        """성공한 스포츠 결과 반환"""
        return [r for r in self._results if r.success]
    
    def has_errors(self) -> bool:
        """에러가 있는지 확인"""
        return any(r.errors for r in self._results)
    
    def clear_results(self) -> None:
        """결과 초기화"""
        self._results.clear()
        self._start_time = None
        logger.info("🧹 결과 수집기 초기화됨")
    
    def log_summary(self) -> None:
        """요약 로그 출력"""
        if not self._results:
            logger.info("📊 수집된 결과 없음")
            return
        
        summary = self.get_summary()
        logger.info(f"📊 조율 결과 요약: {summary.message}")
        logger.info(f"   총 소요 시간: {summary.total_duration:.2f}초")
        logger.info(f"   성공률: {summary.successful_sports}/{summary.total_sports}")
        
        if summary.errors:
            logger.warning(f"   총 에러 수: {len(summary.errors)}")
            for error in summary.errors[:3]:  # 최대 3개까지만 표시
                logger.warning(f"     - {error}")
    
    def export_results(self, format: str = "dict") -> Any:
        """결과 내보내기"""
        if format == "dict":
            return {
                "summary": self.get_summary().__dict__,
                "statistics": self.get_statistics(),
                "results": [r.__dict__ for r in self._results]
            }
        elif format == "json":
            import json
            return json.dumps(self.export_results("dict"), indent=2, default=str)
        else:
            raise ValueError(f"지원하지 않는 형식: {format}")