"""
Enhanced Configuration Management System
SRP: 설정 관리만 담당하는 단일 책임 클래스
OCP: 새로운 설정 타입을 쉽게 추가할 수 있는 확장 가능한 구조
"""
import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any, Optional, Type, TypeVar, Generic
from dataclasses import dataclass, field

from .environment import EnvironmentManager

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ConfigProvider(ABC, Generic[T]):
    """설정 제공자 인터페이스 (DIP 원칙 적용)"""
    
    @abstractmethod
    def load_config(self) -> T:
        """설정 로드"""
        pass
    
    @abstractmethod
    def validate_config(self, config: T) -> bool:
        """설정 검증"""
        pass


@dataclass
class PlatformConfig:
    """플랫폼별 설정"""
    platform_type: str
    browser_settings: Dict[str, Any] = field(default_factory=dict)
    memory_settings: Dict[str, Any] = field(default_factory=dict)
    performance_settings: Dict[str, Any] = field(default_factory=dict)


@dataclass
class OrchestrationConfig:
    """오케스트레이션 설정"""
    max_browsers: int = 1
    batch_size: int = 5
    worker_timeout: int = 600
    memory_limit: int = 1024**3
    headless: bool = True
    enable_cache: bool = True
    sport_delay: int = 5
    platform_type: str = "auto"


@dataclass
class ValidationConfig:
    """검증 시스템 설정"""
    output_dir: str = "results/validation"
    max_browsers: int = 3
    headless: bool = True
    enable_html_report: bool = True
    cache_enabled: bool = True


class OrchestrationConfigProvider(ConfigProvider[OrchestrationConfig]):
    """오케스트레이션 설정 제공자"""
    
    def __init__(self, env_manager: EnvironmentManager):
        self.env_manager = env_manager
    
    def load_config(self) -> OrchestrationConfig:
        """오케스트레이션 설정 로드"""
        performance_config = self.env_manager.get_performance_config()
        crawling_config = self.env_manager.get_crawling_config()
        
        return OrchestrationConfig(
            max_browsers=performance_config.max_browsers,
            batch_size=performance_config.batch_size,
            worker_timeout=performance_config.worker_count * 200,  # 계산된 값
            memory_limit=performance_config.max_memory_usage,
            headless=crawling_config.headless,
            enable_cache=performance_config.cache_ttl > 0,
            sport_delay=5,  # 기본값
            platform_type=self.env_manager.get_environment_type()
        )
    
    def validate_config(self, config: OrchestrationConfig) -> bool:
        """설정 검증"""
        return (
            config.max_browsers > 0 and
            config.batch_size > 0 and
            config.worker_timeout > 0 and
            config.memory_limit > 0 and
            config.sport_delay >= 0
        )


class ValidationConfigProvider(ConfigProvider[ValidationConfig]):
    """검증 설정 제공자"""
    
    def __init__(self, env_manager: EnvironmentManager):
        self.env_manager = env_manager
    
    def load_config(self) -> ValidationConfig:
        """검증 설정 로드"""
        performance_config = self.env_manager.get_performance_config()
        crawling_config = self.env_manager.get_crawling_config()
        
        return ValidationConfig(
            output_dir="results/validation",
            max_browsers=min(performance_config.max_browsers, 3),
            headless=crawling_config.headless,
            enable_html_report=not self.env_manager.is_development(),
            cache_enabled=performance_config.cache_ttl > 0
        )
    
    def validate_config(self, config: ValidationConfig) -> bool:
        """설정 검증"""
        return (
            config.max_browsers > 0 and
            bool(config.output_dir)
        )


class ConfigManager:
    """설정 관리자 - 모든 설정을 통합 관리"""
    
    def __init__(self, env_manager: Optional[EnvironmentManager] = None):
        self.env_manager = env_manager or EnvironmentManager()
        self._config_cache: Dict[str, Any] = {}
        self._providers: Dict[str, ConfigProvider] = {}
        
        # 기본 제공자 등록
        self._register_default_providers()
    
    def _register_default_providers(self) -> None:
        """기본 설정 제공자 등록"""
        self._providers['orchestration'] = OrchestrationConfigProvider(self.env_manager)
        self._providers['validation'] = ValidationConfigProvider(self.env_manager)
    
    def register_provider(self, name: str, provider: ConfigProvider) -> None:
        """설정 제공자 등록"""
        self._providers[name] = provider
        logger.info(f"설정 제공자 등록: {name}")
    
    def get_config(self, config_type: str, use_cache: bool = True) -> Any:
        """설정 조회"""
        if use_cache and config_type in self._config_cache:
            return self._config_cache[config_type]
        
        if config_type not in self._providers:
            raise ValueError(f"알 수 없는 설정 타입: {config_type}")
        
        provider = self._providers[config_type]
        config = provider.load_config()
        
        # 검증
        if not provider.validate_config(config):
            raise ValueError(f"설정 검증 실패: {config_type}")
        
        # 캐시 저장
        if use_cache:
            self._config_cache[config_type] = config
        
        return config
    
    def get_orchestration_config(self) -> OrchestrationConfig:
        """오케스트레이션 설정 조회"""
        return self.get_config('orchestration')
    
    def get_validation_config(self) -> ValidationConfig:
        """검증 설정 조회"""
        return self.get_config('validation')
    
    def get_platform_config(self, platform_type: str = "auto") -> PlatformConfig:
        """플랫폼 설정 조회"""
        from core.browser.platform_adapter import PlatformAdapter
        
        adapter = PlatformAdapter(platform_type)
        full_config = adapter.get_full_config()
        
        return PlatformConfig(
            platform_type=full_config['platform_type'],
            browser_settings=full_config['browser'],
            memory_settings=full_config['memory'],
            performance_settings=full_config['performance']
        )
    
    def reload_config(self, config_type: str) -> Any:
        """설정 재로드"""
        if config_type in self._config_cache:
            del self._config_cache[config_type]
        
        return self.get_config(config_type, use_cache=False)
    
    def clear_cache(self) -> None:
        """설정 캐시 초기화"""
        self._config_cache.clear()
        logger.info("설정 캐시 초기화됨")
    
    def get_all_configs(self) -> Dict[str, Any]:
        """모든 설정 조회"""
        configs = {}
        for config_type in self._providers.keys():
            try:
                configs[config_type] = self.get_config(config_type)
            except Exception as e:
                logger.error(f"설정 로드 실패: {config_type} - {e}")
                configs[config_type] = None
        
        return configs
    
    def validate_all_configs(self) -> Dict[str, bool]:
        """모든 설정 검증"""
        validation_results = {}
        
        for config_type, provider in self._providers.items():
            try:
                config = provider.load_config()
                validation_results[config_type] = provider.validate_config(config)
            except Exception as e:
                logger.error(f"설정 검증 실패: {config_type} - {e}")
                validation_results[config_type] = False
        
        return validation_results
    
    def get_config_summary(self) -> Dict[str, Any]:
        """설정 요약 정보"""
        orchestration_config = self.get_orchestration_config()
        validation_config = self.get_validation_config()
        
        return {
            "orchestration": {
                "max_browsers": orchestration_config.max_browsers,
                "batch_size": orchestration_config.batch_size,
                "memory_limit_mb": orchestration_config.memory_limit // 1024 // 1024,
                "platform_type": orchestration_config.platform_type,
                "cache_enabled": orchestration_config.enable_cache
            },
            "validation": {
                "output_dir": validation_config.output_dir,
                "max_browsers": validation_config.max_browsers,
                "html_report": validation_config.enable_html_report,
                "cache_enabled": validation_config.cache_enabled
            },
            "environment": {
                "type": self.env_manager.get_environment_type(),
                "is_development": self.env_manager.is_development(),
                "is_production": self.env_manager.is_production()
            }
        }


# 전역 설정 관리자 인스턴스
_global_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """전역 설정 관리자 인스턴스 반환"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = ConfigManager()
    return _global_config_manager


def get_orchestration_config() -> OrchestrationConfig:
    """오케스트레이션 설정 조회 (편의 함수)"""
    return get_config_manager().get_orchestration_config()


def get_validation_config() -> ValidationConfig:
    """검증 설정 조회 (편의 함수)"""
    return get_config_manager().get_validation_config()


def get_platform_config(platform_type: str = "auto") -> PlatformConfig:
    """플랫폼 설정 조회 (편의 함수)"""
    return get_config_manager().get_platform_config(platform_type)