"""
환경 변수 및 설정 관리
"""
import logging
import os
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Optional

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    logging.warning("python-dotenv not installed. Environment variables must be set manually.")


@dataclass
class DatabaseConfig:
    """데이터베이스 설정"""
    url: str
    key: str
    service_role_key: Optional[str] = None
    pool_size: int = 10
    timeout: int = 30


@dataclass
class CrawlingConfig:
    """크롤링 설정"""
    max_concurrent_requests: int = 5
    request_delay_ms: int = 1000
    retry_attempts: int = 3
    browser_timeout: int = 30000
    headless: bool = True


@dataclass
class LoggingConfig:
    """로깅 설정"""
    level: str = "INFO"
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


@dataclass
class SecurityConfig:
    """보안 설정"""
    encryption_key: Optional[str] = None
    api_rate_limit: int = 100
    max_login_attempts: int = 5
    session_timeout: int = 3600


@dataclass
class PerformanceConfig:
    """성능 설정 (EC2 t3.medium 최적화)"""
    cache_ttl: int = 43200  # 12시간
    max_memory_usage: int = 1024**3  # 1GB (EC2 최적화)
    worker_count: int = 3  # EC2 2 vCPU 최적화
    batch_size: int = 5  # EC2 메모리 최적화
    max_browsers: int = 1  # EC2 메모리 제약
    memory_check_interval: int = 15  # 15초마다 체크


class EnvironmentManager:
    """환경 설정 관리자"""
    
    def __init__(self):
        self._config_cache: Dict[str, Any] = {}
        self._load_environment()
    
    def _load_environment(self):
        """환경 변수 로드"""
        # 프로젝트 루트에서 .env 파일 찾기
        project_root = Path(__file__).parent.parent.parent
        env_file = project_root / ".env"
        
        if env_file.exists():
            load_dotenv(env_file)
    
    def get_database_config(self) -> DatabaseConfig:
        """데이터베이스 설정 조회"""
        if 'database' not in self._config_cache:
            self._config_cache['database'] = DatabaseConfig(
                url=self._get_env_var("SUPABASE_URL", required=True),
                key=self._get_env_var("SUPABASE_KEY", required=True),
                service_role_key=self._get_env_var("SUPABASE_SERVICE_ROLE_KEY"),
                pool_size=int(self._get_env_var("DB_POOL_SIZE", "10")),
                timeout=int(self._get_env_var("DB_TIMEOUT", "30"))
            )
        return self._config_cache['database']
    
    def get_crawling_config(self) -> CrawlingConfig:
        """크롤링 설정 조회"""
        if 'crawling' not in self._config_cache:
            self._config_cache['crawling'] = CrawlingConfig(
                max_concurrent_requests=int(self._get_env_var("MAX_CONCURRENT_REQUESTS", "5")),
                request_delay_ms=int(self._get_env_var("REQUEST_DELAY_MS", "1000")),
                retry_attempts=int(self._get_env_var("RETRY_ATTEMPTS", "3")),
                browser_timeout=int(self._get_env_var("BROWSER_TIMEOUT", "30000")),
                headless=self._get_env_var("BROWSER_HEADLESS", "true").lower() == "true"
            )
        return self._config_cache['crawling']
    
    def get_logging_config(self) -> LoggingConfig:
        """로깅 설정 조회"""
        if 'logging' not in self._config_cache:
            self._config_cache['logging'] = LoggingConfig(
                level=self._get_env_var("LOG_LEVEL", "INFO"),
                file_path=self._get_env_var("LOG_FILE_PATH"),
                max_file_size=int(self._get_env_var("LOG_MAX_FILE_SIZE", str(10 * 1024 * 1024))),
                backup_count=int(self._get_env_var("LOG_BACKUP_COUNT", "5")),
                format=self._get_env_var("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            )
        return self._config_cache['logging']
    
    def get_security_config(self) -> SecurityConfig:
        """보안 설정 조회"""
        if 'security' not in self._config_cache:
            self._config_cache['security'] = SecurityConfig(
                encryption_key=self._get_env_var("ENCRYPTION_KEY"),
                api_rate_limit=int(self._get_env_var("API_RATE_LIMIT", "100")),
                max_login_attempts=int(self._get_env_var("MAX_LOGIN_ATTEMPTS", "5")),
                session_timeout=int(self._get_env_var("SESSION_TIMEOUT", "3600"))
            )
        return self._config_cache['security']
    
    def get_performance_config(self) -> PerformanceConfig:
        """성능 설정 조회 (EC2 최적화)"""
        if 'performance' not in self._config_cache:
            self._config_cache['performance'] = PerformanceConfig(
                cache_ttl=int(self._get_env_var("CACHE_TTL", "43200")),
                max_memory_usage=int(self._get_env_var("MAX_MEMORY_USAGE", str(1024**3))),
                worker_count=int(self._get_env_var("WORKER_COUNT", "3")),
                batch_size=int(self._get_env_var("BATCH_SIZE", "5")),
                max_browsers=int(self._get_env_var("MAX_BROWSERS", "1")),
                memory_check_interval=int(self._get_env_var("MEMORY_CHECK_INTERVAL", "15"))
            )
        return self._config_cache['performance']
    
    def _get_env_var(self, key: str, default: str = "", required: bool = False) -> str:
        """환경 변수 조회"""
        value = os.environ.get(key, default)
        
        if required and not value:
            raise ValueError(f"Required environment variable '{key}' is not set")
        
        return value
    
    def get_environment_type(self) -> str:
        """환경 타입 조회 (development, staging, production)"""
        return self._get_env_var("ENVIRONMENT", "development").lower()
    
    def is_development(self) -> bool:
        """개발 환경 여부"""
        return self.get_environment_type() == "development"
    
    def is_production(self) -> bool:
        """운영 환경 여부"""
        return self.get_environment_type() == "production"
    
    def get_all_configs(self) -> Dict[str, Any]:
        """모든 설정 조회"""
        return {
            'database': self.get_database_config(),
            'crawling': self.get_crawling_config(),
            'logging': self.get_logging_config(),
            'security': self.get_security_config(),
            'performance': self.get_performance_config(),
            'environment': self.get_environment_type()
        }
    
    def validate_config(self) -> Dict[str, bool]:
        """설정 유효성 검증"""
        validation_results = {}
        
        try:
            # 데이터베이스 설정 검증
            db_config = self.get_database_config()
            validation_results['database'] = bool(db_config.url and db_config.key)
            
            # 크롤링 설정 검증
            crawling_config = self.get_crawling_config()
            validation_results['crawling'] = (
                crawling_config.max_concurrent_requests > 0 and
                crawling_config.request_delay_ms >= 0 and
                crawling_config.retry_attempts >= 0
            )
            
            # 로깅 설정 검증
            logging_config = self.get_logging_config()
            validation_results['logging'] = logging_config.level in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            
            # 성능 설정 검증
            performance_config = self.get_performance_config()
            validation_results['performance'] = (
                performance_config.worker_count > 0 and
                performance_config.batch_size > 0
            )
            
        except Exception as e:
            logging.error(f"설정 검증 실패: {e}")
            validation_results['error'] = str(e)
        
        return validation_results


# 전역 환경 관리자 인스턴스
env_manager = EnvironmentManager()


def get_env_manager() -> EnvironmentManager:
    """환경 관리자 인스턴스 반환"""
    return env_manager


# 편의 함수들
def get_database_config() -> DatabaseConfig:
    """데이터베이스 설정 조회"""
    return env_manager.get_database_config()


def get_crawling_config() -> CrawlingConfig:
    """크롤링 설정 조회"""
    return env_manager.get_crawling_config()


def get_logging_config() -> LoggingConfig:
    """로깅 설정 조회"""
    return env_manager.get_logging_config()


def is_development() -> bool:
    """개발 환경 여부"""
    return env_manager.is_development()


def is_production() -> bool:
    """운영 환경 여부"""
    return env_manager.is_production()
