"""
스포츠별 설정 관리
"""
import os
from dataclasses import dataclass
from datetime import time
from enum import Enum
from typing import Any, Dict, List, Optional, Union


class Sport(Enum):
    """스포츠 타입 열거형"""
    SOCCER = "soccer"
    BASEBALL = "baseball"
    BASKETBALL = "basketball"
    VOLLEYBALL = "volleyball"


@dataclass
class LeagueInfo:
    """리그 정보"""
    id: str
    name: str
    code: str
    tab_id: str
    order: int
    country: str
    season: str


@dataclass
class TimeSlot:
    """시간대 정보"""
    start: str  # "HH:MM" 형식
    end: str    # "HH:MM" 형식
    
    def to_time(self, time_str: str) -> time:
        """문자열을 time 객체로 변환"""
        hour, minute = map(int, time_str.split(':'))
        return time(hour, minute)
    
    @property
    def start_time(self) -> time:
        """시작 시간"""
        return self.to_time(self.start)
    
    @property
    def end_time(self) -> time:
        """종료 시간"""
        return self.to_time(self.end)


@dataclass 
class SportConfig:
    """스포츠 설정"""
    sport: Sport
    sport_code: str
    base_url: str
    leagues: List[LeagueInfo]
    team_url_pattern: str
    player_url_pattern: str
    time_slots: List[TimeSlot]
    
    def is_active_time(self, current_time: time) -> bool:
        """현재 시간이 활성 시간대인지 확인"""
        for slot in self.time_slots:
            if slot.start_time <= current_time <= slot.end_time:
                return True
        return False
    
    def get_time_slots(self) -> List[TimeSlot]:
        """시간대 목록 반환"""
        return self.time_slots


# 공통 시간대 설정: 08:12, 11:12, 12:12, 14:12, 14:32, 16:12, 17:12
COMMON_TIME_SLOTS = [
    TimeSlot("08:12", "08:13"),
    TimeSlot("11:12", "11:13"),
    TimeSlot("12:12", "12:13"),
    TimeSlot("14:12", "14:13"),
    TimeSlot("14:32", "14:33"),
    TimeSlot("16:12", "16:13"),
    TimeSlot("17:12", "17:13"),
]

# Soccer configuration
SOCCER_CONFIG = SportConfig(
    sport=Sport.SOCCER,
    sport_code="SC",
    base_url="https://www.betman.co.kr/main/mainPage/gameinfo/dataOfSoccerSchedule.do",
    leagues=[
        LeagueInfo("SC001", "K리그1", "K1", "#ui-id-1", 1, "KOR", "25"),
        LeagueInfo("SC003", "K리그2", "K2", "#ui-id-2", 2, "KOR", "25"),
        LeagueInfo("52", "EPL", "EPL", "#ui-id-3", 3, "ENG", "24-25"),
        LeagueInfo("67", "프리메라리가", "La Liga", "#ui-id-4", 4, "ESP", "24-25"),
        LeagueInfo("53", "세리에A", "Serie A", "#ui-id-5", 5, "ITA", "24-25"),
        LeagueInfo("56", "분데스리가", "Bundesliga", "#ui-id-6", 6, "GER", "24-25"),
        LeagueInfo("54", "프랑스리그", "Ligue 1", "#ui-id-7", 7, "FRA", "24-25"),
        LeagueInfo("2", "에레디비시", "Eredivisie", "#ui-id-8", 8, "NED", "24-25"),
        LeagueInfo("SC007", "J리그", "J-League", "#ui-id-9", 9, "JPN", "25"),
        LeagueInfo("SC017", "J리그2", "J2", "#ui-id-10", 10, "JPN", "25"),
        LeagueInfo("SC018", "MLS", "MLS", "#ui-id-11", 11, "USA", "25"),
    ],
    team_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}",
    player_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/scPlayerDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}&playerId={playerId}",
    time_slots=COMMON_TIME_SLOTS
)

# Baseball configuration
BASEBALL_CONFIG = SportConfig(
    sport=Sport.BASEBALL,
    sport_code="BS",
    base_url="https://www.betman.co.kr/main/mainPage/gameinfo/dataOfBaseballSchedule.do",
    leagues=[
        LeagueInfo('BS001', 'KBO', 'KBO', '#ui-id-1', 1, 'KOR', '25'),
        LeagueInfo('BS002', 'MLB', 'MLB', '#ui-id-2', 2, 'USA', '25'),
        LeagueInfo('BS004', 'NPB', 'NPB', '#ui-id-3', 3, 'JPN', '25'),
    ],
    team_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/bsTeamDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}",
    player_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/bsPlayerDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}&playerId={playerId}",
    time_slots=COMMON_TIME_SLOTS
)

# Basketball configuration (기본값 - 필요 시 Collector 단계에서 동적 매핑)
BASKETBALL_CONFIG = SportConfig(
    sport=Sport.BASKETBALL,
    sport_code="BK",
    base_url="https://www.betman.co.kr/main/mainPage/gameinfo/dataOfBasketballSchedule.do",
    leagues=[
        LeagueInfo('BK001', 'KBL', 'KBL', '#ui-id-1', 1, 'KOR', '25'),
        LeagueInfo('BK002', 'NBA', 'NBA', '#ui-id-2', 2, 'USA', '24-25'),
        LeagueInfo('BK003', 'WKBL', 'WKBL', '#ui-id-3', 3, 'KOR', '25'),
    ],
    team_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/bkTeamDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}",
    player_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/bkPlayerDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}&playerId={playerId}",
    time_slots=COMMON_TIME_SLOTS
)

# Volleyball configuration (기본값)
VOLLEYBALL_CONFIG = SportConfig(
    sport=Sport.VOLLEYBALL,
    sport_code="VL",
    base_url="https://www.betman.co.kr/main/mainPage/gameinfo/dataOfVolleyballSchedule.do",
    leagues=[
        LeagueInfo('VL001', 'KOVO 남자부', 'KOVO-M', '#ui-id-1', 1, 'KOR', '25'),
        LeagueInfo('VL002', 'KOVO 여자부', 'KOVO-W', '#ui-id-2', 2, 'KOR', '25'),
    ],
    team_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/vlTeamDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}",
    player_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/vlPlayerDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}&playerId={playerId}",
    time_slots=COMMON_TIME_SLOTS
)


def _load_soccer_leagues_from_db() -> List[LeagueInfo]:
    """데이터베이스에서 축구 리그 정보 로드 - target_games에 있는 리그만"""
    try:
        from supabase import create_client
        
        url = os.environ.get('SUPABASE_URL')
        key = os.environ.get('SUPABASE_KEY')
        
        if not url or not key:
            # DB 연결 실패시 기본 설정 반환
            return _get_default_soccer_leagues()
        
        client = create_client(url, key)
        
        # target_games에 있는 축구 리그 ID들 조회
        target_leagues_response = client.table('target_games').select(
            'league_id'
        ).eq('sports', 'soccer').execute()
        
        if not target_leagues_response.data:
            return _get_default_soccer_leagues()
        
        # 고유한 리그 ID들 추출
        unique_league_ids = list(set([
            game['league_id'] for game in target_leagues_response.data 
            if game['league_id'] is not None
        ]))
        
        if not unique_league_ids:
            return _get_default_soccer_leagues()
        
        # 이 리그 ID들의 정보를 league_info에서 조회
        league_info_response = client.table('league_info').select(
            'league_id, league, league_name, eng_league, league_order_kr'
        ).in_('league_id', unique_league_ids).order('league_order_kr').execute()
        
        leagues = []
        for i, league_data in enumerate(league_info_response.data):
            league_id = league_data['league_id']
            
            # 탭 ID는 수집기에서 동적으로 결정하도록 임시 값 할당
            # 실제 탭 매핑은 collector에서 웹사이트를 조사해서 결정
            tab_id = f"#ui-id-{i+1}"  # 임시 값, 실제로는 collector에서 동적 매핑
            
            leagues.append(LeagueInfo(
                id=league_id,
                name=league_data['league'],
                code=league_data['eng_league'],
                tab_id=tab_id,
                order=league_data['league_order_kr'],
                country="KOR" if league_id.startswith('SC') and "K리그" in league_data['league'] else "INTL",
                season="25"
            ))
        
        # target_games에는 있지만 league_info에는 없는 리그들도 추가
        known_league_ids = [league.id for league in leagues]
        for league_id in unique_league_ids:
            if league_id not in known_league_ids:
                # 기본 정보로 추가
                leagues.append(LeagueInfo(
                    id=league_id,
                    name=f"League-{league_id}",
                    code=league_id,
                    tab_id=f"#ui-id-{len(leagues)+1}",
                    order=len(leagues)+1,
                    country="UNKNOWN",
                    season="25"
                ))
        
        return leagues
        
    except Exception as e:
        # 에러 발생시 기본 설정 반환
        return _get_default_soccer_leagues()


def _get_default_soccer_leagues() -> List[LeagueInfo]:
    """기본 축구 리그 설정 (DB 연결 실패시 사용)"""
    return [
        LeagueInfo("SC001", "K리그1", "K1", "#ui-id-1", 1, "KOR", "25"),
        LeagueInfo("SC003", "K리그2", "K2", "#ui-id-2", 2, "KOR", "25"),
        LeagueInfo("52", "EPL", "EPL", "#ui-id-3", 3, "ENG", "24-25"),
        LeagueInfo("67", "프리메라리가", "La Liga", "#ui-id-4", 4, "ESP", "24-25"),
        LeagueInfo("53", "세리에A", "Serie A", "#ui-id-5", 5, "ITA", "24-25"),
        LeagueInfo("56", "분데스리가", "Bundesliga", "#ui-id-6", 6, "GER", "24-25"),
        LeagueInfo("54", "프랑스리그", "Ligue 1", "#ui-id-7", 7, "FRA", "24-25"),
        LeagueInfo("2", "에레디비시", "Eredivisie", "#ui-id-8", 8, "NED", "24-25"),
        LeagueInfo("SC007", "J리그", "J-League", "#ui-id-9", 9, "JPN", "25"),
    ]


def get_sport_config(sport_name: Union[str, Sport]) -> Optional[SportConfig]:
    """스포츠별 설정 반환"""
    try:
        # Sport enum이 전달된 경우 문자열로 변환
        if isinstance(sport_name, Sport):
            sport_name = sport_name.value
        
        if sport_name == "soccer":
            # 동적으로 축구 리그 로드
            soccer_leagues = _load_soccer_leagues_from_db()
            return SportConfig(
                sport=Sport.SOCCER,
                sport_code="SC",
                base_url="https://www.betman.co.kr/main/mainPage/gameinfo/dataOfSoccerSchedule.do",
                leagues=soccer_leagues,
                team_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}",
                player_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/scPlayerDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}&playerId={playerId}",
                time_slots=COMMON_TIME_SLOTS
            )
        elif sport_name == "baseball":
            return BASEBALL_CONFIG
        elif sport_name == "basketball":
            return BASKETBALL_CONFIG
        elif sport_name == "volleyball":
            return VOLLEYBALL_CONFIG
        else:
            return None
    except Exception:
        return None


def get_all_sport_configs() -> Dict[str, SportConfig]:
    """모든 스포츠 설정 반환"""
    configs = {}
    sport_names = ["baseball", "soccer", "basketball", "volleyball"]
    
    for sport_name in sport_names:
        config = get_sport_config(sport_name)
        if config:
            configs[sport_name] = config
    
    return configs


def get_active_sports(current_time: Optional[time] = None) -> List[str]:
    """현재 시간에 활성화된 스포츠 목록 반환"""
    if current_time is None:
        # 시간이 지정되지 않으면 모든 스포츠 반환
        return ["baseball", "soccer", "basketball", "volleyball"]
    
    active_sports = []
    all_configs = get_all_sport_configs()
    
    for sport_name, config in all_configs.items():
        if config.is_active_time(current_time):
            active_sports.append(sport_name)
    
    return active_sports