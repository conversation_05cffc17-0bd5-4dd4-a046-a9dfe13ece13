#!/usr/bin/env python3
"""
리그 데이터 수집 및 검증 통합 시스템
실제 리그별 데이터를 수집하고 품질을 검증하여 상세한 리포트 생성
"""
import asyncio
import json
import os
import sys
from datetime import datetime
from typing import Any, Dict, List, Optional

# 프로젝트 루트 경로 추가
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.validation.cross_league_validator import CrossLeagueValidator
from core.validation.league_data_validator import LeagueDataValidator
from core.validation.validation_reporter import ValidationReporter
from utils.logger import Logger

logger = Logger(__name__)


class LeagueDataValidationSystem:
    """리그 데이터 검증 통합 시스템"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.validator = LeagueDataValidator(config)
        self.cross_validator = CrossLeagueValidator()
        self.reporter = ValidationReporter(
            output_dir=self.config.get('output_dir', 'results/validation')
        )
        
    async def run_full_validation(
        self, 
        sports: Optional[List[str]] = None,
        generate_html: bool = True
    ) -> Dict[str, Any]:
        """전체 검증 프로세스 실행"""
        logger.info("🚀 리그 데이터 검증 시스템 시작")
        
        start_time = datetime.now()
        
        try:
            # 1. 모든 리그 데이터 수집 및 기본 검증
            logger.info("📊 1단계: 리그별 데이터 수집 및 검증")
            league_results = await self.validator.validate_all_leagues(sports)
            
            if not league_results:
                logger.error("❌ 검증할 리그 데이터가 없습니다")
                return {"error": "No leagues to validate"}
            
            # 2. 리그간 일관성 검증
            logger.info("🔗 2단계: 리그간 일관성 검증")
            cross_league_report = self.cross_validator.validate_cross_league_consistency(
                league_results
            )
            
            # 3. 종합 리포트 생성
            logger.info("📋 3단계: 종합 리포트 생성")
            comprehensive_report = self.reporter.generate_comprehensive_report(
                league_results, 
                cross_league_report,
                include_samples=True
            )
            
            # 4. HTML 리포트 생성 (옵션)
            if generate_html:
                logger.info("🌐 4단계: HTML 리포트 생성")
                html_path = self.reporter.generate_html_report(
                    league_results, 
                    cross_league_report
                )
                comprehensive_report['html_report_path'] = html_path
            
            # 5. 결과 요약 출력
            await self._print_validation_summary(league_results, cross_league_report)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"✅ 검증 완료: {duration:.1f}초 소요")
            
            return {
                "success": True,
                "validation_time": start_time.isoformat(),
                "duration_seconds": duration,
                "league_results": {
                    league_id: {
                        "league_name": result.league_name,
                        "sport": result.sport,
                        "is_valid": result.is_valid,
                        "data_count": result.data_count,
                        "error_count": len(result.validation_errors),
                        "warning_count": len(result.validation_warnings)
                    }
                    for league_id, result in league_results.items()
                },
                "cross_league_summary": {
                    "compatibility_score": cross_league_report.compatibility_score,
                    "total_issues": len(cross_league_report.issues),
                    "recommendations_count": len(cross_league_report.recommendations)
                },
                "report": comprehensive_report
            }
            
        except Exception as e:
            logger.error(f"❌ 검증 시스템 실행 실패: {e}")
            return {
                "success": False,
                "error": str(e),
                "validation_time": start_time.isoformat()
            }
    
    async def run_quick_validation(
        self, 
        sports: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """빠른 검증 (기본 검증만)"""
        logger.info("⚡ 빠른 검증 모드 시작")
        
        try:
            # 리그별 기본 검증만 실행
            league_results = await self.validator.validate_all_leagues(sports)
            
            # 간단한 요약만 생성
            summary = await self.validator.get_validation_summary()
            
            return {
                "success": True,
                "mode": "quick",
                "summary": summary,
                "league_count": len(league_results),
                "valid_leagues": sum(1 for r in league_results.values() if r.is_valid)
            }
            
        except Exception as e:
            logger.error(f"❌ 빠른 검증 실패: {e}")
            return {"success": False, "error": str(e)}
    
    async def validate_specific_league(
        self, 
        sport: str, 
        league_id: str
    ) -> Dict[str, Any]:
        """특정 리그만 검증"""
        logger.info(f"🎯 특정 리그 검증: {sport} - {league_id}")
        
        try:
            # 전체 검증 후 특정 리그만 필터링
            all_results = await self.validator.validate_all_leagues([sport])
            
            if league_id not in all_results:
                return {
                    "success": False,
                    "error": f"리그를 찾을 수 없습니다: {league_id}"
                }
            
            result = all_results[league_id]
            
            return {
                "success": True,
                "league_id": league_id,
                "league_name": result.league_name,
                "sport": result.sport,
                "is_valid": result.is_valid,
                "data_count": result.data_count,
                "quality_metrics": {
                    "completeness": result.quality_metrics.completeness,
                    "consistency": result.quality_metrics.consistency,
                    "accuracy": result.quality_metrics.accuracy,
                    "timeliness": result.quality_metrics.timeliness
                } if result.quality_metrics else None,
                "errors": result.validation_errors,
                "warnings": result.validation_warnings,
                "sample_data": result.sample_data[:3] if result.sample_data else []
            }
            
        except Exception as e:
            logger.error(f"❌ 특정 리그 검증 실패: {e}")
            return {"success": False, "error": str(e)}
    
    async def _print_validation_summary(
        self,
        league_results: Dict[str, Any],
        cross_league_report
    ):
        """검증 결과 요약 출력"""
        print("\n" + "="*80)
        print("🔍 리그 데이터 검증 결과 요약")
        print("="*80)
        
        # 전체 통계
        total_leagues = len(league_results)
        valid_leagues = sum(1 for r in league_results.values() if r.is_valid)
        total_data = sum(r.data_count for r in league_results.values())
        
        print(f"📊 전체 통계:")
        print(f"   • 총 리그 수: {total_leagues}")
        print(f"   • 유효한 리그: {valid_leagues}")
        print(f"   • 성공률: {valid_leagues/total_leagues:.1%}")
        print(f"   • 총 데이터: {total_data:,}건")
        
        # 스포츠별 현황
        sports_summary = {}
        for result in league_results.values():
            sport = result.sport
            if sport not in sports_summary:
                sports_summary[sport] = {'total': 0, 'valid': 0}
            sports_summary[sport]['total'] += 1
            if result.is_valid:
                sports_summary[sport]['valid'] += 1
        
        print(f"\n🏆 스포츠별 현황:")
        for sport, stats in sports_summary.items():
            success_rate = stats['valid'] / stats['total'] if stats['total'] > 0 else 0
            print(f"   • {sport.upper()}: {stats['valid']}/{stats['total']} ({success_rate:.1%})")
        
        # 문제가 있는 리그
        problem_leagues = [
            r for r in league_results.values() 
            if not r.is_valid or r.validation_errors
        ]
        
        if problem_leagues:
            print(f"\n⚠️  문제가 있는 리그 ({len(problem_leagues)}개):")
            for league in problem_leagues[:5]:  # 최대 5개만 표시
                status = "❌ 실패" if not league.is_valid else "⚠️ 경고"
                print(f"   • {league.league_name} ({league.sport}): {status}")
                if league.validation_errors:
                    print(f"     오류: {league.validation_errors[0]}")
        
        # 크로스 리그 분석
        print(f"\n🔗 리그간 호환성:")
        print(f"   • 호환성 점수: {cross_league_report.compatibility_score:.2f}/1.0")
        print(f"   • 발견된 이슈: {len(cross_league_report.issues)}개")
        
        if cross_league_report.recommendations:
            print(f"\n💡 주요 권고사항:")
            for i, rec in enumerate(cross_league_report.recommendations[:3], 1):
                print(f"   {i}. {rec}")
        
        print("="*80)


async def main():
    """메인 실행 함수"""
    print("🚀 리그 데이터 검증 시스템")
    print("각 리그에서 실제 데이터를 수집하고 품질을 검증합니다.\n")
    
    # 설정
    config = {
        'max_browsers': 3,
        'headless': True,
        'output_dir': 'results/validation'
    }
    
    # 검증 시스템 초기화
    validation_system = LeagueDataValidationSystem(config)
    
    try:
        # 사용자 입력 받기
        print("검증 모드를 선택하세요:")
        print("1. 전체 검증 (모든 리그 + 크로스 검증 + 리포트)")
        print("2. 빠른 검증 (기본 검증만)")
        print("3. 특정 스포츠만 검증")
        
        choice = input("\n선택 (1-3): ").strip()
        
        if choice == "1":
            # 전체 검증
            result = await validation_system.run_full_validation()
            
        elif choice == "2":
            # 빠른 검증
            result = await validation_system.run_quick_validation()
            
        elif choice == "3":
            # 특정 스포츠 검증
            print("\n사용 가능한 스포츠: soccer, basketball, volleyball, baseball")
            sport = input("스포츠를 입력하세요: ").strip().lower()
            
            if sport in ['soccer', 'basketball', 'volleyball', 'baseball']:
                result = await validation_system.run_full_validation([sport])
            else:
                print("❌ 잘못된 스포츠입니다.")
                return
        else:
            print("❌ 잘못된 선택입니다.")
            return
        
        # 결과 저장
        if result.get('success'):
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            result_file = f"results/validation_result_{timestamp}.json"
            
            os.makedirs('results', exist_ok=True)
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n📁 결과 저장됨: {result_file}")
            
            if 'html_report_path' in result:
                print(f"🌐 HTML 리포트: {result['html_report_path']}")
        
        else:
            print(f"\n❌ 검증 실패: {result.get('error', 'Unknown error')}")
    
    except KeyboardInterrupt:
        print("\n\n⏹️ 사용자에 의해 중단됨")
    except Exception as e:
        print(f"\n❌ 시스템 오류: {e}")
        logger.error(f"시스템 오류: {e}")


if __name__ == "__main__":
    # 비동기 실행
    asyncio.run(main()) 