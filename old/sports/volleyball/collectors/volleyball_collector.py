"""
배구 데이터 수집기
2개 리그 지원: KOVO남, KOVO여
"""
import asyncio
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from playwright.async_api import Browser, Page

from core.config.sport_config import Sport, get_sport_config
from core.matcher.universal_team_matcher import <PERSON><PERSON><PERSON>, team_matcher
from utils.logger import Logger

logger = Logger(__name__)


@dataclass
class VolleyballDataItem:
    """배구 데이터 수집 항목"""
    team: str
    league: str
    league_id: str
    name: str
    date: str
    time: str
    home_team: str
    away_team: str
    status: str


class VolleyballCollector:
    """배구 데이터 수집기 - 농구/축구 패턴 재사용"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        self.sport_config = get_sport_config(Sport.VOLLEYBALL)
        self.team_mappings = team_mappings or {}
        self.base_url = self.sport_config.base_url
        
    async def collect_schedule(self, target_games: List[Dict]) -> List[Dict]:
        """배구 스케줄 수집 - 2개 리그 처리"""
        logger.info(f"🚀 배구 스케줄 수집 시작 - {len(target_games)}개 타겟 경기")
        
        all_games = []
        browser = None
        
        try:
            from playwright.async_api import async_playwright
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                # 배구 스케줄 페이지로 이동
                await page.goto(self.base_url, wait_until='networkidle')
                await asyncio.sleep(2)
                
                # 각 리그별로 데이터 수집
                for league in self.sport_config.leagues:
                    logger.info(f"📊 {league.name} 리그 처리 중...")
                    
                    # 리그 탭 클릭
                    if not await self._click_league_tab(page, league.tab_id):
                        logger.warning(f"⚠️ {league.name} 탭 클릭 실패")
                        continue
                    
                    # 경기 데이터 파싱
                    web_games = await self._parse_game_table(page, league)
                    
                    # 타겟 경기와 매칭
                    matched_games = await self._match_target_games(
                        web_games, target_games, league
                    )
                    
                    all_games.extend(matched_games)
                    logger.info(f"✅ {league.name}: {len(matched_games)}개 매칭")
                    
                    await asyncio.sleep(1)  # 리그 간 딜레이
                
        except Exception as e:
            logger.error(f"❌ 배구 스케줄 수집 실패: {e}")
        finally:
            if browser:
                await browser.close()
        
        logger.info(f"🎯 배구 스케줄 수집 완료: {len(all_games)}개")
        return all_games
    
    async def _click_league_tab(self, page: Page, tab_id: str) -> bool:
        """리그 탭 클릭"""
        try:
            # 탭 클릭 (다른 스포츠와 동일한 패턴)
            await page.click(tab_id)
            await asyncio.sleep(1)
            
            # 테이블 로드 대기
            await page.wait_for_selector('.tbl', timeout=5000)
            return True
            
        except Exception as e:
            logger.debug(f"탭 클릭 실패 {tab_id}: {e}")
            return False
    
    async def _parse_game_table(self, page: Page, league) -> List[Dict]:
        """경기 테이블 파싱 - 다른 스포츠와 동일한 vsDIv 구조 활용"""
        try:
            # HTML 가져오기
            html = await page.content()
            
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')
            
            games = []
            
            # vsDIv 구조 파싱 (다른 스포츠와 동일 패턴)
            vs_divs = soup.find_all('div', class_='vsDIv')
            
            for vs_div in vs_divs:
                try:
                    game_data = await self._extract_game_info(vs_div, league)
                    if game_data:
                        games.append(game_data)
                except Exception as e:
                    logger.debug(f"경기 파싱 오류: {e}")
                    continue
            
            return games
            
        except Exception as e:
            logger.error(f"테이블 파싱 실패: {e}")
            return []
    
    async def _extract_game_info(self, vs_div, league) -> Optional[Dict]:
        """개별 경기 정보 추출"""
        try:
            # 날짜/시간 추출
            date_elem = vs_div.find('span', class_='day')
            time_elem = vs_div.find('span', class_='time')
            
            if not date_elem or not time_elem:
                return None
            
            date_str = date_elem.get_text(strip=True)
            time_str = time_elem.get_text(strip=True)
            
            # 팀명 추출
            team_elems = vs_div.find_all('span', class_='team')
            if len(team_elems) < 2:
                return None
            
            home_team = team_elems[0].get_text(strip=True)
            away_team = team_elems[1].get_text(strip=True)
            
            # 경기 상태 추출
            status_elem = vs_div.find('span', class_='status')
            status = status_elem.get_text(strip=True) if status_elem else ''
            
            return {
                'date': date_str,
                'time': time_str,
                'home_team': home_team,
                'away_team': away_team,
                'status': status,
                'league': league.name,
                'league_id': league.id,
                'sport': 'volleyball'
            }
            
        except Exception as e:
            logger.debug(f"경기 정보 추출 실패: {e}")
            return None
    
    async def _match_target_games(self, web_games: List[Dict], 
                                 target_games: List[Dict], league) -> List[Dict]:
        """타겟 경기와 웹 경기 매칭"""
        matched_games = []
        
        for target_game in target_games:
            # 리그 필터
            if target_game.get('league_id') != league.id:
                continue
            
            # 웹 경기와 매칭
            for web_game in web_games:
                if await self._is_game_match(target_game, web_game):
                    # 팀 매칭 정보 추가
                    enhanced_game = await self._enhance_game_with_team_info(
                        web_game, target_game
                    )
                    matched_games.append(enhanced_game)
                    break
        
        return matched_games
    
    async def _is_game_match(self, target_game: Dict, web_game: Dict) -> bool:
        """경기 매칭 여부 확인"""
        try:
            # 날짜 매칭
            if target_game.get('date') != web_game.get('date'):
                return False
            
            # 시간 매칭 (선택적)
            target_time = target_game.get('time', '')
            web_time = web_game.get('time', '')
            if target_time and web_time and target_time != web_time:
                return False
            
            # 팀명 매칭 (유사도 기반)
            target_home = target_game.get('home_team', '')
            target_away = target_game.get('away_team', '')
            web_home = web_game.get('home_team', '')
            web_away = web_game.get('away_team', '')
            
            home_match = await team_matcher.match_team(
                target_home, Sport.VOLLEYBALL, target_game.get('league_id')
            )
            away_match = await team_matcher.match_team(
                target_away, Sport.VOLLEYBALL, target_game.get('league_id')
            )
            
            return bool(home_match and away_match)
            
        except Exception as e:
            logger.debug(f"경기 매칭 확인 실패: {e}")
            return False
    
    async def _enhance_game_with_team_info(self, web_game: Dict, 
                                          target_game: Dict) -> Dict:
        """팀 정보로 경기 데이터 강화"""
        enhanced_game = web_game.copy()
        
        try:
            # 팀 매칭 정보 추가
            home_match = await team_matcher.match_team(
                web_game['home_team'], Sport.VOLLEYBALL, web_game['league_id']
            )
            away_match = await team_matcher.match_team(
                web_game['away_team'], Sport.VOLLEYBALL, web_game['league_id']
            )
            
            if home_match:
                enhanced_game['home_team_id'] = home_match.team_id
                enhanced_game['home_team_url'] = self._generate_team_url(
                    home_match.team_id, web_game['league_id']
                )
            
            if away_match:
                enhanced_game['away_team_id'] = away_match.team_id
                enhanced_game['away_team_url'] = self._generate_team_url(
                    away_match.team_id, web_game['league_id']
                )
            
            # 타겟 게임 정보 병합
            enhanced_game.update({
                'target_game_id': target_game.get('id'),
                'target_date': target_game.get('date'),
                'target_time': target_game.get('time')
            })
            
        except Exception as e:
            logger.debug(f"경기 데이터 강화 실패: {e}")
        
        return enhanced_game
    
    def _generate_team_url(self, team_id: str, league_id: str) -> str:
        """팀 상세 URL 생성"""
        return self.sport_config.team_url_pattern.format(
            item=self.sport_config.sport_code,
            leagueId=league_id,
            teamId=team_id
        )
    
    def _generate_player_url(self, team_id: str, league_id: str, 
                           player_id: str) -> str:
        """선수 상세 URL 생성"""
        return self.sport_config.player_url_pattern.format(
            item=self.sport_config.sport_code,
            leagueId=league_id,
            teamId=team_id,
            playerId=player_id
        )
