"""
축구 데이터 수집기 - 야구 수집기의 성공적인 패턴 적용

# flake8: noqa  # 줄 길이(E501) 검사 비활성화
"""
import asyncio
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import pytz
from playwright.async_api import Page, async_playwright

from utils.logger import Logger

# 로거 설정
logger = Logger(__name__)

# 베트맨 축구 스케줄 URL
SCHEDULE_URL = "https://www.betman.co.kr/main/mainPage/gameinfo/dataOfSoccerSchedule.do"


class SoccerLeagueStrategy:
    """축구 리그별 탭 매핑 전략 - 베트맨 사이트 실제 구조 기반"""
    
    # 베트맨 축구 사이트의 실제 리그 ID와 탭 매핑
    LEAGUE_TAB_MAP = {
        "SC003": "#tabs-2",   # K리그2 탭 콘텐츠 
        # SC017 (J2리그)는 팀 페이지가 없어서 지원하지 않음
        # SC018 (MLS)는 베트맨 사이트에서 지원하지 않음
    }
    
    # 탭 클릭용 버튼 ID 매핑 (별도 관리)
    CLICK_TAB_MAP = {
        "SC003": "#ui-id-2",  # K리그2 탭 버튼
        # SC017 (J2리그)는 팀 페이지가 없어서 지원하지 않음
    }
    
    @staticmethod
    def get_tab_id(league_id: str) -> str:
        """리그 ID에 해당하는 탭 ID 반환"""
        return SoccerLeagueStrategy.LEAGUE_TAB_MAP.get(
            league_id, "#tabs-1"
        )
    
    @staticmethod
    def get_tab_selector(league_id: str) -> str:
        """리그 ID에 해당하는 탭 콘텐츠 셀렉터 반환 (데이터 조회용)"""
        return SoccerLeagueStrategy.LEAGUE_TAB_MAP.get(
            league_id, "#tabs-1"
        )
    
    @staticmethod
    def get_click_selector(league_id: str) -> str:
        """리그 ID에 해당하는 탭 버튼 셀렉터 반환 (클릭용)"""
        return SoccerLeagueStrategy.CLICK_TAB_MAP.get(
            league_id, "#ui-id-1"
        )
    
    @staticmethod
    def is_supported_league(league_id: str) -> bool:
        """베트맨 사이트에서 지원하는 리그인지 확인"""
        return league_id in SoccerLeagueStrategy.LEAGUE_TAB_MAP
    
    @staticmethod
    async def click_league_tab(page: Page, league_id: str) -> bool:
        """리그 탭 클릭 (버튼 ID 사용)"""
        if not SoccerLeagueStrategy.is_supported_league(league_id):
            logger.warning(f"❌ 지원하지 않는 리그: {league_id}")
            return False
            
        # 클릭용 버튼 선택자 사용
        click_selector = SoccerLeagueStrategy.get_click_selector(league_id)
        try:
            await page.click(click_selector)
            logger.info(f"✅ {league_id} 탭 클릭 완료: {click_selector}")
            return True
        except Exception as e:
            logger.error(f"❌ {league_id} 탭 클릭 실패: {e}")
            return False


class SoccerCollector:
    """축구 데이터 수집기 - 야구 패턴 적용"""
    
    def __init__(self, client=None, wait_time=2.0):
        self.client = client
        self.league_strategy = SoccerLeagueStrategy()
        self.KST = pytz.timezone('Asia/Seoul')
        self.wait_time = wait_time
        self.playwright = None
        self.browser = None
        
    async def collect_data(self, **kwargs) -> List[Dict]:
        """데이터 수집 진입점"""
        start_time = time.time()
        logger.info("🚀 축구 수집 시작")
        
        try:
            # 조건에 맞는 target_games 조회
            target_games = await self._get_target_games()
            
            if not target_games:
                logger.info("수집할 경기가 없습니다")
                return []
                
            # 웹 크롤링
            crawled_games = await self._crawl_target_games(target_games)
            
            # 팀 통계 수집 (새로 추가)
            if crawled_games:
                await self._collect_team_stats(crawled_games)
            
            # 결과 요약
            success_count = len(crawled_games)
            total_time = time.time() - start_time
            
            if success_count > 0:
                logger.info(
                    "✅ 성공: %s개 (%.1f초)",
                    success_count,
                    total_time,
                )
            else:
                # ‘실패’가 아니라 수집 대상이 없다는 의미로 정보 로그 처리
                logger.info(
                    f"ℹ️ 수집 대상 없음 ({total_time:.1f}초)"
                )
                
            return crawled_games
            
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"❌ 실패: {e} ({total_time:.1f}초)")
            return []
    
    async def _get_target_games(self) -> List[Dict]:
        """조건에 맞는 target_games 조회 - 야구와 동일한 로직 적용"""
        if not self.client:
            logger.debug("DB 클라이언트 없음")
            return []
            
        try:
            now = datetime.now(self.KST)
            
            # 🎯 야구와 동일: 현재 시간 이후 7일간의 경기만 조회
            end_date = (now + timedelta(days=7)).strftime('%Y-%m-%d')
            
            response = self.client.table("target_games").select(
                "match_id, match_date, match_time, league_id, "
                "home_team, away_team, home_team_id, away_team_id"
            ).eq("sports", "soccer").eq("game_type", "W").gte(
                "match_date", now.strftime('%Y-%m-%d')
            ).lte("match_date", end_date).order(
                "match_date, match_time"
            ).execute()
            
            if not (response and hasattr(response, 'data') and 
                    response.data):
                logger.info("📭 축구 target_games 없음")
                return []
                
            # 🎯 야구와 동일: 현재 시간 이후 경기만 필터링
            filtered_games = []
            for game in response.data:
                try:
                    # 🚨 Team ID 체크 (null인 경우 스킵)
                    if not game.get('home_team_id') or not game.get('away_team_id'):
                        continue
                        
                    date_str = game['match_date']
                    time_str = game['match_time']
                    
                    # 시간 형식 정규화
                    if ':' in time_str and len(time_str.split(':')) == 3:
                        time_parts = time_str.split(':')
                        time_str = f"{time_parts[0]}:{time_parts[1]}"
                        
                    match_dt = datetime.strptime(
                        f"{date_str} {time_str}", '%Y-%m-%d %H:%M'
                    )
                    match_dt = self.KST.localize(match_dt)
                    
                    # 🎯 야구와 동일: 현재 시간 이후 경기만
                    if match_dt > now:
                        filtered_games.append(game)
                        
                except Exception as e:
                    logger.debug(f"시간 필터링 오류: {e}")
                    continue
                    
            logger.info(f"🎯 축구 경기: {len(filtered_games)}개")
            return filtered_games
            
        except Exception as e:
            logger.error(f"target_games 조회 실패: {e}")
            return []
    
    async def _crawl_target_games(
        self, target_games: List[Dict]
    ) -> List[Dict]:
        """target_games에 있는 경기들만 크롤링 - 베트맨 지원 리그만"""
        if not target_games:
            return []
        
        # 리그별로 그룹화 (베트맨 지원 리그만 - 조용히 스킵)
        league_groups = {}
        for game in target_games:
            league_id = game.get("league_id")
            if (league_id and 
                SoccerLeagueStrategy.is_supported_league(league_id)):
                if league_id not in league_groups:
                    league_groups[league_id] = []
                league_groups[league_id].append(game)
            # 지원하지 않는 리그는 조용히 스킵
        
        logger.info(
            f"베트맨 지원 리그별 그룹화: "
            f"{dict((k, len(v)) for k, v in league_groups.items())}"
        )
        
        if not league_groups:
            logger.warning(
                "❌ 베트맨 사이트에서 지원하는 리그의 경기가 없습니다."
            )
            return []

        # 브라우저 설정
        playwright_instance = None
        browser = None
        page = None
        crawled_games = []

        try:
            playwright_instance = await async_playwright().start()
            browser = await playwright_instance.chromium.launch(headless=True)
            page = await browser.new_page()
            await self.setup_browser_page(page)
            await page.goto(SCHEDULE_URL, timeout=15000)

            # 베트맨 지원 리그별로만 크롤링
            for league_id, games in league_groups.items():
                league_crawled = await self._crawl_league_target_games(
                    page, league_id, games
                )
                crawled_games.extend(league_crawled)

        except Exception as e:
            logger.error(f"크롤링 중 오류: {e}")
            
        finally:
            # 리소스 정리
            if page:
                try:
                    await page.close()
                except Exception:
                    pass
            
            if browser:
                try:
                    await browser.close()
                except Exception:
                    pass
            
            if playwright_instance:
                try:
                    await playwright_instance.stop()
                except Exception:
                    pass

        return crawled_games

    async def _crawl_league_target_games(
        self, page: Page, league_id: str, target_games: List[Dict]
    ) -> List[Dict]:
        """특정 리그의 target_games만 크롤링 - 야구 방식 완전 적용"""
        # 리그 탭 클릭
        await SoccerLeagueStrategy.click_league_tab(page, league_id)
        
        # 추가 대기 시간 적용
        await asyncio.sleep(self.wait_time)
        
        tab_selector = SoccerLeagueStrategy.get_tab_selector(league_id)
        crawled_games = []
        
        for target_game in target_games:
            target_date = target_game['match_date']
            target_time = target_game['match_time']
            
            # 날짜에서 일(day) 추출 (예: "2025-07-06" -> "06")
            try:
                day = target_date.split('-')[2]  # "06"
            except (IndexError, AttributeError):
                continue
            
            # 해당 날짜의 모든 경기 행 찾기
            day_selector = f'{tab_selector} tbody tr[data-day="{day}"]'
            day_rows = await page.query_selector_all(day_selector)
            
            if not day_rows:
                continue
            
            # 각 행에서 팀명으로 매칭 확인 (야구 방식 완전 적용)
            matched_game = None
            for row in day_rows:
                # vs_cell에서 팀 링크 직접 추출
                vs_cell = await row.query_selector("td:nth-child(2)")
                if not vs_cell:
                    continue
                
                # 팀 링크 추출
                urls = await self._extract_team_urls(vs_cell)
                if not urls['home_url'] or not urls['away_url']:
                    continue
                
                # 팀명 추출
                home_link = await vs_cell.query_selector('.vsDIv > div:first-child .tbCell a.team')
                away_link = await vs_cell.query_selector('.vsDIv > div:last-child .tbCell a.team')
                
                if not home_link or not away_link:
                    logger.debug(f"팀 링크 요소 찾기 실패: home_link={home_link}, away_link={away_link}")
                    continue
                
                web_home = await home_link.text_content()
                web_away = await away_link.text_content()
                
                if not web_home or not web_away:
                    logger.debug(f"팀명 텍스트 추출 실패: web_home={web_home}, web_away={web_away}")
                    continue
                
                web_home = web_home.strip()
                web_away = web_away.strip()
                
                # 팀 ID 추출 및 매칭 확인 (더 안정적인 방식)
                home_team_id = self._get_team_id(urls['home_url'])
                away_team_id = self._get_team_id(urls['away_url'])
                
                target_home_id = target_game.get('home_team_id')
                target_away_id = target_game.get('away_team_id')
                
                if (home_team_id and away_team_id and target_home_id and target_away_id and 
                    home_team_id == target_home_id and away_team_id == target_away_id):
                    # 시간 확인
                    time_cell = await row.query_selector("td:nth-child(1)")
                    if time_cell:
                        time_text = await time_cell.text_content()
                        # 시간 포맷 정규화 (19:00:00 -> 19:00)
                        normalized_target_time = target_time.rsplit(':', 1)[0] if ':' in target_time else target_time
                        if time_text and normalized_target_time in time_text:
                            matched_game = {
                                'league_id': league_id,
                                'date': target_date,
                                'time': target_time,
                                'home_team': web_home,
                                'away_team': web_away,
                                'home_team_url': urls['home_url'],
                                'away_team_url': urls['away_url'],
                                'home_team_id': target_game.get('home_team_id'),
                                'away_team_id': target_game.get('away_team_id'),
                                'match_id': target_game.get('match_id'),
                                'game_status': '경기전'
                            }
                            logger.info(f"✅ 경기 매칭 성공: {web_home} vs {web_away}")
                            break

            
            if matched_game:
                crawled_games.append(matched_game)
                
            # 각 게임 처리 후 짧은 대기 시간 추가
            await asyncio.sleep(self.wait_time / 2)
        
        return crawled_games
    
    def _normalize_team_name(self, team_name: str) -> str:
        """팀명 정규화 - 접미사 제거 및 축약명 처리"""
        if not team_name:
            return ""
        
        # 원본 팀명 보존 (디버깅용)
        original = team_name
        
        # 공백 제거 후 처리
        normalized = team_name.strip()
        
        # 접미사 제거 (순서 중요 - 긴 것부터)
        suffixes = ['FC1906', 'FC', 'SC', 'AC', 'CF', 'United', 
                   '유나이티드', '시티', 'City', '아이', 'FC브루마블']
        
        for suffix in suffixes:
            if normalized.endswith(suffix):
                normalized = normalized[:-len(suffix)].strip()
                break
        
        # 공백 제거 후 소문자 변환
        result = normalized.replace(" ", "").lower()
        
        # 디버깅 로그
        if original != result:
            logger.debug(f"팀명 정규화: '{original}' -> '{result}'")
        
        return result

    async def _build_matched_game_result_by_teams(
        self, row, league_id: str, target_game: Dict, home_link, away_link,
        web_home: str, web_away: str, game_dt: datetime
    ) -> Optional[Dict]:
        """팀명 기반 매칭된 경기의 결과 구성"""
        try:
            # URL 정보 구성
            home_href = await home_link.get_attribute('href')
            away_href = await away_link.get_attribute('href')
            
            home_url = f"https://www.betman.co.kr{home_href}" if home_href else ""
            away_url = f"https://www.betman.co.kr{away_href}" if away_href else ""

            # 최종 결과 구성
            return {
                'league_id': league_id,
                'date': game_dt.strftime('%Y-%m-%d'),
                'time': game_dt.strftime('%H:%M'),
                'home_team': web_home,
                'away_team': web_away,
                'home_team_url': home_url,
                'away_team_url': away_url,
                'home_team_id': target_game['home_team_id'],
                'away_team_id': target_game['away_team_id'],
                'match_id': target_game['match_id'],
                'game_status': '경기전'
            }
            
        except Exception as e:
            logger.debug(f"경기 결과 구성 중 오류: {e}")
            return None
    
    async def _get_game_status(self, vs_cell) -> str:
        """경기 상태 확인"""
        try:
            # 경기 상태 텍스트 확인
            status_text = await vs_cell.text_content()
            if not status_text:
                return "알 수 없음"
                
            # 점수가 있으면 경기 진행 중 또는 종료
            if re.search(r'\d+:\d+', status_text):
                return "경기 종료"
                
            # "VS" 텍스트가 있으면 경기전
            if "VS" in status_text.upper():
                return "경기전"
                
            return "알 수 없음"
            
        except Exception as e:
            logger.debug(f"경기 상태 확인 오류: {e}")
            return "알 수 없음"
    
    async def close(self):
        """브라우저 정리"""
        if self.browser:
            await self.browser.close()
            self.browser = None
        if self.playwright:
            await self.playwright.stop()
            self.playwright = None
    
    async def _collect_team_stats(self, games: List[Dict]) -> None:
        """수집된 경기의 팀 통계 수집"""
        logger.info("🎯 팀 통계 수집 시작")
        
        # 고유한 팀 URL 수집
        team_urls = set()
        for game in games:
            if game.get('home_team_url'):
                team_urls.add((
                    game['home_team_url'], 
                    game['home_team'], 
                    game['home_team_id'],
                    'home'
                ))
            if game.get('away_team_url'):
                team_urls.add((
                    game['away_team_url'], 
                    game['away_team'], 
                    game['away_team_id'],
                    'away'
                ))
        
        logger.info(f"수집할 팀: {len(team_urls)}개")
        
        if not team_urls:
            logger.warning("수집할 팀 URL이 없습니다")
            return
        
        # 브라우저 생성
        if not self.browser:
            return
            
        # 팀별 통계 수집
        for team_url, team_name, team_id, team_type in team_urls:
            await self._collect_single_team_stats(
                self.browser, team_url, team_name, team_id, team_type
            )
            # 팀 간 대기 시간 추가 (너무 빠른 요청 방지)
            await asyncio.sleep(1)
        
        logger.info("🎯 모든 팀 통계 수집 완료")
                    
    async def _collect_single_team_stats(
        self, browser, team_url: str, team_name: str, team_id: str, 
        team_type: str
    ) -> None:
        """단일 팀 통계 수집"""
        # Team stats collection started - individual logging removed
        
        try:
            # 새 페이지 생성
            page = await browser.new_page()
            
            # 팀 페이지로 이동
            await page.goto(team_url, timeout=30000)
            await page.wait_for_load_state('networkidle', timeout=15000)
            await asyncio.sleep(2)
            
            # 축구 팀 통계 직접 파싱
            team_stats = await self._parse_soccer_team_stats(
                page, team_name, team_id
            )
            
            if team_stats:
                pass  # Team stats collection completed - individual logging removed
                
                # Structured team stats output removed for cleaner logging
                
            else:
                logger.warning(f"❌ {team_name} ({team_type}) 통계 수집 실패")
            
            await page.close()
            
        except Exception as e:
            logger.error(f"❌ {team_name} ({team_type}) 통계 수집 실패: {e}")
    
    async def _collect_homeaway_data(self, page: Page, team_name: str) -> Dict[str, str]:
        """전체/홈/원정 각각의 데이터를 수집 (야구 방식)"""
        data = {}
        
        # 카테고리별 설정: (홈/원정 옵션, 경기수)
        category_configs = {
            'total': ('0', '5'),    # 전체, 5경기
            'home': ('1', '5'),     # 홈, 5경기  
            'away': ('2', '5')      # 원정, 5경기
        }
        
        for category, (option, games_count) in category_configs.items():
            try:
                # 경기수 설정
                await self._set_games_count(page, games_count)
                
                # 홈/원정 버튼 클릭
                await self._click_homeaway_button(page, option)
                
                # HTML 가져오기
                html = await page.content()
                data[category] = html
                
                # Data collection completed for category - logging removed
                
            except Exception as e:
                logger.warning(f"❌ {team_name} {category} 데이터 수집 실패: {e}")
                data[category] = ""
        
        return data
    
    async def _set_games_count(self, page: Page, count: str) -> None:
        """경기수 설정 (야구 방식)"""
        try:
            # JavaScript 함수로 직접 설정
            await page.evaluate(f"searchLatestRecord('latestCount', '{count}')")
            await page.wait_for_timeout(500)
        except Exception as e:
            logger.debug(f"경기수 설정 실패 (count={count}): {e}")
    
    async def _click_homeaway_button(self, page: Page, option: str) -> None:
        """홈/원정 선택 버튼 클릭 (야구 방식)"""
        try:
            # JavaScript 함수 직접 호출
            await page.evaluate(f"searchLatestRecord('homeAway', '{option}')")
            await page.wait_for_timeout(500)
        except Exception as e:
            logger.debug(f"홈/원정 버튼 클릭 실패 (option={option}): {e}")
    
    def _extract_games_count(self, record_text: str) -> int:
        """record 텍스트에서 경기수 추출 (예: '3승1무1패' -> 5)"""
        try:
            import re

            # 승무패 패턴 찾기
            pattern = r'(\d+)승(\d+)무(\d+)패'
            match = re.search(pattern, record_text)
            if match:
                wins = int(match.group(1))
                draws = int(match.group(2))
                losses = int(match.group(3))
                return wins + draws + losses
            return 5  # 기본값
        except Exception:
            return 5

    async def _parse_soccer_team_stats(self, page, team_name: str, team_id: str) -> Dict:
        """축구 팀 통계 파싱 - 야구 스타일 홈/원정 별도 수집"""
        try:
            # 야구 방식: 전체/홈/원정 각각의 HTML 수집
            homeaway_data = await self._collect_homeaway_data(page, team_name)
            
            if not homeaway_data:
                logger.warning(f"❌ {team_name} 홈/원정 데이터 수집 실패")
                return {}
            
            # 파서를 이용해 데이터 파싱 - 수정된 파서 사용
            from sports.soccer.parsers.k_league_stats_parser_fixed import \
                KLeagueStatsParserFixed as KLeagueStatsParser

            # 동적 리그 결정 (야구 방식 참고)
            league = self._get_league_from_team(team_name) or self._get_league_from_url(page.url)
            
            combined_stats = {
                'team_id': team_id,
                'team_name': team_name,
                'league': league,
                'seasons_summary': {},
                'recent_games': {},
                'recent_games_summary': {},
                'season_stats': {},
                'sports': 'soccer',
                'collection_time': datetime.now().isoformat()
            }
            
            # 각 카테고리별로 파싱 (야구와 동일한 방식)
            for category, html in homeaway_data.items():
                if not html:
                    continue
                    
                parser = KLeagueStatsParser(html, team_name)
                
                # 시즌 요약은 전체에서만 수집
                if category == 'total':
                    combined_stats['seasons_summary'] = parser.parse_season_summary()
                
                # 최근 경기는 카테고리별로 처리
                recent_games = parser.parse_recent_games()
                if recent_games and category != 'total':
                    n_games = len(recent_games)
                    if category == 'home':
                        key = f"recent_{n_games}_home_games"
                    else:  # away
                        key = f"recent_{n_games}_away_games"
                    combined_stats['recent_games'][key] = recent_games
                
                # 최근 경기 요약
                recent_summary = parser.parse_recent_games_summary()
                if recent_summary and category != 'total':
                    if category == 'home':
                        home_data = recent_summary.get('home', {})
                        if home_data:
                            n_home = self._extract_games_count(home_data.get('record', ''))
                            key_home = f"recent_{n_home}_home_games" if n_home else 'recent_home_games'
                            combined_stats['recent_games_summary'][key_home] = home_data
                    elif category == 'away':
                        away_data = recent_summary.get('away', {})
                        if away_data:
                            n_away = self._extract_games_count(away_data.get('record', ''))
                            key_away = f"recent_{n_away}_away_games" if n_away else 'recent_away_games'
                            combined_stats['recent_games_summary'][key_away] = away_data
            
            return combined_stats
            
        except Exception as e:
            logger.error(f"팀 통계 파싱 오류: {e}")
            return {}

    async def _parse_season_stats(self, table, stats: Dict) -> None:
        """시즌 전체 통계 파싱"""
        try:
            # 첫 번째 데이터 행 찾기
            first_row = await table.query_selector('tbody tr')
            if not first_row:
                return
            
            cells = await first_row.query_selector_all('td')
            if len(cells) < 5:
                return
            
            # 기본 통계 추출
            stats['season_rank'] = await self._get_cell_text(cells, 0)  # 순위
            stats['home_away'] = await self._get_cell_text(cells, 1)   # 홈/원정
            stats['matches_played'] = await self._get_cell_text(cells, 2)  # 경기수
            stats['record'] = await self._get_cell_text(cells, 3)      # 성적 (승무패)
            stats['points'] = await self._get_cell_text(cells, 4)      # 승점
            
            # 추가 통계가 있다면 계속 추출
            if len(cells) > 6:
                stats['goals_for'] = await self._get_cell_text(cells, 6)   # 득점
                stats['goals_against'] = await self._get_cell_text(cells, 7) # 실점
                
        except Exception as e:
            logger.error(f"시즌 통계 파싱 오류: {e}")

    async def _parse_recent_matches(self, table, stats: Dict) -> None:
        """최근 경기 기록 파싱"""
        try:
            rows = await table.query_selector_all('tbody tr')
            recent_matches = []
            
            for row in rows[:5]:  # 최근 5경기만
                cells = await row.query_selector_all('td')
                if len(cells) < 5:
                    continue
                
                match_data = {
                    'date': await self._get_cell_text(cells, 0),        # 일시
                    'home_away': await self._get_cell_text(cells, 1),   # 홈/원정
                    'opponent': await self._get_cell_text(cells, 2),    # 상대팀
                    'result': await self._get_cell_text(cells, 3),      # 결과
                    'goals_scored': await self._get_cell_text(cells, 4) # 득점
                }
                recent_matches.append(match_data)
            
            stats['recent_matches'] = recent_matches
            stats['recent_matches_count'] = len(recent_matches)
            
        except Exception as e:
            logger.error(f"최근 경기 파싱 오류: {e}")

    async def _get_cell_text(self, cells, index: int) -> str:
        """안전한 셀 텍스트 추출"""
        try:
            if index < len(cells):
                text = await cells[index].text_content()
                return text.strip() if text else ""
            return ""
        except Exception:
            return ""

    # _print_structured_team_stats method removed - individual team logging disabled
        
        # Team stats output completed - individual logging removed
        logger.info("")  # 빈 줄 추가

    @staticmethod
    def _parse_time(date_text: str) -> Optional[datetime]:
        """날짜 파싱"""
        try:
            if "(" not in date_text:
                return None

            date_part = date_text.split("(")[0].strip()
            time_part = "18:30"
            if ") " in date_text:
                time_part = date_text.split(") ")[1]

            year = 2025
            month, day = map(int, date_part.split(".")[1:])
            hour, minute = map(int, time_part.split(":"))

            game_dt = datetime(year, month, day, hour, minute)
            # KST 타임존 직접 생성
            from zoneinfo import ZoneInfo
            kst = ZoneInfo("Asia/Seoul")
            return game_dt.replace(tzinfo=kst)

        except Exception:
            return None

    @staticmethod
    def _get_team_id(url: str) -> Optional[str]:
        """URL에서 팀 ID 추출"""
        if not url:
            return None
        match = re.search(r'teamId=([^&]+)', url)
        return match.group(1) if match else None

    @staticmethod
    async def setup_browser_page(page: Page) -> None:
        """브라우저 페이지 초기 설정"""
        await page.set_viewport_size({"width": 1920, "height": 1080})
        await page.set_extra_http_headers({
            'User-Agent': (
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                'AppleWebKit/537.36'
            )
        })

    @staticmethod
    async def _extract_team_urls(vs_cell) -> Dict[str, Optional[str]]:
        """팀 URL 정보 추출 (실제 HTML 구조 기반)"""
        home_link = await vs_cell.query_selector(
            '.vsDIv > div:first-child .tbCell a.team'
        )
        away_link = await vs_cell.query_selector(
            '.vsDIv > div:last-child .tbCell a.team'
        )

        home_url = None
        away_url = None

        if home_link:
            href = await home_link.get_attribute('href')
            if href:
                home_url = f"https://www.betman.co.kr{href}"

        if away_link:
            href = await away_link.get_attribute('href')
            if href:
                away_url = f"https://www.betman.co.kr{href}"

        return {
            'home_url': home_url,
            'away_url': away_url
        }

    @staticmethod
    def _teams_match(web_team: str, target_team: str) -> bool:
        """팀명 유연 매칭 (공백, 특수문자 무시)"""
        import re

        # 공백과 특수문자 제거 후 비교
        web_clean = re.sub(r'[^가-힣a-zA-Z0-9]', '', web_team.lower())
        target_clean = re.sub(r'[^가-힣a-zA-Z0-9]', '', target_team.lower())
        
        return (web_clean == target_clean or 
                web_clean in target_clean or 
                target_clean in web_clean)

    async def _extract_and_validate_teams(
        self, vs_cell, target_game: Dict
    ) -> Optional[Dict]:
        """팀 추출 및 검증 (야구 방식 그대로)"""
        try:
            # 홈팀, 원정팀 요소 찾기
            home_elem = await vs_cell.query_selector('.vsDIv > div:first-child .tbCell .team')
            away_elem = await vs_cell.query_selector('.vsDIv > div:last-child .tbCell .team')
            
            if not home_elem or not away_elem:
                return None

            web_home = await home_elem.text_content()
            web_away = await away_elem.text_content()
            
            if not web_home or not web_away:
                return None

            web_home = web_home.strip()
            web_away = web_away.strip()

            # 타겟 팀명과 매칭 확인
            home_match = self._teams_match(web_home, target_game['home_team'])
            away_match = self._teams_match(web_away, target_game['away_team'])

            if home_match and away_match:
                return {
                    'home_team': web_home,
                    'away_team': web_away
                }

            return None
        except Exception:
            return None

    async def _build_matched_game_result(
        self, row, league_id: str, target_game: Dict,
        home_link, away_link
    ) -> Optional[Dict]:
        """매칭된 경기의 결과 구성 - 야구 방식 적용"""
        try:
            # 게임 datetime 생성
            target_date = target_game['match_date']
            target_time = target_game['match_time']
            
            # 팀명 텍스트 추출
            home_team_text = await home_link.text_content()
            away_team_text = await away_link.text_content()

            if not home_team_text or not away_team_text:
                return None

            # URL 정보 구성
            home_href = await home_link.get_attribute('href')
            away_href = await away_link.get_attribute('href')
            
            home_url = f"https://www.betman.co.kr{home_href}"
            away_url = f"https://www.betman.co.kr{away_href}"

            # 최종 결과 구성
            return {
                'league_id': league_id,
                'date': target_date,
                'time': target_time,
                'home_team': home_team_text.strip(),
                'away_team': away_team_text.strip(),
                'home_team_url': home_url,
                'away_team_url': away_url,
                'home_team_id': target_game.get('home_team_id'),
                'away_team_id': target_game.get('away_team_id'),
                'match_id': target_game.get('match_id'),
                'game_status': '경기전'
            }
            
        except Exception as e:
            logger.debug(f"경기 결과 구성 중 오류: {e}")
            return None

    def _get_league_from_team(self, team_name: str) -> str:
        """팀명으로부터 리그 결정 (야구 방식 참고)"""
        try:
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                return ""
            
            # team_info 테이블에서 sportic_league_id로 리그 조회
            response = client.table('team_info').select(
                'sportic_league_id'
            ).eq('team_name', team_name).execute()
            
            if response.data and len(response.data) > 0:
                league_id = response.data[0].get('sportic_league_id', '')
                
                # sportic_league_id를 리그명으로 변환
                league_mapping = {
                    'sc003': 'K리그',
                    'sc007': 'J리그',
                    'sc018': 'MLS'
                }
                
                return league_mapping.get(league_id.lower(), '')
            
            return ""
            
        except Exception as e:
            logger.debug(f"리그 조회 오류 ({team_name}): {e}")
            return ""
    
    def _get_league_from_url(self, url: str) -> str:
        """URL에서 리그 결정 (야구 방식 참고)"""
        try:
            if 'SC003' in url or 'scTeamDetail' in url:
                return 'K리그'
            elif 'SC007' in url:
                return 'J리그'
            elif 'SC018' in url:
                return 'MLS'
            else:
                return 'K리그'  # 기본값
        except Exception:
            return 'K리그'  # 기본값

    async def _crawl_league_target_games_by_league(self, league: str, target_games: List[Dict]) -> List[Dict]:
        """특정 리그의 target_games만 크롤링 - 플러그인 호출용"""
        if not target_games:
            return []
        
        # 리그명을 league_id로 변환
        league_id_map = {
            "K리그2": "SC003",
            "J리그": "SC007", 
            "MLS": "SC018"
        }
        
        league_id = league_id_map.get(league)
        if not league_id or not SoccerLeagueStrategy.is_supported_league(league_id):
            logger.warning(f"❌ 지원하지 않는 리그: {league} ({league_id})")
            return []

        # 브라우저 설정
        playwright_instance = None
        browser = None
        page = None
        crawled_games = []

        try:
            playwright_instance = await async_playwright().start()
            browser = await playwright_instance.chromium.launch(headless=True)
            page = await browser.new_page()
            
            await page.goto(
                SCHEDULE_URL,
                timeout=60000,
                wait_until="networkidle",
            )

            # 해당 리그만 크롤링
            crawled_games = await self._crawl_league_target_games(
                page, league_id, target_games
            )

        except Exception as e:
            logger.error(f"❌ {league} 크롤링 실패: {e}")
            
        finally:
            # 리소스 정리
            if page:
                try:
                    await page.close()
                except Exception:
                    pass
            
            if browser:
                try:
                    await browser.close()
                except Exception:
                    pass
            
            if playwright_instance:
                try:
                    await playwright_instance.stop()
                except Exception:
                    pass

        return crawled_games


# 테스트용 함수
async def test_soccer_collector():
    """축구 수집기 테스트"""
    collector = SoccerCollector()
    
    # 테스트 데이터 (실제 DB 없이)
    test_games = [
        {
            'match_id': 'test001',
            'match_date': '2025-07-05',
            'match_time': '19:00',
            'league_id': 'SC003',
            'home_team': '경남FC',
            'away_team': '안산 그리너스',
            'home_team_id': 'SC00301',
            'away_team_id': 'SC00302'
        }
    ]
    
    try:
        # 직접 크롤링 테스트
        results = await collector._crawl_target_games(test_games)
        
        print(f"테스트 결과: {len(results)}개 경기")
        for result in results:
            print(f"- {result['home_team']} vs {result['away_team']} "
                  f"({result['date']} {result['time']})")
            
    except Exception as e:
        print(f"테스트 오류: {e}")
    finally:
        await collector.close()


if __name__ == "__main__":
    asyncio.run(test_soccer_collector())
