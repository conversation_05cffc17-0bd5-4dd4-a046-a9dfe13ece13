"""
수정된 K리그 팀 통계 파서 - 새로운 베트맨 HTML 구조 기반
현재 HTML 구조 분석 결과를 반영하여 완전히 새로 작성
"""
from typing import Any, Dict, List

from bs4 import BeautifulSoup, Tag

from utils.logger import Logger

logger = Logger(__name__)


class KLeagueStatsParserFixed:
    """새로운 HTML 구조 기반 K리그 팀 통계 파서"""
    
    def __init__(self, html: str, team_name: str):
        self.html = html
        self.team_name = team_name
        self.soup = BeautifulSoup(html, 'html.parser')
        
    def parse_season_summary(self) -> Dict[str, Any]:
        """시즌 요약 정보 파싱 - 새로운 테이블 구조 기반"""
        summary = {}
        
        try:
            # 새로운 구조: class='tbl tblAuto' 테이블들 찾기
            tables = self.soup.find_all('table', class_='tbl tblAuto')
            
            if len(tables) >= 1:
                # 첫 번째 테이블: 시즌 전체성적
                season_table = tables[0]
                summary = self._parse_new_season_stats(season_table)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 시즌 요약 파싱 실패: {e}")
             
        return summary
        
    def _parse_new_season_stats(self, table: Tag) -> Dict[str, Any]:
        """새로운 테이블 구조에서 시즌 통계 파싱"""
        stats = {'홈': {}, '원정': {}, '전체': {}}
        
        try:
            tbody = table.find('tbody')
            if not tbody:
                return stats
                
            rows = tbody.find_all('tr')
            
            # 데이터 행들 (헤더 행들 제외)
            data_rows = []
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 8:  # 최소 필요한 셀 수
                    data_rows.append(cells)
            
            # 홈/원정 데이터 파싱
            for row_cells in data_rows:
                cell_texts = [cell.get_text(strip=True) for cell in row_cells]
                
                # 두 번째 셀이 홈/원정 구분자
                if len(cell_texts) < 8:
                    continue
                    
                location = cell_texts[1]  # 홈/원정
                
                if location in ['홈', '원정']:
                    parsed_data = self._extract_team_stats_from_row(cell_texts, location)
                    stats[location] = parsed_data
            
            # 전체 통계 계산
            stats['전체'] = self._calculate_total_stats(stats['홈'], stats['원정'])
            
        except Exception as e:
            logger.error(f"❌ 시즌 통계 테이블 파싱 실패: {e}")
            
        return stats
    
    def _extract_team_stats_from_row(self, cells: List[str], location: str) -> Dict[str, Any]:
        """행 데이터에서 팀 통계 추출"""
        try:
            # 새로운 컬럼 매핑 (현재 HTML 구조 기반)
            if location == '홈':
                # 홈 행: 순위 포함 (21개 셀)
                mapping = {
                    0: 'rank',
                    1: 'home_away',
                    2: 'matches_played',
                    3: 'wins', 
                    4: 'draws',
                    5: 'losses',
                    6: 'points',
                    7: 'win_rate',
                    8: 'goals_scored',
                    9: 'goals_scored_avg',
                    10: 'shooting_efficiency',
                    11: 'goals_conceded',
                    12: 'goals_conceded_avg',
                    13: 'goal_difference',
                    14: 'assists',
                    15: 'goal_kicks',
                    16: 'corner_kicks',
                    17: 'penalty_kicks',
                    18: 'offsides',
                    19: 'fouls',
                    20: 'yellow_cards',
                    21: 'red_cards'
                }
            else:
                # 원정 행: 순위 없음 (20개 셀)
                mapping = {
                    0: 'home_away',
                    1: 'matches_played',
                    2: 'wins',
                    3: 'draws', 
                    4: 'losses',
                    5: 'points',
                    6: 'win_rate',
                    7: 'goals_scored',
                    8: 'goals_scored_avg',
                    9: 'shooting_efficiency',
                    10: 'goals_conceded',
                    11: 'goals_conceded_avg',
                    12: 'goal_difference',
                    13: 'assists',
                    14: 'goal_kicks',
                    15: 'corner_kicks',
                    16: 'penalty_kicks',
                    17: 'offsides',
                    18: 'fouls',
                    19: 'yellow_cards',
                    20: 'red_cards'
                }
            
            result = {}
            
            for index, field_name in mapping.items():
                if index < len(cells):
                    value = cells[index]
                    
                    # 숫자 필드 변환
                    if field_name in ['matches_played', 'wins', 'draws', 'losses', 'points', 
                                     'goals_scored', 'goals_conceded', 'assists', 'goal_kicks',
                                     'corner_kicks', 'penalty_kicks', 'offsides', 'fouls',
                                     'yellow_cards', 'red_cards', 'rank']:
                        result[field_name] = self._safe_int(value)
                    elif field_name in ['win_rate', 'goals_scored_avg', 'goals_conceded_avg', 
                                       'shooting_efficiency', 'goal_difference']:
                        result[field_name] = self._safe_float(value)
                    else:
                        result[field_name] = value
            
            return result
            
        except Exception as e:
            logger.error(f"팀 통계 추출 실패 ({location}): {e}")
            return {}
    
    def _calculate_total_stats(self, home_stats: Dict, away_stats: Dict) -> Dict[str, Any]:
        """홈/원정 통계로 전체 통계 계산"""
        total = {}
        
        try:
            # 숫자 필드들 합계
            sum_fields = ['matches_played', 'wins', 'draws', 'losses', 'points',
                         'goals_scored', 'goals_conceded', 'assists', 'goal_kicks',
                         'corner_kicks', 'penalty_kicks', 'offsides', 'fouls',
                         'yellow_cards', 'red_cards']
            
            for field in sum_fields:
                home_val = home_stats.get(field, 0)
                away_val = away_stats.get(field, 0)
                total[field] = home_val + away_val
            
            # 평균/비율 계산
            if total.get('matches_played', 0) > 0:
                total['win_rate'] = round(total['wins'] / total['matches_played'], 3)
                total['goals_scored_avg'] = round(total['goals_scored'] / total['matches_played'], 2)
                total['goals_conceded_avg'] = round(total['goals_conceded'] / total['matches_played'], 2)
            else:
                total['win_rate'] = 0.0
                total['goals_scored_avg'] = 0.0
                total['goals_conceded_avg'] = 0.0
            
            # 득실차
            total['goal_difference'] = total.get('goals_scored', 0) - total.get('goals_conceded', 0)
            
            # 슈팅 효율 (임시 계산)
            total['shooting_efficiency'] = round(
                (total.get('goals_scored', 0) / max(total.get('corner_kicks', 1), 1)) * 100, 1
            )
            
        except Exception as e:
            logger.error(f"전체 통계 계산 실패: {e}")
            
        return total
        
    def parse_recent_games(self) -> Dict[str, Any]:
        """최근 경기 파싱 - 새로운 테이블 구조 기반"""
        games = {'recent_5_home_games': {}, 'recent_5_away_games': {}}
        
        try:
            # 두 번째 테이블: 시즌 최근성적
            tables = self.soup.find_all('table', class_='tbl tblAuto')
            
            if len(tables) >= 2:
                recent_table = tables[1]
                games = self._parse_new_recent_games(recent_table)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 최근 경기 파싱 실패: {e}")
            
        return games
    
    def _parse_new_recent_games(self, table: Tag) -> Dict[str, Any]:
        """새로운 테이블 구조에서 최근 경기 파싱"""
        games = {'recent_5_home_games': {}, 'recent_5_away_games': {}}
        
        try:
            tbody = table.find('tbody')
            if not tbody:
                return games
                
            rows = tbody.find_all('tr')
            
            # 데이터 행들 (헤더 행들 제외)
            data_rows = []
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 8:  # 최소 필요한 셀 수
                    data_rows.append(cells)
            
            home_count = 0
            away_count = 0
            
            # 각 경기 데이터 파싱
            for row_cells in data_rows:
                cell_texts = [cell.get_text(strip=True) for cell in row_cells]
                
                if len(cell_texts) < 8:
                    continue
                
                # 경기 데이터 추출
                game_data = self._extract_game_data_from_row(cell_texts)
                
                if not game_data:
                    continue
                
                # 홈/원정 구분
                home_away = game_data.get('home_away', '')
                
                if home_away == '홈' and home_count < 5:
                    # 홈 경기
                    date_key = f"2025-07-{6-home_count:02d}"  # 임시 날짜
                    games['recent_5_home_games'][date_key] = self._create_game_structure(
                        game_data, True, self.team_name
                    )
                    home_count += 1
                    
                elif home_away == '원정' and away_count < 5:
                    # 원정 경기
                    date_key = f"2025-07-{1+away_count:02d}"  # 임시 날짜  
                    games['recent_5_away_games'][date_key] = self._create_game_structure(
                        game_data, False, self.team_name
                    )
                    away_count += 1
                    
        except Exception as e:
            logger.error(f"최근 경기 테이블 파싱 실패: {e}")
            
        return games
        
    def _extract_game_data_from_row(self, cells: List[str]) -> Dict[str, Any]:
        """행에서 경기 데이터 추출"""
        try:
            if len(cells) < 8:
                return {}
                
            return {
                'date': cells[0],           # 일시
                'home_away': cells[1],      # 홈/원정
                'opponent': cells[2],       # 상대팀
                'result': cells[3],         # 결과
                'goals_scored': self._safe_int(cells[4]),     # 득점
                'goals_conceded': self._safe_int(cells[5]),   # 실점
                'goal_difference': self._safe_int(cells[6]),  # 득실차
                'assists': self._safe_int(cells[7]) if len(cells) > 7 else 0,  # 도움
                'corners': self._safe_int(cells[8]) if len(cells) > 8 else 0,  # 코너킥
                'penalty_kicks': self._safe_int(cells[9]) if len(cells) > 9 else 0,  # 패널티
                'shots': self._safe_int(cells[10]) if len(cells) > 10 else 0,  # 슈팅
                'shots_on_target': self._safe_int(cells[11]) if len(cells) > 11 else 0,  # 유효슈팅
                'fouls': self._safe_int(cells[12]) if len(cells) > 12 else 0,  # 파울
                'yellow_cards': self._safe_int(cells[13]) if len(cells) > 13 else 0,  # 경고
                'red_cards': self._safe_int(cells[14]) if len(cells) > 14 else 0,  # 퇴장
            }
            
        except Exception as e:
            logger.error(f"경기 데이터 추출 실패: {e}")
            return {}
    
    def _create_game_structure(self, game_data: Dict, is_home: bool, team_name: str) -> Dict[str, Any]:
        """야구 스타일의 경기 구조 생성"""
        try:
            # 결과 변환
            result_map = {'승': 'W', '무': 'D', '패': 'L'}
            my_result = result_map.get(game_data.get('result', ''), game_data.get('result', ''))
            opponent_result = 'L' if my_result == 'W' else ('W' if my_result == 'L' else 'D')
            
            if is_home:
                # 홈 경기
                return {
                    'home': {
                        'home_team_name': team_name,
                        'result': my_result,
                        'goals_scored': game_data.get('goals_scored', 0),
                        'goals_conceded': game_data.get('goals_conceded', 0),
                        'goal_difference': game_data.get('goal_difference', 0),
                        'assists': game_data.get('assists', 0),
                        'corners': game_data.get('corners', 0),
                        'penalty_kicks': game_data.get('penalty_kicks', 0),
                        'shots': game_data.get('shots', 0),
                        'shots_on_target': game_data.get('shots_on_target', 0),
                        'fouls': game_data.get('fouls', 0),
                        'yellow_cards': game_data.get('yellow_cards', 0),
                        'red_cards': game_data.get('red_cards', 0),
                        'clean_sheet': 1 if game_data.get('goals_conceded', 0) == 0 else 0
                    },
                    'away': {
                        'away_team_name': game_data.get('opponent', ''),
                        'result': opponent_result,
                        'goals_scored': game_data.get('goals_conceded', 0),
                        'goals_conceded': game_data.get('goals_scored', 0),
                        'goal_difference': -game_data.get('goal_difference', 0),
                        'clean_sheet': 1 if game_data.get('goals_scored', 0) == 0 else 0
                    }
                }
            else:
                # 원정 경기
                return {
                    'home': {
                        'home_team_name': game_data.get('opponent', ''),
                        'result': opponent_result,
                        'goals_scored': game_data.get('goals_conceded', 0),
                        'goals_conceded': game_data.get('goals_scored', 0),
                        'goal_difference': -game_data.get('goal_difference', 0),
                        'clean_sheet': 1 if game_data.get('goals_scored', 0) == 0 else 0
                    },
                    'away': {
                        'away_team_name': team_name,
                        'result': my_result,
                        'goals_scored': game_data.get('goals_scored', 0),
                        'goals_conceded': game_data.get('goals_conceded', 0),
                        'goal_difference': game_data.get('goal_difference', 0),
                        'assists': game_data.get('assists', 0),
                        'corners': game_data.get('corners', 0),
                        'penalty_kicks': game_data.get('penalty_kicks', 0),
                        'shots': game_data.get('shots', 0),
                        'shots_on_target': game_data.get('shots_on_target', 0),
                        'fouls': game_data.get('fouls', 0),
                        'yellow_cards': game_data.get('yellow_cards', 0),
                        'red_cards': game_data.get('red_cards', 0),
                        'clean_sheet': 1 if game_data.get('goals_conceded', 0) == 0 else 0
                    }
                }
                
        except Exception as e:
            logger.error(f"경기 구조 생성 실패: {e}")
            return {}

    def parse_recent_games_summary(self) -> Dict[str, Any]:
        """최근 경기 요약 파싱"""
        try:
            # 최근 경기 데이터 기반으로 요약 계산
            recent_games = self.parse_recent_games()
            
            summary = {}
            
            # 홈 경기 요약
            home_games = recent_games.get('recent_5_home_games', {})
            if home_games:
                home_summary = self._calculate_games_summary(
                    [game['home'] for game in home_games.values()]
                )
                n_home = len(home_games)
                summary[f'recent_{n_home}_홈_games'] = home_summary
            
            # 원정 경기 요약  
            away_games = recent_games.get('recent_5_away_games', {})
            if away_games:
                away_summary = self._calculate_games_summary(
                    [game['away'] for game in away_games.values()]
                )
                n_away = len(away_games)
                summary[f'recent_{n_away}_원정_games'] = away_summary
                
            return summary
            
        except Exception as e:
            logger.error(f"최근 경기 요약 파싱 실패: {e}")
            return {}
    
    def _calculate_games_summary(self, games: List[Dict]) -> Dict[str, Any]:
        """경기 리스트에서 요약 통계 계산"""
        if not games:
            return {
                'record': '-',
                'goals_for': 0.0,
                'goals_against': 0.0,
                'goal_difference': 0.0,
                'assists': 0.0,
                'shots': 0.0,
                'shots_on_target': 0.0,
                'fouls': 0.0,
                'yellow_cards': 0.0,
                'red_cards': 0.0
            }
        
        wins = sum(1 for g in games if g.get('result') == 'W')
        draws = sum(1 for g in games if g.get('result') == 'D')
        losses = sum(1 for g in games if g.get('result') == 'L')
        
        total_goals_for = sum(g.get('goals_scored', 0) for g in games)
        total_goals_against = sum(g.get('goals_conceded', 0) for g in games)
        total_assists = sum(g.get('assists', 0) for g in games)
        total_shots = sum(g.get('shots', 0) for g in games)
        total_shots_on_target = sum(g.get('shots_on_target', 0) for g in games)
        total_fouls = sum(g.get('fouls', 0) for g in games)
        total_yellow = sum(g.get('yellow_cards', 0) for g in games)
        total_red = sum(g.get('red_cards', 0) for g in games)
        
        n_games = len(games)
        
        return {
            'record': f'{wins}승 {draws}무 {losses}패',
            'goals_for': round(total_goals_for / n_games, 2),
            'goals_against': round(total_goals_against / n_games, 2),
            'goal_difference': total_goals_for - total_goals_against,
            'assists': round(total_assists / n_games, 2),
            'shots': round(total_shots / n_games, 2),
            'shots_on_target': round(total_shots_on_target / n_games, 2),
            'fouls': round(total_fouls / n_games, 2),
            'yellow_cards': round(total_yellow / n_games, 2),
            'red_cards': round(total_red / n_games, 2)
        }

    def parse_season_stats(self) -> Dict[str, Any]:
        """시즌 통계 파싱 (역대 시즌 성적)"""
        try:
            # 세 번째 테이블: 역대시즌 성적
            tables = self.soup.find_all('table', class_='tbl tblAuto')
            
            if len(tables) >= 3:
                history_table = tables[2]
                return self._parse_season_history(history_table)
            
            return {}
            
        except Exception as e:
            logger.error(f"시즌 통계 파싱 실패: {e}")
            return {}
    
    def _parse_season_history(self, table: Tag) -> Dict[str, Any]:
        """역대 시즌 성적 파싱 - 최근 1개 시즌만 (야구와 동일)"""
        history = {}
        
        try:
            tbody = table.find('tbody')
            if not tbody:
                return history
                
            rows = tbody.find_all('tr')
            
            # 🚀 야구와 동일: 가장 최근 1개 시즌만 저장
            latest_season_found = False
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 7:
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    
                    # "조회 결과가 없습니다" 체크
                    if "조회 결과가 없습니다" in cell_texts[0]:
                        break
                        
                    season = cell_texts[0]  # 시즌
                    
                    if season and season != '시즌':
                        # 가장 최근 시즌만 저장하고 중단
                        history[season] = {
                            'rank': self._safe_int(cell_texts[1]),
                            'matches_played': self._safe_int(cell_texts[2]),
                            'wins': self._safe_int(cell_texts[3]),
                            'draws': self._safe_int(cell_texts[4]), 
                            'losses': self._safe_int(cell_texts[5]),
                            'points': self._safe_int(cell_texts[6]),
                            'win_rate': self._safe_float(cell_texts[7]) if len(cell_texts) > 7 else 0.0
                        }
                        latest_season_found = True
                        break  # 첫 번째 시즌만 저장하고 중단
                        
        except Exception as e:
            logger.error(f"역대 시즌 성적 파싱 실패: {e}")
            
        return history
    
    def _safe_int(self, value: str) -> int:
        """안전한 정수 변환"""
        try:
            import re

            # 숫자만 추출
            cleaned = re.sub(r'[^\d-]', '', str(value).strip())
            return int(cleaned) if cleaned else 0
        except:
            return 0
    
    def _safe_float(self, value: str) -> float:
        """안전한 실수 변환"""
        try:
            import re

            # 숫자와 소수점만 추출
            cleaned = re.sub(r'[^\d.-]', '', str(value).strip())
            return float(cleaned) if cleaned else 0.0
        except:
            return 0.0