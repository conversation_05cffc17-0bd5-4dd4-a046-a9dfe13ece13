"""
축구 데이터 파서
기존 야구 파서 패턴을 축구에 적용
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from bs4 import BeautifulSoup, Tag

from utils.helpers import convert_date_str
from utils.logger import Logger

logger = Logger(__name__)


@dataclass
class SoccerData:
    """축구 데이터 구조"""
    team_name: str
    league: str
    league_id: str
    profile: Dict[str, Any]
    season_stats: Dict[str, Any]
    recent_games: List[Dict[str, Any]]
    career_stats: Dict[str, Any]
    players: List[Dict[str, Any]]


@dataclass
class SoccerPlayerData:
    """축구 선수 데이터 구조"""
    player_name: str
    team_name: str
    league: str
    league_id: str
    position: str
    profile: Dict[str, Any]
    season_stats: Dict[str, Any]
    career_stats: Dict[str, Any]


class BaseSoccerParser(ABC):
    """축구 파서 기본 클래스 - 야구 BaseStatsParser 패턴 적용"""
    
    def __init__(self, html: str, team_mappings: Optional[Dict[str, str]] = None):
        self.soup = BeautifulSoup(html, "lxml")
        self.team_mappings = team_mappings or {}
        self.league_code = self.get_league_code()
    
    @abstractmethod
    def get_league_code(self) -> str:
        """리그 코드 반환"""
        pass
    
    @abstractmethod
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """시즌 요약 컬럼 매핑 반환"""
        pass
    
    @abstractmethod
    def get_recent_games_mapping(self) -> Dict[str, Dict[str, int]]:
        """최근 경기 컬럼 매핑 반환"""
        pass
    
    def parse_season_summary(self) -> Dict[str, Optional[Dict[str, str]]]:
        """시즌 전체 성적 파싱 - 실제 HTML 구조 기반"""
        season_stats = {
            "home": None,
            "away": None,
            "total": None
        }

        try:
            # 시즌 전체성적 테이블 찾기
            season_table = self.soup.find('table', string=lambda text: text and '시즌 전체성적' in text)
            if not season_table:
                season_table = self.soup.find('table')
                if not season_table:
                    return season_stats

            # 테이블 행들 가져오기
            rows = season_table.find_all('tr')
            if len(rows) < 3:
                return season_stats

            # 데이터 행들 (헤더 제외)
            data_rows = []
            for row in rows[2:]:  # 첫 두 행은 헤더
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 10:  # 최소 필요한 컬럼 수
                    data_rows.append(cells)

            # 홈, 원정, 전체 데이터 파싱
            for row in data_rows:
                row_type = row[1].get_text(strip=True) if len(row) > 1 else ""

                if "홈" in row_type:
                    season_stats["home"] = self._extract_season_data(row, "home")
                elif "원정" in row_type:
                    season_stats["away"] = self._extract_season_data(row, "away")
                elif "전체" in row_type:
                    season_stats["total"] = self._extract_season_data(row, "total")

        except Exception as e:
            logger.debug(f"시즌 통계 파싱 실패: {e}")

        return season_stats
    
    def parse_recent_games(self, limit: int = 5) -> List[Dict[str, str]]:
        """최근 경기 파싱 - 실제 HTML 구조 기반"""
        try:
            games = []

            # 최근 경기 테이블 찾기
            recent_table = self._find_recent_games_table()
            if not recent_table:
                return games

            rows = recent_table.find_all('tr')
            if len(rows) < 3:
                return games

            # 데이터 행들 (헤더 제외)
            data_rows = []
            for row in rows[2:]:  # 첫 두 행은 헤더
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 8:  # 최소 필요한 컬럼 수
                    data_rows.append(cells)

            # 최근 경기 데이터 파싱
            for row in data_rows[:limit]:
                try:
                    game_data = self._extract_recent_game_data(row)
                    if game_data:
                        game_data["league_code"] = self.league_code
                        games.append(game_data)
                except Exception as e:
                    logger.debug(f"개별 경기 파싱 실패: {e}")
                    continue

            return games

        except Exception as e:
            logger.debug(f"최근 경기 파싱 실패: {e}")
            return []
    
    def _parse_season_row(self, row_id: str, mapping: Dict[str, int]) -> Optional[Dict[str, str]]:
        """시즌 통계 행 파싱"""
        try:
            row = self.soup.find('tr', id=row_id)
            if not row:
                return None
            
            cells = row.find_all(['td', 'th'])
            if len(cells) < max(mapping.values()) + 1:
                return None
            
            result = {}
            for field, index in mapping.items():
                if index < len(cells):
                    result[field] = cells[index].get_text(strip=True)
            
            return result if result else None
            
        except Exception as e:
            logger.debug(f"시즌 행 파싱 실패 {row_id}: {e}")
            return None
    
    def _extract_season_data(self, row: List, row_type: str) -> Dict[str, str]:
        """시즌 데이터 추출"""
        try:
            data = {
                "type": row_type,
                "league_code": self.league_code
            }

            # 실제 HTML 구조에 맞는 인덱스 매핑
            if len(row) >= 20:  # 축구 테이블 컬럼 수
                data.update({
                    "rank": row[0].get_text(strip=True) if row[0] else "",
                    "home_away": row[1].get_text(strip=True) if row[1] else "",
                    "games": row[2].get_text(strip=True) if row[2] else "",
                    "wins": row[3].get_text(strip=True) if row[3] else "",
                    "draws": row[4].get_text(strip=True) if row[4] else "",
                    "losses": row[5].get_text(strip=True) if row[5] else "",
                    "points": row[6].get_text(strip=True) if row[6] else "",
                    "win_rate": row[7].get_text(strip=True) if row[7] else "",
                    "goals_for": row[8].get_text(strip=True) if row[8] else "",
                    "goals_avg": row[9].get_text(strip=True) if row[9] else "",
                    "shooting_rate": row[10].get_text(strip=True) if row[10] else "",
                    "goals_against": row[11].get_text(strip=True) if row[11] else "",
                    "goals_against_avg": row[12].get_text(strip=True) if row[12] else "",
                    "assists": row[13].get_text(strip=True) if row[13] else "",
                    "goal_kicks": row[14].get_text(strip=True) if row[14] else "",
                    "corner_kicks": row[15].get_text(strip=True) if row[15] else "",
                    "penalty_kicks": row[16].get_text(strip=True) if row[16] else "",
                    "offsides": row[17].get_text(strip=True) if row[17] else "",
                    "fouls": row[18].get_text(strip=True) if row[18] else "",
                    "yellow_cards": row[19].get_text(strip=True) if row[19] else "",
                    "red_cards": row[20].get_text(strip=True) if len(row) > 20 else ""
                })

            return data

        except Exception as e:
            logger.debug(f"시즌 데이터 추출 실패: {e}")
            return {"type": row_type, "league_code": self.league_code}

    def _extract_recent_game_data(self, row: List) -> Optional[Dict[str, str]]:
        """최근 경기 데이터 추출"""
        try:
            if len(row) < 8:
                return None

            data = {
                "date": row[0].get_text(strip=True) if row[0] else "",
                "home_away": row[1].get_text(strip=True) if row[1] else "",
                "opponent": row[2].get_text(strip=True) if row[2] else "",
                "result": row[3].get_text(strip=True) if row[3] else "",
                "goals_total": row[4].get_text(strip=True) if row[4] else "",
                "goals_first": row[5].get_text(strip=True) if row[5] else "",
                "goals_second": row[6].get_text(strip=True) if row[6] else "",
                "goals_extra": row[7].get_text(strip=True) if row[7] else "",
                "goals_penalty": row[8].get_text(strip=True) if len(row) > 8 else "",
                "goals_against": row[9].get_text(strip=True) if len(row) > 9 else "",
                "goal_diff": row[10].get_text(strip=True) if len(row) > 10 else "",
                "assists": row[11].get_text(strip=True) if len(row) > 11 else "",
                "corner_kicks": row[12].get_text(strip=True) if len(row) > 12 else "",
                "penalty_kicks": row[13].get_text(strip=True) if len(row) > 13 else "",
                "offsides": row[14].get_text(strip=True) if len(row) > 14 else "",
                "fouls": row[15].get_text(strip=True) if len(row) > 15 else "",
                "yellow_cards": row[16].get_text(strip=True) if len(row) > 16 else "",
                "red_cards": row[17].get_text(strip=True) if len(row) > 17 else ""
            }

            return data

        except Exception as e:
            logger.debug(f"최근 경기 데이터 추출 실패: {e}")
            return None

    def _find_recent_games_table(self) -> Optional[Tag]:
        """최근 경기 테이블 찾기"""
        # 실제 HTML 구조에 맞는 테이블 찾기
        try:
            # 시즌 최근성적 테이블 찾기
            tables = self.soup.find_all('table')
            for table in tables:
                caption = table.find('caption')
                if caption and '최근성적' in caption.get_text():
                    return table

            # 대안: 두 번째 테이블 (보통 최근 경기)
            if len(tables) >= 2:
                return tables[1]

        except Exception as e:
            logger.debug(f"최근 경기 테이블 찾기 실패: {e}")

        return None


class KLeagueParser(BaseSoccerParser):
    """K리그 파서"""
    
    def get_league_code(self) -> str:
        return "K_LEAGUE"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {
                "games": 0, "wins": 1, "draws": 2, "losses": 3,
                "goals_for": 4, "goals_against": 5, "goal_diff": 6, "points": 7
            },
            "away": {
                "games": 0, "wins": 1, "draws": 2, "losses": 3,
                "goals_for": 4, "goals_against": 5, "goal_diff": 6, "points": 7
            },
            "total": {
                "games": 0, "wins": 1, "draws": 2, "losses": 3,
                "goals_for": 4, "goals_against": 5, "goal_diff": 6, "points": 7
            }
        }
    
    def get_recent_games_mapping(self) -> Dict[str, int]:
        return {
            "date": 0,
            "opponent": 1,
            "home_away": 2,
            "result": 3,
            "score": 4,
            "goals_for": 5,
            "goals_against": 6
        }


class EPLParser(BaseSoccerParser):
    """EPL 파서"""
    
    def get_league_code(self) -> str:
        return "EPL"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {
                "played": 0, "won": 1, "drawn": 2, "lost": 3,
                "goals_for": 4, "goals_against": 5, "goal_difference": 6, "points": 7
            },
            "away": {
                "played": 0, "won": 1, "drawn": 2, "lost": 3,
                "goals_for": 4, "goals_against": 5, "goal_difference": 6, "points": 7
            },
            "total": {
                "played": 0, "won": 1, "drawn": 2, "lost": 3,
                "goals_for": 4, "goals_against": 5, "goal_difference": 6, "points": 7
            }
        }
    
    def get_recent_games_mapping(self) -> Dict[str, int]:
        return {
            "date": 0,
            "opponent": 1,
            "venue": 2,
            "result": 3,
            "score": 4,
            "competition": 5
        }


class SoccerParser:
    """축구 통합 파서 - 리그별 파서 선택"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        self.team_mappings = team_mappings or {}
        self.league_parsers = {
            'SC001': KLeagueParser,  # K리그1
            'SC003': KLeagueParser,  # K리그2
            '52': EPLParser,         # EPL
            '53': EPLParser,         # 프리메라리가 (EPL 패턴 재사용)
            '54': EPLParser,         # 세리에A
            '55': EPLParser,         # 분데스리가
            '56': EPLParser,         # 프랑스리그
            '57': EPLParser,         # 에레디비시
            'SC007': KLeagueParser,     # J리그 (K리그 패턴 재사용)
        }
    
    def parse_team_data(self, html: str, team_name: str, **kwargs) -> SoccerData:
        """팀 데이터 파싱"""
        try:
            league_id = kwargs.get('league_id', 'SC001')
            league = kwargs.get('league', 'K리그1')

            # 리그별 파서 선택
            parser_class = self.league_parsers.get(league_id, KLeagueParser)
            parser = parser_class(html, self.team_mappings)

            # 프로필 파싱
            profile = self._parse_team_profile(html, team_name)

            # 시즌 통계 파싱
            season_stats = parser.parse_season_summary()

            # 최근 경기 파싱
            recent_games = parser.parse_recent_games()

            # 선수 명단 파싱 - 현재 비활성화됨 (팀 통계에 집중)
            players = []

            # 커리어 통계 파싱 (축구는 시즌 통계와 동일)
            career_stats = season_stats.copy()

            return SoccerData(
                team_name=team_name,
                league=league,
                league_id=league_id,
                profile=profile,
                season_stats=season_stats,
                recent_games=recent_games,
                career_stats=career_stats,
                players=players
            )

        except Exception as e:
            logger.error(f"축구 팀 데이터 파싱 실패 {team_name}: {e}")
            return SoccerData(
                team_name=team_name,
                league=kwargs.get('league', ''),
                league_id=kwargs.get('league_id', ''),
                profile={},
                season_stats={},
                recent_games=[],
                career_stats={},
                players=[]
            )
    
    def parse_team_players(self) -> List[Dict[str, str]]:
        """팀 선수 명단 파싱 - 실제 HTML 구조 기반"""
        try:
            players = []

            # 선수명단 섹션 찾기
            player_sections = self.soup.find_all('div', class_=lambda x: x and any(pos in str(x) for pos in ['GK', 'DF', 'MF', 'FW']))

            if not player_sections:
                # 대안: 선수명단이 포함된 리스트 아이템 찾기
                player_list = self.soup.find('li', string=lambda text: text and '선수명단' in text)
                if player_list:
                    player_sections = player_list.find_all_next('div')[:4]  # GK, DF, MF, FW

            for section in player_sections:
                try:
                    # 포지션 추출
                    position_elem = section.find(string=lambda text: text and text.strip() in ['GK', 'DF', 'MF', 'FW'])
                    position = position_elem.strip() if position_elem else ""

                    # 선수 링크들 찾기
                    player_links = section.find_all('a', href=lambda x: x and 'scPlayerDetail.do' in x)

                    for link in player_links:
                        try:
                            player_name = link.get_text(strip=True)
                            player_url = link.get('href', '')

                            # URL에서 선수 ID 추출
                            player_id = self._extract_player_id_from_url(player_url)

                            if player_name and player_id:
                                players.append({
                                    'player_name': player_name,
                                    'position': position,
                                    'player_id': player_id,
                                    'player_url': player_url,
                                    'league_code': self.league_code
                                })

                        except Exception as e:
                            logger.debug(f"선수 정보 추출 실패: {e}")
                            continue

                except Exception as e:
                    logger.debug(f"포지션 섹션 파싱 실패: {e}")
                    continue

            return players

        except Exception as e:
            logger.debug(f"선수 명단 파싱 실패: {e}")
            return []

    def _extract_player_id_from_url(self, url: str) -> Optional[str]:
        """URL에서 선수 ID 추출"""
        try:
            import re
            match = re.search(r'playerId=([^&]+)', url)
            return match.group(1) if match else None
        except:
            return None

    def _parse_team_profile(self, html: str, team_name: str) -> Dict[str, Any]:
        """팀 프로필 파싱 – 감독·구장·시즌 성적만 추출"""
        try:
            soup = BeautifulSoup(html, "lxml")

            profile: Dict[str, str] = {
                "team_name": team_name,
                "manager": "",
                "stadium": "",
                "season_record": "",
            }

            # ul.list 내부 li 항목 순회
            info_ul = soup.select_one(".infoBox ul.list")
            if info_ul:
                for li in info_ul.find_all("li"):
                    label = li.select_one("strong.lb")
                    if not label:
                        continue

                    label_text = label.get_text(strip=True)
                    value_span = li.find("span")

                    if not value_span:
                        continue

                    value_text = value_span.get_text(strip=True)

                    if label_text == "감독":
                        profile["manager"] = value_text
                    elif label_text == "구장":
                        profile["stadium"] = value_text
                    elif label_text == "시즌성적":
                        profile["season_record"] = value_text

            return profile

        except Exception as exc:  # noqa: BLE001
            logger.debug("팀 프로필 파싱 실패 %s: %s", team_name, exc)
            return {"team_name": team_name}

    def _parse_team_players_from_html(self, html: str, league_id: str) -> List[Dict]:
        """HTML에서 팀 선수 목록 파싱"""
        try:
            soup = BeautifulSoup(html, 'lxml')
            players = []
            
            # 선수명단 섹션 찾기
            player_sections = soup.find_all('div', class_=lambda x: x and any(pos in str(x) for pos in ['GK', 'DF', 'MF', 'FW']))
            
            if not player_sections:
                # 대안: 선수명단이 포함된 리스트 아이템 찾기
                player_list = soup.find('li', string=lambda text: text and '선수명단' in text)
                if player_list:
                    player_sections = player_list.find_all_next('div')[:4]  # GK, DF, MF, FW
            
            for section in player_sections:
                try:
                    # 포지션 추출
                    position_elem = section.find(string=lambda text: text and text.strip() in ['GK', 'DF', 'MF', 'FW'])
                    position = position_elem.strip() if position_elem else ""
                    
                    # 선수 링크들 찾기
                    player_links = section.find_all('a', href=lambda x: x and 'scPlayerDetail.do' in x)
                    
                    for link in player_links:
                        try:
                            player_name = link.get_text(strip=True)
                            player_url = link.get('href', '')
                            
                            # URL에서 선수 ID 추출
                            player_id = self._extract_player_id_from_url(player_url)
                            
                            if player_name and player_id:
                                players.append({
                                    'player_name': player_name,
                                    'position': position,
                                    'player_id': player_id,
                                    'player_url': player_url,
                                    'league_id': league_id
                                })
                                
                        except Exception as e:
                            logger.debug(f"선수 정보 추출 실패: {e}")
                            continue
                            
                except Exception as e:
                    logger.debug(f"포지션 섹션 파싱 실패: {e}")
                    continue
            
            return players
            
        except Exception as e:
            logger.error(f"선수 명단 파싱 실패: {e}")
            return []


class SoccerPlayerParser:
    """축구 선수 개별 파서"""

    def __init__(self, html: str):
        self.soup = BeautifulSoup(html, "lxml")

    def parse_player_data(self, player_name: str, **kwargs) -> SoccerPlayerData:
        """선수 데이터 파싱"""
        try:
            league_id = kwargs.get('league_id', 'SC001')
            league = kwargs.get('league', 'K리그1')
            team_name = kwargs.get('team_name', '')
            position = kwargs.get('position', '')

            # 프로필 파싱
            profile = self._parse_player_profile(player_name)

            # 시즌 통계 파싱
            season_stats = self._parse_player_season_stats()

            # 커리어 통계 파싱
            career_stats = self._parse_player_career_stats()

            return SoccerPlayerData(
                player_name=player_name,
                team_name=team_name,
                league=league,
                league_id=league_id,
                position=position,
                profile=profile,
                season_stats=season_stats,
                career_stats=career_stats
            )

        except Exception as e:
            logger.error(f"축구 선수 데이터 파싱 실패 {player_name}: {e}")
            return SoccerPlayerData(
                player_name=player_name,
                team_name=kwargs.get('team_name', ''),
                league=kwargs.get('league', ''),
                league_id=kwargs.get('league_id', ''),
                position=kwargs.get('position', ''),
                profile={},
                season_stats={},
                career_stats={}
            )

    def _parse_player_profile(self, player_name: str) -> Dict[str, Any]:
        """선수 프로필 파싱"""
        try:
            profile = {
                'player_name': player_name,
                'birth_date': '',
                'height': '',
                'weight': '',
                'nationality': '',
                'position': '',
                'back_number': '',
                'career': []
            }

            # 선수 정보 리스트에서 추출
            info_list = self.soup.find('ul')
            if info_list:
                items = info_list.find_all('li')

                for item in items:
                    text = item.get_text(strip=True)

                    if '생년월일' in text:
                        profile['birth_date'] = text.replace('생년월일', '').strip()
                    elif '신장' in text:
                        profile['height'] = text.replace('신장', '').strip()
                    elif '체중' in text:
                        profile['weight'] = text.replace('체중', '').strip()
                    elif '국적' in text:
                        profile['nationality'] = text.replace('국적', '').strip()
                    elif '포지션' in text:
                        profile['position'] = text.replace('포지션', '').strip()
                    elif '등번호' in text:
                        profile['back_number'] = text.replace('등번호', '').strip()

            return profile

        except Exception as e:
            logger.debug(f"선수 프로필 파싱 실패 {player_name}: {e}")
            return {'player_name': player_name}

    def _parse_player_season_stats(self) -> Dict[str, Any]:
        """선수 시즌 통계 파싱"""
        try:
            stats = {}

            # 시즌 통계 테이블 찾기
            tables = self.soup.find_all('table')

            for table in tables:
                caption = table.find('caption')
                if caption and '시즌' in caption.get_text():
                    rows = table.find_all('tr')
                    if len(rows) >= 2:
                        # 헤더와 데이터 행
                        header_row = rows[0].find_all(['th', 'td'])
                        data_row = rows[1].find_all(['th', 'td'])

                        if len(header_row) == len(data_row):
                            for i, header in enumerate(header_row):
                                header_text = header.get_text(strip=True)
                                data_text = data_row[i].get_text(strip=True)
                                if header_text and data_text:
                                    stats[header_text] = data_text

            return stats

        except Exception as e:
            logger.debug(f"선수 시즌 통계 파싱 실패: {e}")
            return {}

    def _parse_player_career_stats(self) -> Dict[str, Any]:
        """선수 커리어 통계 파싱"""
        try:
            stats = {}

            # 커리어 통계 테이블 찾기
            tables = self.soup.find_all('table')

            for table in tables:
                caption = table.find('caption')
                if caption and ('통산' in caption.get_text() or '커리어' in caption.get_text()):
                    rows = table.find_all('tr')
                    if len(rows) >= 2:
                        # 헤더와 데이터 행
                        header_row = rows[0].find_all(['th', 'td'])

                        # 여러 시즌 데이터가 있을 수 있음
                        career_data = []
                        for row in rows[1:]:
                            data_row = row.find_all(['th', 'td'])
                            if len(data_row) == len(header_row):
                                season_data = {}
                                for i, header in enumerate(header_row):
                                    header_text = header.get_text(strip=True)
                                    data_text = data_row[i].get_text(strip=True)
                                    if header_text and data_text:
                                        season_data[header_text] = data_text
                                if season_data:
                                    career_data.append(season_data)

                        stats['career_seasons'] = career_data

            return stats

        except Exception as e:
            logger.debug(f"선수 커리어 통계 파싱 실패: {e}")
            return {}

    def _parse_team_profile(self, html: str, team_name: str) -> Dict[str, Any]:
        """팀 프로필 파싱 - 실제 HTML 구조 기반"""
        try:
            soup = BeautifulSoup(html, 'lxml')

            profile = {
                'team_name': team_name,
                'manager': '',
                'stadium': '',
                'website': '',
                'season_rank': '',
                'season_record': '',
                'last_updated': ''
            }

            # 팀 정보 리스트에서 추출
            info_list = soup.find('ul')
            if info_list:
                items = info_list.find_all('li')

                for item in items:
                    text = item.get_text(strip=True)

                    if '감독' in text:
                        profile['manager'] = text.replace('감독', '').strip()
                    elif '구장' in text:
                        profile['stadium'] = text.replace('구장', '').strip()
                    elif '공식홈페이지' in text:
                        link = item.find('a')
                        if link:
                            profile['website'] = link.get('href', '')
                    elif '시즌성적' in text:
                        profile['season_record'] = text.replace('시즌성적', '').strip()

            # 순위 정보 추출
            rank_elem = soup.find('strong', string=lambda text: text and '위' in text)
            if rank_elem:
                profile['season_rank'] = rank_elem.get_text(strip=True)

            return profile

        except Exception as e:
            logger.debug(f"팀 프로필 파싱 실패 {team_name}: {e}")
            return {'team_name': team_name}


class SoccerStatsParser:
    """축구 팀 통계 파서 - 야구 방식 적용"""
    
    def __init__(self, soup, team_name: str):
        self.soup = soup
        self.team_name = team_name
        
    def parse_season_summary(self) -> Dict[str, Optional[Dict[str, Any]]]:
        """시즌 요약 정보 파싱 - 야구 방식 적용 (홈/원정/전체)"""
        season_stats: Dict[str, Optional[Dict[str, Any]]] = {
            "home": None, "away": None, "total": None
        }
        
        try:
            # 홈/원정 데이터: tbody id="homeAway_record"
            homeaway_tbody = self.soup.find("tbody", id="homeAway_record")
            if homeaway_tbody:
                rows = homeaway_tbody.find_all('tr')
                for i, row in enumerate(rows):
                    cells = row.find_all(['td', 'th'])
                    
                    # 첫 번째 행: rowspan="2"가 있어서 홈 데이터
                    if i == 0 and len(cells) >= 8:
                        location_text = cells[1].get_text(strip=True) if len(cells) > 1 else ""
                        if location_text == '홈':
                            season_stats["home"] = self._extract_season_summary_data(cells)
                    
                    # 두 번째 행: rowspan 때문에 첫 번째 셀이 없어서 인덱스 -1
                    elif i == 1 and len(cells) >= 7:
                        location_text = cells[0].get_text(strip=True) if len(cells) > 0 else ""
                        if location_text == '원정':
                            # 원정 데이터는 rowspan으로 인해 인덱스가 -1
                            away_cells = [None] + cells  # 가상의 첫 번째 셀 추가
                            season_stats["away"] = self._extract_season_summary_data(away_cells)
            
            # 전체 데이터: tfoot id="total_record"  
            total_tfoot = self.soup.find("tfoot", id="total_record")
            if total_tfoot:
                rows = total_tfoot.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 7:  # colspan="2"로 인해 셀 수가 적음
                        location_text = cells[0].get_text(strip=True) if len(cells) > 0 else ""
                        
                        if '전체' in location_text:
                            # 전체 데이터는 colspan="2"로 인해 실제 데이터가 1부터 시작
                            # 가상의 두 번째 셀을 추가해서 인덱스 맞추기
                            total_cells = [cells[0], None] + cells[1:]
                            season_stats["total"] = self._extract_season_summary_data(total_cells)
                
        except Exception as e:
            logger.debug(f"❌ [{self.team_name}] 시즌 요약 파싱 실패: {e}")
             
        return season_stats
    
    def parse_season_stats(self) -> Dict[str, Any]:
        """역대 시즌 성적 파싱"""
        stats = {}
        
        try:
            # 역대 시즌 성적 테이블 찾기
            tables = self.soup.find_all('table')
            historical_table = None
            
            for table in tables:
                caption = table.find('caption')
                if caption and '역대' in caption.get_text():
                    historical_table = table
                    break
            
            if historical_table:
                stats = self._parse_historical_season_table(historical_table)
                
        except Exception as e:
            logger.debug(f"❌ [{self.team_name}] 역대 시즌 성적 파싱 실패: {e}")
             
        return stats
    
    def parse_recent_games(self) -> Dict[str, Dict[str, Dict[str, str]]]:
        """최근 경기 성적 파싱 - 홈/원정 구분"""
        result = {
            "recent_5_home_games": {},
            "recent_5_away_games": {}
        }
        
        try:
            # seasonLatestTeamRecord tbody 찾기
            tbody = self.soup.find("tbody", id="seasonLatestTeamRecord")
            if not isinstance(tbody, BeautifulSoup) and tbody is not None:
                rows = tbody.find_all("tr")
                if rows:
                    # 모든 경기 파싱
                    all_games = self._parse_match_rows(rows)
                    
                    # 홈/원정 구분하여 분류
                    for date, game_data in all_games.items():
                        home_data = game_data.get('home', {})
                        
                        def _norm(txt: str) -> str:
                            return txt.replace(' ', '').lower()

                        home_team = home_data.get('home_team_name', '')

                        # 팀명 정규화 후 포함 여부 확인
                        if (
                            _norm(self.team_name) in _norm(home_team)
                            or _norm(home_team) in _norm(self.team_name)
                        ):
                            result["recent_5_home_games"][date] = game_data
                        else:
                            # 아니면 원정 경기
                            result["recent_5_away_games"][date] = game_data
                
        except Exception as e:
            logger.debug(f"❌ [{self.team_name}] 최근 경기 파싱 실패: {e}")
            
        return result
    
    def parse_recent_games_summary(self) -> Dict[str, Any]:
        """최근 경기 요약 파싱 - 야구 방식 (홈/원정 모두 반환)"""
        summary = {}
        
        try:
            # 직접 tfoot id로 찾기 (더 안정적)
            tfoot = self.soup.find('tfoot', id='seasonLatestTeamRecordTotal')
            if tfoot:
                rows = tfoot.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 8:
                        # 홈 행: cells[1] = '홈', 원정 행: cells[0] = '원정'
                        if len(cells) == 17:  # K리그 홈 행 (rowspan 포함)
                            location = cells[1].get_text(strip=True)
                            if location == '홈':
                                record = cells[2].get_text(strip=True)
                                # K리그 홈 행이 유효한 경우에만 처리
                                if self._is_valid_record(record):
                                    summary['home'] = self._extract_summary_data(cells)
                        elif len(cells) == 16:  # K리그 원정 행 (rowspan 없음)
                            location = cells[0].get_text(strip=True)
                            if location == '원정':
                                record = cells[1].get_text(strip=True)
                                # K리그 원정 행이 유효한 경우에만 처리
                                if self._is_valid_record(record):
                                    # 원정 행은 인덱스가 하나씩 앞당겨짐
                                    summary['away'] = self._extract_summary_data_away(cells)
                        elif len(cells) == 12:  # J1 리그 홈 행 (rowspan 포함)
                            location = cells[1].get_text(strip=True)
                            if location == '홈':
                                record = cells[2].get_text(strip=True)
                                # J1 홈 행이 유효한 경우에만 처리
                                if self._is_valid_record(record):
                                    summary['home'] = self._extract_j1_summary_data(cells, is_home=True)
                        elif len(cells) == 11:  # J1 리그 원정 행 (rowspan 없음)
                            location = cells[0].get_text(strip=True)
                            if location == '원정':
                                record = cells[1].get_text(strip=True)
                                # J1 원정 행이 유효한 경우에만 처리
                                if self._is_valid_record(record):
                                    summary['away'] = self._extract_j1_summary_data(cells, is_home=False)
                
        except Exception as e:
            logger.debug(f"❌ [{self.team_name}] 최근 경기 요약 파싱 실패: {e}")
            
        return summary
    
    def _parse_season_table(self, table) -> Dict[str, Any]:
        """시즌 테이블 파싱"""
        data = {}
        
        try:
            rows = table.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 8:  # 최소 필요한 컬럼 수
                    
                    # 홈/원정/전체 구분
                    location = cells[1].get_text(strip=True) if len(cells) > 1 else ""
                    
                    if location in ['홈', '원정', '전체']:
                        key = {'홈': 'home', '원정': 'away', '전체': 'total'}.get(location, 'total')
                        
                        data[key] = {
                            'matches_played': self._safe_int(cells[2].get_text(strip=True)) if len(cells) > 2 else 0,
                            'wins': self._safe_int(cells[3].get_text(strip=True)) if len(cells) > 3 else 0,
                            'draws': self._safe_int(cells[4].get_text(strip=True)) if len(cells) > 4 else 0,
                            'losses': self._safe_int(cells[5].get_text(strip=True)) if len(cells) > 5 else 0,
                            'goals_scored': self._safe_int(cells[6].get_text(strip=True)) if len(cells) > 6 else 0,
                            'goals_conceded': self._safe_int(cells[7].get_text(strip=True)) if len(cells) > 7 else 0,
                            'goal_difference': self._safe_int(cells[8].get_text(strip=True)) if len(cells) > 8 else 0,
                            'points': self._safe_int(cells[9].get_text(strip=True)) if len(cells) > 9 else 0
                        }
                        
                        # 승률 계산
                        total_games = data[key]['matches_played']
                        if total_games > 0:
                            data[key]['win_rate'] = round(data[key]['wins'] / total_games, 3)
                        else:
                            data[key]['win_rate'] = 0.0
                            
        except Exception as e:
            logger.debug(f"시즌 테이블 파싱 실패: {e}")
            
        return data
    
    def _parse_historical_season_table(self, table) -> Dict[str, Any]:
        """역대 시즌 성적 테이블 파싱 - 최근 1개 시즌만 (야구와 동일)"""
        year_based_seasons = {}
        
        try:
            tbody = table.find('tbody')
            if tbody:
                rows = tbody.find_all('tr')
                
                # 🚀 야구와 동일: 가장 최근 1개 시즌만 수집
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 17:  # 완전한 17개 셀 필요
                        season_data = {
                            # 기본 통계 (8개)
                            'season': cells[0].get_text(strip=True),
                            'rank': self._safe_int(cells[1].get_text(strip=True)),
                            'matches_played': self._safe_int(cells[2].get_text(strip=True)),
                            'wins': self._safe_int(cells[3].get_text(strip=True)),
                            'draws': self._safe_int(cells[4].get_text(strip=True)),
                            'losses': self._safe_int(cells[5].get_text(strip=True)),
                            'points': self._safe_int(cells[6].get_text(strip=True)),
                            'win_rate': self._safe_float(cells[7].get_text(strip=True)),
                            
                            # 공격 통계 (2개)
                            'goals_scored': self._safe_int(cells[8].get_text(strip=True)),
                            'goals_scored_avg': self._safe_float(cells[9].get_text(strip=True)),
                            
                            # 수비 통계 (3개)
                            'goals_conceded': self._safe_int(cells[10].get_text(strip=True)),
                            'goals_conceded_avg': self._safe_float(cells[11].get_text(strip=True)),
                            'goal_difference': self._safe_int(cells[12].get_text(strip=True)),
                            
                            # 기타 통계 (4개)
                            'assists': self._safe_int(cells[13].get_text(strip=True)),
                            'fouls': self._safe_int(cells[14].get_text(strip=True)),
                            'yellow_cards': self._safe_int(cells[15].get_text(strip=True)),
                            'red_cards': self._safe_int(cells[16].get_text(strip=True))
                        }
                        
                        # 첫 번째 시즌만 저장하고 중단
                        year = season_data['season']
                        del season_data['season']  # 중복 제거
                        year_based_seasons[year] = season_data
                        break  # 최근 1개 시즌만 수집
                        
                    elif len(cells) >= 8:  # 기본 8개 필드만 있는 경우 (하위 호환성)
                        season_data = {
                            'season': cells[0].get_text(strip=True),
                            'rank': self._safe_int(cells[1].get_text(strip=True)),
                            'matches_played': self._safe_int(cells[2].get_text(strip=True)),
                            'wins': self._safe_int(cells[3].get_text(strip=True)),
                            'draws': self._safe_int(cells[4].get_text(strip=True)),
                            'losses': self._safe_int(cells[5].get_text(strip=True)),
                            'points': self._safe_int(cells[6].get_text(strip=True)),
                            'win_rate': self._safe_float(cells[7].get_text(strip=True)),
                            # 누락된 필드는 기본값
                            'goals_scored': 0,
                            'goals_scored_avg': 0.0,
                            'goals_conceded': 0,
                            'goals_conceded_avg': 0.0,
                            'goal_difference': 0,
                            'assists': 0,
                            'fouls': 0,
                            'yellow_cards': 0,
                            'red_cards': 0
                        }
                        
                        # 첫 번째 시즌만 저장하고 중단
                        year = season_data['season']
                        del season_data['season']  # 중복 제거
                        year_based_seasons[year] = season_data
                        break  # 최근 1개 시즌만 수집
                        
        except Exception as e:
            logger.debug(f"역대 시즌 성적 테이블 파싱 실패: {e}")
            
        return year_based_seasons
    
    def _parse_recent_games_table(self, table, category: str) -> List[Dict[str, Any]]:
        """최근 경기 테이블 파싱 - 야구 방식"""
        games = []
        
        try:
            tbody = table.find('tbody')
            if not tbody:
                return games
                
            rows = tbody.find_all('tr')
            
            # 2행씩 묶어서 처리 (홈팀 행 + 어웨이팀 행)
            i = 0
            while i < len(rows) - 1:
                try:
                    home_row = rows[i]
                    away_row = rows[i + 1]
                    
                    home_cells = home_row.find_all(['td', 'th'])
                    away_cells = away_row.find_all(['td', 'th'])
                    
                    if len(home_cells) >= 6 and len(away_cells) >= 6:
                        
                        # 경기 날짜 (홈팀 행에서)
                        match_date = home_cells[0].get_text(strip=True) if home_cells else ''
                        
                        # 홈팀 데이터
                        home_venue = home_cells[1].get_text(strip=True) if len(home_cells) > 1 else ''
                        
                        # 현재 팀의 홈/원정 여부 확인하여 해당 데이터만 추출
                        if category == 'home' and home_venue == '홈':
                            game_data = self._extract_game_data(home_cells, 'home', match_date)
                            if game_data:
                                games.append(game_data)
                        elif category == 'away' and home_venue == '원정':
                            game_data = self._extract_game_data(away_cells, 'away', match_date)
                            if game_data:
                                games.append(game_data)
                        elif category == 'total':
                            # 전체인 경우 둘 다 추가
                            if home_venue == '홈':
                                game_data = self._extract_game_data(home_cells, 'home', match_date)
                                if game_data:
                                    games.append(game_data)
                            else:
                                game_data = self._extract_game_data(away_cells, 'away', match_date)
                                if game_data:
                                    games.append(game_data)
                
                except Exception as e:
                    logger.debug(f"개별 경기 파싱 실패: {e}")
                
                i += 2
                
        except Exception as e:
            logger.debug(f"최근 경기 테이블 파싱 실패: {e}")
            
        return games[:5]  # 최근 5경기만
    
    def _extract_game_data(self, cells, venue: str, match_date: str) -> Optional[Dict[str, Any]]:
        """개별 경기 데이터 추출"""
        try:
            return {
                'date': match_date,
                'venue': venue,
                'opponent': cells[2].get_text(strip=True) if len(cells) > 2 else '',
                'result': cells[3].get_text(strip=True) if len(cells) > 3 else '',
                'goals_scored': self._safe_int(cells[4].get_text(strip=True)) if len(cells) > 4 else 0,
                'goals_conceded': self._safe_int(cells[5].get_text(strip=True)) if len(cells) > 5 else 0,
                'assists': self._safe_int(cells[7].get_text(strip=True)) if len(cells) > 7 else 0,
                'corners': self._safe_int(cells[8].get_text(strip=True)) if len(cells) > 8 else 0,
                'yellow_cards': self._safe_int(cells[12].get_text(strip=True)) if len(cells) > 12 else 0,
                'red_cards': self._safe_int(cells[13].get_text(strip=True)) if len(cells) > 13 else 0,
                'clean_sheet': 1 if self._safe_int(cells[5].get_text(strip=True)) == 0 else 0 if len(cells) > 5 else 0
            }
        except Exception as e:
            logger.debug(f"경기 데이터 추출 실패: {e}")
            return None
    
    def _parse_recent_summary(self, table, category: str) -> Dict[str, Any]:
        """최근 경기 요약 파싱"""
        summary = {}
        
        try:
            tfoot = table.find('tfoot')
            if not tfoot:
                return summary
                
            rows = tfoot.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 8:
                    
                    location = cells[1].get_text(strip=True) if len(cells) > 1 else ""
                    
                    if category == 'total':
                        # 전체인 경우 홈/원정 둘 다 포함
                        if location in ['홈', '원정']:
                            key = {'홈': 'home', '원정': 'away'}[location]
                            summary[key] = self._extract_summary_data(cells)
                    elif category == 'home' and location == '홈':
                        summary = self._extract_summary_data(cells)
                    elif category == 'away' and location == '원정':
                        summary = self._extract_summary_data(cells)
                        
        except Exception as e:
            logger.debug(f"최근 경기 요약 파싱 실패: {e}")
            
        return summary
    
    def _extract_summary_data(self, cells) -> Dict[str, Any]:
        """요약 데이터 추출 - 정확한 컬럼 매핑 (홈 행용)"""
        try:
            return {
                'record': cells[2].get_text(strip=True) if len(cells) > 2 else '',
                'avg_goals_scored': self._safe_float(cells[3].get_text(strip=True)) if len(cells) > 3 else 0.0,
                'avg_goals_conceded': self._safe_float(cells[8].get_text(strip=True)) if len(cells) > 8 else 0.0,
                'avg_assists': self._safe_float(cells[10].get_text(strip=True)) if len(cells) > 10 else 0.0,
                'avg_corners': self._safe_float(cells[11].get_text(strip=True)) if len(cells) > 11 else 0.0,
                'avg_yellow_cards': self._safe_float(cells[15].get_text(strip=True)) if len(cells) > 15 else 0.0,
                'avg_red_cards': self._safe_float(cells[16].get_text(strip=True)) if len(cells) > 16 else 0.0
            }
        except Exception as e:
            logger.debug(f"요약 데이터 추출 실패: {e}")
            return {}

    def _extract_summary_data_away(self, cells) -> Dict[str, Any]:
        """요약 데이터 추출 - 원정 행용 (인덱스 -1)"""
        try:
            return {
                'record': cells[1].get_text(strip=True) if len(cells) > 1 else '',
                'avg_goals_scored': self._safe_float(cells[2].get_text(strip=True)) if len(cells) > 2 else 0.0,
                'avg_goals_conceded': self._safe_float(cells[7].get_text(strip=True)) if len(cells) > 7 else 0.0,
                'avg_assists': self._safe_float(cells[9].get_text(strip=True)) if len(cells) > 9 else 0.0,
                'avg_corners': self._safe_float(cells[10].get_text(strip=True)) if len(cells) > 10 else 0.0,
                'avg_yellow_cards': self._safe_float(cells[14].get_text(strip=True)) if len(cells) > 14 else 0.0,
                'avg_red_cards': self._safe_float(cells[15].get_text(strip=True)) if len(cells) > 15 else 0.0
            }
        except Exception as e:
            logger.debug(f"원정 요약 데이터 추출 실패: {e}")
            return {}
    
    def _extract_j1_summary_data(self, cells, is_home: bool = False) -> Dict[str, Any]:
        """J1 리그 요약 데이터 추출"""
        try:
            if is_home:
                # J1 홈 행 (12 cells, colspan="2" 고려)
                return {
                    'record': cells[2].get_text(strip=True) if len(cells) > 2 else '',
                    'avg_goals_scored': self._safe_float(cells[3].get_text(strip=True)) if len(cells) > 3 else 0.0,
                    'avg_goals_conceded': self._safe_float(cells[8].get_text(strip=True)) if len(cells) > 8 else 0.0,
                    'avg_assists': self._safe_float(cells[10].get_text(strip=True)) if len(cells) > 10 else 0.0,
                    'avg_corners': self._safe_float(cells[11].get_text(strip=True)) if len(cells) > 11 else 0.0,
                    'avg_yellow_cards': 0.0,  # J1에서 제공하지 않음
                    'avg_red_cards': 0.0
                }
            else:
                # J1 원정 행 (11 cells, colspan="2" 고려)
                return {
                    'record': cells[1].get_text(strip=True) if len(cells) > 1 else '',
                    'avg_goals_scored': self._safe_float(cells[2].get_text(strip=True)) if len(cells) > 2 else 0.0,
                    'avg_goals_conceded': self._safe_float(cells[7].get_text(strip=True)) if len(cells) > 7 else 0.0,
                    'avg_assists': self._safe_float(cells[9].get_text(strip=True)) if len(cells) > 9 else 0.0,
                    'avg_corners': self._safe_float(cells[10].get_text(strip=True)) if len(cells) > 10 else 0.0,
                    'avg_yellow_cards': 0.0,  # J1에서 제공하지 않음
                    'avg_red_cards': 0.0
                }
        except Exception as e:
            logger.debug(f"J1 요약 데이터 추출 실패: {e}")
            return {}
    
    def _safe_int(self, text: str) -> int:
        """안전한 정수 변환 (마이너스 부호 허용)"""
        try:
            if not text:
                return 0
            # 텍스트 정리
            text = str(text).strip()
            # 마이너스 부호 확인
            is_negative = text.startswith('-')
            # 숫자만 추출
            import re
            cleaned = re.sub(r'[^\d]', '', text)
            if not cleaned:
                return 0
            # 마이너스 부호 적용
            result = int(cleaned)
            return -result if is_negative else result
        except:
            return 0
    
    def _safe_float(self, text: str) -> float:
        """안전한 실수 변환"""
        try:
            if not text:
                return 0.0
            # 숫자와 소수점만 남기기
            import re
            cleaned = re.sub(r'[^\d.]', '', str(text).strip())
            return float(cleaned) if cleaned else 0.0
        except:
            return 0.0

    def _safe_percentage(self, text: str) -> float:
        """안전한 퍼센트 값 변환"""
        try:
            if not text:
                return 0.0
            # % 기호 제거 후 숫자와 소수점만 남기기
            import re
            cleaned = re.sub(r'[^\d.]', '', str(text).strip())
            return float(cleaned) if cleaned else 0.0
        except:
            return 0.0
    
    def _is_valid_record(self, record: str) -> bool:
        """record 값이 유효한지 확인 (-, 빈 문자열, 0승0무0패 등 무효 데이터 필터링)"""
        if not record or record.strip() == "-":
            return False
        
        # 0승0무0패 같은 경우도 무효 데이터로 판단
        if record.strip() == "0승0무0패":
            return False
            
        # 빈 문자열이나 의미 없는 값들
        if record.strip() in ["", "N/A", "없음", "---"]:
            return False
            
        return True

    def _extract_season_summary_data(self, cells) -> Dict[str, Any]:
        """시즌 요약 데이터 추출 (전체 축구 통계 포함)"""
        try:
            # None 셀 필터링 (가상 셀 제거)
            real_cells = [cell for cell in cells if cell is not None]
            
            # 전체 데이터: 첫 번째 셀이 "전체"
            # 홈/원정: 두 번째 셀이 "홈"/"원정"
            location_text = ""
            if len(real_cells) > 0:
                location_text = real_cells[0].get_text(strip=True)
            if not location_text and len(real_cells) > 1:
                location_text = real_cells[1].get_text(strip=True)
                
            # 데이터 구조에 따른 인덱스 시작점 조정
            is_home = False
            if '전체' in location_text:
                # 전체: 셀 0="전체", 셀 1=경기수, 셀 2=승, ...
                start_idx = 1
            elif '원정' in location_text:
                # 원정: 셀 0="원정", 셀 1=경기수, 셀 2=승, ...  
                start_idx = 1
            else:
                # 홈: 셀 0=순위, 셀 1="홈", 셀 2=경기수, 셀 3=승, ...
                # 수정: start_idx=2 (셀 2=경기수부터 시작)
                start_idx = 2
                is_home = True
            
            # 전체 축구 통계 데이터 매핑
            matches_played = self._safe_int(real_cells[start_idx].get_text(strip=True)) if len(real_cells) > start_idx else 0
            wins = self._safe_int(real_cells[start_idx+1].get_text(strip=True)) if len(real_cells) > start_idx+1 else 0
            draws = self._safe_int(real_cells[start_idx+2].get_text(strip=True)) if len(real_cells) > start_idx+2 else 0
            losses = self._safe_int(real_cells[start_idx+3].get_text(strip=True)) if len(real_cells) > start_idx+3 else 0
            points = self._safe_int(real_cells[start_idx+4].get_text(strip=True)) if len(real_cells) > start_idx+4 else 0
            win_rate = self._safe_float(real_cells[start_idx+5].get_text(strip=True)) if len(real_cells) > start_idx+5 else 0.0
            goals_scored = self._safe_int(real_cells[start_idx+6].get_text(strip=True)) if len(real_cells) > start_idx+6 else 0
            goals_scored_avg = self._safe_float(real_cells[start_idx+7].get_text(strip=True)) if len(real_cells) > start_idx+7 else 0.0
            shooting_efficiency = self._safe_percentage(real_cells[start_idx+8].get_text(strip=True)) if len(real_cells) > start_idx+8 else 0.0
            goals_conceded = self._safe_int(real_cells[start_idx+9].get_text(strip=True)) if len(real_cells) > start_idx+9 else 0
            goals_conceded_avg = self._safe_float(real_cells[start_idx+10].get_text(strip=True)) if len(real_cells) > start_idx+10 else 0.0
            # 득실차는 HTML에서 직접 추출 (계산 아님)
            goal_difference = self._safe_int(real_cells[start_idx+11].get_text(strip=True)) if len(real_cells) > start_idx+11 else 0
            assists = self._safe_int(real_cells[start_idx+12].get_text(strip=True)) if len(real_cells) > start_idx+12 else 0
            goal_kicks = self._safe_int(real_cells[start_idx+13].get_text(strip=True)) if len(real_cells) > start_idx+13 else 0
            corner_kicks = self._safe_int(real_cells[start_idx+14].get_text(strip=True)) if len(real_cells) > start_idx+14 else 0
            penalty_kicks = self._safe_int(real_cells[start_idx+15].get_text(strip=True)) if len(real_cells) > start_idx+15 else 0
            offsides = self._safe_int(real_cells[start_idx+16].get_text(strip=True)) if len(real_cells) > start_idx+16 else 0
            fouls = self._safe_int(real_cells[start_idx+17].get_text(strip=True)) if len(real_cells) > start_idx+17 else 0
            yellow_cards = self._safe_int(real_cells[start_idx+18].get_text(strip=True)) if len(real_cells) > start_idx+18 else 0
            
            # 퇴장 데이터 추출 (홈: 21개 셀, 원정/전체: 20개 셀)
            if is_home:
                # 홈: 마지막 셀(인덱스 20)이 퇴장
                red_cards = self._safe_int(real_cells[20].get_text(strip=True)) if len(real_cells) > 20 else 0
            else:
                # 원정/전체: 마지막 셀(인덱스 19)이 퇴장
                red_cards = self._safe_int(real_cells[19].get_text(strip=True)) if len(real_cells) > 19 else 0
            
            return {
                # 기본 통계
                'matches_played': matches_played,
                'wins': wins,
                'draws': draws,
                'losses': losses,
                'points': points,
                'win_rate': win_rate,
                
                # 공격 통계
                'goals_scored': goals_scored,
                'goals_scored_avg': goals_scored_avg,
                'shooting_efficiency': shooting_efficiency,
                'assists': assists,
                'corner_kicks': corner_kicks,
                'penalty_kicks': penalty_kicks,
                
                # 수비 통계
                'goals_conceded': goals_conceded,
                'goals_conceded_avg': goals_conceded_avg,
                'goal_difference': goal_difference,
                'goal_kicks': goal_kicks,
                'offsides': offsides,
                
                # 경기 운영 통계
                'fouls': fouls,
                'yellow_cards': yellow_cards,
                'red_cards': red_cards
            }
        except Exception as e:
            logger.debug(f"시즌 요약 데이터 추출 실패: {e}")
            return {}

    def _calculate_win_rate(self, cells) -> float:
        """승률 계산"""
        try:
            matches = self._safe_int(cells[2].get_text(strip=True)) if len(cells) > 2 else 0
            wins = self._safe_int(cells[3].get_text(strip=True)) if len(cells) > 3 else 0
            return round(wins / matches, 3) if matches > 0 else 0.0
        except:
            return 0.0

    def _calculate_win_rate_with_offset(self, cells, offset: int) -> float:
        """승률 계산 (오프셋 적용)"""
        try:
            matches = self._safe_int(cells[2+offset].get_text(strip=True)) if len(cells) > 2+offset else 0
            wins = self._safe_int(cells[3+offset].get_text(strip=True)) if len(cells) > 3+offset else 0
            return round(wins / matches, 3) if matches > 0 else 0.0
        except:
            return 0.0

    def _calculate_win_rate_simple(self, cells, start_idx: int) -> float:
        """승률 계산 (시작 인덱스 적용)"""
        try:
            matches = self._safe_int(cells[start_idx].get_text(strip=True)) if len(cells) > start_idx else 0
            wins = self._safe_int(cells[start_idx+1].get_text(strip=True)) if len(cells) > start_idx+1 else 0
            return round(wins / matches, 3) if matches > 0 else 0.0
        except:
            return 0.0

    def _parse_match_rows(self, rows) -> Dict[str, Dict[str, Dict[str, str]]]:
        """경기 행들을 파싱하여 날짜별 홈/원정 데이터 반환"""
        recent_stats = {}
        
        i = 0
        while i < len(rows) - 1:
            try:
                home_row = rows[i]
                away_row = rows[i + 1]
                
                home_cells = home_row.find_all(['td', 'th'])
                away_cells = away_row.find_all(['td', 'th'])
                
                if len(home_cells) >= 6 and len(away_cells) >= 6:
                    # 날짜 추출 및 변환
                    date_text = home_cells[0].get_text(strip=True)
                    try:
                        converted_date = convert_date_str(date_text)
                    except:
                        converted_date = date_text
                    
                    # 홈팀 데이터
                    home_data = self._extract_match_data(home_cells, 'home')
                    # 원정팀 데이터  
                    away_data = self._extract_match_data(away_cells, 'away')
                    
                    recent_stats[converted_date] = {
                        "home": home_data,
                        "away": away_data
                    }
                
            except Exception as e:
                logger.debug(f"경기 행 파싱 실패: {e}")
            
            i += 2
            
        return recent_stats

    def _extract_match_data(self, cells, venue: str) -> Dict[str, str]:
        """개별 경기 데이터 추출 (홈/원정 행 인덱스 차이 고려)"""
        try:
            # 홈 행과 원정 행의 인덱스 차이 고려
            # 홈 행: 날짜 셀 있음 (18개 셀)
            # 원정 행: 날짜 셀 없음 (17개 셀) - 인덱스 1씩 작음
            offset = 0 if venue == 'home' else -1
            
            # 팀 이름 추출 (a 태그에서)
            team_idx = 2 + offset
            team_cell = cells[team_idx] if len(cells) > team_idx else None
            if team_cell:
                a_tag = team_cell.find('a')
                team_name = a_tag.get_text(strip=True) if a_tag else team_cell.get_text(strip=True)
            else:
                team_name = ''
            
            return {
                f"{venue}_team_name": team_name,
                "result": cells[3 + offset].get_text(strip=True) if len(cells) > 3 + offset else '',
                "goals_scored": cells[4 + offset].get_text(strip=True) if len(cells) > 4 + offset else '0',
                "goals_conceded": cells[9 + offset].get_text(strip=True) if len(cells) > 9 + offset else '0',
                "assists": cells[11 + offset].get_text(strip=True) if len(cells) > 11 + offset else '0',
                "corners": cells[12 + offset].get_text(strip=True) if len(cells) > 12 + offset else '0',
                "penalty_kicks": cells[13 + offset].get_text(strip=True) if len(cells) > 13 + offset else '0',
                "yellow_cards": cells[16 + offset].get_text(strip=True) if len(cells) > 16 + offset else '0',
                "red_cards": cells[17 + offset].get_text(strip=True) if len(cells) > 17 + offset else '0'
            }
        except Exception as e:
            logger.debug(f"경기 데이터 추출 실패: {e}")
            return {}
