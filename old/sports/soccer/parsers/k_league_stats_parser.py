"""
K리그 팀 통계 파서
베트맨 사이트의 축구 팀 상세 페이지에서 통계를 추출
"""
from typing import Any, Dict, List

from bs4 import BeautifulSoup, Tag

from utils.helpers import map_result
from utils.logger import Logger

logger = Logger(__name__)


class KLeagueStatsParser:
    """K리그 팀 통계 파서"""
    
    def __init__(self, html: str, team_name: str):
        self.html = html
        self.team_name = team_name
        self.soup = BeautifulSoup(html, 'html.parser')
        
    def parse_season_summary(self) -> Dict[str, Any]:
        """시즌 요약 정보 파싱 (홈/원정/전체 시즌 통계)"""
        summary = {}
        
        try:
            # 홈/원정 통계 (homeAway_record)
            home_away_section = self.soup.find('tbody', id='homeAway_record')
            if home_away_section:
                summary.update(self._parse_home_away_stats(home_away_section))
                
            # 전체 통계 (total_record)
            total_section = self.soup.find('tfoot', id='total_record')
            if total_section:
                summary.update(self._parse_total_stats(total_section))
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 시즌 요약 파싱 실패: {e}")
             
        return summary
         
    def _parse_home_away_stats(self, section: Tag) -> Dict[str, Any]:
        """홈/원정 통계 파싱 (야구 방식 매핑 기반)"""
        stats = {}
        
        try:
            rows = section.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 15:
                    continue
                
                # 홈/원정 매핑 정의 (야구 방식)
                if cells[0].get('rowspan') == '2':
                    # 홈 행 (순위 rowspan + 21개 셀, 파울 없음)
                    location = cells[1].get_text(strip=True)  # "홈"
                    mapping = {
                        'matches_played': 2,
                        'wins': 3,
                        'draws': 4,
                        'losses': 5,
                        'points': 6,
                        'win_rate': 7,
                        'goals_scored': 8,
                        'goals_scored_avg': 9,
                        'shooting_efficiency': 10,
                        'goals_conceded': 11,
                        'goals_conceded_avg': 12,
                        'goal_difference': 13,
                        'assists': 14,
                        'goal_kicks': 15,
                        'corner_kicks': 16,
                        'penalty_kicks': 17,
                        'offsides': 18,
                        'fouls': None,  # 홈 행에는 파울 없음
                        'yellow_cards': 19,  # [19] = 15 (경고)
                        'red_cards': 20   # [20] = 0 (퇴장)
                    }
                else:
                    # 원정 행 (20개 셀: 파울 없이 경고와 퇴장)
                    location = cells[0].get_text(strip=True)  # "원정"
                    mapping = {
                        'matches_played': 1,
                        'wins': 2,
                        'draws': 3,
                        'losses': 4,
                        'points': 5,
                        'win_rate': 6,
                        'goals_scored': 7,
                        'goals_scored_avg': 8,
                        'shooting_efficiency': 9,
                        'goals_conceded': 10,
                        'goals_conceded_avg': 11,
                        'goal_difference': 12,
                        'assists': 13,
                        'goal_kicks': 14,
                        'corner_kicks': 15,
                        'penalty_kicks': 16,
                        'offsides': 17,
                        'fouls': None,  # 원정 행에도 파울 없음
                        'yellow_cards': 18,  # [18] = 19 (경고)
                        'red_cards': 19   # [19] = 1 (퇴장)
                    }
                
                # 매핑 기반 데이터 추출
                location_stats = {}
                for field, index in mapping.items():
                    if index is None or index >= len(cells):
                        location_stats[field] = 0
                    else:
                        cell_text = cells[index].get_text(strip=True)
                        if field == 'shooting_efficiency':
                            location_stats[field] = self._safe_percentage_as_percent(
                                cell_text
                            )
                        elif field in ['win_rate', 'goals_scored_avg', 
                                     'goals_conceded_avg']:
                            location_stats[field] = self._safe_float(cell_text)
                        else:
                            location_stats[field] = self._safe_int(cell_text)
                
                stats[location] = location_stats
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 홈/원정 통계 파싱 실패: {e}")
            
        return stats
    
    def _parse_total_stats(self, section: Tag) -> Dict[str, Any]:
        """전체 통계 파싱 (야구 방식 매핑 기반)"""
        stats = {}
        
        try:
            rows = section.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 15:
                    continue
                
                # 전체 행 매핑 (20개 셀: 파울 없이 경고와 퇴장)
                mapping = {
                    'matches_played': 1,
                    'wins': 2,
                    'draws': 3,
                    'losses': 4,
                    'points': 5,
                    'win_rate': 6,
                    'goals_scored': 7,
                    'goals_scored_avg': 8,
                    'shooting_efficiency': 9,
                    'goals_conceded': 10,
                    'goals_conceded_avg': 11,
                    'goal_difference': 12,
                    'assists': 13,
                    'goal_kicks': 14,
                    'corner_kicks': 15,
                    'penalty_kicks': 16,
                    'offsides': 17,
                    'fouls': None,  # 전체 행에는 파울 없음
                    'yellow_cards': 18,
                    'red_cards': 19
                }
                
                # 매핑 기반 데이터 추출
                total_stats = {}
                for field, index in mapping.items():
                    if index is None or index >= len(cells):
                        total_stats[field] = 0
                    else:
                        cell_text = cells[index].get_text(strip=True)
                        if field == 'shooting_efficiency':
                            total_stats[field] = self._safe_percentage_as_percent(
                                cell_text
                            )
                        elif field in ['win_rate', 'goals_scored_avg', 
                                     'goals_conceded_avg']:
                            total_stats[field] = self._safe_float(cell_text)
                        else:
                            total_stats[field] = self._safe_int(cell_text)
                
                stats['전체'] = total_stats
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 전체 통계 파싱 실패: {e}")
            
        return stats

    def parse_recent_games_summary(self) -> Dict[str, Any]:
        """최근 경기 요약 정보 파싱 - K-League 형식"""
        summary = {}
        
        try:
            # 최근 경기 평균 통계 (seasonLatestTeamRecordTotal)
            total_section = self.soup.find(
                'tfoot', id='seasonLatestTeamRecordTotal'
            )
            if total_section:
                raw_data = self._parse_recent_games_summary_data(total_section)
                
                # K-League 형식으로 변환
                summary = self._convert_to_kleague_format(raw_data)
                
        except Exception as e:
            logger.error(
                f"❌ [{self.team_name}] 최근 경기 요약 파싱 실패: {e}"
            )
             
        return summary
         
    def _parse_recent_games_summary_data(self, total_section: Tag) -> Dict[str, Any]:
        """최근 경기 평균 통계 파싱"""
        summary = {}
        
        try:
            rows = total_section.find_all('tr')
            
            home_section = {'home': None, 'away': None}
            away_section = {'home': None, 'away': None}
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 8:
                    continue
                    
                # 홈/원정 구분
                location = self._safe_get_cell_text(cells, 1) if len(cells) > 1 else ""
                
                # 첫 번째 행 (홈) - rowspan으로 인해 다른 구조
                if len(cells) == 12:  # 홈 행
                    record = self._safe_get_cell_text(cells, 2)  # colspan="2"인 셀
                    
                    # J-League처럼 홈 데이터가 모두 "-"인 경우 스킵
                    if record == "-" or record == "" or all(
                        self._safe_get_cell_text(cells, i) == "-" 
                        for i in [3, 8, 10]
                    ):
                        continue
                    
                    # 홈 행 데이터 매핑 (colspan="2" 고려)
                    home_data = {
                        'record': record,
                        'avg_goals_scored': self._safe_float(self._safe_get_cell_text(cells, 3)),
                        'avg_goals_conceded': self._safe_float(self._safe_get_cell_text(cells, 8)),
                        'avg_assists': self._safe_float(self._safe_get_cell_text(cells, 10)),
                        'avg_corners': 0.0,
                        'avg_red_cards': 0.0,
                        'avg_yellow_cards': 0.0
                    }
                    home_section['home'] = home_data
                    
                elif len(cells) == 11:  # 원정 행
                    record = self._safe_get_cell_text(cells, 1)  # colspan="2"인 셀
                    
                    # 원정 행 데이터 매핑 (colspan="2" 고려)
                    away_data = {
                        'record': record,
                        'avg_goals_scored': self._safe_float(self._safe_get_cell_text(cells, 2)),
                        'avg_goals_conceded': self._safe_float(self._safe_get_cell_text(cells, 7)),
                        'avg_assists': self._safe_float(self._safe_get_cell_text(cells, 9)),
                        'avg_corners': 0.0,
                        'avg_red_cards': 0.0,
                        'avg_yellow_cards': 0.0
                    }
                    away_section['away'] = away_data
            
            # 홈 섹션 추가 (valid data만)
            if home_section['home']:
                summary['recent_5_home_games'] = home_section
            
            # 원정 섹션 추가 (valid data만)
            if away_section['away']:
                summary['recent_5_away_games'] = away_section
                
        except Exception as e:
            logger.error(
                f"❌ [{self.team_name}] 최근 경기 요약 파싱 실패: {e}"
            )
             
        return summary
     
    def _convert_to_kleague_format(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """원시 데이터를 K-League 형식으로 변환"""
        kleague_format = {}
        
        # recent_5_games 구조 생성
        recent_5_games = {}
        
        # 홈 게임 데이터 처리 (last_N_home_games)
        if 'recent_5_home_games' in raw_data:
            home_data = raw_data['recent_5_home_games']
            if 'home' in home_data and self._is_valid_game_data(home_data['home']):
                recent_5_games['last_3_home_games'] = home_data['home']
        
        # 원정 게임 데이터 처리 (last_N_away_games)
        if 'recent_5_away_games' in raw_data:
            away_data = raw_data['recent_5_away_games']
            if 'away' in away_data and self._is_valid_game_data(away_data['away']):
                recent_5_games['last_2_away_games'] = away_data['away']
        
        # recent_5_games가 비어있지 않은 경우에만 추가
        if recent_5_games:
            kleague_format['recent_5_games'] = recent_5_games
        
        # recent_5_away_games 구조 생성 (away 데이터만)
        if 'recent_5_away_games' in raw_data:
            away_section = raw_data['recent_5_away_games']
            if 'away' in away_section and self._is_valid_game_data(away_section['away']):
                kleague_format['recent_5_away_games'] = {
                    'away': away_section['away']
                }
        
        # recent_5_home_games 구조 생성 (home 데이터만)
        if 'recent_5_home_games' in raw_data:
            home_section = raw_data['recent_5_home_games']
            if 'home' in home_section and self._is_valid_game_data(home_section['home']):
                kleague_format['recent_5_home_games'] = {
                    'home': home_section['home']
                }
        
        return kleague_format
     
    def _filter_empty_data(self, summary: Dict[str, Any]) -> Dict[str, Any]:
        """빈 데이터나 무효한 데이터 항목 필터링"""
        filtered = {}
        
        for key, value in summary.items():
            if isinstance(value, dict):
                # 'home' 또는 'away' 키가 있는 경우 확인
                if 'home' in value or 'away' in value:
                    filtered_value = {}
                    
                    # 홈 데이터 확인
                    if 'home' in value:
                        home_data = value['home']
                        if self._is_valid_game_data(home_data):
                            filtered_value['home'] = home_data
                    
                    # 원정 데이터 확인
                    if 'away' in value:
                        away_data = value['away']
                        if self._is_valid_game_data(away_data):
                            filtered_value['away'] = away_data
                    
                    # 유효한 데이터가 있는 경우에만 포함
                    if filtered_value:
                        filtered[key] = filtered_value
                else:
                    # 기존 형식 지원
                    if self._is_valid_game_data(value):
                        filtered[key] = value
            else:
                filtered[key] = value
        
        return filtered
    
    def _is_valid_game_data(self, data: Dict[str, Any]) -> bool:
        """게임 데이터가 유효한지 확인"""
        if not isinstance(data, dict):
            return False
        
        # 기록이 '-' 또는 빈 문자열인 경우 무효
        record = data.get('record', '')
        if record in ['-', '', '0승 0무 0패']:
            return False
        
        # 모든 통계가 0인 경우 무효 (단, 정상적인 0-0 경기는 제외)
        goals_scored = data.get('avg_goals_scored', 0)
        goals_conceded = data.get('avg_goals_conceded', 0)
        assists = data.get('avg_assists', 0)
        
        # 기록이 있으면서 모든 통계가 0인 경우만 무효로 처리
        if record != '-' and goals_scored == 0 and goals_conceded == 0 and assists == 0:
            return False
        
        return True
     
    def parse_recent_games(self) -> Dict[str, Dict]:
        """최근 경기 상세 정보 파싱 (실제 베트맨 사이트 구조)"""
        recent_games = {
            'recent_5_home_games': {},
            'recent_5_away_games': {}
        }
        
        try:
            # 테스트 HTML용 우선 시도 (seasonLatestTeamRecord ID 존재)
            record_section = self.soup.find('tbody', id='seasonLatestTeamRecord')
            if record_section:
                recent_games = self._parse_game_records_baseball_style(record_section)
            else:
                # 실제 베트맨 사이트: 두 번째 테이블이 최근 경기 결과
                tables = self.soup.find_all('table', class_=['tbl', 'tblAuto'])
                if len(tables) >= 2:
                    recent_table = tables[1]  # 두 번째 테이블
                    recent_games = self._parse_betman_recent_games(recent_table)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 최근 경기 파싱 실패: {e}")
            
        return recent_games
    
    def _parse_betman_recent_games(self, recent_table: Tag) -> Dict[str, Dict]:
        """베트맨 사이트 최근 경기 결과 파싱"""
        recent_games = {
            'recent_5_home_games': {},
            'recent_5_away_games': {}
        }
        
        try:
            # 데이터 행들 찾기 (헤더 제외)
            tbody = recent_table.find('tbody')
            if not tbody:
                return recent_games
                
            rows = tbody.find_all('tr')
            home_games = []
            away_games = []
            
            # 베트맨 사이트 컬럼 구조:
            # ['일시', '홈/원정', '팀명', '결과', '득점', '실점', '득실차', 
            #  '도움', '코너킥', '패널티킥', '오프사이드', '파울', '경고', '퇴장', 
            #  '전체', '전반', '후반', '연장', '승부차기']
            
            for row in rows:
                try:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) < 14:  # 최소 필수 컬럼 수
                        continue
                    
                    # 컬럼 데이터 추출
                    date_text = cells[0].get_text(strip=True)
                    location = cells[1].get_text(strip=True)  # 홈/원정
                    team_name_cell = cells[2]
                    team_link = team_name_cell.find('a')
                    team_name = (
                        team_link.get_text(strip=True) 
                        if team_link 
                        else team_name_cell.get_text(strip=True)
                    )
                    
                    result = cells[3].get_text(strip=True)
                    goals_scored = self._safe_int(cells[4].get_text(strip=True))
                    goals_conceded = self._safe_int(cells[5].get_text(strip=True))
                    goal_difference = self._safe_int(cells[6].get_text(strip=True))
                    assists = self._safe_int(cells[7].get_text(strip=True))
                    corners = self._safe_int(cells[8].get_text(strip=True))
                    penalty_kicks = self._safe_int(cells[9].get_text(strip=True))
                    # 10: 오프사이드 (베트맨에만 있음, 우리는 사용 안함)
                    fouls = self._safe_int(cells[11].get_text(strip=True))
                    yellow_cards = self._safe_int(cells[12].get_text(strip=True))
                    red_cards = self._safe_int(cells[13].get_text(strip=True))
                    
                    # 현재팀인지 확인 (부분 매칭)
                    def is_same_team(team1, team2):
                        if not team1 or not team2:
                            return False
                        team1_clean = team1.replace('FC', '').replace('CF', '').strip()
                        team2_clean = team2.replace('FC', '').replace('CF', '').strip()
                        return team1_clean in team2_clean or team2_clean in team1_clean
                    
                    if not is_same_team(team_name, self.team_name):
                        continue  # 현재팀 경기가 아니면 스킵
                    
                    # 결과 변환
                    current_result = map_result(result)
                    opponent_result = {
                        'W': 'L', 'L': 'W', 'D': 'D'
                    }.get(current_result, current_result)
                    
                    # 현재팀 데이터
                    current_team_data = {
                        'result': current_result,
                        'goals_scored': goals_scored,
                        'goals_conceded': goals_conceded,
                        'goal_difference': goal_difference,
                        'assists': assists,
                        'corners': corners,
                        'penalty_kicks': penalty_kicks,
                        'shots': 0,  # 베트맨에 없음
                        'shots_on_target': 0,  # 베트맨에 없음
                        'fouls': fouls,
                        'yellow_cards': yellow_cards,
                        'red_cards': red_cards,
                        'clean_sheet': 1 if goals_conceded == 0 else 0
                    }
                    
                    # 상대팀 데이터 (추정)
                    opponent_data = {
                        'result': opponent_result,
                        'goals_scored': goals_conceded,
                        'goals_conceded': goals_scored,
                        'goal_difference': -goal_difference,
                        'assists': 0,  # 상대팀 정보 없음
                        'corners': 0,
                        'penalty_kicks': 0,
                        'shots': 0,
                        'shots_on_target': 0,
                        'fouls': 0,
                        'yellow_cards': 0,
                        'red_cards': 0,
                        'clean_sheet': 1 if goals_scored == 0 else 0
                    }
                    
                    # 날짜 정리 (2025-01-07 형식으로 변환)
                    import re
                    from datetime import datetime, timedelta
                    date_match = re.search(r'(\d{2})\.(\d{2})', date_text)
                    if date_match:
                        month, day = date_match.groups()
                        # 현재 년도 기준으로 날짜 생성
                        current_year = datetime.now().year
                        date_key = f"{current_year}-{month}-{day}"
                    else:
                        # 날짜 파싱 실패시 임시 날짜
                        base_date = datetime.now() - timedelta(days=len(home_games + away_games))
                        date_key = base_date.strftime('%Y-%m-%d')
                    
                    # 상대팀 이름 찾기 (베트맨에서는 다른 행에 있음)
                    opponent_name = "상대팀"  # 기본값
                    
                    # 같은 경기의 다른 행에서 상대팀 찾기
                    for other_row in rows:
                        try:
                            other_cells = other_row.find_all(['td', 'th'])
                            if len(other_cells) < 3:
                                continue
                                
                            other_date = other_cells[0].get_text(strip=True)
                            other_location = other_cells[1].get_text(strip=True)
                            other_team_cell = other_cells[2]
                            other_team_link = other_team_cell.find('a')
                            other_team_name = (
                                other_team_link.get_text(strip=True)
                                if other_team_link
                                else other_team_cell.get_text(strip=True)
                            )
                            
                            # 같은 날짜이면서 다른 홈/원정인 경우
                            is_same_date = other_date == date_text
                            is_different_location = other_location != location
                            is_not_current_team = not is_same_team(
                                other_team_name, self.team_name
                            )
                            
                            if (is_same_date and 
                                is_different_location and 
                                is_not_current_team):
                                opponent_name = other_team_name
                                break
                        except:
                            continue
                    
                    # 홈/원정 구분하여 저장
                    if location == "홈":
                        # 현재팀이 홈
                        match_data = {
                            'home': dict(current_team_data, **{
                                'home_team_name': self.team_name
                            }),
                            'away': dict(opponent_data, **{
                                'away_team_name': opponent_name
                            })
                        }
                        home_games.append((date_key, match_data))
                    else:
                        # 현재팀이 원정
                        match_data = {
                            'home': dict(opponent_data, **{
                                'home_team_name': opponent_name
                            }),
                            'away': dict(current_team_data, **{
                                'away_team_name': self.team_name
                            })
                        }
                        away_games.append((date_key, match_data))
                        
                except Exception as e:
                    logger.error(f"❌ [{self.team_name}] 베트맨 경기 행 파싱 실패: {e}")
                    continue
                    
            # 최근 5경기로 제한하고 딕셔너리 구성
            for i, (date_key, game_data) in enumerate(home_games[-5:]):
                recent_games['recent_5_home_games'][date_key] = game_data
                
            for i, (date_key, game_data) in enumerate(away_games[-5:]):
                recent_games['recent_5_away_games'][date_key] = game_data
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 베트맨 최근경기 파싱 실패: {e}")
            
        return recent_games

    def _parse_game_records_baseball_style(self, record_section: Tag) -> Dict[str, Dict]:
        """테스트 HTML용 최근 경기 파싱 (야구 스타일)"""
        recent_games = {
            'recent_5_home_games': {},
            'recent_5_away_games': {}
        }
        
        # 변수 초기화
        home_games = []
        away_games = []
        
        try:
            rows = record_section.find_all('tr')
            
            for i in range(0, len(rows), 2):  # 2행씩 처리 (홈+원정)
                if i + 1 >= len(rows):
                    break
                
                try:
                    # 첫 번째 행 (홈팀 정보)
                    home_row = rows[i]
                    home_cells = home_row.find_all(['td', 'th'])
                    
                    # 두 번째 행 (원정팀 정보) 
                    away_row = rows[i + 1]
                    away_cells = away_row.find_all(['td', 'th'])
                    
                    if len(home_cells) < 15 or len(away_cells) < 15:
                        continue
                    
                    # 날짜 정보 (첫 번째 행에서)
                    current_date = home_cells[0].get_text(strip=True).replace('\n', ' ')
                    
                    # 홈팀 정보 (현재팀이 홈인지 확인)
                    home_location = home_cells[1].get_text(strip=True)
                    home_team_cell = home_cells[2]
                    home_team_link = home_team_cell.find('a')
                    home_team_name = (
                        home_team_link.get_text(strip=True)
                        if home_team_link
                        else home_team_cell.get_text(strip=True)
                    )
                    
                    # 원정팀 정보
                    away_location = away_cells[0].get_text(strip=True)
                    away_team_cell = away_cells[1]
                    away_team_link = away_team_cell.find('a')
                    away_team_name = (
                        away_team_link.get_text(strip=True)
                        if away_team_link
                        else away_team_cell.get_text(strip=True)
                    )
                    
                    # 현재팀이 홈인지 원정인지 확인 (부분 매칭)
                    def is_same_team(team1, team2):
                        """팀명 매칭 (부분 매칭 지원)"""
                        if not team1 or not team2:
                            return False
                        team1_clean = team1.replace('FC', '').replace('CF', '').strip()
                        team2_clean = team2.replace('FC', '').replace('CF', '').strip()
                        return team1_clean in team2_clean or team2_clean in team1_clean
                    
                    current_is_home = is_same_team(home_team_name, self.team_name)
                    
                    if current_is_home:
                        # 현재팀이 홈 경기
                        current_cells = home_cells
                        opponent_name = away_team_name
                        offset = 0  # 홈 행은 날짜 포함
                    else:
                        # 현재팀이 원정 경기
                        current_cells = away_cells
                        opponent_name = home_team_name
                        offset = 1  # 원정 행은 날짜 없음
                    
                    # 현재팀 데이터 추출
                    result = current_cells[3 - offset].get_text(strip=True)
                    goals_scored = self._safe_int(current_cells[4 - offset].get_text(strip=True))
                    goals_conceded = self._safe_int(current_cells[9 - offset].get_text(strip=True))
                    goal_difference = self._safe_int(current_cells[10 - offset].get_text(strip=True))
                    assists = self._safe_int(current_cells[11 - offset].get_text(strip=True))
                    corners = self._safe_int(current_cells[12 - offset].get_text(strip=True))
                    penalty_kicks = self._safe_int(self._safe_get_cell_text(current_cells, 13 - offset))
                    shots = self._safe_int(self._safe_get_cell_text(current_cells, 14 - offset))
                    shots_on_target = self._safe_int(self._safe_get_cell_text(current_cells, 15 - offset))
                    fouls = self._safe_int(self._safe_get_cell_text(current_cells, 16 - offset))
                    yellow_cards = self._safe_int(self._safe_get_cell_text(current_cells, 17 - offset))
                    red_cards = self._safe_int(self._safe_get_cell_text(current_cells, 18 - offset))
                    
                    # 결과 변환 (먼저 수행)
                    current_result = map_result(result)
                    opponent_result = {
                        'W': 'L', 'L': 'W', 'D': 'D'
                    }.get(current_result, current_result)
                    
                    clean_sheet = 1 if goals_conceded == 0 else 0
                    
                    # 야구 스타일 경기 정보 구성
                    current_team_data = {
                        'result': current_result,
                        'goals_scored': goals_scored,
                        'goals_conceded': goals_conceded,
                        'goal_difference': goal_difference,
                    'assists': assists,
                        'corners': corners,
                        'penalty_kicks': penalty_kicks,
                    'shots': shots,
                    'shots_on_target': shots_on_target,
                    'fouls': fouls,
                    'yellow_cards': yellow_cards,
                        'red_cards': red_cards,
                        'clean_sheet': clean_sheet
                    }
                    
                    # 야구 스타일 매치 데이터 구성
                    if current_is_home:
                        # 현재팀이 홈
                        match_data = {
                            'home': dict(current_team_data, **{
                                'home_team_name': self.team_name
                            }),
                            'away': {
                                'result': opponent_result,
                                'goals_scored': goals_conceded,
                                'goals_conceded': goals_scored,
                                'away_team_name': opponent_name,
                                'goal_difference': -goal_difference,
                                'assists': 0,
                                'corners': 0,
                                'penalty_kicks': 0,
                                'shots': 0,
                                'shots_on_target': 0,
                                'fouls': 0,
                                'yellow_cards': 0,
                                'red_cards': 0,
                                'clean_sheet': 1 if goals_scored == 0 else 0
                            }
                        }
                        home_games.append((current_date, match_data))
                    else:
                        # 현재팀이 원정
                        match_data = {
                            'home': {
                                'result': opponent_result,
                                'goals_scored': goals_conceded,
                                'goals_conceded': goals_scored,
                                'home_team_name': opponent_name,
                                'goal_difference': -goal_difference,
                                'assists': 0,
                                'corners': 0,
                                'penalty_kicks': 0,
                                'shots': 0,
                                'shots_on_target': 0,
                                'fouls': 0,
                                'yellow_cards': 0,
                                'red_cards': 0,
                                'clean_sheet': 1 if goals_scored == 0 else 0
                            },
                            'away': dict(current_team_data, **{
                                'away_team_name': self.team_name
                            })
                        }
                        away_games.append((current_date, match_data))
                        
                except Exception as e:
                    logger.error(f"❌ [{self.team_name}] 경기 행 파싱 실패: {e}")
                    continue
                
            # 최근 5경기로 제한하고 날짜 키로 야구 스타일 구조 생성
            from datetime import datetime, timedelta
            base_date = datetime.now()
            
            for i, (date, game_data) in enumerate(home_games[:5]):
                date_key = (base_date - timedelta(days=i)).strftime('%Y-%m-%d')
                recent_games['recent_5_home_games'][date_key] = game_data
            
            for i, (date, game_data) in enumerate(away_games[:5]):
                date_key = (base_date - timedelta(days=i+5)).strftime('%Y-%m-%d')
                recent_games['recent_5_away_games'][date_key] = game_data
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 경기 기록 파싱 실패: {e}")
            
        return recent_games
    
    def parse_season_stats(self) -> Dict[str, Dict]:
        """역대 시즌 통계 파싱 (야구 스타일 구조)"""
        seasons = {}
        
        try:
            # 역대 시즌 성적 (history_record)
            history_section = self.soup.find('tbody', id='history_record')
            if history_section:
                seasons = self._parse_historical_seasons(history_section)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 시즌 통계 파싱 실패: {e}")
            
        return seasons
    
    def _parse_historical_seasons(self, history_section: Tag) -> Dict[str, Dict]:
        """역대 시즌 성적 파싱 (야구 스타일 구조)"""
        seasons = {}
        
        try:
            rows = history_section.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 15:
                    continue
                
                season_year = cells[0].get_text(strip=True)
                
                season_info = {
                    'rank': self._safe_int(cells[1].get_text(strip=True)),
                    'games_played': self._safe_int(
                        cells[2].get_text(strip=True)
                    ),
                    'wins': self._safe_int(cells[3].get_text(strip=True)),
                    'draws': self._safe_int(cells[4].get_text(strip=True)),
                    'losses': self._safe_int(cells[5].get_text(strip=True)),
                    'points': self._safe_int(cells[6].get_text(strip=True)),
                    'win_rate': self._safe_float(
                        cells[7].get_text(strip=True)
                    ),
                    'goals_for': self._safe_int(cells[8].get_text(strip=True)),
                    'goals_for_avg': self._safe_float(
                        cells[9].get_text(strip=True)
                    ),
                    'goals_against': self._safe_int(
                        cells[10].get_text(strip=True)
                    ),
                    'goals_against_avg': self._safe_float(
                        cells[11].get_text(strip=True)
                    ),
                    'goal_difference': self._safe_int(
                        cells[12].get_text(strip=True)
                    ),
                    'assists': self._safe_int(cells[13].get_text(strip=True)),
                    'fouls': self._safe_int(cells[14].get_text(strip=True)),
                    'yellow_cards': self._safe_int(
                        cells[15].get_text(strip=True)
                    ),
                    'red_cards': self._safe_int(
                        cells[16].get_text(strip=True)
                    )
                }
                
                seasons[season_year] = season_info
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 역대 시즌 파싱 실패: {e}")
            
        return seasons
    
    def parse_all_stats(self) -> Dict[str, Any]:
        """모든 통계 파싱"""
        return {
            'season_summary': self.parse_season_summary(),
            'recent_games_summary': self.parse_recent_games_summary(),
            'recent_games': self.parse_recent_games(),
            'season_stats': self.parse_season_stats()
        }
    
    @staticmethod
    def _safe_int(value: str) -> int:
        """안전한 정수 변환 (마이너스 부호 유지)"""
        try:
            # 마이너스 부호 확인
            is_negative = value.strip().startswith('-')
            # 숫자만 추출
            clean_value = ''.join(char for char in value if char.isdigit())
            if not clean_value:
                return 0
            result = int(clean_value)
            return -result if is_negative else result
        except (ValueError, AttributeError):
            return 0
    
    @staticmethod
    def _safe_float(value: str) -> float:
        """안전한 실수 변환 (마이너스 부호 유지)"""
        try:
            # 마이너스 부호 확인
            is_negative = value.strip().startswith('-')
            # 숫자와 소수점만 추출
            clean_value = ''.join(char for char in value if char.isdigit() or char == '.')
            if not clean_value or clean_value == '.':
                return 0.0
            result = float(clean_value)
            return -result if is_negative else result
        except (ValueError, AttributeError):
            return 0.0

    @staticmethod
    def _safe_percentage_to_float(value: str) -> float:
        """퍼센트 문자열을 실수로 변환"""
        try:
            # % 제거하고 숫자만 추출
            clean_value = value.replace('%', '').strip()
            return float(clean_value) / 100.0 if clean_value else 0.0
        except (ValueError, AttributeError):
            return 0.0

    @staticmethod
    def _safe_percentage_as_percent(value: str) -> float:
        """퍼센트 문자열을 0-100 범위의 실수로 변환"""
        try:
            # % 제거하고 숫자만 추출
            clean_value = value.replace('%', '').strip()
            return float(clean_value) if clean_value else 0.0
        except (ValueError, AttributeError):
            return 0.0

    @staticmethod
    def _safe_get_cell_text(cells: List, index: int) -> str:
        """안전한 셀 텍스트 추출"""
        try:
            return cells[index].get_text(strip=True) if index < len(cells) else ""
        except (IndexError, AttributeError):
            return ""
    
    @staticmethod
    def _is_valid_record(record: str) -> bool:
        """record 값이 유효한지 확인 (-, 빈 문자열, 0승0무0패 등 무효 데이터 필터링)"""
        if not record or record.strip() == "-":
            return False
        
        # 0승0무0패 같은 경우도 무효 데이터로 판단
        if record.strip() == "0승0무0패":
            return False
            
        # 빈 문자열이나 의미 없는 값들
        if record.strip() in ["", "N/A", "없음", "---"]:
            return False
            
        return True
