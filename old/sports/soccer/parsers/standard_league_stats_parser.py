"""
Standard League Stats Parser
For international leagues (J-League, EPL, LaLiga, Serie A, Bundesliga, etc.)
with simpler data structures than K-League
"""

import re
from typing import Any, Dict, List, Optional
from bs4 import BeautifulSoup, Tag

from utils.logger import Logger

logger = Logger(__name__)


class StandardLeagueStatsParser:
    """
    Standard League 축구 통계 파서
    
    J-League, EPL, LaLiga, Serie A, Bundesliga, Ligue 1, MLS 등
    K-League보다 단순한 데이터 구조를 가진 리그들에 사용
    """
    
    def __init__(self, html_content: str, team_name: str = ""):
        """
        Args:
            html_content: HTML 문자열
            team_name: 팀 이름 (로깅용)
        """
        self.soup = BeautifulSoup(html_content, 'html.parser')
        self.team_name = team_name
        
    def parse_season_summary(self) -> Dict[str, Any]:
        """시즌 요약 통계 파싱"""
        summary = {}
        
        try:
            # 시즌 전체 성적 테이블 찾기
            season_table = self.soup.find('tbody', id='homeAway_record')
            if season_table:
                summary = self._parse_season_table(season_table)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 시즌 요약 파싱 실패: {e}")
            
        return summary
    
    def _parse_season_table(self, table: Tag) -> Dict[str, Any]:
        """시즌 테이블 파싱"""
        summary = {}
        
        try:
            rows = table.find_all('tr')
            
            home_data = {}
            away_data = {}
            
            # 첫 번째 행: 홈 데이터
            if len(rows) >= 1:
                home_cells = rows[0].find_all(['td', 'th'])
                if len(home_cells) >= 10:
                    home_data = {
                        'matches_played': self._safe_int(home_cells[2].get_text(strip=True)),
                        'wins': self._safe_int(home_cells[3].get_text(strip=True)),
                        'draws': self._safe_int(home_cells[4].get_text(strip=True)),
                        'losses': self._safe_int(home_cells[5].get_text(strip=True)),
                        'points': self._safe_int(home_cells[6].get_text(strip=True)),
                        'win_rate': self._safe_float(home_cells[7].get_text(strip=True)),
                        'goals_scored': self._safe_int(home_cells[8].get_text(strip=True)),
                        'goals_conceded': self._safe_int(home_cells[11].get_text(strip=True)),
                        'assists': self._safe_int(home_cells[13].get_text(strip=True)) if len(home_cells) > 13 else 0
                    }
            
            # 두 번째 행: 원정 데이터
            if len(rows) >= 2:
                away_cells = rows[1].find_all(['td', 'th'])
                if len(away_cells) >= 10:
                    away_data = {
                        'matches_played': self._safe_int(away_cells[1].get_text(strip=True)),
                        'wins': self._safe_int(away_cells[2].get_text(strip=True)),
                        'draws': self._safe_int(away_cells[3].get_text(strip=True)),
                        'losses': self._safe_int(away_cells[4].get_text(strip=True)),
                        'points': self._safe_int(away_cells[5].get_text(strip=True)),
                        'win_rate': self._safe_float(away_cells[6].get_text(strip=True)),
                        'goals_scored': self._safe_int(away_cells[7].get_text(strip=True)),
                        'goals_conceded': self._safe_int(away_cells[10].get_text(strip=True)),
                        'assists': self._safe_int(away_cells[12].get_text(strip=True)) if len(away_cells) > 12 else 0
                    }
            
            # 전체 데이터 계산
            total_data = self._calculate_total_stats(home_data, away_data)
            
            # 순위 정보 (첫 번째 행에서 추출)
            if len(rows) >= 1:
                first_cells = rows[0].find_all(['td', 'th'])
                if first_cells:
                    total_data['rank'] = self._safe_int(first_cells[0].get_text(strip=True))
            
            summary = {
                'home': home_data,
                'away': away_data,
                'total': total_data
            }
            
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 시즌 테이블 파싱 실패: {e}")
            
        return summary
    
    def _calculate_total_stats(self, home_data: Dict, away_data: Dict) -> Dict[str, Any]:
        """홈/원정 데이터를 합쳐서 전체 통계 계산"""
        total = {}
        
        # 기본 통계 합산
        total['matches_played'] = home_data.get('matches_played', 0) + away_data.get('matches_played', 0)
        total['wins'] = home_data.get('wins', 0) + away_data.get('wins', 0)
        total['draws'] = home_data.get('draws', 0) + away_data.get('draws', 0)
        total['losses'] = home_data.get('losses', 0) + away_data.get('losses', 0)
        total['points'] = home_data.get('points', 0) + away_data.get('points', 0)
        total['goals_scored'] = home_data.get('goals_scored', 0) + away_data.get('goals_scored', 0)
        total['goals_conceded'] = home_data.get('goals_conceded', 0) + away_data.get('goals_conceded', 0)
        total['assists'] = home_data.get('assists', 0) + away_data.get('assists', 0)
        
        # 계산 통계
        total['goal_difference'] = total['goals_scored'] - total['goals_conceded']
        if total['matches_played'] > 0:
            total['win_rate'] = round(total['wins'] / total['matches_played'], 3)
            total['goals_scored_avg'] = round(total['goals_scored'] / total['matches_played'], 2)
            total['goals_conceded_avg'] = round(total['goals_conceded'] / total['matches_played'], 2)
        else:
            total['win_rate'] = 0.0
            total['goals_scored_avg'] = 0.0
            total['goals_conceded_avg'] = 0.0
        
        return total
    
    def parse_recent_games_summary(self) -> Dict[str, Any]:
        """최근 경기 요약 정보 파싱 - Standard League 형식"""
        summary = {}
        
        try:
            # 최근 경기 평균 통계 (seasonLatestTeamRecordTotal)
            total_section = self.soup.find('tfoot', id='seasonLatestTeamRecordTotal')
            if total_section:
                raw_data = self._parse_recent_games_summary_data(total_section)
                
                # Standard League 형식으로 변환
                summary = self._convert_to_standard_format(raw_data)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 최근 경기 요약 파싱 실패: {e}")
             
        return summary
    
    def _parse_recent_games_summary_data(self, total_section: Tag) -> Dict[str, Any]:
        """최근 경기 평균 통계 파싱"""
        summary = {}
        
        try:
            rows = total_section.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 3:
                    continue
                    
                # 홈/원정 구분
                location = self._safe_get_cell_text(cells, 1) if len(cells) > 1 else ""
                
                if location == "홈":
                    # 홈 행 데이터
                    record = self._safe_get_cell_text(cells, 2)  # colspan="2"인 경우
                    
                    # J-League처럼 모든 데이터가 "-"인 경우 스킵
                    if record == "-" or record == "":
                        continue
                    
                    # 홈 데이터 매핑
                    home_data = {
                        'record': record,
                        'avg_goals_scored': self._safe_float(self._safe_get_cell_text(cells, 3)) if len(cells) > 3 else 0.0,
                        'avg_goals_conceded': self._safe_float(self._safe_get_cell_text(cells, 8)) if len(cells) > 8 else 0.0,
                        'avg_assists': self._safe_float(self._safe_get_cell_text(cells, 10)) if len(cells) > 10 else 0.0,
                        'avg_corners': 0.0,
                        'avg_red_cards': 0.0,
                        'avg_yellow_cards': 0.0
                    }
                    
                    # 유효한 데이터인지 확인
                    if self._is_valid_game_data(home_data):
                        summary['recent_5_home_games'] = {'home': home_data}
                    
                elif location == "원정":
                    # 원정 행 데이터
                    record = self._safe_get_cell_text(cells, 2)  # colspan="2"인 경우
                    
                    if record == "-" or record == "":
                        continue
                    
                    # 원정 데이터 매핑 (J-League 구조에 맞춤)
                    away_data = {
                        'record': record,
                        'avg_goals_scored': self._safe_float(self._safe_get_cell_text(cells, 3)) if len(cells) > 3 else 0.0,
                        'avg_goals_conceded': self._safe_float(self._safe_get_cell_text(cells, 8)) if len(cells) > 8 else 0.0,
                        'avg_assists': self._safe_float(self._safe_get_cell_text(cells, 10)) if len(cells) > 10 else 0.0,
                        'avg_corners': 0.0,
                        'avg_red_cards': 0.0,
                        'avg_yellow_cards': 0.0
                    }
                    
                    # 유효한 데이터인지 확인
                    if self._is_valid_game_data(away_data):
                        summary['recent_5_away_games'] = {'away': away_data}
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 최근 경기 요약 파싱 실패: {e}")
             
        return summary
    
    def _convert_to_standard_format(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """원시 데이터를 Standard League 형식으로 변환"""
        standard_format = {}
        
        # recent_5_games 구조 생성
        recent_5_games = {}
        
        # 홈 게임 데이터 처리
        if 'recent_5_home_games' in raw_data:
            home_data = raw_data['recent_5_home_games']
            if 'home' in home_data and self._is_valid_game_data(home_data['home']):
                recent_5_games['last_3_home_games'] = home_data['home']
        
        # 원정 게임 데이터 처리
        if 'recent_5_away_games' in raw_data:
            away_data = raw_data['recent_5_away_games']
            if 'away' in away_data and self._is_valid_game_data(away_data['away']):
                recent_5_games['last_2_away_games'] = away_data['away']
        
        # recent_5_games가 비어있지 않은 경우에만 추가
        if recent_5_games:
            standard_format['recent_5_games'] = recent_5_games
        
        # recent_5_away_games 구조 생성 (away 데이터만)
        if 'recent_5_away_games' in raw_data:
            away_section = raw_data['recent_5_away_games']
            if 'away' in away_section and self._is_valid_game_data(away_section['away']):
                standard_format['recent_5_away_games'] = {
                    'away': away_section['away']
                }
        
        # recent_5_home_games 구조 생성 (home 데이터만)
        if 'recent_5_home_games' in raw_data:
            home_section = raw_data['recent_5_home_games']
            if 'home' in home_section and self._is_valid_game_data(home_section['home']):
                standard_format['recent_5_home_games'] = {
                    'home': home_section['home']
                }
        
        return standard_format
    
    def _is_valid_game_data(self, data: Dict[str, Any]) -> bool:
        """게임 데이터가 유효한지 확인"""
        if not isinstance(data, dict):
            return False
        
        # 기록이 '-' 또는 빈 문자열인 경우 무효
        record = data.get('record', '')
        if record in ['-', '', '0승 0무 0패']:
            return False
        
        # 모든 통계가 0인 경우 무효 (단, 정상적인 0-0 경기는 제외)
        goals_scored = data.get('avg_goals_scored', 0)
        goals_conceded = data.get('avg_goals_conceded', 0)
        assists = data.get('avg_assists', 0)
        
        # 기록이 있으면서 모든 통계가 0인 경우만 무효로 처리
        if record != '-' and goals_scored == 0 and goals_conceded == 0 and assists == 0:
            return False
        
        return True
    
    def parse_recent_games(self, limit: int = 5) -> Dict[str, Dict]:
        """최근 경기 상세 정보 파싱"""
        recent_games = {
            'recent_5_home_games': {},
            'recent_5_away_games': {}
        }
        
        try:
            # 최근 경기 테이블 찾기
            recent_table = self.soup.find('tbody', id='seasonLatestTeamRecord')
            if recent_table:
                recent_games = self._parse_recent_games_table(recent_table, limit)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 최근 경기 파싱 실패: {e}")
            
        return recent_games
    
    def _parse_recent_games_table(self, table: Tag, limit: int) -> Dict[str, Dict]:
        """최근 경기 테이블 파싱"""
        recent_games = {
            'recent_5_home_games': {},
            'recent_5_away_games': {}
        }
        
        try:
            rows = table.find_all('tr')
            
            # 2행씩 묶어서 처리 (각 매치 = 2행)
            matches_processed = 0
            i = 0
            
            while i < len(rows) - 1 and matches_processed < limit:
                try:
                    # 첫 번째 행 (홈팀 데이터)
                    home_row = rows[i]
                    # 두 번째 행 (원정팀 데이터)
                    away_row = rows[i + 1]
                    
                    home_cells = home_row.find_all(['td', 'th'])
                    away_cells = away_row.find_all(['td', 'th'])
                    
                    if len(home_cells) < 5 or len(away_cells) < 5:
                        i += 2
                        continue
                    
                    # 경기 날짜 (홈팀 행에서 추출)
                    date_text = home_cells[0].get_text(strip=True)
                    
                    # 홈/원정 구분
                    venue = home_cells[1].get_text(strip=True)
                    
                    # 현재 팀의 역할에 따라 데이터 저장
                    if venue == "홈":
                        # 현재 팀이 홈에서 경기
                        match_data = self._extract_match_data(home_cells, away_cells, date_text, True)
                        if match_data:
                            recent_games['recent_5_home_games'][date_text] = match_data
                    else:
                        # 현재 팀이 원정에서 경기
                        match_data = self._extract_match_data(away_cells, home_cells, date_text, False)
                        if match_data:
                            recent_games['recent_5_away_games'][date_text] = match_data
                    
                    matches_processed += 1
                    i += 2
                    
                except Exception as e:
                    logger.debug(f"매치 파싱 실패 (행 {i}): {e}")
                    i += 2
                    continue
                    
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 최근 경기 테이블 파싱 실패: {e}")
            
        return recent_games
    
    def _extract_match_data(self, current_cells: List, opponent_cells: List, date_text: str, is_home: bool) -> Optional[Dict]:
        """매치 데이터 추출"""
        try:
            # 현재 팀 데이터
            current_result = current_cells[3].get_text(strip=True) if len(current_cells) > 3 else ''
            current_goals = self._safe_int(current_cells[4].get_text(strip=True)) if len(current_cells) > 4 else 0
            current_assists = self._safe_int(current_cells[11].get_text(strip=True)) if len(current_cells) > 11 else 0
            
            # 상대 팀 데이터
            opponent_result = opponent_cells[2].get_text(strip=True) if len(opponent_cells) > 2 else ''
            opponent_goals = self._safe_int(opponent_cells[3].get_text(strip=True)) if len(opponent_cells) > 3 else 0
            
            # 결과 변환
            result_map = {'승': 'W', '무': 'D', '패': 'L'}
            current_result = result_map.get(current_result, current_result)
            opponent_result = result_map.get(opponent_result, opponent_result)
            
            if is_home:
                return {
                    'home': {
                        'result': current_result,
                        'goals_scored': current_goals,
                        'goals_conceded': opponent_goals,
                        'assists': current_assists,
                        'match_date': date_text
                    },
                    'away': {
                        'result': opponent_result,
                        'goals_scored': opponent_goals,
                        'goals_conceded': current_goals,
                        'assists': 0,
                        'match_date': date_text
                    }
                }
            else:
                return {
                    'home': {
                        'result': opponent_result,
                        'goals_scored': opponent_goals,
                        'goals_conceded': current_goals,
                        'assists': 0,
                        'match_date': date_text
                    },
                    'away': {
                        'result': current_result,
                        'goals_scored': current_goals,
                        'goals_conceded': opponent_goals,
                        'assists': current_assists,
                        'match_date': date_text
                    }
                }
                
        except Exception as e:
            logger.debug(f"매치 데이터 추출 실패: {e}")
            return None
    
    def parse_season_history(self) -> Dict[str, Any]:
        """역대 시즌 성적 파싱"""
        history = {}
        
        try:
            # 역대 시즌 테이블 찾기
            history_table = self.soup.find('tbody', id='history_record')
            if history_table:
                history = self._parse_history_table(history_table)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 역대 시즌 파싱 실패: {e}")
            
        return history
    
    def _parse_history_table(self, table: Tag) -> Dict[str, Any]:
        """역대 시즌 테이블 파싱"""
        history = {}
        
        try:
            rows = table.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 8:
                    continue
                
                season = cells[0].get_text(strip=True)
                if season:
                    history[season] = {
                        'rank': self._safe_int(cells[1].get_text(strip=True)),
                        'matches_played': self._safe_int(cells[2].get_text(strip=True)),
                        'wins': self._safe_int(cells[3].get_text(strip=True)),
                        'draws': self._safe_int(cells[4].get_text(strip=True)),
                        'losses': self._safe_int(cells[5].get_text(strip=True)),
                        'points': self._safe_int(cells[6].get_text(strip=True)),
                        'win_rate': self._safe_float(cells[7].get_text(strip=True)),
                        'goals_scored': self._safe_int(cells[8].get_text(strip=True)) if len(cells) > 8 else 0,
                        'goals_conceded': self._safe_int(cells[10].get_text(strip=True)) if len(cells) > 10 else 0,
                        'assists': self._safe_int(cells[13].get_text(strip=True)) if len(cells) > 13 else 0,
                        'fouls': 0,
                        'yellow_cards': 0,
                        'red_cards': 0,
                        'goal_difference': 0,
                        'goals_scored_avg': 0.0,
                        'goals_conceded_avg': 0.0
                    }
                    
                    # 계산 통계
                    season_data = history[season]
                    season_data['goal_difference'] = season_data['goals_scored'] - season_data['goals_conceded']
                    if season_data['matches_played'] > 0:
                        season_data['goals_scored_avg'] = round(season_data['goals_scored'] / season_data['matches_played'], 2)
                        season_data['goals_conceded_avg'] = round(season_data['goals_conceded'] / season_data['matches_played'], 2)
                    
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 역대 시즌 테이블 파싱 실패: {e}")
            
        return history
    
    def _safe_get_cell_text(self, cells: List[Tag], index: int) -> str:
        """셀 텍스트 안전 추출"""
        try:
            return cells[index].get_text(strip=True) if index < len(cells) else ""
        except (IndexError, AttributeError):
            return ""
    
    def _safe_int(self, text: str) -> int:
        """텍스트를 안전하게 정수로 변환"""
        if not text or text.strip() == '-':
            return 0
        try:
            # 숫자가 아닌 문자 제거
            cleaned = re.sub(r'[^\d-]', '', str(text).strip())
            return int(cleaned) if cleaned else 0
        except (ValueError, TypeError):
            return 0
    
    def _safe_float(self, text: str) -> float:
        """텍스트를 안전하게 실수로 변환"""
        if not text or text.strip() == '-':
            return 0.0
        try:
            # 숫자와 소수점, 마이너스만 남기기
            cleaned = re.sub(r'[^\d.-]', '', str(text).strip())
            return float(cleaned) if cleaned else 0.0
        except (ValueError, TypeError):
            return 0.0