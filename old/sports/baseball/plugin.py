# flake8: noqa
"""
야구 플러그인 구현
기존 야구 로직을 새로운 아키텍처에 통합
"""
import time
from typing import Any, Dict, List, Optional

from core.factories.sport_factory import SportPlugin, SportType
from core.interfaces.collector import SportCollector
from core.interfaces.parser import SportParser
from core.interfaces.service import SportService
from utils.logger import Logger

from .collectors.baseball_collector import BaseballCollector
from .parsers.baseball_parser import BaseballParser
from .services.baseball_service import BaseballService

logger = Logger(__name__)


class BaseballPlugin(SportPlugin):
    """
    야구 플러그인 구현
    KBO, MLB, NPB 리그 지원
    """

    def __init__(self):
        """초기화"""
        from core.config.sport_config import Sport, get_sport_config

        self.sport_config = get_sport_config(Sport.BASEBALL)
        self.service = None
        self.cache = None  # 🚀 캐시 추가
    
    def set_cache(self, cache):
        """캐시 설정"""
        self.cache = cache
    
    @property
    def sport_type(self) -> SportType:
        """스포츠 타입 반환"""
        return SportType.BASEBALL

    @property
    def display_name(self) -> str:
        """표시용 스포츠 이름"""
        return "야구"

    @property
    def supported_leagues(self) -> List[str]:
        """지원하는 리그 목록"""
        return ["KBO", "MLB", "NPB"]

    def get_sport_config(self):
        """스포츠 설정 반환"""
        return self.sport_config

    async def collect_data(self, **kwargs) -> Dict[str, Any]:
        """야구 데이터 수집 실행"""
        try:
            # 서비스 초기화
            from database.database import connect_supabase
            from sports.baseball.services.team_pitcher_unified_service import \
                TeamPitcherUnifiedService
            
            self.service = TeamPitcherUnifiedService()
            
            # 🚀 캐시를 서비스에 주입
            if self.cache:
                self.service._cache = self.cache
            
            # Supabase 클라이언트 연결
            client = connect_supabase()
            if not client:
                return {
                    'success': False,
                    'message': '데이터베이스 연결 실패',
                    'count': 0,
                    'duration': 0,
                    'errors': ['DB 연결 실패']
                }
            
            # target_games에서 게임 데이터 조회
            from database.database import get_target_games_by_sport
            games = get_target_games_by_sport('BS')
            
            if not games:
                return {
                    'success': True,
                    'message': '처리할 야구 게임 없음',
                    'count': 0,
                    'duration': 0,
                    'errors': []
                }
            
            # 팀별 통합 처리
            start_time = time.time()
            result = await self.service.process_teams_with_pitchers(games, client)
            duration = time.time() - start_time
            
            return {
                'success': True,
                'message': f"야구 데이터 수집 완료: {result.get('teams_saved', 0)}팀 처리",
                'count': result.get('teams_saved', 0),
                'duration': duration,
                'errors': [],
                'teams_processed': result.get('teams_saved', 0),
                'players_processed': result.get('pitchers_saved', 0)
            }
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"야구 플러그인 실행 실패: {e}")
            return {
                'success': False,
                'message': f'야구 데이터 수집 실패: {str(e)}',
                'count': 0,
                'duration': 0,
                'errors': [str(e)],
                'teams_processed': 0,
                'players_processed': 0
            }

    async def _get_target_games(self) -> List[Dict]:
        """타겟 게임 조회"""
        try:
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                return []
            
            # 야구 리그 ID 목록
            baseball_league_ids = ["BS001", "BS002", "BS004"]  # KBO, MLB, NPB
            
            # target_games 테이블에서 야구 경기만 조회 (W 게임 타입만)
            response = client.table('target_games').select('*').in_(
                'league_id', baseball_league_ids
            ).eq('game_type', 'W').execute()
            
            if not response.data:
                return []
            
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"📋 야구 타겟 경기: {len(response.data)}개")
            return response.data
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"❌ 타겟 게임 조회 실패: {e}")
            return []

    async def collect_all_data(self, page=None) -> Dict[str, Any]:
        """팀 통계와 선발투수(투수) 통계만 수집하여 요약 반환"""
        import logging
        logger = logging.getLogger(__name__)

        from database.database import connect_supabase
        from sports.baseball.collectors.unified_crawler import \
            UnifiedDataCollector

        try:
            client = connect_supabase()
            if not client:
                raise RuntimeError("DB 연결 실패")

            # 1) 경기 스케줄 + 통계 + 투수까지 한 번에 처리 (리그별 순차)
            # page 객체가 있으면 전달, 없으면 자체 브라우저 관리
            if page:
                # 외부에서 관리되는 브라우저 페이지 사용
                schedule_collector = UnifiedDataCollector(client, wait_time=5.0)
                # UnifiedDataCollector가 외부 page를 사용하도록 수정 필요
                # 현재는 자체 브라우저 관리를 사용
                logger.info("🔧 외부 브라우저 페이지 전달받음 (향후 지원 예정)")
                
            schedule_collector = UnifiedDataCollector(client, wait_time=5.0)
            games = await schedule_collector.collect_data()

            # collect_data 내부에서 이미 저장되었으므로 DB에서 다시 카운트할 필요 없음
            teams_processed = sum(1 for g in games if g.get('team_stats_saved'))
            players_processed = sum(1 for g in games if g.get('pitcher_stats_saved'))

            return {
                'teams_processed': teams_processed,
                'players_processed': players_processed,
                'games_processed': len(games),
                'errors': [],
            }

        except Exception as e:
            logger.error(f"❌ 야구 데이터 수집 실패: {e}")
            return {
                'teams_processed': 0,
                'players_processed': 0,
                'games_processed': 0,
                'errors': [str(e)],
            }

    async def collect_league_data(self, page, league: str) -> Dict[str, Any]:
        """🏟️ 리그별 데이터 수집 - 오케스트레이터용"""
        import logging
        logger = logging.getLogger(__name__)
        
        try:
            from database.database import connect_supabase
            from sports.baseball.collectors.unified_crawler import \
                UnifiedDataCollector

            client = connect_supabase()
            if not client:
                raise RuntimeError("DB 연결 실패")

            # 리그별 타겟 게임 필터링
            target_games = await self._get_target_games_by_league(league)
            
            if not target_games:
                logger.info(f"📭 {league} 수집할 경기 없음")
                return {
                    'teams_processed': 0,
                    'players_processed': 0,
                    'errors': []
                }

            logger.info(f"🎯 {league} 경기 {len(target_games)}개")
            
            # 해당 리그만 처리
            collector = UnifiedDataCollector(client, wait_time=5.0)
            league_games = await collector._crawl_league_games(league, target_games)
            
            if not league_games:
                logger.info(f"📭 {league} 매칭된 경기 없음")
                return {
                    'teams_processed': 0,
                    'players_processed': 0,
                    'errors': []
                }

            logger.info(f"⚾ {league} 수집 중: {len(league_games)}개 경기")
            
            # 팀별 통합 처리 (팀 통계 + 선발투수)
            unified_results = await collector._process_teams_unified(league_games)
            
            return {
                'teams_processed': unified_results.get('teams_saved', 0),
                'players_processed': unified_results.get('pitchers_saved', 0),
                'errors': []
            }

        except Exception as e:
            logger.error(f"❌ {league} 리그 데이터 수집 실패: {e}")
            return {
                'teams_processed': 0,
                'players_processed': 0,
                'errors': [str(e)]
            }

    async def _get_target_games_by_league(self, league: str) -> List[Dict]:
        """리그별 타겟 게임 조회"""
        try:
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                return []
            
            # 리그명을 league_id로 변환
            league_id_map = {
                "KBO": "BS001",
                "MLB": "BS002", 
                "NPB": "BS004"
            }
            
            league_id = league_id_map.get(league)
            if not league_id:
                return []
            
            # 해당 리그의 경기만 조회 (W 게임 타입만)
            response = client.table('target_games').select('*').eq(
                'league_id', league_id
            ).eq('game_type', 'W').execute()
            
            return response.data or []
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"❌ {league} 리그 타겟 게임 조회 실패: {e}")
            return []

    def create_collector(self, **kwargs) -> SportCollector:
        """야구 데이터 수집기 생성"""
        return BaseballCollector(**kwargs)

    def create_parser(self, **kwargs) -> SportParser:
        """야구 데이터 파서 생성"""
        return BaseballParser(**kwargs)

    def create_service(self, **kwargs) -> SportService:
        """야구 비즈니스 서비스 생성"""
        return BaseballService(**kwargs)
    
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """야구 플러그인 설정 검증"""
        required_keys = ["leagues", "batch_size"]
        
        # 필수 키 검증
        for key in required_keys:
            if key not in config:
                return False
        
        # 리그 설정 검증
        leagues = config.get("leagues", [])
        if not isinstance(leagues, list):
            return False
        
        # 지원하는 리그인지 확인
        supported = set(self.supported_leagues)
        for league in leagues:
            if league not in supported:
                return False
        
        # 배치 크기 검증
        batch_size = config.get("batch_size", 10)
        if not isinstance(batch_size, int) or batch_size <= 0:
            return False
        
        return True
    
    def get_default_configuration(self) -> Dict[str, Any]:
        """기본 설정 반환"""
        return {
            "leagues": ["KBO", "MLB", "NPB"],
            "batch_size": 10,
            "max_concurrent": 3,
            "timeout_seconds": 30,
            "retry_attempts": 3,
            "browser_pool_size": 3,
            "cache_enabled": True,
            "cache_ttl_seconds": 43200,
            "supported_positions": [
                "pitcher", "catcher", "first_base", "second_base", 
                "third_base", "shortstop", "left_field", 
                "center_field", "right_field", "designated_hitter"
            ]
        } 