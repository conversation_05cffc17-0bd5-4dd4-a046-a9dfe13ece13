"""
야구 데이터 수집기 구현
기존 PitcherCrawler 로직을 새로운 아키텍처에 통합
"""
import asyncio
import logging
import time
from typing import Any, Dict, List, Optional

from playwright.async_api import Page, async_playwright

from core.config.sport_config import Sport, get_sport_config
from core.exceptions import ParsingException as DataParsingException
from core.exceptions import ResourceException as BrowserInitializationException
from core.exceptions import TimeoutException as CollectionTimeoutException
from core.interfaces.collector import (CollectionStatus, CollectorResult,
                                       ResourceManager, SportCollector)
from utils.logger import Logger

logger = Logger(__name__, level=logging.ERROR)


class BrowserResourceManager:
    """브라우저 리소스 관리자"""
    
    def __init__(self):
        self.browser = None
        self.playwright = None
    
    async def acquire(self):
        """브라우저 리소스 획득"""
        if not self.playwright:
            self.playwright = await async_playwright().start()
        
        if not self.browser:
            self.browser = await self.playwright.chromium.launch(headless=True)
        
        return self.browser
    
    async def release(self, resource):
        """리소스 해제 (페이지 수준에서만 해제)"""
        # 브라우저는 계속 유지하고 페이지만 닫음
        pass
    
    async def cleanup(self):
        """전체 리소스 정리"""
        errors = []
        
        try:
            if self.browser:
                try:
                    await self.browser.close()
                except Exception as e:
                    errors.append(f"브라우저 종료 실패: {e}")
                finally:
                    self.browser = None
            
            if self.playwright:
                try:
                    await self.playwright.stop()
                except Exception as e:
                    errors.append(f"Playwright 종료 실패: {e}")
                finally:
                    self.playwright = None
            
            if errors:
                logger.warning(f"브라우저 정리 중 일부 오류 발생: {errors}")
                
        except Exception as e:
            logger.error(f"브라우저 정리 중 예상치 못한 오류: {e}")
            # 강제 정리
            self.browser = None
            self.playwright = None


class BaseballDataItem:
    """야구 데이터 아이템"""
    
    def __init__(self, name: str, position: str, team: str, league: str, **kwargs):
        self.name = name
        self.position = position
        self.team = team
        self.league = league
        self.extra_data = kwargs
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리로 변환"""
        return {
            "name": self.name,
            "position": self.position,
            "team": self.team,
            "league": self.league,
            **self.extra_data
        }


class BaseballCollector(SportCollector[BaseballDataItem]):
    """
    야구 데이터 수집기
    기존 PitcherCrawler 로직을 새로운 인터페이스에 맞게 적용
    """
    
    def __init__(self, batch_size: int = 10, max_concurrent: int = 3, 
                 timeout_seconds: int = 30, **kwargs):
        # 브라우저 리소스 매니저 생성
        resource_manager = BrowserResourceManager()
        super().__init__(resource_manager)
        
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.timeout_seconds = timeout_seconds
        self.team_mappings: Dict[str, str] = kwargs.get('team_mappings', {})
        self.cache: Dict[str, Dict] = {}
        # 스포츠 설정 로드 (URL 패턴 등에서 사용)
        self.sport_config = get_sport_config(Sport.BASEBALL)

    # ------------------------------------------------------------------
    # 플러그인 호환용 편의 메서드
    # ------------------------------------------------------------------

    async def collect_all_teams(self) -> Dict[str, Any]:
        """모든 팀 통계를 수집하는 간단한 래퍼

        현재는 완전 구현 전 단계이므로 0건 처리 결과만 반환합니다.
        플러그인에서 required key('teams_processed') 를 기대하므로 형식을 맞춥니다.
        """
        logger.info("⚾ collect_all_teams() - 아직 구현되지 않음, 0건 반환")
        return {
            "teams_processed": 0,
            "errors": [] if False else ["collect_all_teams not implemented"],
        }

    async def collect_all_players(self) -> Dict[str, Any]:
        """모든 선수 통계를 수집하는 간단한 래퍼 (미구현)"""
        logger.info("⚾ collect_all_players() - 아직 구현되지 않음, 0건 반환")
        return {
            "players_processed": 0,
            "errors": [] if True else ["collect_all_players not implemented"],
        }
    
    async def _do_collect(self, **kwargs) -> CollectorResult[BaseballDataItem]:
        """실제 수집 로직 구현"""
        items = kwargs.get('items', [])
        if not items:
            return CollectorResult(
                status=CollectionStatus.SUCCESS,
                data=[],
                total_count=0,
                success_count=0
            )
        
        # 브라우저 초기화
        browser = await self._resource_manager.acquire()
        if not browser:
            raise BrowserInitializationException()
        
        collected_data = []
        errors = []
        
        try:
            # 배치 처리
            batches = self._create_batches(items, self.batch_size)
            
            for i, batch in enumerate(batches):
                self._notify_progress(
                    i * self.batch_size, 
                    len(items), 
                    f"배치 {i+1}/{len(batches)} 처리 중"
                )
                
                batch_results = await self._process_batch(batch, browser)
                
                for result in batch_results:
                    if isinstance(result, Exception):
                        errors.append(str(result))
                    elif result:
                        collected_data.append(result)
            
            status = CollectionStatus.SUCCESS
            if errors and not collected_data:
                status = CollectionStatus.FAILED
            elif errors:
                status = CollectionStatus.PARTIAL
            
            return CollectorResult(
                status=status,
                data=collected_data,
                errors=errors,
                total_count=len(items),
                success_count=len(collected_data)
            )
            
        except Exception as e:
            logger.error(f"데이터 수집 중 오류: {e}")
            return CollectorResult(
                status=CollectionStatus.FAILED,
                errors=[str(e)],
                total_count=len(items),
                success_count=len(collected_data)
            )
    
    async def collect_team_stats(self, teams: List[str], **kwargs) -> CollectorResult[BaseballDataItem]:
        """팀 통계 수집"""
        # 팀별 선수 목록을 가져와서 수집
        items = []
        for team in teams:
            # TODO: 팀별 선수 목록 조회 로직 구현
            items.extend(self._get_team_players(team))
        
        return await self.collect(items=items, **kwargs)
    
    async def collect_player_stats(self, players: List[str], **kwargs) -> CollectorResult[BaseballDataItem]:
        """선수 통계 수집"""
        items = []
        for player in players:
            # 선수 정보를 BaseballDataItem으로 변환
            player_data = kwargs.get(f'{player}_data', {})
            item = BaseballDataItem(
                name=player,
                position=player_data.get('position', 'unknown'),
                team=player_data.get('team', 'unknown'),
                league=player_data.get('league', 'KBO')
            )
            items.append(item)
        
        return await self.collect(items=items, **kwargs)
    
    def _create_batches(self, items: List, batch_size: int) -> List[List]:
        """아이템을 배치로 분할"""
        batches = []
        for i in range(0, len(items), batch_size):
            batches.append(items[i:i + batch_size])
        return batches
    
    async def _process_batch(self, batch: List, browser) -> List:
        """배치 처리"""
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_item(item):
            async with semaphore:
                return await self._process_single_item(item, browser)
        
        tasks = [process_item(item) for item in batch]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _process_single_item(self, item: BaseballDataItem, browser) -> Optional[BaseballDataItem]:
        """단일 아이템 처리 (기존 crawler 로직 적용)"""
        page = None
        try:
            page = await browser.new_page()
            
            # 캐시 확인
            cache_key = f"{item.name}_{item.team}"
            if cache_key in self.cache:
                cached_data = self.cache[cache_key]
                return BaseballDataItem(
                    name=item.name,
                    position=item.position,
                    team=item.team,
                    league=item.league,
                    stats=cached_data
                )
            
            # 실제 크롤링 수행
            stats = await self._crawl_player_stats(page, item)
            
            if stats:
                # 캐시에 저장
                self.cache[cache_key] = stats
                
                return BaseballDataItem(
                    name=item.name,
                    position=item.position,
                    team=item.team,
                    league=item.league,
                    stats=stats
                )
            
            return None
            
        except Exception as e:
            logger.debug(f"선수 데이터 수집 실패 - {item.name}: {e}")
            return None
        finally:
            if page:
                try:
                    await page.close()
                except Exception:
                    pass
    
    async def _crawl_player_stats(self, page: Page, item: BaseballDataItem) -> Optional[Dict]:
        """선수 통계 크롤링 (기존 로직 적용)"""
        try:
            # 팀 페이지 URL 생성 (임시 로직)
            team_url = self._generate_team_url(item.team, item.league)
            
            # 페이지 로드
            if not await self._load_team_page(page, team_url, item.name):
                return None
            
            # 선수 링크 찾기 및 클릭
            if not await self._find_and_click_player_link(page, item.name):
                return None
            
            # 선수 페이지 로드 대기
            await self._wait_for_player_page_load(page, item.name)
            
            # 통계 파싱
            html = await page.content()
            from .parsers.baseball_parser import BaseballParser
            parser = BaseballParser(team_mappings=self.team_mappings)
            stats = parser.parse_player_data(html, item.name)
            
            return stats
            
        except Exception as e:
            logger.debug(f"선수 통계 크롤링 오류 - {item.name}: {e}")
            return None
    
    def _generate_team_url(self, team: str, league: str) -> str:
        """팀 URL 생성 (임시 구현)"""
        # TODO: 실제 URL 생성 로직 구현
        base_urls = {
            "KBO": "https://betman.com/kbo/team/",
            "MLB": "https://betman.com/mlb/team/",
            "NPB": "https://betman.com/npb/team/"
        }
        return f"{base_urls.get(league, base_urls['KBO'])}{team}"
    
    async def _load_team_page(self, page: Page, team_url: str, player_name: str) -> bool:
        """팀 페이지 로드 (기존 로직)"""
        for retry in range(2):
            try:
                await page.goto(team_url, timeout=10000)
                await page.wait_for_selector("table.tbl", timeout=5000)
                return True
            except Exception as e:
                if retry == 1:
                    logger.warning(f"❌ {player_name} 팀페이지 로드 최종 실패: {e}")
                    return False
                else:
                    logger.debug(f"⚠️ {player_name} 팀페이지 로드 재시도: {e}")
                    await asyncio.sleep(1)
        return False
    
    async def _find_and_click_player_link(self, page: Page, player_name: str) -> bool:
        """선수 링크 찾기 및 클릭 (기존 로직)"""
        try:
            # 선수 링크 찾기
            link = await self._find_player_link(page, player_name)
            if link:
                await link.click()
                return True
            return False
        except Exception as e:
            logger.debug(f"선수 링크 클릭 오류 - {player_name}: {e}")
            return False
    
    async def _find_player_link(self, page: Page, player_name: str):
        """선수 링크 찾기 (기존 로직)"""
        try:
            return await page.wait_for_selector(
                f'a[href*="player"][title*="{player_name}"], '
                f'a[href*="player"]:has-text("{player_name}")',
                timeout=3000
            )
        except Exception:
            return None
    
    async def _wait_for_player_page_load(self, page: Page, player_name: str) -> None:
        """선수 페이지 로드 대기 (기존 로직)"""
        try:
            await page.wait_for_load_state('networkidle', timeout=9000)
            await page.wait_for_selector('.infoBox', timeout=7000)
        except Exception as e:
            logger.debug(f"페이지 로드 대기 중 오류: {player_name} - {e}")
            await asyncio.sleep(4)
    
    def _get_team_players(self, team: str) -> List[BaseballDataItem]:
        """팀별 선수 목록 조회 (임시 구현)"""
        # TODO: 실제 팀 선수 목록 조회 로직 구현
        return []
    
    def _create_no_data_item(self, name: str) -> BaseballDataItem:
        """No data 케이스를 위한 기본 아이템"""
        return BaseballDataItem(
            name=name,
            position="unknown",
            team="unknown",
            league="unknown",
            stats={"no_data": True}
        ) 