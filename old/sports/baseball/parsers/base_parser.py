"""
파서 전략 기본 인터페이스 - SOLID 원칙 적용
"""
from abc import ABC, abstractmethod
from typing import Generic, TypeVar

from utils.exceptions import ParsingError

# 제네릭 타입 변수 정의
T = TypeVar('T')


class ParserStrategy(Generic[T], ABC):
    """
    파서 전략 추상 클래스
    
    Strategy 패턴 적용: 각 웹사이트나 데이터 형식에 따라 
    다양한 파싱 전략 구현 가능
    """
    
    @abstractmethod
    def parse(self, html: str, **kwargs) -> T:
        """HTML 문자열을 파싱하여 원하는 형식의 데이터로 변환"""
        pass


class BaseParser(ParserStrategy[T], ABC):
    """
    공통 파싱 기능을 제공하는 기본 파서 클래스
    """
    
    def _handle_parsing_error(self, parser_type: str, 
                              error: Exception) -> None:
        """파싱 에러 처리"""
        raise ParsingError(parser_type, f"파싱 오류: {str(error)}")
    
    @staticmethod
    def _safe_extract_text(element, default: str = "") -> str:
        """안전한 텍스트 추출"""
        if element is None:
            return default
        return element.text.strip() if hasattr(element, 'text') else default
    
    @staticmethod
    def _safe_extract_int(text: str, default: int = 0) -> int:
        """안전한 정수 변환"""
        try:
            return int(text.strip()) if text.strip() else default
        except (ValueError, AttributeError):
            return default 