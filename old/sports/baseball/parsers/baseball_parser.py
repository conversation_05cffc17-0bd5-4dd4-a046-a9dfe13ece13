# flake8: noqa
"""
야구 파서 구현 - 기존 파서들을 통합하는 래퍼
"""
from dataclasses import dataclass
from typing import Any, Dict

from core.interfaces.parser import SportParser
from sports.baseball.parsers.kbo_stats_parser import KBOStatsParser
from sports.baseball.parsers.mlb_stats_parser import MLBStatsParser
from sports.baseball.parsers.npb_stats_parser import NPBStatsParser
from sports.baseball.parsers.pitcher_parser import PitcherDataParser


@dataclass
class BaseballData:
    """야구 데이터 구조체"""
    league: str
    team: str
    season_summary: Dict[str, Any]
    recent_games: Dict[str, Any]
    season_stats: Dict[str, Any]
    player_stats: Dict[str, Any] = None


class BaseballParser(SportParser):
    """야구 통합 파서 - 기존 파서들을 래핑"""
    
    def __init__(self, **kwargs):
        """초기화"""
        self.team_mappings = kwargs.get('team_mappings', {})
        # HTML 기반 파서들은 html 인자가 필요
        self.kbo_parser = None
        self.mlb_parser = None
        self.npb_parser = None
        self.pitcher_parser = PitcherDataParser(team_mappings=self.team_mappings)
    
    def parse(self, data: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """데이터 파싱 - 기본 인터페이스 구현"""
        if context is None:
            context = {}
        
        league = context.get('league', 'KBO')
        data_type = context.get('data_type', 'team_stats')
        
        if data_type == 'pitcher':
            pitcher_name = context.get('pitcher_name', '')
            return self.pitcher_parser.parse_pitcher_data(data, pitcher_name)
        
        # 팀 통계 파싱 - HTML 데이터와 함께 파서 초기화
        if league.upper() == 'KBO':
            parser = KBOStatsParser(html=data)
            return {
                'season_summary': parser.parse_season_summary(),
                'recent_games': parser.parse_recent_games(),
                'season_stats': parser.parse_season_stats()
            }
        elif league.upper() == 'MLB':
            parser = MLBStatsParser(html=data)
            return {
                'season_summary': parser.parse_season_summary(),
                'recent_games': parser.parse_recent_games(),
                'season_stats': parser.parse_season_stats()
            }
        elif league.upper() == 'NPB':
            parser = NPBStatsParser(html=data)
            return {
                'season_summary': parser.parse_season_summary(),
                'recent_games': parser.parse_recent_games(),
                'season_stats': parser.parse_season_stats()
            }
        else:
            # 기본값으로 KBO 사용
            parser = KBOStatsParser(html=data)
            return {
                'season_summary': parser.parse_season_summary(),
                'recent_games': parser.parse_recent_games(),
                'season_stats': parser.parse_season_stats()
            }
    
    def validate(self, data: str) -> bool:
        """데이터 유효성 검증"""
        return bool(data and len(data.strip()) > 0)