"""
투수 데이터 프로세서 - 데이터 구조화 및 변환 전담
"""
from typing import Any, Dict

from utils.helpers import normalize_innings
from utils.logger import Logger
from utils.service_utils import TeamMappingService

# 로거 설정
logger = Logger(__name__)


class PitcherDataProcessor:
    """투수 데이터 구조화 및 변환 전담 클래스"""
    
    def __init__(self):
        self.team_mapping_service = TeamMappingService()
    
    @staticmethod
    def structure_pitcher_data(
        pitcher_name: str,
        position: str,
        game_role: str,
        pitcher_stats: Dict
    ) -> Dict:
        """
        투수 데이터를 최종 구조로 정리
        
        Args:
            pitcher_name: 투수명
            position: 포지션 (starting_pitcher, relief_pitcher 등)
            game_role: 경기에서의 역할 (home_starter, away_starter)
            pitcher_stats: 파싱된 투수 통계
        
        Returns:
            Dict: 구조화된 투수 데이터
        """
        # 팀 정보 추출
        team_name, league = PitcherDataProcessor._extract_team_info(pitcher_stats)
        
        # 선수 기본 정보 구성
        pitcher_profile = PitcherDataProcessor._build_starter_info(
            pitcher_stats, league, position, game_role, team_name
        )
        
        # 투구 통계 데이터 구성
        pitcher_stats_data = PitcherDataProcessor._build_pitching_stats(pitcher_stats)
        
        return {
            "pitcher_profile": pitcher_profile,
            "pitcher_stats": pitcher_stats_data
        }
    
    @staticmethod
    def _extract_team_info(pitcher_stats: Dict) -> tuple[str, str]:
        """팀 정보 및 리그 추출"""
        team_name = ""
        league = ""
        
        if isinstance(pitcher_stats, dict) and "name" in pitcher_stats:
            team_name = pitcher_stats.get("team", "")
            league = PitcherDataProcessor._get_league_from_team(team_name)
        
        return team_name, league
    
    @staticmethod
    def _build_starter_info(
        pitcher_stats: Dict, 
        league: str, 
        position: str, 
        game_role: str, 
        team_name: str
    ) -> Dict:
        """선수 기본 정보 구성"""
        return {
            "league": league,
            "position": position,
            "game_role": game_role,
            "team": team_name,
            "birth": PitcherDataProcessor._safe_get(pitcher_stats, "birth", ""),
            "height": PitcherDataProcessor._safe_get(pitcher_stats, "height", 0),
            "weight": PitcherDataProcessor._safe_get(pitcher_stats, "weight", ""),
            "no": PitcherDataProcessor._safe_get(pitcher_stats, "no", ""),
            "season_summary": PitcherDataProcessor._safe_get(
                pitcher_stats, "season_summary", {}
            )
        }
    
    @staticmethod
    def _build_pitching_stats(pitcher_stats: Dict) -> Dict:
        """투구 통계 데이터 구성"""
        pitcher_stats_data: Dict[str, Any] = {}

        # ----- 시즌 통계 -----
        pitcher_stats_data["season_stats"] = PitcherDataProcessor._process_season_stats(
                    pitcher_stats.get("season_stats", [])
                )

        # ----- 최근 경기 & 요약 -----
        raw_recent_games = pitcher_stats.get("recent_games", {})
        processed_recent_games = PitcherDataProcessor._process_recent_games(
            raw_recent_games
        )

        n_games = len(processed_recent_games)
        recent_games_key = f"recent_{n_games}_games"
        pitcher_stats_data[recent_games_key] = processed_recent_games

        # 요약
        processed_summary = PitcherDataProcessor._process_recent_games_summary(
                    pitcher_stats.get("recent_games_summary", {})
                )
        summary_key = f"recent_{n_games}_games_summary"
        pitcher_stats_data[summary_key] = processed_summary
        
        return pitcher_stats_data
    
    @staticmethod
    def _safe_get(data: Dict, key: str, default):
        """안전한 딕셔너리 값 추출"""
        if isinstance(data, dict):
            return data.get(key, default)
        return default
    
    @staticmethod
    def _process_season_stats(season_data: list) -> list:
        """시즌 통계 데이터 처리"""
        if not season_data:
            return []
        
        cleaned_season = []
        for stat in season_data:
            if isinstance(stat, dict):
                cleaned_stat = PitcherDataProcessor._clean_stat_dict(stat)
                cleaned_season.append(cleaned_stat)
        
        return cleaned_season
    
    @staticmethod
    def _process_recent_games(recent_games_data: Dict) -> Dict:
        """최근 경기 데이터 처리"""
        if not isinstance(recent_games_data, dict):
            return {}
        
        cleaned_recent_games = {}
        for date_key, game_data in recent_games_data.items():
            if isinstance(game_data, dict):
                cleaned_game = PitcherDataProcessor._clean_stat_dict(game_data)
                cleaned_recent_games[date_key] = cleaned_game
        
        return cleaned_recent_games
    
    @staticmethod
    def _process_recent_games_summary(summary_data: Dict) -> Dict:
        """최근 경기 요약 데이터 처리"""
        if not isinstance(summary_data, dict):
            return {}
        
        cleaned_summary = PitcherDataProcessor._clean_stat_dict(summary_data)
        
        # 필수 필드가 없는 경우 추가
        required_fields = ["pitch_count", "batters_faced", "at_bats"]
        for field in required_fields:
            if field not in cleaned_summary:
                cleaned_summary[field] = ""
        
        return cleaned_summary
    
    @staticmethod
    def _clean_stat_dict(stat_dict: Dict) -> Dict:
        """통계 딕셔너리의 "없음", "No data" 값들을 정리하고 이닝 정규화"""
        cleaned_stat = {}
        for key, value in stat_dict.items():
            if value in ["없음", "No data"]:
                cleaned_stat[key] = ""
            else:
                cleaned_stat[key] = value
        
        # 이닝 필드 정규화 ("25 2" -> "25 2/3" 형태로 변환)
        if 'innings' in cleaned_stat:
            cleaned_stat['innings'] = normalize_innings(cleaned_stat['innings'])
            
        return cleaned_stat
    
    @staticmethod
    def _get_league_from_team(team_name: str) -> str:
        """팀명으로 리그 조회 (DB 기반, 하드코딩 제거)"""
        try:
            # DB에서 팀 정보 조회하여 리그 결정
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                return ""
            
            # 🚀 team_info 테이블에서 리그 정보 직접 조회 (하드코딩 제거)
            response = client.table('team_info').select(
                'league, sportic_league_id'
            ).eq('team_name', team_name).execute()
            
            if response.data and len(response.data) > 0:
                # 1) league 컬럼이 있으면 우선 사용
                league = response.data[0].get('league', '')
                if league:
                    return league.upper()
                
                # 2) sportic_league_id를 그대로 사용
                league_id = response.data[0].get('sportic_league_id', '')
                if league_id:
                    return league_id.upper()
            
            return ""
            
        except Exception as e:
            logger.debug(f"리그 조회 오류 ({team_name}): {e}")
            return ""
    
    @staticmethod
    def create_no_data_stats(pitcher_name: str) -> Dict:
        """No data 케이스를 위한 기본 통계 구조"""
        return {
            'name': pitcher_name,
            'team': '',
            'birth': '',
            'height': 0,
            'weight': '',
            'season_stats': [{"games": "No data"}],
            'recent_games': {}
        }
    
    @staticmethod
    def prepare_for_db_save(
        structured_data: Dict,
        team_info: Dict
    ) -> Dict:
        """DB 저장을 위한 데이터 준비"""
        try:
            # pitcher_profile과 pitcher_stats 분리
            pitcher_profile = structured_data.get('pitcher_profile', {})
            pitcher_stats = structured_data.get('pitcher_stats', {})
            
            # DB 저장용 데이터 구조
            save_data = {
                'pitcher_profile': pitcher_profile,
                'pitcher_stats': pitcher_stats,
                'team_id': team_info.get('team_id', ''),
                'game_date': team_info.get('game_date', '')
            }
            
            return save_data
            
        except Exception as e:
            logger.debug(f"DB 저장 데이터 준비 오류: {e}")
            return {}
    
    @staticmethod
    def clean_stat_values(stats: Dict) -> Dict:
        """통계 값 정리 (없음, No data 등 처리)"""
        cleaned = {}
        
        for key, value in stats.items():
            if value in ["없음", "No data", "-", ""]:
                cleaned[key] = ""
            else:
                cleaned[key] = value
        
        return cleaned

    @staticmethod
    def build_pitcher_data(pitcher_data: Dict[str, Any]) -> Dict[str, Any]:
        """투수 데이터를 구조화합니다."""
        try:
            pitcher_stats = pitcher_data.get("pitcher_data", {})
            if not pitcher_stats:
                return {}

            pitcher_profile = PitcherDataProcessor._build_pitcher_profile(
                pitcher_stats
            )

            pitcher_stats_data = PitcherDataProcessor._build_pitcher_stats(pitcher_stats)

            structured_data = {
                "pitcher_profile": pitcher_profile,
                "pitcher_stats": pitcher_stats_data
            }
            return structured_data
        except Exception as e:
            print(f"투수 데이터 구조화 오류: {e}")
            return {}

    @staticmethod
    def _build_pitcher_profile(
        pitcher_stats: Dict
    ) -> Dict:
        """기본 선수 정보 구성"""
        if not isinstance(pitcher_stats, dict):
            return {}
        
        return {
            "league": pitcher_stats.get("league", ""),
            "team": pitcher_stats.get("team", ""),
            "position": "starting_pitcher",
            "birth": pitcher_stats.get("birth", ""),
            "height": pitcher_stats.get("height", 0),
            "weight": pitcher_stats.get("weight", ""),
            "no": pitcher_stats.get("no", ""),
            "season_summary": pitcher_stats.get("season_summary", {})
        }

    @staticmethod
    def _build_pitcher_stats(pitcher_stats: Dict) -> Dict:
        """투수 통계 정보 추출"""
        pitcher_stats_data: Dict[str, Any] = {}

        # ----- 시즌 통계 -----
        pitcher_stats_data["season_stats"] = PitcherDataProcessor._process_season_stats(
                pitcher_stats.get("season_stats", [])
            )

        # ----- 최근 경기 & 요약 -----
        raw_recent_games = pitcher_stats.get("recent_games", {})
        processed_recent_games = PitcherDataProcessor._process_recent_games(
            raw_recent_games
        )

        n_games = len(processed_recent_games)
        recent_games_key = f"recent_{n_games}_games"
        pitcher_stats_data[recent_games_key] = processed_recent_games

        # 요약
        processed_summary = PitcherDataProcessor._process_recent_games_summary(
                pitcher_stats.get("recent_games_summary", {})
        )
        summary_key = f"recent_{n_games}_games_summary"
        pitcher_stats_data[summary_key] = processed_summary

        return pitcher_stats_data

    @staticmethod
    def process_structured_pitcher_data(structured_data: Dict[str, Any]) -> Dict[str, Any]:
        """구조화된 투수 데이터를 처리"""
        # pitcher_profile과 pitcher_stats 분리
        pitcher_profile = structured_data.get('pitcher_profile', {})
        pitcher_stats_data = structured_data.get('pitcher_stats', {})

        # 데이터 검증
        processed_data = {
            'pitcher_profile': pitcher_profile,
            'pitcher_stats': pitcher_stats_data,
        }
        
        return processed_data

    @staticmethod
    def _convert_pitcher_data(
        structured_data: Dict[str, Any],
        pitcher_name: str,
        match_date: str,
        match_time: str,
        team_id: str
    ) -> Dict[str, Any]:
        """투수 데이터 변환 (DB 저장용)"""
        try:
            # 필수 필드 검사
            if not structured_data or not pitcher_name or not team_id:
                return {}
            
            # pitcher_profile과 pitcher_stats 분리
            pitcher_profile = structured_data.get('pitcher_profile', {})
            pitcher_stats = structured_data.get('pitcher_stats', {})
            
            # pitcher_profile에서 이름 추출 (중첩 구조 고려)
            if 'profile' in pitcher_profile and 'name' in pitcher_profile['profile']:
                actual_pitcher_name = pitcher_profile['profile']['name']
            else:
                actual_pitcher_name = pitcher_name
            
            # pitcher_profile에 최상위 level name 추가
            pitcher_profile['name'] = actual_pitcher_name
            
            return {
                'pitcher_name': actual_pitcher_name,  # Use the actual extracted name
                'match_date': match_date,
                'match_time': match_time,
                'team_id': team_id,
                'pitcher_profile': pitcher_profile,
                'pitcher_stats': pitcher_stats,
            }
        except Exception as e:
            print(f"투수 데이터 변환 오류: {e}")
            return {} 