"""
팀별 통합 처리 서비스 - 팀 통계 + 선발투수 통계 동시 처리
"""
import asyncio
from typing import Dict, List, Optional

import pytz
from supabase import Client

from database.database import save_team_and_pitcher_stats_unified
from sports.baseball.collectors.browser_manager import TeamStatsBrowser
from sports.baseball.collectors.pitcher_crawler import PitcherCrawler
from sports.baseball.services.stats_collector import TeamStatsCollector
from utils.logger import Logger
from utils.service_utils import get_team_mappings

# 로거 설정
logger = Logger(__name__)


class TeamPitcherUnifiedService:
    """팀별 통합 처리 서비스 - 팀 통계 + 선발투수 한 번에 처리"""
    
    def __init__(self):
        self.browser = TeamStatsBrowser()
        self.pitcher_crawler = PitcherCrawler()
        self.team_collector = None
        self._team_mappings = {}
        self.KST = pytz.timezone('Asia/Seoul')
        
    async def process_teams_with_pitchers(
        self,
        games: List[Dict],
        client: Client
    ) -> Dict[str, int]:
        # logger.info(f"🚀 [야구] {len(games)}개 게임의 URL 정보 크롤링 중")
        teams_saved = 0
        pitchers_saved = 0
        try:
            await self._initialize_components()
            if not games:
                # logger.info(f"✅ [야구] 팀 0개, 투수 0명 완료")
                return {'teams_saved': 0, 'pitchers_saved': 0}
            enhanced_games = await self._enhance_games_with_urls(games)
            team_tasks = self._create_team_tasks(enhanced_games)
            results = await self.process_teams_in_batches(team_tasks, client)
            for result in results:
                if result:
                    teams_saved += result.get('team_saved', 0)
                    pitchers_saved += result.get('pitcher_saved', 0)
        finally:
            await self._cleanup_resources()
        logger.info(f"✅ [야구] 통합 저장 완료: 팀 {teams_saved}개, 선발투수 {pitchers_saved}명")
        return {'teams_saved': teams_saved, 'pitchers_saved': pitchers_saved}
    
    async def process_teams_in_batches(
        self, 
        tasks: List[Dict],
        client: Client
    ) -> List[Dict]:
        """🚀 캐시 기반 빠른 팀 처리 (중복은 즉시 스킵, 신규만 처리)"""
        
        results = []
        
        # 🚀 캐시로 즉시 스킵 처리
        tasks_to_process = []
        skipped_count = 0
        
        for task in tasks:
            match_id = task['match_id']
            team_id = task['team_id']
            match_date = task['match_date']
            
            # 🚀 캐시 비활성화 - 항상 DB 체크로 중복 확인
            # from core.cache import get_global_cache_manager
            # cache_manager = get_global_cache_manager()
            # duplicate_cache = cache_manager.get_duplicate_cache()
            
            # if duplicate_cache.is_duplicate_baseball(match_id, team_id, match_date):
            #     skipped_count += 1
            #     results.append({
            #         'team_saved': True, 
            #         'pitcher_saved': True, 
            #         'team_name': task['team_name']
            #     })
            #     continue
            
            tasks_to_process.append(task)
        
        if skipped_count > 0:
            logger.debug(f"⏭️ 캐시 스킵: {skipped_count}개 팀")
        
        if not tasks_to_process:
            logger.info("📋 모든 팀 스킵됨")
            return results
            
        # 🎯 실제 처리가 필요한 팀들만 1개씩 순차 처리
        for idx, task in enumerate(tasks_to_process):
            try:
                result = await self._process_single_team_unified(task, client)
                results.append(result)
                
                # 다음 팀 처리 전 딜레이 (마지막 팀 제외)
                if idx < len(tasks_to_process) - 1:
                    await asyncio.sleep(1)
                    
            except Exception as e:
                team_name = task.get('team_name', 'Unknown')
                logger.warning(f"❌ {team_name} 처리 실패: {e}")
                results.append(None)
        
        processed_count = sum(1 for result in results if result and result.get('team_saved'))
        logger.info(f"🏷️ 야구 팀 처리 완료: {processed_count}/{len(tasks)}개 팀 저장됨")
        
        return results
    
    
    
    async def _process_single_team_unified(
        self, 
        task: Dict, 
        client: Client
    ) -> Optional[Dict]:
        """단일 팀 통합 처리 (팀 통계 + 투수 통계)"""
        team_name = task['team_name']
        team_id = task['team_id']
        match_id = task['match_id']  # 🚀 match_id 추가
        match_date = task.get('match_date', '')
        match_time = task.get('match_time', '')
        pitcher_name = task.get('pitcher_name', '')
        
        try:
            # 🚀 이미 캐시에서 필터링되었으므로 중복 체크 불필요
            
            skip_team_collection = False
            
            # 1. 팀 통계 수집 (중복이 아닌 경우에만)
            team_stats = None
            if not skip_team_collection:
                # League enum 변환
                from config.config import League
                league_obj = task.get('league', '')
                league_enum = None
                
                if league_obj:
                    # 이미 League enum인 경우
                    if hasattr(league_obj, 'value'):
                        league_enum = league_obj
                    else:
                        # 문자열인 경우 변환
                        league_mapping = {
                            'KBO': League.KBO,
                            'MLB': League.MLB,
                            'NPB': League.NPB
                        }
                        league_enum = league_mapping.get(str(league_obj).upper())
                
                if not league_enum:
                    logger.warning(f"❌ {team_name} 알 수 없는 리그: {league_obj}")
                    return None
                
                # URL이 없으면 기본 URL 생성
                team_url = task['team_url']
                if not team_url:
                    from config.config import create_team_url, get_league_id
                    league_id = get_league_id(league_enum)
                    team_url = create_team_url("BS", league_id, task['team_id'])
                    logger.debug(f"🔗 {team_name} URL 생성: {team_url}")
                
                team_stats = await self.team_collector.collect_team_stats(
                    team_id=task['team_id'],
                    team_url=team_url, 
                    team_name=task['team_name'],
                    league=league_enum  # League enum 전달
                )
                
                # 팀 통계 수집 후 대기 시간
                await asyncio.sleep(2)
            
            team_saved = False
            pitcher_saved = False
            
            if team_stats and 'error' not in team_stats:
                
                # 2. 투수 데이터 수집
                pitcher_data = None
                if pitcher_name and pitcher_name.strip():  # 🎯 빈 문자열 및 공백 체크 추가
                    # 팀 통계와 투수 데이터 수집 사이 대기
                    await asyncio.sleep(1)  # 대기 시간
                    pitcher_data = await self._collect_pitcher_data(
                        pitcher_name, 
                        task['team_url'], 
                        task
                    )
                else:
                    
                    if pitcher_data:
                        pass
                
                # 3. 통합 저장
                if task.get('match_time'):
                    team_stats['match_time'] = task.get('match_time')
                
                pitcher_list = [pitcher_data] if pitcher_data else []
                
                saved = save_team_and_pitcher_stats_unified(
                    client=client,
                    team_id=task['team_id'],
                    team_stats=team_stats,
                    match_date=task.get('match_date', ''),
                    pitcher_data_list=pitcher_list
                )
                
                if saved:
                    team_saved = True
                    if pitcher_data:
                        pitcher_saved = True
                    
                    # 🚀 캐시에 새 데이터 추가 (database.py에서 이미 처리됨)
                else:
                    logger.warning(f"❌ {team_name} 통합 저장 실패")
            elif skip_team_collection and pitcher_name and pitcher_name.strip():  # 🎯 빈 문자열 체크 추가
                # 팀 통계는 스킵하고 투수 데이터만 수집하는 경우
                pitcher_data = await self._collect_pitcher_data(
                    pitcher_name, 
                    task['team_url'], 
                    task
                )
                
                if pitcher_data:
                    
                    # 투수 데이터만 저장 (기존 팀 데이터에 추가)
                    pitcher_list = [pitcher_data]
                    saved = save_team_and_pitcher_stats_unified(
                        client=client,
                        team_id=task['team_id'],
                        # 최소한의 데이터
                        team_stats={'match_time': task.get('match_time', '')},
                        match_date=task.get('match_date', ''),
                        pitcher_data_list=pitcher_list
                    )
                    
                    if saved:
                        team_saved = True  # 기존 데이터 활용
                        pitcher_saved = True
                        
                        # 🚀 캐시 비활성화
                        # if hasattr(self, '_cache') and self._cache:
                        #     self._cache.add_baseball(match_id, team_id, match_date)
                    else:
                        logger.warning(f"❌ {team_name} 투수 데이터 저장 실패")
                else:
                    logger.warning(f"❌ {team_name} 투수 데이터 수집 실패")
                    team_saved = True  # 기존 팀 데이터는 있음
            else:
                logger.warning(f"❌ {team_name} 팀 통계 수집 실패")
            
            return {
                'team_saved': team_saved,
                'pitcher_saved': pitcher_saved,
                'team_name': team_name
            }
            
        except Exception as e:
            logger.error(f"❌ {team_name} 통합 처리 실패: {e}")
            return None
    
    async def _collect_pitcher_data(
        self, 
        pitcher_name: str, 
        team_url: str,
        task: Dict
    ) -> Optional[Dict]:
        """선발투수 데이터 수집"""
        pitcher_crawler = None
        try:
            # 독립적인 투수 크롤러 생성
            pitcher_crawler = PitcherCrawler()
            await pitcher_crawler.initialize_browser()
            
            # 투수 크롤러 초기화 후 충분한 대기 시간
            await asyncio.sleep(3)
            
            # 투수 데이터 수집
            pitcher_stats = await pitcher_crawler.crawl_pitcher_stats(
                pitcher_name=pitcher_name,
                team_url=team_url
            )
            
            # 투수 데이터 수집 후 충분한 대기 시간
            await asyncio.sleep(2)
            
            # 🎯 투수 데이터 검증 개선: 더 관대한 검증 (빈 데이터도 허용)
            has_name = bool(pitcher_stats.get('name'))
            has_season_stats = bool(
                pitcher_stats.get('season_stats') and 
                len(pitcher_stats.get('season_stats', [])) > 0
                # 'No data' 조건 제거 - 빈 데이터도 허용
            )
            has_recent_games = bool(
                pitcher_stats.get('recent_5_games') or 
                pitcher_stats.get('recent_games') or
                any(k.startswith('recent_') and k.endswith('_games') 
                    for k in pitcher_stats.keys())
            )
            
            # 🚀 NPB/KBO 투수는 투수명만 있어도 유효 데이터로 처리
            league_obj = task.get('league', '')
            if hasattr(league_obj, 'value'):
                league = league_obj.value.upper()
            else:
                league = str(league_obj).upper()
            if league in ['NPB', 'KBO']:
                is_valid_data = bool(pitcher_name.strip())  # 투수명만 있으면 OK
                logger.debug(
                    f"🎯 {league} 투수 {pitcher_name}: 투수명 기반 처리 "
                    f"(name={has_name}, season={has_season_stats}, "
                    f"recent={has_recent_games})"
                )
            else:
                # MLB는 기존 검증 유지
                is_valid_data = has_name or has_season_stats or has_recent_games
            
            if is_valid_data:
                structured_data = self._structure_pitcher_data(
                    pitcher_name, pitcher_stats, task
                )

                return structured_data
            else:
                name = (
                    pitcher_stats.get('name', 'No Name') 
                    if pitcher_stats else 'No Name'
                )
                recent = (
                    bool(pitcher_stats.get('recent_5_games')) 
                    if pitcher_stats else False
                )
                season = (
                    bool(pitcher_stats.get('season_stats')) 
                    if pitcher_stats else False
                )
                logger.warning(
                    f"❌ {pitcher_name} 투수 데이터 없음 - "
                    f"name: {name}, recent_games: {recent}, "
                    f"season_stats: {season}"
                )
                return None
                
        except Exception as e:
            logger.warning(f"❌ {pitcher_name} 투수 데이터 수집 실패: {e}")
            return None
        finally:
            if pitcher_crawler:
                try:
                    await pitcher_crawler.cleanup()
                except Exception:
                    pass
    
    def _structure_pitcher_data(
        self, 
        pitcher_name: str, 
        pitcher_stats: Dict, 
        task: Dict
    ) -> Dict:
        """투수 데이터 구조화 (개선된 시즌 통계 처리)"""
        position = task.get('position', 'starting_pitcher')
        game_role = task.get('game_role', 'starter')
        team_name = task.get('team_name', '')
        league_obj = task.get('league', '')
        if hasattr(league_obj, 'value'):
            league = league_obj.value
        else:
            league = str(league_obj)
        
        # 🎯 팀 이름 매핑 적용 (리그별 정책: KBO/NPB=짧은이름, MLB=풀네임)
        from database.database import connect_supabase, get_team_display_name
        try:
            client = connect_supabase()
            if client and team_name:
                mapped_team_name = get_team_display_name(
                    client, team_name, league
                )
                if mapped_team_name != team_name:
                    logger.debug(
                        f"🔄 팀명 매핑 ({league}): {team_name} → {mapped_team_name}"
                    )
                    team_name = mapped_team_name
        except Exception as e:
            logger.debug(f"팀명 매핑 실패: {e}")
        
        # 🎯 투수 프로필 구성 (old 코드와 같은 중첩 구조 사용)
        pitcher_profile = {
            'profile': {
                'player_number': self._safe_int(pitcher_stats.get('no', 0)),
                'name': pitcher_stats.get('name', pitcher_name),
                'team_name': team_name,  # 매핑된 팀명 사용
                'birth_date': pitcher_stats.get('birth', ''),
                'height_cm': self._safe_float(pitcher_stats.get('height', 0)),
                'weight_kg': self._safe_int(
                    str(pitcher_stats.get('weight', '')).replace('kg', '')
                ),
                'league': league,
                'position': position,
                'game_role': game_role,
                'season_stats_summary': pitcher_stats.get('season_summary', {})
            }
        }
        
        # 🚀 시즌 통계 검증 및 처리 개선
        season_stats = pitcher_stats.get("season_stats", [])
        
        # 시즌 통계가 빈 배열이거나 무효한 경우 처리
        if not season_stats or (
            isinstance(season_stats, list) and len(season_stats) == 0
        ):
            logger.warning(
                f"⚠️ {pitcher_name} 시즌 통계가 비어있음 - 기본 구조 생성"
            )
            season_stats = [{
                "year": "2025",
                "games": "데이터 없음",
                "wins": "0",
                "losses": "0", 
                "era": "0.00",
                "innings": "0.0"
            }]
        
        # 🎯 투수 통계 구성 (season_stats 맨 앞 배치)
        pitcher_stats_data = {}
        
        # 1. 시즌 통계 (맨 앞)
        pitcher_stats_data["season_stats"] = season_stats
        
        # 2. 최근 경기 데이터 처리 (중복 완전 제거)
        # 2-1. 동적 키 우선 처리 (recent_N_games, recent_N_games_summary)
        dynamic_keys = [
            k for k in pitcher_stats.keys() 
            if k.startswith('recent_') and ('_games' in k) 
            and k not in ['recent_games', 'recent_games_summary']  # 기존 키 제외
        ]
        
        # 동적 키 데이터 복사
        for key in dynamic_keys:
            pitcher_stats_data[key] = pitcher_stats[key]
        
        # 2-2. 기존 recent_games 처리 (중복 방지)
        if 'recent_games' in pitcher_stats:
            recent_games = pitcher_stats["recent_games"]
            n_games = len(recent_games) if isinstance(recent_games, dict) else 0
            if recent_games and n_games > 0:
                recent_games_key = f"recent_{n_games}_games"
                # 동적 키로 이미 처리된 경우 스킵
                if recent_games_key not in pitcher_stats_data:
                    pitcher_stats_data[recent_games_key] = recent_games
        
        # 2-3. recent_games_summary 처리 (recent_5_games_summary 등)
        if 'recent_games_summary' in pitcher_stats:
            recent_summary = pitcher_stats["recent_games_summary"]
            # recent_games와 매칭하여 N 값 계산
            if 'recent_games' in pitcher_stats:
                recent_games = pitcher_stats["recent_games"]
                n_games = len(recent_games) if isinstance(recent_games, dict) else 5
                summary_key = f"recent_{n_games}_games_summary"
            else:
                summary_key = "recent_5_games_summary"  # 기본값
            
            # 동적 키로 이미 처리된 경우가 아니면 추가
            if summary_key not in pitcher_stats_data and recent_summary:
                pitcher_stats_data[summary_key] = recent_summary
        
        # 📊 데이터 검증 로그
        has_season = bool(season_stats and len(season_stats) > 0)
        has_recent = bool(
            dynamic_keys or 
            pitcher_stats.get('recent_games') or
            pitcher_stats.get('recent_games_summary')
        )
        logger.debug(
            f"📊 {pitcher_name} 데이터 구조화 완료 - "
            f"시즌기록: {has_season}, 최근경기: {has_recent}"
        )
        
        return {
            "pitcher_profile": pitcher_profile,
            "pitcher_stats": pitcher_stats_data,
            "league": league  # 🎯 NPB 데이터 처리를 위한 리그 정보 추가
        }
    
    def _safe_int(self, value, default=0):
        """안전한 정수 변환"""
        try:
            if value is None or value == '':
                return default
            return int(float(str(value)))
        except (ValueError, TypeError):
            return default
    
    def _safe_float(self, value, default=0.0):
        """안전한 실수 변환"""
        try:
            if value is None or value == '':
                return default
            return float(str(value))
        except (ValueError, TypeError):
            return default
    
    def _create_team_tasks(self, games: List[Dict]) -> List[Dict]:
        """팀별 작업 목록 생성"""
        team_tasks = []
        processed_teams = set()
        
        for game in games:
            # 홈팀 처리
            home_task = self._create_team_task(game, 'home')
            if home_task:
                team_key = f"{home_task['team_id']}_{home_task['match_date']}"
                if team_key not in processed_teams:
                    team_tasks.append(home_task)
                    processed_teams.add(team_key)
            
            # 원정팀 처리
            away_task = self._create_team_task(game, 'away')
            if away_task:
                team_key = f"{away_task['team_id']}_{away_task['match_date']}"
                if team_key not in processed_teams:
                    team_tasks.append(away_task)
                    processed_teams.add(team_key)
        
        # 중복 로그 제거 - process_teams_with_pitchers에서 이미 출력함
        return team_tasks
    
    def _create_team_task(self, game: Dict, team_type: str) -> Optional[Dict]:
        """팀 작업 정보 생성"""
        team_name = game.get(f'{team_type}_team', '')
        team_url = game.get(f'{team_type}_team_url', '')
        team_id = game.get(f'{team_type}_team_id', '')
        # 🚀 투수명은 무조건 크롤링된 URL 데이터에서만 가져오기
        pitcher_name = game.get(f'{team_type}_pitcher', '')
        match_id = game.get('match_id', '')  # 🚀 match_id 추가
        game_status = game.get('game_status', '')  # 🚀 게임 상태 추가
        
        if not match_id:
            logger.error(f"❌ {team_name} match_id 누락 - 게임 데이터: {list(game.keys())}")
            return None
        
        # 🚀 경기취소된 경기는 task 생성하지 않음
        if game_status == '경기취소':
            logger.info(f"🚫 경기취소로 태스크 생성 스킵: {team_name} ({match_id})")
            return None
        
        # 🚀 URL이 없어도 팀 통계는 수집 가능하도록 수정 (크롤러에서 URL 가져옴)
        if not (team_name and team_id):
            return None
        
        return {
            'team_name': team_name,
            'team_url': team_url,
            'team_id': team_id,
            'match_id': match_id,  # 🚀 match_id 추가
            'pitcher_name': pitcher_name,  # 🚀 크롤링된 데이터에서 가져온 투수명 사용
            'league': game.get('league'),
            'match_date': game.get('match_date') or game.get('date'),
            'match_time': game.get('match_time') or game.get('time'),
            'position': 'starting_pitcher',
            'game_role': f'{team_type}_starter',
            'game_status': game_status  # 🚀 게임 상태 추가
        }
    
    async def _initialize_components(self) -> bool:
        """컴포넌트 초기화"""
        try:
            # 팀 매핑 정보 로드
            self._team_mappings = await get_team_mappings()
            
            # 브라우저 초기화
            if not await self.browser.initialize():
                logger.error("❌ 브라우저 초기화 실패")
                return False
            
            # 팀 통계 수집기 초기화
            self.team_collector = TeamStatsCollector(
                self.browser, self._team_mappings
            )
            
            # 🚀 캐시 비활성화 - DB 체크만 사용
            # from core.cache import get_global_cache_manager
            # cache_manager = get_global_cache_manager()
            # await cache_manager.initialize()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 컴포넌트 초기화 실패: {e}")
            return False
    
    async def _cleanup_resources(self):
        """리소스 정리"""
        try:
            # 브라우저 정리
            if self.browser:
                await self.browser.cleanup()
            
            # 투수 크롤러 정리
            if (self.pitcher_crawler and 
                    hasattr(self.pitcher_crawler, 'cleanup')):
                await self.pitcher_crawler.cleanup()
            
            # 메모리 정리
            import gc
            gc.collect()
            
            
        except Exception as e:
            logger.warning(f"⚠️ 리소스 정리 중 오류: {e}")

    async def _enhance_games_with_urls(self, games: List[Dict]) -> List[Dict]:
        """URL이 없는 게임에 대해 크롤러를 통해 URL 정보 가져오기"""
        try:
            # URL이 없는 게임이 있는지 확인
            missing_url_games = []
            for game in games:
                home_url = game.get('home_team_url', '')
                away_url = game.get('away_team_url', '')
                if not home_url or not away_url:
                    missing_url_games.append(game)
            
            if not missing_url_games:
                return games  # 모든 게임에 URL이 있음
            
            # logger.info(f"🔍 {len(missing_url_games)}개 게임의 URL 정보 크롤링 중...")
            
            # 크롤러를 통해 URL 정보 수집
            from database.database import connect_supabase
            from sports.baseball.collectors.unified_crawler import \
                UnifiedDataCollector
            
            crawler_client = connect_supabase()
            crawler = UnifiedDataCollector(client=crawler_client)
            
            try:
                # 타겟 게임 크롤링하여 URL 정보 가져오기
                crawled_games = await crawler._crawl_target_games(missing_url_games)
                
                # 크롤링된 데이터로 URL 정보 업데이트
                enhanced_games = []
                for game in games:
                    enhanced_game = game.copy()
                    
                    # 매칭되는 크롤링된 게임 찾기
                    for crawled_game in crawled_games:
                        if (game.get('match_id') == crawled_game.get('match_id') or
                            (game.get('home_team_id') == crawled_game.get('home_team_id') and
                             game.get('away_team_id') == crawled_game.get('away_team_id') and
                             game.get('match_date') == crawled_game.get('date'))):
                            
                            # URL 정보 업데이트
                            if not enhanced_game.get('home_team_url'):
                                enhanced_game['home_team_url'] = crawled_game.get('home_team_url', '')
                            if not enhanced_game.get('away_team_url'):
                                enhanced_game['away_team_url'] = crawled_game.get('away_team_url', '')
                            
                            # 투수 정보도 업데이트 (빈 문자열도 덮어쓰기)
                            enhanced_game['home_pitcher'] = crawled_game.get('home_pitcher', '')
                            enhanced_game['away_pitcher'] = crawled_game.get('away_pitcher', '')
                            break
                    
                    enhanced_games.append(enhanced_game)
                
                return enhanced_games
                
            finally:
                # 크롤러 정리
                if hasattr(crawler, 'cleanup'):
                    await crawler.cleanup()
                elif hasattr(crawler, '_cleanup_browser'):
                    await crawler._cleanup_browser()
                else:
                    # UnifiedDataCollector는 자체 브라우저 관리
                    pass
                
        except Exception as e:
            logger.warning(f"⚠️ URL 정보 크롤링 실패: {e}")
            return games  # 실패해도 원본 게임 데이터 반환
    
