"""
데이터 포맷팅 및 정리 모듈
"""
from typing import Any

from database.config import DatabaseConfig


class NumberFormatter:
    """숫자 포맷팅 전담 클래스"""
    
    @staticmethod
    def format_number(key: str, value: Any) -> Any:
        """숫자를 문자열로 변환하여 반환.

        * ERA/타율 등 `DECIMAL_FIELDS` 는 소수점 2자리 고정
        * 그 외 숫자는 정수면 정수로, 실수면 실수로 표시
        """
        # 숫자 혹은 숫자 형태의 문자열 모두 처리
        if isinstance(value, (int, float)):
            if key in DatabaseConfig.DECIMAL_FIELDS:
                return f"{float(value):.2f}"
            if isinstance(value, int) or (isinstance(value, float) and value.is_integer()):
                return str(int(value))
            return str(value)
        elif isinstance(value, str):
            try:
                num_val = float(value)
                if key in DatabaseConfig.DECIMAL_FIELDS:
                    return f"{num_val:.2f}"
                if num_val.is_integer():
                    return str(int(num_val))
                return str(num_val)
            except (ValueError, TypeError):
                return value
        return value

    @staticmethod
    def normalize_numbers(obj: Any) -> Any:
        """dict/list 내부 모든 숫자를 문자열로 재귀 변환."""
        if isinstance(obj, dict):
            return {k: NumberFormatter.format_number(k, NumberFormatter.normalize_numbers(v)) for k, v in obj.items()}
        if isinstance(obj, list):
            return [NumberFormatter.normalize_numbers(item) for item in obj]
        return obj


class NPBDataCleaner:
    """NPB 데이터 정리 전담 클래스"""
    
    @staticmethod
    def clean_npb_stats(obj: Any) -> Any:
        """NPB 데이터에서 사용하지 않는 키(0 값) 제거."""
        if isinstance(obj, dict):
            return {
                k: NPBDataCleaner.clean_npb_stats(v)
                for k, v in obj.items()
                if not (k in DatabaseConfig.UNWANTED_NPB_KEYS and str(v) in {"0", "0.0", "0.00", ""})
            }
        if isinstance(obj, list):
            return [NPBDataCleaner.clean_npb_stats(item) for item in obj]
        return obj