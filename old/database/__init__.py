"""
데이터베이스 패키지 - 새로운 모듈화된 구조만 사용
"""

# 새로운 모듈화된 클래스들 (절대 경로 사용)
from database.config import DatabaseConfig
from database.formatters import Number<PERSON><PERSON>att<PERSON>, NPBDataCleaner
from database.checkers import BatchDuplicate<PERSON>hecker
from database.resolvers import TeamNameResolver
from database.services import DatabaseSaveService, TeamStatsDataService, PitcherStatsProcessor

# 핵심 함수들 (새로운 구현 기반)
from database.database import (
    connect_supabase,
    check_duplicate_baseball_stats,
    get_team_mappings,
    get_team_display_name,
    get_team_full_name,
    normalize_pitcher_stats,
    normalize_numbers,  # 하위 호환성
    check_batch_duplicate_baseball_stats,
    check_batch_duplicate_soccer_stats,
    save_team_and_pitcher_stats_unified,
    get_team_stats_data_from_target_games,
    get_target_games_by_sport,
    BASEBALL_TEAM_STATS_TABLE,
    SOCCER_TEAM_STATS_TABLE
)

# 주로 사용할 새로운 API
def save_team_and_pitcher_data(client, team_id, team_stats, pitcher_data_list=None, match_date=None):
    """새로운 모듈화된 저장 함수 (권장)"""
    service = DatabaseSaveService(client)
    return service.save_team_and_pitcher_data(team_id, team_stats, pitcher_data_list, match_date)

def get_team_resolver(client):
    """팀명 해결기 인스턴스 반환"""
    return TeamNameResolver(client)

def get_data_service(client):
    """팀 데이터 서비스 인스턴스 반환"""
    return TeamStatsDataService(client)

__all__ = [
    # 🏗️ 새로운 모듈화된 클래스들 (메인 API)
    'DatabaseConfig',
    'NumberFormatter',
    'NPBDataCleaner',
    'BatchDuplicateChecker',
    'TeamNameResolver',
    'DatabaseSaveService',
    'TeamStatsDataService',
    'PitcherStatsProcessor',
    
    # 🔌 연결 및 핵심 함수들
    'connect_supabase',
    'check_duplicate_baseball_stats',
    'get_team_mappings',
    'get_team_display_name',
    'get_team_full_name',
    'normalize_pitcher_stats',
    'normalize_numbers',  # 하위 호환성
    'check_batch_duplicate_baseball_stats', 
    'check_batch_duplicate_soccer_stats',
    'save_team_and_pitcher_stats_unified',
    'get_team_stats_data_from_target_games',
    'get_target_games_by_sport',
    
    # 🚀 새로운 권장 API
    'save_team_and_pitcher_data',
    'get_team_resolver',
    'get_data_service',
    
    # 📊 상수
    'BASEBALL_TEAM_STATS_TABLE',
    'SOCCER_TEAM_STATS_TABLE'
]