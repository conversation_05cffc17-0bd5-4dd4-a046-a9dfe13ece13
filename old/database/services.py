"""
데이터베이스 서비스 모듈
"""
from datetime import datetime
from typing import Any, Dict, List

from supabase import Client

from database.config import DatabaseConfig
from database.formatters import NPB<PERSON><PERSON><PERSON>leaner, NumberFormatter
from utils.logger import Logger

logger = Logger(__name__)


class TeamStatsDataService:
    """팀 통계 데이터 처리 전담 클래스"""
    
    def __init__(self, client: Client):
        self.client = client
    
    def get_target_game_data(self, team_id: str, match_date: str, match_time: str = "") -> Dict[str, Any]:
        """target_games에서 팀 데이터 조회"""
        try:
            # 이 함수는 기존 database.py의 get_team_stats_data_from_target_games 함수 호출
            # 실제 구현은 database.py에서 가져와야 함
            from database.database import get_team_stats_data_from_target_games
            return get_team_stats_data_from_target_games(
                self.client, team_id, match_date, match_time
            )
        except Exception as e:
            logger.error(f"target_games 데이터 조회 실패: {e}")
            return {'found_in_target_games': False}
    
    def check_existing_data(self, match_id: str, team_id: str, match_date: str) -> Dict[str, Any]:
        """기존 데이터 확인 및 조회"""
        from core.cache import get_global_cache_manager
        
        cache_manager = get_global_cache_manager()
        duplicate_cache = cache_manager.get_duplicate_cache()
        
        if not duplicate_cache.is_duplicate_baseball(match_id, team_id, match_date):
            return {'exists': False, 'data': None}
        
        try:
            response = (
                self.client.table(DatabaseConfig.BASEBALL_TEAM_STATS_TABLE)
                .select('pitcher_stats, pitcher_profile')
                .eq('match_id', match_id)
                .eq('team_id', team_id)
                .eq('match_date', match_date)
                .execute()
            )
            
            if response.data and len(response.data) > 0:
                return {'exists': True, 'data': response.data[0]}
            
        except Exception as e:
            logger.error(f"기존 데이터 조회 실패: {e}")
        
        return {'exists': False, 'data': None}


class PitcherStatsProcessor:
    """투수 통계 처리 전담 클래스"""
    
    @staticmethod
    def process_pitcher_data_list(
        pitcher_data_list: List[Dict[str, Any]], league: str = None
    ) -> Dict[str, Any]:
        """투수 데이터 리스트 처리 (올바른 구조 처리)"""
        # 🚀 투수 데이터가 없거나 빈 리스트인 경우 None으로 처리
        if not pitcher_data_list:
            logger.debug("투수 데이터 없음 - None으로 처리")
            return {'pitcher_stats': None, 'pitcher_profile': None}
        
        combined_stats = {}
        combined_profile = {}
        
        for pitcher_data in pitcher_data_list:
            if not isinstance(pitcher_data, dict):
                continue
            
            # NPB 데이터 정리
            if league and league.upper() == 'NPB':
                pitcher_data = NPBDataCleaner.clean_npb_stats(pitcher_data)
            
            # 숫자 정규화
            pitcher_data = NumberFormatter.normalize_numbers(pitcher_data)
            
            # 🚀 올바른 구조 처리: 이미 구조화된 데이터 사용
            if 'pitcher_profile' in pitcher_data:
                profile_data = pitcher_data['pitcher_profile']
                if isinstance(profile_data, dict):
                    combined_profile.update(profile_data)
            
            if 'pitcher_stats' in pitcher_data:
                stats_data = pitcher_data['pitcher_stats']
                if isinstance(stats_data, dict):
                    # 🎯 recent_games의 opponent_team 필드에 팀명 매핑 적용
                    stats_data = PitcherStatsProcessor._apply_team_name_mapping(
                        stats_data, league
                    )
                    combined_stats.update(stats_data)
            
            # 🎯 구조화되지 않은 데이터에 대한 호환성 처리
            if ('pitcher_profile' not in pitcher_data and 
                'pitcher_stats' not in pitcher_data):
                # 구조화되지 않은 데이터인 경우 기존 로직 사용
                profile_fields = [
                    'name', 'league', 'position', 'game_role', 'height_cm', 
                    'weight_kg', 'birth_date', 'player_number', 'team_name'
                ]
                
                pitcher_profile = {
                    k: v for k, v in pitcher_data.items() 
                    if k in profile_fields
                }
                pitcher_stats = {
                    k: v for k, v in pitcher_data.items() 
                    if k not in profile_fields
                }
                
                if pitcher_profile:
                    combined_profile.update(pitcher_profile)
                if pitcher_stats:
                    # 🎯 구조화되지 않은 데이터에도 팀명 매핑 적용
                    pitcher_stats = PitcherStatsProcessor._apply_team_name_mapping(
                        pitcher_stats, league
                    )
                    combined_stats.update(pitcher_stats)
        
        # 🚀 실제 데이터가 있는 경우에만 반환, 없으면 None
        final_profile = combined_profile if combined_profile else None
        final_stats = combined_stats if combined_stats else None
        
        logger.debug(
            f"투수 데이터 처리 완료: profile={bool(final_profile)}, "
            f"stats={bool(final_stats)}"
        )
        return {'pitcher_stats': final_stats, 'pitcher_profile': final_profile}

    @staticmethod
    def _apply_team_name_mapping(
        pitcher_stats: Dict[str, Any], league: str = None
    ) -> Dict[str, Any]:
        """투수 통계의 recent_games에서 opponent_team 필드에 팀명 매핑 적용"""
        try:
            from database.database import (connect_supabase,
                                           get_team_display_name)
            
            client = connect_supabase()
            if not client:
                return pitcher_stats
            
            # recent_N_games 필드들을 찾아서 opponent_team 매핑
            for key, value in pitcher_stats.items():
                if (key.startswith('recent_') and key.endswith('_games') and 
                    'summary' not in key and isinstance(value, dict)):
                    
                    for date_key, game_data in value.items():
                        if (isinstance(game_data, dict) and 
                            'opponent_team' in game_data):
                            original_team = game_data['opponent_team']
                            if original_team:
                                # 🎯 팀명 매핑 적용 (MLB는 풀네임, KBO/NPB는 짧은이름)
                                mapped_team = get_team_display_name(
                                    client, original_team, league
                                )
                                if mapped_team != original_team:
                                    logger.debug(
                                        f"🔄 투수 데이터 팀명 매핑 ({league}): "
                                        f"{original_team} → {mapped_team}"
                                    )
                                    game_data['opponent_team'] = mapped_team
            
            return pitcher_stats
            
        except Exception as e:
            logger.debug(f"투수 데이터 팀명 매핑 실패: {e}")
            return pitcher_stats


class DatabaseSaveService:
    """데이터베이스 저장 전담 클래스 (SOLID 원칙 적용)"""
    
    def __init__(self, client: Client):
        self.client = client
        self.team_service = TeamStatsDataService(client)
        self.pitcher_processor = PitcherStatsProcessor()
    
    def save_team_and_pitcher_data(self, team_id: str, team_stats: Dict[str, Any], 
                                   pitcher_data_list: List[Dict[str, Any]] = None, 
                                   match_date: str = None) -> bool:
        """팀 통계와 투수 통계를 통합 저장 (SRP 적용)"""
        try:
            # 1. 기본 데이터 준비
            if match_date is None:
                match_date = datetime.now().strftime("%Y-%m-%d")
            
            match_time = team_stats.get("match_time", "")
            
            # 2. target_games에서 데이터 조회
            target_data = self.team_service.get_target_game_data(team_id, match_date, match_time)
            
            if not target_data['found_in_target_games']:
                # 디버그용: 해당 날짜의 모든 W게임 조회
                available_games = self._get_available_w_games(team_id, match_date)
                logger.warning(f"❌ target_games에서 W-type 경기를 찾을 수 없음")
                logger.warning(f"   - team_id: {team_id}")
                logger.warning(f"   - match_date: {match_date}")
                logger.warning(f"   - match_time: {match_time}")
                logger.warning(f"   - 해당 팀의 W-type 경기 목록: {available_games}")
                return False
            
            match_id = target_data['match_id']
            league = target_data.get('league', '')
            
            # 3. 기존 데이터 확인 (데이터베이스에서만 확인)
            existing_result = self.team_service.check_existing_data(match_id, team_id, match_date)
            
            # 4. 투수 데이터 처리
            processed_pitchers = self.pitcher_processor.process_pitcher_data_list(pitcher_data_list, league)
            
            # 5. 데이터 저장
            return self._save_to_database(match_id, team_id, match_date, match_time, 
                                        team_stats, processed_pitchers, existing_result, 
                                        target_data)
        
        except Exception as e:
            logger.error(f"팀/투수 데이터 저장 실패: {e}")
            return False
    
    def _save_to_database(self, match_id: str, team_id: str, match_date: str, match_time: str,
                         team_stats: Dict[str, Any], processed_pitchers: Dict[str, Any],
                         existing_result: Dict[str, Any], target_data: Dict[str, Any]) -> bool:
        """실제 데이터베이스 저장 로직 (OCP 적용)"""
        try:
            # team_role 결정 및 team_name 매핑
            if target_data.get('home_team_id') == team_id:
                team_role = 'home'
                team_name = target_data.get('home_team_name', '')
            else:
                team_role = 'away'
                team_name = target_data.get('away_team_name', '')
            
            # sportic_team_id 조회
            sportic_team_id = self._get_sportic_team_id(team_id)
            
            # 게임 타입 정보 로깅
            game_type = target_data.get('game_type', '')
            if game_type:
                logger.debug(f"🎯 game_type 저장: {team_name} - {game_type}")
            
            # 저장할 데이터 구성 - 원래 구조로 복원
            save_data = {
                'id': f"{match_id}_{team_id}",
                'match_id': match_id,
                'team_id': team_id,
                'sportic_team_id': sportic_team_id,
                'match_date': match_date,
                'match_time': match_time,
                'sports': target_data.get('sports', 'baseball'),
                'league': target_data.get('league', ''),
                'game_type': game_type,  # 🚀 게임 타입 추가 (W, W1 등)
                'team_role': team_role,
                'team_name': team_name,
                # 🚀 None 값 그대로 저장 (빈 딕셔너리 대신)
                'pitcher_profile': processed_pitchers.get('pitcher_profile'),
                'pitcher_stats': processed_pitchers.get('pitcher_stats'),
                'season_stats': team_stats.get('season_stats', {}),
                'season_summary': team_stats.get('season_summary', {}),
                'recent_games': team_stats.get('recent_games', {}),
                'recent_games_summary': team_stats.get('recent_games_summary', {})
            }
            
            # 기존 데이터가 있으면 업데이트, 없으면 삽입
            if existing_result['exists']:
                response = (
                    self.client.table(DatabaseConfig.BASEBALL_TEAM_STATS_TABLE)
                    .update(save_data)
                    .eq('match_id', match_id)
                    .eq('team_id', team_id)
                    .eq('match_date', match_date)
                    .execute()
                )
            else:
                response = (
                    self.client.table(DatabaseConfig.BASEBALL_TEAM_STATS_TABLE)
                    .insert(save_data)
                    .execute()
                )
            
            # 성공 여부 확인 및 캐시 업데이트
            if response.data:
                from core.cache import get_global_cache_manager
                cache_manager = get_global_cache_manager()
                duplicate_cache = cache_manager.get_duplicate_cache()
                duplicate_cache.add_baseball(match_id, team_id, match_date)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"데이터베이스 저장 실패: {e}")
            return False
    
    def _get_available_w_games(self, team_id: str, match_date: str) -> List[str]:
        """디버그용: 해당 팀/날짜의 사용 가능한 W-type 게임 목록 조회"""
        try:
            from supabase import create_client
            import os
            
            url = os.environ.get('SUPABASE_URL')
            key = os.environ.get('SUPABASE_KEY')
            if not url or not key:
                return ["DB 연결 실패"]
                
            client = create_client(url, key)
            team_filter = f"home_team_id.eq.{team_id},away_team_id.eq.{team_id}"
            response = client.table('target_games').select('id, match_time, home_team_name, away_team_name').or_(team_filter).eq('match_date', match_date).eq('game_type', 'W').execute()
            
            if response.data:
                return [f"{game['id']}({game.get('match_time', 'No_Time')})" for game in response.data]
            else:
                return ["해당 날짜에 W-type 게임 없음"]
        except Exception as e:
            return [f"조회 실패: {str(e)}"]
    
    def _get_sportic_team_id(self, team_id: str) -> str:
        """team_info 테이블에서 sportic_team_id 조회"""
        try:
            response = (
                self.client.table('team_info')
                .select('sportic_team_id')
                .eq('team_id', team_id)
                .execute()
            )
            
            if response.data and len(response.data) > 0:
                return response.data[0].get('sportic_team_id', team_id)
            else:
                logger.warning(f"team_info에서 team_id {team_id} 찾을 수 없음, team_id 사용")
                return team_id
                
        except Exception as e:
            logger.error(f"sportic_team_id 조회 실패 ({team_id}): {e}")
            return team_id