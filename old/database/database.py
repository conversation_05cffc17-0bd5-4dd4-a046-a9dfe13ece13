"""
통합 Supabase 데이터베이스 연결 및 쿼리 유틸리티 - 모듈화된 새 버전
"""
from __future__ import annotations

from datetime import datetime
from typing import Any, Dict, List, Optional

from supabase import Client, create_client

from config.config import SUPABASE_KEY, SUPABASE_URL
from database.config import DatabaseConfig
from database.formatters import <PERSON><PERSON><PERSON><PERSON><PERSON>, NPBDataCleaner
from database.checkers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from database.resolvers import TeamNameResolver
from database.services import DatabaseSaveService, TeamStatsDataService, PitcherStatsProcessor
from utils.helpers import normalize_innings_in_dict
from utils.logger import Logger

# 테이블 상수 (하위 호환성을 위해 유지)
BASEBALL_TEAM_STATS_TABLE = DatabaseConfig.BASEBALL_TEAM_STATS_TABLE
SOCCER_TEAM_STATS_TABLE = DatabaseConfig.SOCCER_TEAM_STATS_TABLE

logger = Logger(__name__)


def connect_supabase() -> Client:
    """Supabase 클라이언트 연결 초기화"""
    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError("Supabase URL과 API 키를 설정해야 합니다.")
    return create_client(SUPABASE_URL, SUPABASE_KEY)


def check_duplicate_baseball_stats(
    client: Client,
    match_id: str,
    team_id: str,
    match_date: str,
    check_type: str = "team",
    pitcher_name: Optional[str] = None
) -> bool:
    """
    baseball_stats 테이블에서 중복 데이터 체크 (간단화됨)
    
    Args:
        client: Supabase 클라이언트
        match_id: 경기 ID
        team_id: 팀 ID
        match_date: 경기 날짜
        check_type: 체크 타입 (사용하지 않음 - 호환성 유지)
        pitcher_name: 투수명 (사용하지 않음 - 호환성 유지)
    
    Returns:
        bool: 중복 데이터 존재 여부 (True: 중복 존재, False: 중복 없음)
    """
    try:
        # 간단한 중복 체크: match_id, team_id, match_date만 확인
        response = (
            client.table(BASEBALL_TEAM_STATS_TABLE)
            .select('id')
            .eq('match_id', match_id)
            .eq('team_id', team_id)
            .eq('match_date', match_date)
            .execute()
        )
        
        return bool(response.data and len(response.data) > 0)
        
    except Exception as e:
        logger.error(f"중복 체크 실패: {e}")
        return False


def get_team_mappings(client: Client) -> Dict[str, str]:
    """team_info 테이블에서 team_name -> team_full_name 매핑 가져오기"""
    resolver = TeamNameResolver(client)
    return resolver.get_team_mappings()


def get_team_display_name(client: Client, team_name: str, league: Optional[str] = None) -> str:
    """리그에 따라 적절한 팀명을 반환"""
    resolver = TeamNameResolver(client)
    return resolver.get_team_display_name(team_name, league)


def get_team_full_name(client: Client, team_name: str, league: Optional[str] = None) -> str:
    """team_name으로 team_full_name 조회"""
    resolver = TeamNameResolver(client)
    return resolver.get_team_full_name(team_name, league)


def normalize_pitcher_stats(pitcher_stats: Dict[str, Any]) -> Dict[str, Any]:
    """
    투수 통계 데이터를 정규화합니다.
    - "없음", "No data" 값들을 빈 문자열로 변환
    - 이닝 필드 정규화 ("25 2" -> "25 2/3" 형태로 변환)
    - 숫자 포맷팅 적용
    """
    if not isinstance(pitcher_stats, dict):
        return pitcher_stats
    
    # 기본 정규화
    normalized_stats = {}
    
    for key, value in pitcher_stats.items():
        # 중첩된 딕셔너리 처리
        if isinstance(value, dict):
            normalized_value = {}
            for nested_key, nested_value in value.items():
                if nested_value in ["없음", "No data", None]:
                    normalized_value[nested_key] = ""
                else:
                    # 이닝 정규화
                    if nested_key == 'innings':
                        normalized_value[nested_key] = normalize_innings_in_dict({nested_key: nested_value})[nested_key]
                    else:
                        normalized_value[nested_key] = nested_value
            normalized_stats[key] = normalized_value
        
        # 리스트 처리
        elif isinstance(value, list):
            normalized_list = []
            for item in value:
                if isinstance(item, dict):
                    normalized_item = {}
                    for item_key, item_value in item.items():
                        if item_value in ["없음", "No data", None]:
                            normalized_item[item_key] = ""
                        else:
                            # 이닝 정규화
                            if item_key == 'innings':
                                normalized_item[item_key] = normalize_innings_in_dict({item_key: item_value})[item_key]
                            else:
                                normalized_item[item_key] = item_value
                    normalized_list.append(normalized_item)
                else:
                    normalized_list.append(item)
            normalized_stats[key] = normalized_list
        
        # 기본 값 처리
        else:
            if value in ["없음", "No data", None]:
                normalized_stats[key] = ""
            else:
                normalized_stats[key] = value
    
    # 숫자 정규화 적용
    return NumberFormatter.normalize_numbers(normalized_stats)


def check_batch_duplicate_baseball_stats(combinations: List[tuple]) -> set:
    """배치 야구 통계 중복 확인"""
    return BatchDuplicateChecker.check_batch_duplicate_baseball_stats(combinations)


def check_batch_duplicate_soccer_stats(combinations: List[tuple]) -> set:
    """배치 축구 통계 중복 확인"""
    return BatchDuplicateChecker.check_batch_duplicate_soccer_stats(combinations)


def save_team_and_pitcher_stats_unified(
    client: Client,
    team_id: str,
    team_stats: Dict[str, Any],
    pitcher_data_list: Optional[List[Dict[str, Any]]] = None,
    match_date: Optional[str] = None
) -> bool:
    """
    팀 통계와 투수 통계를 한 번에 저장 (새로운 모듈화된 구현)
    """
    service = DatabaseSaveService(client)
    return service.save_team_and_pitcher_data(team_id, team_stats, pitcher_data_list, match_date)


def get_team_stats_data_from_target_games(
    client: Client,
    team_id: str,
    match_date: str,
    match_time: str = ""
) -> Dict[str, Any]:
    """
    target_games 테이블에서 팀 통계 데이터 조회
    
    Args:
        client: Supabase 클라이언트
        team_id: 팀 ID
        match_date: 경기 날짜
        match_time: 경기 시간 (선택적)
    
    Returns:
        Dict: 조회된 게임 데이터 또는 빈 결과
    """
    try:
        # 기본 쿼리 구성
        query = client.table('target_games').select('*')
        
        # 팀 ID 조건 (home_team_id 또는 away_team_id)
        team_filter = f"home_team_id.eq.{team_id},away_team_id.eq.{team_id}"
        query = query.or_(team_filter)
        
        # 날짜 조건
        query = query.eq('match_date', match_date)
        
        # 시간 조건 (제공된 경우)
        if match_time:
            query = query.eq('match_time', match_time)
        
        # 게임 타입 조건 (W 타입만)
        query = query.eq('game_type', 'W')
        
        response = query.execute()
        
        if response.data and len(response.data) > 0:
            game_data = response.data[0]
            return {
                'found_in_target_games': True,
                'match_id': game_data.get('id', ''),
                'league': game_data.get('league', ''),
                'team_name': game_data.get('home_team' if game_data.get('home_team_id') == team_id else 'away_team', ''),
                **game_data
            }
        else:
            return {'found_in_target_games': False}
            
    except Exception as e:
        logger.error(f"target_games 데이터 조회 실패: {e}")
        return {'found_in_target_games': False}


def get_target_games_by_sport(sport_code: str) -> List[Dict[str, Any]]:
    """
    스포츠별 target_games 조회
    
    Args:
        sport_code: 스포츠 코드 (BS, SC 등)
    
    Returns:
        List[Dict]: 해당 스포츠의 게임 목록
    """
    try:
        client = connect_supabase()
        
        # 스포츠 이름으로 변환
        sport_name = DatabaseConfig.get_sports_name(sport_code)
        
        query = client.table('target_games').select('*').eq('sports', sport_name)
        
        # 야구인 경우 W 게임 타입만 필터링
        if sport_name == 'baseball':
            query = query.eq('game_type', 'W')
        
        response = query.execute()
        
        return response.data if response.data else []
        
    except Exception as e:
        logger.error(f"스포츠별 게임 조회 실패 ({sport_code}): {e}")
        return []


# 하위 호환성을 위한 함수 별칭
normalize_numbers = NumberFormatter.normalize_numbers


# 새로운 모듈화된 클래스들을 직접 노출
__all__ = [
    # 연결 및 기본 함수들
    'connect_supabase',
    'check_duplicate_baseball_stats',
    'get_team_mappings',
    'get_team_display_name', 
    'get_team_full_name',
    'normalize_pitcher_stats',
    'normalize_numbers',  # 하위 호환성
    'check_batch_duplicate_baseball_stats',
    'check_batch_duplicate_soccer_stats',
    'save_team_and_pitcher_stats_unified',
    'get_team_stats_data_from_target_games',
    'get_target_games_by_sport',
    
    # 새로운 모듈화된 클래스들
    'DatabaseConfig',
    'NumberFormatter',
    'NPBDataCleaner', 
    'BatchDuplicateChecker',
    'TeamNameResolver',
    'DatabaseSaveService',
    'TeamStatsDataService',
    'PitcherStatsProcessor',
    
    # 상수
    'BASEBALL_TEAM_STATS_TABLE',
    'SOCCER_TEAM_STATS_TABLE'
]