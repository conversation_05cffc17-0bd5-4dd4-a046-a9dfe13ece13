#!/usr/bin/env python3
"""
worker_crawl.py - 멀티스포츠 데이터 수집 워커

이 스크립트는 main.py(스케줄러)에 의해 주기적으로 실행됩니다.
별도 서브프로세스로 실행되어 모든 스포츠의 데이터 수집·가공·저장을 수행한 뒤
프로세스가 종료되므로 장기 실행 시 메모리 누수를 방지합니다.
"""
import asyncio
import atexit
import signal
import sys
from datetime import datetime
from typing import List, Optional
import pytz

from core.orchestrator import MultiSportOrchestrator
from utils.logger import Logger

logger = Logger(__name__)

# 전역 orchestrator 참조 (시그널 핸들러에서 사용)
_global_orchestrator: Optional[MultiSportOrchestrator] = None

def is_working_hours():
    """서울 시간대 기준 작업 시간 체크 (8am-2am KST)"""
    seoul_tz = pytz.timezone('Asia/Seoul')
    now = datetime.now(seoul_tz)
    current_hour = now.hour
    
    # 8am(08) ~ 2am(02) 사이가 작업 시간 (다음날 새벽 2시까지)
    return 8 <= current_hour <= 23 or 0 <= current_hour <= 1

def should_run_crawler():
    """크롤러 실행 여부 결정"""
    if not is_working_hours():
        seoul_tz = pytz.timezone('Asia/Seoul')
        now = datetime.now(seoul_tz)
        logger.info(f"Non-working hours (2am-8am KST). Current time: {now.strftime('%H:%M:%S KST')}. Crawler suspended.")
        return False
    return True


def _cleanup_handler():
    """프로세스 종료 시 동기 정리 핸들러"""
    global _global_orchestrator
    if _global_orchestrator:
        try:
            # asyncio가 아직 실행 중이면 비동기 정리
            loop = asyncio.get_event_loop()
            if loop.is_running():
                logger.warning("🧹 프로세스 종료 - 비동기 정리 스케줄링")
                loop.create_task(_async_cleanup())
            else:
                logger.warning("🧹 프로세스 종료 - 이벤트 루프 종료됨")
        except RuntimeError:
            logger.warning("🧹 프로세스 종료 - 이벤트 루프 없음")
        except Exception as e:
            logger.error(f"정리 핸들러 오류: {e}")


def _signal_handler(signum, frame):
    """시그널 핸들러"""
    global _global_orchestrator
    logger.warning(f"🛑 시그널 {signum} 수신 - 정리 중...")
    if _global_orchestrator:
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.create_task(_async_cleanup())
            sys.exit(0)
        except Exception as e:
            logger.error(f"시그널 핸들러 오류: {e}")
            sys.exit(1)


async def _async_cleanup():
    """비동기 정리 함수"""
    global _global_orchestrator
    if _global_orchestrator:
        try:
            await _global_orchestrator.cleanup()
            _global_orchestrator = None
        except Exception as e:
            logger.error(f"비동기 정리 오류: {e}")


def _setup_cleanup_handlers():
    """정리 핸들러 설정"""
    # atexit 핸들러 등록
    atexit.register(_cleanup_handler)
    
    # 시그널 핸들러 등록 (유닉스 계열 시스템)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, _signal_handler)
    if hasattr(signal, 'SIGINT'):
        signal.signal(signal.SIGINT, _signal_handler)


async def _run_orchestrator(target_sports: Optional[List[str]] = None) -> None:
    """멀티스포츠 오케스트레이터 실행

    Args:
        target_sports: 실행할 스포츠 목록 (예: ["soccer", "baseball"]).
                       None이면 모든 지원 스포츠 실행.
    """
    global _global_orchestrator
    
    # old 폴더의 정상 작동하는 설정 사용
    orchestrator = MultiSportOrchestrator({
        "max_browsers": 5,
        "headless": True,
    })
    
    # 전역 참조 설정 (시그널 핸들러에서 사용)
    _global_orchestrator = orchestrator
    
    try:
        summary = await orchestrator.start_orchestration(
            sports=target_sports,
            force_run=True,
        )

        # 2. 성공 로그 (요약 형태)
        total_teams = 0
        total_players = 0
        for res in summary.results:
            if res.success:
                total_teams += res.teams_processed
                total_players += res.players_processed
        
        if total_teams > 0 or total_players > 0:
            logger.info(f"✅ 성공: NPB {total_teams}개팀/선발투수 {total_players}명")
    finally:
        # 브라우저 풀 등 리소스 정리
        try:
            await orchestrator.cleanup()
            _global_orchestrator = None
        except Exception as e:
            logger.error(f"❌ 리소스 정리 오류: {e}")
            _global_orchestrator = None


def parse_cli_args() -> Optional[List[str]]:
    """명령행 인수를 파싱하여 특정 스포츠 만 실행하도록 지원"""
    if len(sys.argv) <= 1:
        return None
    # 첫 번째 인자 이후를 스포츠 이름 리스트로 취급
    return [arg.lower() for arg in sys.argv[1:]]


async def main() -> None:
    # Seoul 시간대 기반 작업 시간 체크
    if not should_run_crawler():
        return  # 비작업 시간이면 즉시 종료
    
    target_sports = parse_cli_args()

    try:
        # 1. 시작 로그
        logger.info("🚀 시작")
        
        await _run_orchestrator(target_sports)
        
        # 3. 종료 로그  
        logger.info("🏁 종료")
    except Exception as exc:
        end_ts = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logger.error(f"❌ 워커 실행 실패 - {end_ts}: {exc}")
        global _global_orchestrator
        if _global_orchestrator:
            try:
                await _async_cleanup()
            except Exception as cleanup_err:
                logger.error(f"정리 중 추가 오류: {cleanup_err}")
        raise


def run_worker():
    """워커 실행 함수 - 적절한 이벤트 루프 관리"""
    # 정리 핸들러 설정
    _setup_cleanup_handlers()
    
    try:
        # 이벤트 루프 정책 설정 (Windows 호환성)
        if sys.platform.startswith('win'):
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 새 이벤트 루프 생성
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 메인 함수 실행
            loop.run_until_complete(main())
        finally:
            # 남은 태스크들 정리
            try:
                # 아직 완료되지 않은 태스크들 취소
                pending = asyncio.all_tasks(loop)
                if pending:
                    for task in pending:
                        task.cancel()
                    
                    # 취소된 태스크들이 완료될 때까지 대기
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                
                # 이벤트 루프 종료
                loop.close()
                
            except Exception as e:
                logger.error(f"이벤트 루프 정리 오류: {e}")
                
    except KeyboardInterrupt:
        logger.warning("🛑 키보드 인터럽트 수신")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ 워커 실행 오류: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run_worker() 