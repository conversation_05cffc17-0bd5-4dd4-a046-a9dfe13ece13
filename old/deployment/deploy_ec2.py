#!/usr/bin/env python3
"""
Deployment script for natstat application on EC2.
Handles Playwright setup, requirements installation, and environment setup.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('deploy.log')
    ]
)

logger = logging.getLogger(__name__)


class EC2Deployer:
    """Handles EC2 deployment for natstat application."""
    
    def __init__(self, project_root: Path):
        """
        Initialize deployer.
        
        Args:
            project_root: Path to project root directory
        """
        self.project_root = project_root
        self.is_ec2 = self._detect_ec2()
        
    def _detect_ec2(self) -> bool:
        """Detect if running on EC2."""
        try:
            result = subprocess.run(
                ['curl', '-s', '--max-time', '2', 'http://***************/latest/meta-data/'],
                capture_output=True,
                text=True,
                timeout=3
            )
            return result.returncode == 0 and result.stdout
        except:
            return False
    
    def install_system_dependencies(self) -> bool:
        """Install system dependencies for EC2."""
        if not self.is_ec2:
            logger.info("Not on EC2, skipping system dependencies")
            return True
            
        logger.info("Installing system dependencies...")
        
        try:
            # Update package list
            subprocess.run(['sudo', 'apt-get', 'update', '-y'], check=True)
            
            # Install essential packages
            packages = [
                'python3-pip',
                'python3-venv',
                'curl',
                'wget',
                'git',
                'build-essential',
                'libpq-dev',
                'python3-dev',
                # Playwright dependencies
                'libnss3',
                'libatk-bridge2.0-0',
                'libdrm2',
                'libxkbcommon0',
                'libxcomposite1',
                'libxdamage1',
                'libxrandr2',
                'libgbm1',
                'libxss1',
                'libasound2',
                'libatspi2.0-0',
                'libgtk-3-0',
                'libxshmfence1',
                'libgconf-2-4',
                'libxcursor1',
                'libxi6',
                'libxrender1',
                'libcairo2',
                'libcups2',
                'libdbus-1-3',
                'libexpat1',
                'libfontconfig1',
                'libgcc1',
                'libglib2.0-0',
                'libnspr4',
                'libpango-1.0-0',
                'libpangocairo-1.0-0',
                'libstdc++6',
                'libx11-6',
                'libx11-xcb1',
                'libxcb1',
                'libxcb-dri3-0',
                'libxext6',
                'libxfixes3',
                'libxtst6',
                'ca-certificates',
                'fonts-liberation',
                'libappindicator1',
                'lsb-release',
                'xdg-utils'
            ]
            
            subprocess.run(['sudo', 'apt-get', 'install', '-y'] + packages, check=True)
            logger.info("System dependencies installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to install system dependencies: {e}")
            return False
    
    def create_requirements_file(self) -> bool:
        """Create requirements.txt file without version pinning."""
        logger.info("Creating requirements.txt file...")
        
        requirements = [
            "# Core dependencies",
            "python-dotenv",
            "requests",
            "supabase",
            "aiohttp",
            "",
            "# Data processing",
            "pandas",
            "numpy",
            "",
            "# Web automation",
            "playwright",
            "",
            "# Testing",
            "pytest",
            "pytest-asyncio",
            "pytest-mock",
            "",
            "# Development",
            "black",
            "flake8",
            "mypy",
            ""
        ]
        
        requirements_path = self.project_root / "requirements.txt"
        
        try:
            with open(requirements_path, 'w') as f:
                f.write('\n'.join(requirements))
            
            logger.info(f"Requirements file created at {requirements_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create requirements file: {e}")
            return False
    
    def install_python_dependencies(self) -> bool:
        """Install Python dependencies."""
        logger.info("Installing Python dependencies...")
        
        try:
            # Upgrade pip
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], check=True)
            
            # Install from requirements.txt
            requirements_path = self.project_root / "requirements.txt"
            if requirements_path.exists():
                subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', str(requirements_path)], check=True)
            else:
                logger.warning("requirements.txt not found, installing core dependencies manually")
                core_deps = [
                    'python-dotenv',
                    'requests',
                    'supabase',
                    'aiohttp',
                    'playwright'
                ]
                subprocess.run([sys.executable, '-m', 'pip', 'install'] + core_deps, check=True)
            
            logger.info("Python dependencies installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to install Python dependencies: {e}")
            return False
    
    def setup_playwright(self) -> bool:
        """Setup Playwright with Chromium."""
        logger.info("Setting up Playwright...")
        
        try:
            # Import and use our custom Playwright setup
            sys.path.insert(0, str(self.project_root))
            from src.utils.playwright_setup import setup_playwright_for_deployment
            
            return setup_playwright_for_deployment()
            
        except Exception as e:
            logger.error(f"Failed to setup Playwright: {e}")
            return False
    
    def setup_environment(self) -> bool:
        """Setup environment variables and configuration."""
        logger.info("Setting up environment...")
        
        try:
            # Create .env file template if it doesn't exist
            env_path = self.project_root / "src" / "config" / ".env"
            
            if not env_path.exists():
                env_path.parent.mkdir(parents=True, exist_ok=True)
                
                env_template = """# Natstat API Configuration
NATSTAT_API_KEY=your_api_key_here

# Database Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Playwright Configuration
PLAYWRIGHT_BROWSERS_PATH=/tmp/playwright
PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0

# Optional: Team filtering
TEAM_CODES=

# Optional: Database connection pool settings
DB_POOL_SIZE=5
DB_MAX_IDLE_TIME=300
DB_CONNECTION_TIMEOUT=30
DB_RETRY_ATTEMPTS=3
DB_RETRY_DELAY=1
"""
                
                with open(env_path, 'w') as f:
                    f.write(env_template)
                
                logger.info(f"Environment template created at {env_path}")
                logger.warning("Please update the .env file with your actual API keys and database credentials")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup environment: {e}")
            return False
    
    def run_tests(self) -> bool:
        """Run basic tests to verify deployment."""
        logger.info("Running deployment tests...")
        
        try:
            # Test basic imports
            test_imports = [
                'import sys',
                'sys.path.insert(0, ".")',
                'from src.utils.validation import InputValidator',
                'from src.services.api.client import NatstatClient',
                'from src.utils.playwright_setup import PlaywrightSetup',
                'print("✅ All imports successful")'
            ]
            
            result = subprocess.run(
                [sys.executable, '-c', '; '.join(test_imports)],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                logger.info("Import tests passed")
                
                # Test Playwright
                playwright_test = """
import sys
sys.path.insert(0, ".")
from src.utils.playwright_setup import PlaywrightSetup
setup = PlaywrightSetup()
if setup.check_playwright_installation():
    print("✅ Playwright installed")
if setup.check_chromium_installation():
    print("✅ Chromium installed")
else:
    print("⚠️  Chromium not installed or not working")
"""
                
                result = subprocess.run(
                    [sys.executable, '-c', playwright_test],
                    capture_output=True,
                    text=True,
                    cwd=self.project_root
                )
                
                if result.returncode == 0:
                    logger.info("Playwright tests passed")
                    return True
                else:
                    logger.warning(f"Playwright tests failed: {result.stderr}")
                    return False
            else:
                logger.error(f"Import tests failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to run tests: {e}")
            return False
    
    def deploy(self) -> bool:
        """Run complete deployment process."""
        logger.info("=== Starting natstat EC2 deployment ===")
        
        steps = [
            ("Installing system dependencies", self.install_system_dependencies),
            ("Creating requirements file", self.create_requirements_file),
            ("Installing Python dependencies", self.install_python_dependencies),
            ("Setting up Playwright", self.setup_playwright),
            ("Setting up environment", self.setup_environment),
            ("Running tests", self.run_tests)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"Step: {step_name}")
            if not step_func():
                logger.error(f"Deployment failed at step: {step_name}")
                return False
            logger.info(f"Step completed: {step_name}")
        
        logger.info("=== Deployment completed successfully ===")
        logger.info("Next steps:")
        logger.info("1. Update src/config/.env with your API keys")
        logger.info("2. Test the application: python3 natstat.py --help")
        logger.info("3. Run the application: python3 natstat.py -l kbo")
        
        return True


def main():
    """Main deployment function."""
    project_root = Path(__file__).parent
    deployer = EC2Deployer(project_root)
    
    try:
        success = deployer.deploy()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("Deployment interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Deployment failed with unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())