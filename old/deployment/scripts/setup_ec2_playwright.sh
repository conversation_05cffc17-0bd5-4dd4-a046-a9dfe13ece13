#!/bin/bash
# Setup script for Playwright Chromium installation on EC2 Ubuntu/Amazon Linux

set -e

echo "=== Playwright Chromium Setup for EC2 ==="

# Detect OS
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VERSION=$VERSION_ID
else
    echo "Cannot detect OS. Exiting."
    exit 1
fi

echo "Detected OS: $OS $VERSION"

# Function to install dependencies for Ubuntu/Debian
install_ubuntu_deps() {
    echo "Installing dependencies for Ubuntu/Debian..."
    
    # Update package list
    sudo apt-get update -y
    
    # Install required packages for Chromium
    sudo apt-get install -y \
        libnss3 \
        libatk-bridge2.0-0 \
        libdrm2 \
        libxkbcommon0 \
        libxcomposite1 \
        libxdamage1 \
        libxrandr2 \
        libgbm1 \
        libxss1 \
        libasound2 \
        libatspi2.0-0 \
        libgtk-3-0 \
        libxshmfence1 \
        libgconf-2-4 \
        libxcursor1 \
        libxi6 \
        libxrender1 \
        libcairo2 \
        libcups2 \
        libdbus-1-3 \
        libexpat1 \
        libfontconfig1 \
        libgcc1 \
        libglib2.0-0 \
        libgtk-3-0 \
        libnspr4 \
        libpango-1.0-0 \
        libpangocairo-1.0-0 \
        libstdc++6 \
        libx11-6 \
        libx11-xcb1 \
        libxcb1 \
        libxcb-dri3-0 \
        libxcomposite1 \
        libxcursor1 \
        libxdamage1 \
        libxext6 \
        libxfixes3 \
        libxi6 \
        libxrandr2 \
        libxrender1 \
        libxss1 \
        libxtst6 \
        ca-certificates \
        fonts-liberation \
        libappindicator1 \
        libnss3 \
        lsb-release \
        xdg-utils \
        wget \
        curl
        
    echo "Ubuntu/Debian dependencies installed successfully"
}

# Function to install dependencies for Amazon Linux
install_amazon_linux_deps() {
    echo "Installing dependencies for Amazon Linux..."
    
    # Update package list
    sudo yum update -y
    
    # Install EPEL repository
    sudo yum install -y epel-release
    
    # Install required packages for Chromium
    sudo yum install -y \
        alsa-lib \
        atk \
        cups-libs \
        gtk3 \
        libXcomposite \
        libXcursor \
        libXdamage \
        libXext \
        libXi \
        libXrandr \
        libXScrnSaver \
        libXtst \
        pango \
        xorg-x11-fonts-100dpi \
        xorg-x11-fonts-75dpi \
        xorg-x11-fonts-cyrillic \
        xorg-x11-fonts-misc \
        xorg-x11-fonts-Type1 \
        xorg-x11-utils \
        libdrm \
        libxkbcommon \
        libxshmfence \
        mesa-libgbm \
        nss \
        nspr
        
    echo "Amazon Linux dependencies installed successfully"
}

# Function to install dependencies for CentOS/RHEL
install_centos_deps() {
    echo "Installing dependencies for CentOS/RHEL..."
    
    # Update package list
    sudo yum update -y
    
    # Install EPEL repository
    sudo yum install -y epel-release
    
    # Install required packages for Chromium
    sudo yum install -y \
        alsa-lib \
        atk \
        cups-libs \
        gtk3 \
        libXcomposite \
        libXcursor \
        libXdamage \
        libXext \
        libXi \
        libXrandr \
        libXScrnSaver \
        libXtst \
        pango \
        xorg-x11-fonts-100dpi \
        xorg-x11-fonts-75dpi \
        xorg-x11-fonts-cyrillic \
        xorg-x11-fonts-misc \
        xorg-x11-fonts-Type1 \
        xorg-x11-utils \
        libdrm \
        libxkbcommon \
        libxshmfence \
        mesa-libgbm \
        nss \
        nspr
        
    echo "CentOS/RHEL dependencies installed successfully"
}

# Install dependencies based on OS
case "$OS" in
    *"Ubuntu"*|*"Debian"*)
        install_ubuntu_deps
        ;;
    *"Amazon Linux"*)
        install_amazon_linux_deps
        ;;
    *"CentOS"*|*"Red Hat"*)
        install_centos_deps
        ;;
    *)
        echo "Unsupported OS: $OS"
        echo "Please install Chromium dependencies manually"
        exit 1
        ;;
esac

echo "System dependencies installed successfully"

# Set environment variables for Playwright
export PLAYWRIGHT_BROWSERS_PATH=/tmp/playwright
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0

echo "Installing Playwright browsers..."

# Try to install Playwright browsers with retries
MAX_RETRIES=3
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    echo "Attempt $((RETRY_COUNT + 1)) of $MAX_RETRIES to install Chromium..."
    
    # Install Chromium browser
    if python3 -m playwright install chromium; then
        echo "Chromium installed successfully"
        break
    else
        echo "Failed to install Chromium, retrying..."
        RETRY_COUNT=$((RETRY_COUNT + 1))
        
        if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
            echo "Waiting 10 seconds before retry..."
            sleep 10
        fi
    fi
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    echo "Failed to install Chromium after $MAX_RETRIES attempts"
    
    # Try alternative installation method
    echo "Trying alternative installation method..."
    
    # Install dependencies for alternative method
    if python3 -m playwright install-deps chromium; then
        echo "Browser dependencies installed"
        
        # Try installing browser again
        if python3 -m playwright install chromium; then
            echo "Chromium installed successfully with alternative method"
        else
            echo "Failed to install Chromium with alternative method"
            exit 1
        fi
    else
        echo "Failed to install browser dependencies"
        exit 1
    fi
fi

# Verify installation
echo "Verifying Playwright Chromium installation..."
if python3 -c "
from playwright.sync_api import sync_playwright
import sys
try:
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        print('✅ Chromium browser is working correctly')
        browser.close()
    sys.exit(0)
except Exception as e:
    print(f'❌ Chromium browser verification failed: {e}')
    sys.exit(1)
"; then
    echo "✅ Playwright Chromium setup completed successfully"
else
    echo "❌ Playwright Chromium setup failed"
    exit 1
fi

echo "=== Setup Complete ==="