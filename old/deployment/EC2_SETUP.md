# EC2 Deployment Guide for Natstat

This guide helps you deploy the natstat application on an EC2 instance with proper Playwright Chromium setup.

## Quick Setup

### Option 1: Automated Deployment (Recommended)

Run the automated deployment script:

```bash
python3 deploy_ec2.py
```

This will:
- Install system dependencies
- Create requirements.txt
- Install Python dependencies
- Setup Playwright with Chromium
- Configure environment
- Run basic tests

### Option 2: Manual Setup

1. **Install system dependencies:**
```bash
# Make the script executable
chmod +x scripts/setup_ec2_playwright.sh

# Run the setup script
./scripts/setup_ec2_playwright.sh
```

2. **Install Python dependencies:**
```bash
pip3 install -r requirements.txt
```

3. **Setup Playwright:**
```bash
python3 -m playwright install-deps chromium
python3 -m playwright install chromium
```

4. **Configure environment:**
```bash
# Copy environment template
cp src/config/.env.template src/config/.env

# Edit with your actual values
nano src/config/.env
```

## Environment Configuration

Update `src/config/.env` with your actual values:

```env
# Natstat API Configuration
NATSTAT_API_KEY=your_actual_api_key_here

# Database Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Playwright Configuration
PLAYWRIGHT_BROWSERS_PATH=/tmp/playwright
PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0

# Optional: Team filtering
TEAM_CODES=

# Optional: Database connection pool settings
DB_POOL_SIZE=5
DB_MAX_IDLE_TIME=300
DB_CONNECTION_TIMEOUT=30
DB_RETRY_ATTEMPTS=3
DB_RETRY_DELAY=1
```

## Testing the Setup

### Basic functionality test:
```bash
python3 natstat.py --help
```

### Test with specific league:
```bash
python3 natstat.py -l kbo
```

### Test Playwright setup:
```bash
python3 -c "
from src.utils.playwright_setup import PlaywrightSetup
setup = PlaywrightSetup()
print('Playwright installed:', setup.check_playwright_installation())
print('Chromium installed:', setup.check_chromium_installation())
"
```

## Working Hours Configuration

The application runs during specific hours (8am-12am KST by default). To modify:

Edit `worker_crawl.py`:
```python
def is_working_hours():
    # Change these hours as needed
    return 8 <= current_hour <= 23  # 8am to 11pm
```

## Troubleshooting

### Playwright Installation Issues

If Playwright installation fails:

1. **Check system dependencies:**
```bash
sudo apt-get install -y libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2
```

2. **Install with dependencies:**
```bash
python3 -m playwright install-deps chromium
python3 -m playwright install chromium
```

3. **Alternative system installation:**
```bash
sudo apt-get install -y chromium-browser
export PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH=/usr/bin/chromium-browser
```

### Common Issues

1. **Permission denied errors:**
```bash
sudo chown -R $USER:$USER /tmp/playwright
```

2. **Network timeouts:**
```bash
export PLAYWRIGHT_DOWNLOAD_CONNECTION_TIMEOUT=300000
```

3. **Memory issues:**
```bash
# Increase swap space
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

## Production Deployment

For production deployment, consider:

1. **Using PM2 for process management:**
```bash
npm install -g pm2
pm2 start "python3 natstat.py -l kbo,npb,mlb" --name natstat
pm2 save
pm2 startup
```

2. **Setting up log rotation:**
```bash
pm2 install pm2-logrotate
```

3. **Monitoring and alerts:**
```bash
pm2 monit
```

## Security Notes

- Never commit API keys to version control
- Use environment variables for sensitive data
- Keep dependencies updated
- Use secure connections (HTTPS) only
- Monitor logs for security issues

## Support

If you encounter issues:
1. Check the deployment logs: `tail -f deploy.log`
2. Review application logs
3. Verify environment variables
4. Test individual components
5. Check EC2 instance resources (memory, disk, network)