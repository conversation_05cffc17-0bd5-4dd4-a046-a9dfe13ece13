"""
통합 데이터 수집기 설정 모듈 - EC2 환경 통합 지원
"""
import os
import platform
from enum import Enum
from typing import Dict, Any
from dataclasses import dataclass

from dotenv import load_dotenv

# .env 파일 로드 (config 폴더 안의 .env 파일 경로 지정)
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
load_dotenv(dotenv_path)

# 환경 감지
IS_EC2 = (
    os.path.exists('/sys/hypervisor/uuid') or
    'ec2' in platform.node().lower() or
    os.getenv('AWS_EXECUTION_ENV') is not None
)
IS_PRODUCTION = os.getenv('ENVIRONMENT', 'development') == 'production'

# Supabase 설정
SUPABASE_URL = os.environ.get("SUPABASE_URL")
SUPABASE_KEY = os.environ.get("SUPABASE_KEY")

# EC2 인스턴스별 최적화 설정


@dataclass
class EC2InstanceConfig:
    """EC2 인스턴스별 최적화 설정"""
    instance_type: str
    cpu_cores: int
    memory_gb: float
    max_browsers: int
    browser_memory_limit: str
    concurrent_games: int


EC2_CONFIGS = {
    't3.medium': EC2InstanceConfig('t3.medium', 2, 4.0, 2, '1GB', 1),
    't3.large': EC2InstanceConfig('t3.large', 2, 8.0, 3, '1.5GB', 2),
    'default': EC2InstanceConfig('unknown', 2, 2.0, 1, '512MB', 2)
}

# 현재 인스턴스 설정
INSTANCE_TYPE = os.getenv('EC2_INSTANCE_TYPE', 'default')
CURRENT_EC2_CONFIG = EC2_CONFIGS.get(INSTANCE_TYPE, EC2_CONFIGS['default'])

# 환경별 설정
if IS_EC2:
    # EC2 환경 설정 (더 보수적)
    DEBUG = False
    CRAWL_DELAY = int(os.getenv('CRAWL_DELAY', '5'))  # 5초로 증가
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))  # 3회로 감소
    TIMEOUT = int(os.getenv('TIMEOUT', '90'))  # 90초로 증가
    HEADLESS_BROWSER = True
    BROWSER_TIMEOUT = int(os.getenv('BROWSER_TIMEOUT', '180'))  # 3분으로 증가
    MAX_CONCURRENT_BROWSERS = CURRENT_EC2_CONFIG.max_browsers
    SCHEDULE_INTERVAL = int(os.getenv('SCHEDULE_INTERVAL', '900'))  # 15분으로 증가
    LOG_LEVEL = 'INFO'
    BROWSER_ARGS = [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--memory-pressure-off',
        '--max_old_space_size=256',  # 메모리 제한 강화
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
    ]
else:
    # 로컬 개발 환경 설정
    DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'
    CRAWL_DELAY = int(os.getenv('CRAWL_DELAY', '1'))
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))
    TIMEOUT = int(os.getenv('TIMEOUT', '30'))
    HEADLESS_BROWSER = os.getenv('HEADLESS_BROWSER', 'False').lower() == 'true'
    BROWSER_TIMEOUT = int(os.getenv('BROWSER_TIMEOUT', '60'))
    MAX_CONCURRENT_BROWSERS = int(os.getenv('MAX_CONCURRENT_BROWSERS', '2'))
    SCHEDULE_INTERVAL = int(os.getenv('SCHEDULE_INTERVAL', '300'))  # 5분
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    BROWSER_ARGS = ['--disable-web-security']


class League(Enum):
    """지원하는 리그"""
    KBO = "KBO"
    MLB = "MLB"
    NPB = "NPB"


class Sports(Enum):
    """지원하는 스포츠"""
    BASEBALL = "BS"
    BASKETBALL = "BK"
    SOCCER = "SC"
    VOLLEYBALL = "VL"


# 지원 리그 목록
SUPPORTED_LEAGUES = [League.KBO, League.MLB, League.NPB]

# 리그 ID 매핑
LEAGUE_ID_MAPPING = {
    League.KBO: "BS001",
    League.MLB: "BS002", 
    League.NPB: "BS004"
}

# Sportic 리그 ID 매핑
SPORTIC_LEAGUE_MAPPING = {
    League.KBO: "bs001",
    League.MLB: "bs002",
    League.NPB: "bs004"
}

# 기본 URL 포맷
BASE_URL_FORMAT = (
    "https://www.betman.co.kr/main/mainPage/gameinfo/{sports}TeamDetail.do"
    "?item={item}&leagueId={league_id}&teamId={team_id}"
)

# 스케줄 URL
SCHEDULE_URL = (
    'https://www.betman.co.kr/main/mainPage/gameinfo/'
    'dataOfBaseballSchedule.do'
)

# 축구 스케줄 URL
SOCCER_SCHEDULE_URL = (
    'https://www.betman.co.kr/main/mainPage/gameinfo/'
    'dataOfSoccerSchedule.do'
)

# 스포츠별 스케줄 URL 매핑
SCHEDULE_URL_MAPPING = {
    'baseball': SCHEDULE_URL,
    'soccer': SOCCER_SCHEDULE_URL,
}

# 스포츠별 URL 경로 매핑
SPORTS_URL_MAPPING: Dict[str, str] = {
    "BS": "bs",  # 야구
    "BK": "bk",  # 농구
    "SC": "sc",  # 축구
    "VL": "vl",  # 배구
}

# 출력 설정
OUTPUT_CONFIG = {
    'print_summary': True,
    'save_to_file': False,
    'file_format': 'json'
}


def create_team_url(sports: str, league_id: str, team_id: str) -> str:
    """
    ⚠️ 사용되지 않는 함수 - 야구/축구 수집기에서 실제 사용하지 않음
    실제로는 베트맨 사이트에서 직접 팀 링크를 추출하는 방식 사용
    
    스포츠, 리그 ID, 팀 ID를 기반으로 팀 통계 URL을 생성합니다.

    Args:
        sports: 스포츠 코드 (예: 'BS', 'BK')
        league_id: 리그 ID (예: 'BS002')
        team_id: 팀 ID (예: 'CL')

    Returns:
        str: 생성된 팀 통계 URL
    """
    sports_path = SPORTS_URL_MAPPING.get(sports, sports.lower())
    return BASE_URL_FORMAT.format(
        sports=sports_path,
        item=sports,
        league_id=league_id,
        team_id=team_id
    )


def get_league_id(league: League) -> str:
    """리그 열거형을 리그 ID로 변환"""
    return LEAGUE_ID_MAPPING.get(league, "BS001")


def get_sportic_league_id(league: League) -> str:
    """리그 열거형을 Sportic 리그 ID로 변환"""
    return SPORTIC_LEAGUE_MAPPING.get(league, "bs001")


# 스포츠별 설정 (환경별 최적화)
SPORTS_CONFIG = {
    'baseball': {
        'enabled': True,
        'leagues': ['KBO', 'MLB', 'NPB'],
        'update_interval': SCHEDULE_INTERVAL,
        'max_concurrent_games': (
            CURRENT_EC2_CONFIG.concurrent_games if IS_EC2 else 10
        ),
        'priority': 1
    },
    'soccer': {
        'enabled': True,
        'leagues': ['K-League', 'Premier', 'La Liga'],
        'update_interval': SCHEDULE_INTERVAL,
        'max_concurrent_games': (
            max(2, CURRENT_EC2_CONFIG.concurrent_games // 2) if IS_EC2 else 6
        ),
        'priority': 2
    }
}


def get_sport_config(sport: str) -> Dict[str, Any]:
    """스포츠별 설정 반환"""
    return SPORTS_CONFIG.get(sport, {})


def is_sport_enabled(sport: str) -> bool:
    """스포츠 활성화 여부 확인"""
    return get_sport_config(sport).get('enabled', False)


def get_environment_info() -> Dict[str, Any]:
    """환경 정보 반환"""
    return {
        'is_ec2': IS_EC2,
        'is_production': IS_PRODUCTION,
        'platform': platform.system(),
        'node': platform.node(),
        'ec2_instance_type': INSTANCE_TYPE,
        'cpu_cores': CURRENT_EC2_CONFIG.cpu_cores,
        'memory_gb': CURRENT_EC2_CONFIG.memory_gb,
        'max_browsers': MAX_CONCURRENT_BROWSERS,
        'schedule_interval': SCHEDULE_INTERVAL,
        'headless_browser': HEADLESS_BROWSER,
        'debug_mode': DEBUG
    }


def validate_config() -> Dict[str, Any]:
    """설정 유효성 검증"""
    issues = []
    warnings = []

    # 필수 환경 변수 검증
    if not SUPABASE_URL:
        issues.append("Missing required environment variable: SUPABASE_URL")
    if not SUPABASE_KEY:
        issues.append("Missing required environment variable: SUPABASE_KEY")

    # EC2 환경 검증
    if IS_EC2:
        if not os.getenv('AWS_DEFAULT_REGION'):
            warnings.append("AWS_DEFAULT_REGION not set for EC2 environment")

        if CURRENT_EC2_CONFIG.memory_gb < 1.0:
            warnings.append(
                f"Low memory configuration: {CURRENT_EC2_CONFIG.memory_gb}GB"
            )

    return {
        'valid': len(issues) == 0,
        'issues': issues,
        'warnings': warnings,
        'config_summary': get_environment_info()
    }