"""
메인 스케줄러 - 초경량 버전 (최대 메모리 절약)
Target crawling: 08:01, 12:01, 14:01, 16:01, 22:01
Match results: 08:06, 12:06, 14:06, 16:06, 22:06
Program runs: 08:12, 11:12, 12:12, 14:12, 14:32, 16:12, 17:12
"""
import logging
import os
import subprocess
import sys
import time
from datetime import datetime

from natstat.worker_run import execute_workflow

# ==============================================================================
# 로거 설정
# ==============================================================================
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)
# ==============================================================================


def run_ec2_setup():
    """Run EC2 setup script automatically if needed."""
    setup_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                               "deployment", "scripts", "setup_ec2_playwright.sh")
    
    if os.path.exists(setup_script):
        logger.info("🔧 [Setup] Running EC2 Playwright setup script...")
        try:
            # Make script executable
            os.chmod(setup_script, 0o755)
            
            # Run the setup script
            result = subprocess.run(
                ["bash", setup_script],
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout
            )
            
            if result.returncode == 0:
                logger.info("✅ [Setup] EC2 setup completed successfully")
                return True
            else:
                logger.error("❌ [Setup] EC2 setup failed")
                logger.error(f"    Error: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ [Setup] EC2 setup timed out (10 minutes)")
            return False
        except Exception as e:
            logger.error(f"❌ [Setup] EC2 setup error: {e}")
            return False
    else:
        logger.warning("⚠️ [Setup] EC2 setup script not found, skipping...")
        return False


def install_playwright_chromium():
    """Checks if Playwright Chromium is installed and installs it if not."""
    try:
        # First check if Chromium is already working
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            browser.close()
            logger.info("✅ [Playwright] Chromium already working")
            return True
    except Exception:
        logger.info("🔍 [Playwright] Chromium not working, attempting installation...")
    
    try:
        # First try to install dependencies
        subprocess.run(
            [sys.executable, "-m", "playwright", "install-deps", "chromium"],
            check=True,
            capture_output=True,
            text=True,
        )
        # Then install chromium
        subprocess.run(
            [sys.executable, "-m", "playwright", "install", "chromium"],
            check=True,
            capture_output=True,
            text=True,
        )
        logger.info("✅ [Playwright] Chromium installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error("❌ [Playwright] Failed to install Chromium.")
        logger.error(f"    Error: {e.stderr}")
        
        # Try running EC2 setup script as fallback
        logger.info("🔧 [Playwright] Attempting EC2 setup as fallback...")
        if run_ec2_setup():
            # Try Playwright installation again after setup
            try:
                subprocess.run(
                    [sys.executable, "-m", "playwright", "install", "chromium"],
                    check=True,
                    capture_output=True,
                    text=True,
                )
                logger.info("✅ [Playwright] Chromium installed after EC2 setup")
                return True
            except subprocess.CalledProcessError:
                logger.error("❌ [Playwright] Still failed after EC2 setup")
                return False
        else:
            return False
    except FileNotFoundError:
        logger.error(
            "❌ [Playwright] Playwright is not installed. "
            "Please run 'pip install playwright'."
        )
        return False


if __name__ == "__main__":
    # Ensure Playwright Chromium is ready before starting the scheduler.
    logger.info("🚀 [Startup] Initializing application...")
    
    chromium_installed = install_playwright_chromium()
    if not chromium_installed:
        logger.warning("⚠️ [Playwright] Chromium not installed, but continuing anyway...")
        logger.info("💡 [Tip] You can manually run: ./deployment/scripts/setup_ec2_playwright.sh")

    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    worker_path = os.path.join(BASE_DIR, "worker_crawl.py")
    natstat_path = os.path.join(BASE_DIR, "natstat", "natstat.py")

    # Initial run for all sports
    logger.info("[스케줄러] 초기 실행...")
    sports_to_run = [
        "baseball",
        "basketball",
        "soccer",
        "volleyball",
    ]
    natstat_error_msg = "⚾️ [Natstat] @natstat 실행 오류: 종료 코드 %s"
    natstat_exception_msg = (
        "⚾️ [Natstat] @natstat 실행 중 예외 발생: %s"
    )

    for sport in sports_to_run:
        logger.info(f"[스케줄러] {sport} 워커 실행...")
        result = subprocess.run(
            [sys.executable, worker_path, sport],
            check=False,
            env=os.environ.copy(),
        )
        if result.returncode != 0:
            logger.error(f"[스케줄러] {sport} 워커 오류: {result.returncode}")
        else:
            logger.info(f"[스케줄러] {sport} 워커 완료")
            # --- 야구 수집 성공 시 @natstat 실행 ---
            if sport == "baseball":
                logger.info("⚾️ [Natstat] 1분 후 @natstat 을 실행합니다.")
                time.sleep(60)  # 1분 대기
                try:
                    # natstat 워크플로우 직접 실행
                    return_code = execute_workflow(use_async=True)
                    if return_code != 0:
                        logger.error(natstat_error_msg, return_code)
                except Exception as e:
                    logger.error(natstat_exception_msg, e)

    end_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.info(f"[스케줄러] {end_timestamp} - 첫 실행 완료")
    logger.info("-" * 50)

    # Schedule subsequent runs
    import schedule

    def get_schedule_times():
        return [
            "08:12", "11:12", "12:12", "14:12", "14:32", "16:12", "17:12",
        ]

    def run_scheduled_job():
        for sport in sports_to_run:
            current_time = datetime.now().strftime("%H:%M:%S")
            logger.info(f"[{current_time}] {sport} 워커 실행...")
            result = subprocess.run(
                [sys.executable, worker_path, sport],
                check=False,
                env=os.environ.copy(),
            )
            if result.returncode != 0:
                logger.error(f"[스케줄러] {sport} 워커 오류: {result.returncode}")
            else:
                logger.info(f"[스케줄러] {sport} 워커 완료")
                # --- 야구 수집 성공 시 @natstat 실행 ---
                if sport == "baseball":
                    logger.info("⚾️ [Natstat] 1분 후 @natstat 을 실행합니다.")
                    time.sleep(60)  # 1분 대기
                    try:
                        # natstat 워크플로우 직접 실행
                        return_code = execute_workflow(use_async=True)
                        if return_code != 0:
                            logger.error(natstat_error_msg, return_code)
                    except Exception as e:
                        logger.error(natstat_exception_msg, e)

    for time_str in get_schedule_times():
        schedule.every().day.at(time_str).do(run_scheduled_job)

    while True:
        schedule.run_pending()
        time.sleep(60)