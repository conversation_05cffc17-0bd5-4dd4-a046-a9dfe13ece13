# TimeSlot 스케줄러 + 메모리 최적화 가이드 (EC2 t3a.medium 기준)

## 목차
1. 개요 및 최신 최적화 성과
2. TimeSlot 스케줄링 시스템
3. 메모리 최적화 (72% 절약)
4. 브라우저/프로세스 제한
5. 메모리/CPU 최적화
6. 스케줄러/비동기 패턴
7. 운영/모니터링/트러블슈팅
8. 체크리스트
9. 적용 전후 비교
10. 결론

---

## 1. 개요 및 최신 최적화 성과
- **2025년 TimeSlot 최적화**: 정밀 시간 제어 + 72% 메모리 절약 (68MB → 19MB)
- **정밀 스케줄링**: 08:12, 11:12, 12:12, 14:12, 14:32, 16:12, 17:12 정확한 시간 실행
- **완전 프로세스 분리**: 워커 완전 종료로 메모리 누수 방지
- **라이브러리 분리**: 대용량 라이브러리를 워커로 이동, 스케줄러 경량화

### 🎯 최신 성능 지표
- **스탠바이 메모리**: 68MB → 19MB (72% 절약)
- **실행 정확도**: 30분 간격 → 1분 정밀도 (30배 향상)
- **프로세스 분리**: 100% 메모리 누수 방지
- **스케줄링 효율**: 7회/일 정밀 실행

---

## 2. TimeSlot 스케줄링 시스템

### 2.1 정밀 시간 제어 스케줄러
- **TimeSlotScheduler**: 스포츠별 시간대 설정으로 정확한 시간에만 실행
- **1분 정밀도**: 기존 30분 간격 → 1분마다 체크, 정확한 시간에만 실행
- **자동 우선순위**: 야구 > 축구 > 농구 > 배구 순서로 실행

```python
# core/scheduler/time_slot_scheduler.py
class TimeSlotScheduler:
    def get_active_sports_now(self, current_time: datetime) -> List[str]:
        """현재 시간에 활성화된 스포츠 목록 조회"""
        active_sports = []
        for sport_name, config in get_all_sport_configs().items():
            if config.is_active_time(current_time.time()):
                active_sports.append(sport_name)
        return active_sports
```

### 2.2 시간대 설정 (Seoul Time, KST)
```python
# 공통 시간대 설정: 08:12, 11:12, 12:12, 14:12, 14:32, 16:12, 17:12
COMMON_TIME_SLOTS = [
    TimeSlot("08:12", "08:13"),  # 전일 결과 + 당일 준비
    TimeSlot("11:12", "11:13"),  # 오전 경기 결과 수집
    TimeSlot("12:12", "12:13"),  # 점심시간 업데이트
    TimeSlot("14:12", "14:13"),  # 오후 경기 준비
    TimeSlot("14:32", "14:33"),  # 경기 중간 업데이트
    TimeSlot("16:12", "16:13"),  # 오후 경기 결과
    TimeSlot("17:12", "17:13"),  # 저녁 경기 준비
]
```

### 2.3 최적화된 main.py 스케줄러
```python
# main.py - 초경량 TimeSlot 스케줄러 (19MB)
from core.scheduler.time_slot_scheduler import TimeSlotScheduler

scheduler = TimeSlotScheduler()
while True:
    current_time = datetime.now()
    active_sports = scheduler.get_active_sports_now(current_time)
    
    if active_sports:  # 정확한 시간에만 실행
        subprocess.run([sys.executable, "worker_crawl.py"] + active_sports)
    
    time.sleep(60)  # 1분마다 체크
```

---

## 3. 메모리 최적화 (72% 절약)

### 3.1 라이브러리 분리 전략
**기존 main.py 문제점:**
- `config`, `logger`, `pytz` 등 무거운 라이브러리 상시 로드
- 대기 중에도 68MB 메모리 사용

**최적화 후 main.py:**
- 최소한의 imports만 사용: `os`, `subprocess`, `sys`, `time`, `datetime`
- `TimeSlotScheduler`만 추가 로드
- 대기 중 19MB로 72% 메모리 절약

```python
# 최적화 전 main.py imports (68MB)
import pytz
from config import config
from utils.logger import Logger

# 최적화 후 main.py imports (19MB)
import os, subprocess, sys, time
from datetime import datetime
from core.scheduler.time_slot_scheduler import TimeSlotScheduler
```

### 3.2 완전 프로세스 분리
- **worker_crawl.py**: 모든 무거운 작업 담당
- **완전 종료**: 각 실행 후 프로세스 완전 종료로 메모리 누수 방지
- **리소스 격리**: 브라우저, 비동기 루프 등 모든 리소스 완전 정리

---

## 4. 브라우저/프로세스 제한
### 2.1 파일 락 기반 멀티프로세스 브라우저 제한
- 여러 프로세스/서버/스케줄러/워커가 동시에 브라우저를 띄우지 못하도록 **파일 락**을 사용합니다.
- 예시 코드 (StandardBrowserManager):
```python
import fcntl

class StandardBrowserManager(BrowserManager):
    _file_lock = None
    _file_lock_path = '/tmp/crawler_browser.lock'

    def acquire_file_lock(self):
        if StandardBrowserManager._file_lock is not None:
            return
        f = open(self._file_lock_path, 'w')
        try:
            fcntl.flock(f, fcntl.LOCK_EX | fcntl.LOCK_NB)
            StandardBrowserManager._file_lock = f
        except BlockingIOError:
            f.close()
            raise RuntimeError('다른 프로세스에서 이미 브라우저를 실행 중입니다.')

    def release_file_lock(self):
        if StandardBrowserManager._file_lock is not None:
            try:
                fcntl.flock(StandardBrowserManager._file_lock, fcntl.LOCK_UN)
                StandardBrowserManager._file_lock.close()
            except Exception:
                pass
            StandardBrowserManager._file_lock = None
```
- 크롤러 시작 시 `acquire_file_lock()`, 종료 시 `release_file_lock()` 호출.
- 락 파일 경로는 환경에 맞게 조정 가능.

### 2.2 브라우저 인스턴스 개수 제한
- config/config.py:
```python
MAX_CONCURRENT_BROWSERS = 2
```
- 크롤러에서 인스턴스 개수 초과 시 예외 발생, 추가 생성 불가.

### 2.3 Playwright 옵션 (프로세스 폭증 방지)
```python
BROWSER_ARGS = [
    '--no-sandbox',
    '--disable-dev-shm-usage',
    '--disable-gpu',
    '--disable-web-security',
    '--memory-pressure-off',
    '--max_old_space_size=128',
    '--disable-background-timer-throttling',
    '--disable-backgrounding-occluded-windows',
    '--disable-renderer-backgrounding',
    '--disable-extensions',
    '--disable-plugins',
    '--disable-images',
    '--single-process',
    '--renderer-process-limit=1',
    '--no-zygote',
]
```

---

## 3. 메모리/CPU 최적화
### 3.1 지연 로딩 패턴
- 무거운 라이브러리/객체는 필요할 때만 import/생성.
- 작업 후 즉시 del/gc.collect()로 메모리 반환.

### 3.2 임시 로거 패턴
- 로거를 사용 후 즉시 정리, 핸들러도 모두 제거.

### 3.3 적극적 가비지 컬렉션
```python
gc.set_threshold(50, 5, 5)
for _ in range(5):
    gc.collect()
```

### 3.4 컨텍스트 관리자 활용
- 브라우저, DB 등 리소스는 async context manager로 관리.

### 3.5 DB 연결/리소스 최소화
- 필요할 때만 연결, 사용 후 즉시 close/del/gc.collect().

---

## 4. 스케줄러/비동기 패턴
### 4.1 직렬 실행
- 타겟/매치 크롤링 등은 반드시 직렬로 실행, 동시 실행 금지.

### 4.2 빠른 페이지 로딩
```python
await page.goto(url, wait_until='domcontentloaded')
await asyncio.sleep(0.1)  # CPU spike 방지
```

### 4.3 Seoul 시간대 기반 비작업 시간 (8pm-8am KST)
```python
import pytz
from datetime import datetime

def is_working_hours():
    """서울 시간대 기준 작업 시간 체크 (8am-8pm)"""
    seoul_tz = pytz.timezone('Asia/Seoul')
    now = datetime.now(seoul_tz)
    current_hour = now.hour
    
    # 8am(08) ~ 8pm(20) 사이가 작업 시간
    return 8 <= current_hour < 20

def should_run_crawler():
    """크롤러 실행 여부 결정"""
    if not is_working_hours():
        print(f"Non-working hours (8pm-8am KST). Crawler suspended.")
        return False
    return True
```

### 4.4 스케줄러 적용 예시
```python
# main.py 또는 스케줄러에서 사용
if should_run_crawler():
    # 크롤러 실행
    await run_crawler()
else:
    # 비작업 시간: 대기 또는 최소한의 작업만 수행
    await asyncio.sleep(300)  # 5분 대기 후 재확인
```

### 4.5 스케줄러/워커 최적화
- 임시 로거, 지연 로딩, 컨텍스트 매니저, 적극적 GC, 리소스 최소화 패턴 적용.

---

## 5. 운영/모니터링/트러블슈팅
### 5.1 EC2 모니터링 명령어
```bash
# 메모리 사용량 (EC2 최적화)
free -h
ps aux | grep -E "(python|chrome|chromium)" | head -10

# EC2 인스턴스 메트릭
df -h  # 디스크 사용량
iostat -x 1 5  # I/O 성능 모니터링

# CPU 사용량 및 EC2 성능
htop
top -p $(pgrep -f "python.*main.py")
vmstat 1 5  # 시스템 성능 개요

# Chrome 프로세스 개수
pgrep -c chrome
ps aux | grep -c chromium

# EC2 네트워크 모니터링
iftop  # 네트워크 트래픽
netstat -i  # 네트워크 인터페이스 통계

# EC2 시스템 로그 확인
sudo tail -f /var/log/messages
sudo journalctl -f -u your-crawler-service
```

### 5.2 고아 프로세스/메모리 폭증 트러블슈팅
- 파일 락, 브라우저 인스턴스 제한, BROWSER_ARGS, GC, 컨텍스트 매니저가 모두 적용되어야 함.
- 고아 프로세스 발생 시:
```bash
pkill -f "chromium|chrome"
```
- PM2, systemd, crontab 등에서 중복 실행 방지 옵션 사용.

### 5.3 EC2 운영 팁
- 프로세스/메모리/브라우저 개수는 항상 예측 가능해야 함.
- 운영 중에는 반드시 2개 이하의 브라우저/크롬 프로세스만 존재해야 함.
- 장애/예외 발생 시 cleanup/락 해제 코드가 반드시 실행되어야 함.

#### EC2 특화 권장사항:
- **Swap 메모리 설정**: t3a.medium에서 2GB swap 파일 생성 권장
```bash
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

- **CloudWatch 모니터링 활용**: EC2 기본 메트릭 + 커스텀 메트릭
- **EBS 최적화**: gp3 볼륨 사용 권장 (비용/성능 최적)
- **Auto Scaling**: CPU/메모리 임계값 기반 자동 스케일링
- **Seoul 시간대 적용**: 비작업 시간(8pm-8am KST) 동안 리소스 절약

---

## 6. EC2 최적화 체크리스트
- [ ] 파일 락 기반 중복 실행 방지 적용
- [ ] 브라우저 인스턴스 개수 제한 적용
- [ ] BROWSER_ARGS 등 메모리/CPU 최적화 옵션 적용
- [ ] 지연 로딩/임시 로거/적극적 GC/컨텍스트 매니저 패턴 적용
- [ ] 스케줄러/워커 직렬 실행 보장
- [ ] Seoul 시간대 기반 비작업 시간 (8pm-8am KST) 적용
- [ ] EC2 Swap 메모리 설정 (2GB)
- [ ] CloudWatch 모니터링 설정
- [ ] 운영/모니터링/트러블슈팅 스크립트 준비
- [ ] PM2/systemd/crontab 등에서 중복 실행 방지
- [ ] pytz 패키지 설치 및 시간대 함수 구현

---

## 7. 적용 전후 비교
| 구분         | 기존(예상)         | 최적화 후(실측)      |
|--------------|-------------------|---------------------|
| 브라우저 개수| 1~N(중복 가능)    | 최대 2개            |
| 메모리 사용  | 3~4GB             | 2~2.5GB             |
| CPU 사용     | 90~100%           | 60~80%              |
| Chrome 프로세스| 5~8개           | 2~3개               |
| 장애 빈도    | OOM/다운 가끔     | 거의 없음           |
| 페이지 로딩  | 3~5초             | 1~2초               |

---

## 8. 결론
- 본 가이드의 모든 패턴(파일 락, 브라우저 제한, 메모리/CPU 옵션, GC, 컨텍스트 매니저, 직렬 실행, 운영 스크립트 등)을 적용하면
  - **운영 안정성**이 대폭 향상되고,
  - **메모리/CPU 사용량**이 30~50% 이상 절감되며,
  - **고아 프로세스/장애/서버 다운** 위험이 원천적으로 차단됩니다.
- EC2 t3a.medium 환경뿐 아니라, 모든 Python+Playwright 크롤러/스케줄러 운영에 강력히 권장합니다. 