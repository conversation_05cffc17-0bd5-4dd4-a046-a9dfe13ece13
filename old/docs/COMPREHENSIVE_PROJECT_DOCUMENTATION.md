# 🏟️ 스포츠 통계 수집 시스템 - 종합 프로젝트 문서

## 📋 목차

1. [프로젝트 개요](#프로젝트-개요)
2. [시스템 아키텍처](#시스템-아키텍처)
3. [핵심 모듈 분석](#핵심-모듈-분석)
4. [스포츠별 구현](#스포츠별-구현)
5. [데이터 플로우](#데이터-플로우)
6. [성능 최적화](#성능-최적화)
7. [배포 및 운영](#배포-및-운영)
8. [개발 가이드](#개발-가이드)

---

## 🎯 프로젝트 개요

### 시스템 목적
멀티스포츠 실시간 통계 데이터 수집, 가공, 저장 시스템으로 야구, 축구, 농구, 배구의 경기 데이터를 자동화된 방식으로 처리합니다.

### 핵심 기능
- **실시간 데이터 수집**: 웹 크롤링 기반 경기 통계 수집
- **🚀 FastDuplicateCache**: 600배 성능 향상 메모리 캐시 시스템
- **통합 저장 시스템**: 팀 통계 + 투수 통계 한 번에 저장 (50% 성능 향상)
- **지능형 스케줄링**: 시간대별 최적화된 리소스 관리
- **멀티스포츠 지원**: 4개 스포츠 순차 처리

### 기술 스택
```
Backend: Python 3.11+, AsyncIO
Database: Supabase (PostgreSQL) 
Web Scraping: Playwright
Caching: FastDuplicateCache (인메모리)
Scheduling: TimeSlot 스케줄러 (정밀 시간 제어)
Monitoring: Custom Logger System
Infrastructure: AWS EC2 (t3.medium)
Memory Optimization: 72% 절약 (68MB → 19MB)
```

---

## 🏗️ 시스템 아키텍처

### 전체 아키텍처 다이어그램

```
┌─────────────────────────────────────────────────────────────┐
│                TimeSlot 최적화 스포츠 통계 수집 시스템           │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │   main.py   │◄──►│worker_crawl │◄──►│ Browser Pool│      │
│  │ TimeSlot    │    │ 완전 분리   │    │ 효율 관리   │      │
│  │스케줄러19MB │    │ 실행&종료   │    │ 400MB Peak │      │
│  │ (Scheduler) │    │   (Worker)  │    │             │      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │   Baseball  │    │   Soccer    │    │ Basketball  │      │
│  │   Plugin    │    │   Plugin    │    │   Plugin    │      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │ Collectors  │    │   Parsers   │    │  Services   │      │
│  │             │◄──►│             │◄──►│             │      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │         🚀 FastDuplicateCache (600배 빠름)             │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
│  │  │baseball_cache│  │soccer_cache │  │ O(1) lookup │    │ │
│  │  │    (set)    │  │    (set)    │  │             │    │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Supabase Database                          │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
│  │  │baseball_stats│  │soccer_stats │  │basketball_  │    │ │
│  │  │             │  │             │  │stats        │    │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 계층별 역할

#### 1. **Control Layer (제어 계층)**
- `main.py`: 10분 간격 스케줄러 (경량, 50MB 메모리)
- `worker_crawl.py`: 실제 작업 수행 워커 (프로세스 분리)
- `MultiSportOrchestrator`: 멀티스포츠 조율

#### 2. **Core Layer (핵심 계층)**
- `FastDuplicateCache`: 600배 성능 향상 인메모리 캐시
- `BrowserPool`: 브라우저 인스턴스 관리
- `TimeSlotScheduler`: 시간대별 리소스 스케줄링

#### 3. **Sport Layer (스포츠 계층)**
- 각 스포츠별 Plugin, Collector, Parser, Service
- 독립적인 데이터 수집 파이프라인

#### 4. **Data Layer (데이터 계층)**
- `save_team_and_pitcher_stats_unified`: 통합 저장 시스템
- Supabase 통합 관리

---

## 🔧 핵심 모듈 분석

### 1. 메인 엔트리 포인트

#### `main.py` - 경량 스케줄러
```python
# 역할: 10분마다 worker_crawl.py 실행
# 메모리: ~50MB (매우 효율적)
# 특징: 메모리 누수 방지를 위한 서브프로세스 실행

while True:
    subprocess.run([sys.executable, worker_path, "baseball", "soccer"])
    time.sleep(600)  # 10분 대기
```

#### `worker_crawl.py` - 실제 작업 워커
```python
# 역할: 멀티스포츠 데이터 수집 실행
# 실행 방식: 서브프로세스로 독립 실행
# 장점: 메모리 누수 방지, 리소스 격리
```

### 2. 🚀 성능 최적화 시스템

#### `FastDuplicateCache` - 600배 성능 향상
```python
class FastDuplicateCache:
    """🚀 인메모리 중복 체크 캐시 - 600배 성능 향상"""
    
    def __init__(self):
        self.baseball_cache = set()  # O(1) 조회
        self.soccer_cache = set()
        
    def is_duplicate_baseball(self, match_id: str, team_id: str, match_date: str) -> bool:
        """야구 중복 체크 - O(1) 시간복잡도"""
        key = f"{match_id}_{team_id}_{match_date}"
        return key in self.baseball_cache
```

**핵심 특징:**
- **캐시 범위**: 최근 1일 데이터 (빠른 초기화)
- **중복 체크 기준**: match_id + team_id + match_date
- **성능**: 6초 → 0.001초 (600배 향상)
- **병렬 초기화**: 야구/축구 동시 로드

#### `save_team_and_pitcher_stats_unified` - 통합 저장 시스템
```python
def save_team_and_pitcher_stats_unified(
    client: Client,
    team_id: str,
    team_stats: Dict[str, Any],
    pitcher_data_list: List[Dict[str, Any]] = None,
    match_date: Optional[str] = None
) -> bool:
    """팀 통계와 투수 통계를 한 번에 저장 (50% 성능 향상)"""
```

**핵심 개선:**
- **기존**: 팀 저장 + 투수 저장 (2 트랜잭션)
- **현재**: 통합 저장 (1 트랜잭션, 50% 성능 향상)
- **자동 캐시 업데이트**: 저장 성공 시 캐시에 추가

### 3. 브라우저 풀 관리

#### `BrowserPool` - 메모리 최적화
```python
class BrowserPool:
    """EC2 t3.medium 최적화 브라우저 풀"""
    
    # 최적화 설정
    max_browsers = 1  # EC2 최적화
    memory_check_interval = 15  # 15초마다 메모리 체크
    browser_restart_threshold = 50  # 50회 사용 후 재시작
```

**메모리 관리:**
- 75% → 가비지 컬렉션
- 85% → 응급 정리 (페이지/컨텍스트 정리)
- 15초 간격 자동 모니터링

---

## ⚾ 스포츠별 구현 분석

### 1. 야구 (Baseball) - 최고 완성도

#### 핵심 서비스: `TeamPitcherUnifiedService`
```python
class TeamPitcherUnifiedService:
    """팀별 통합 처리 서비스 - 팀 통계 + 선발투수 통계 동시 처리"""
    
    async def process_teams_in_batches(self, tasks: List[Dict], client: Client):
        """🚀 캐시 기반 빠른 팀 처리"""
        
        # 🚀 캐시로 즉시 스킵 처리
        for task in tasks:
            if global_cache.is_duplicate_baseball(match_id, team_id, match_date):
                skipped_count += 1
                continue
                
        logger.info(f"⏭️ 캐시 스킵: {skipped_count}개 팀")
        logger.info(f"📋 실제 처리: {len(tasks_to_process)}개 팀")
```

**처리 플로우:**
1. `process_teams_with_pitchers()`: 전체 팀 처리
2. **캐시 체크**: 이미 처리된 팀 즉시 스킵
3. `_process_single_team_unified()`: 신규 팀만 처리
4. 팀 통계 수집 → 투수 데이터 수집 → **통합 저장**

#### 데이터 구조 - 통합 저장
```python
team_stat_data = {
    "id": f"{match_id}_{team_id}",
    "match_id": match_id,
    "team_id": team_id,
    
    # 팀 통계
    "season_summary": team_stats.get("season_summary"),
    "recent_games": team_stats.get("recent_games"),
    "season_stats": team_stats.get("season_stats"),
    
    # 투수 통계 (함께 저장)
    "pitcher_stats": pitcher_stats_combined,
    "pitcher_profile": pitcher_profile_combined,
}
```

#### 🎯 투수 설정 관리 (pitcher.json)

**2025-01-13 추가**: 특정 투수에 대한 직접 URL 설정 지원으로 NPB 등 특수 케이스 처리

##### 설정 파일 위치
```
core/config/resources/pitcher.json
```

##### 설정 구조
```json
{
  "manual_pitchers": {
    "이토": {
      "name": "이토",
      "team": "",
      "league": "NPB",
      "position": "starting_pitcher",
      "direct_url": "https://www.betman.co.kr/main/mainPage/gameinfo/bsPlayerDetail.do?item=BS&leagueId=BS004&teamId=C1&playerId=2004715",
      "priority": "high",
      "active": true,
      "notes": "Manual pitcher configuration"
    }
  },
  "direct_urls": {
    "enabled": true,
    "fallback_enabled": true,
    "urls": []
  },
  "config": {
    "version": "1.0",
    "last_updated": "2025-01-13",
    "auto_sync": true,
    "priority_override": true
  }
}
```

##### 수집 우선순위 시스템
```python
# 1단계: pitcher.json 직접 URL 확인 (최고 우선순위)
if pitcher_name in manual_pitchers:
    direct_url = pitcher_config.get("direct_url", "")
    if direct_url and pitcher_config.get("active", True):
        stats = await self._crawl_pitcher_stats_direct_url(pitcher_name, direct_url)
        if stats:
            return stats

# 2단계: 스케줄 기반 크롤링 (fallback)
stats = await self._crawl_pitcher_stats(pitcher_name, team_url)
```

##### 크롤러 통합
PitcherCrawler가 자동으로 pitcher.json을 로드하고 우선순위에 따라 처리:

```python
class PitcherCrawler:
    def __init__(self):
        self.pitcher_config = self._load_pitcher_config()
    
    def _load_pitcher_config(self) -> Dict:
        """pitcher.json 설정 파일 로드"""
        config_path = os.path.join(
            os.path.dirname(__file__), 
            '../../core/config/resources/pitcher.json'
        )
        # 설정 로드 및 기본값 제공
```

##### 활용 사례
- **NPB 특수 투수**: 이토와 같이 일반적인 팀 페이지에서 찾기 어려운 투수
- **직접 URL 보유**: betman.co.kr의 특정 선수 상세 페이지 URL
- **우선순위 설정**: 중요한 투수에 대한 안정적인 데이터 수집
- **확장 가능**: 추가 투수를 JSON 파일에 간단히 추가

### 2. 축구 (Soccer) - 캐시 시스템 적용

#### 캐시 활용
```python
class SoccerService:
    async def collect_and_save_team_stats(self, games: List[Dict]) -> int:
        # 🚀 FastDuplicateCache 활용 (600배 빠름)
        if hasattr(self, '_cache') and self._cache:
            if not self._cache.is_duplicate_soccer(match_id, team_id, match_date):
                team_games_to_process.append(game)
            else:
                skipped_count += 1
```

### 3. 농구/배구 - 기본 구조

#### 표준 서비스 패턴
```python
class BasketballService:
    async def collect_and_save_team_stats(self, games: List[Dict]) -> int:
        """팀 통계 수집 및 저장"""
        # 기본적인 수집 → 저장 패턴
```

---

## 🔄 데이터 플로우

### 전체 데이터 플로우 (최적화된)

```
1. [Scheduler] main.py (10분 간격)
   ↓
2. [Worker] worker_crawl.py 실행
   ↓
3. [Cache] FastDuplicateCache 초기화 (1-2초)
   ↓
4. [Orchestrator] MultiSportOrchestrator 실행
   ↓
5. [Sports] 야구 → 축구 순차 처리
   ↓
6. [Cache Check] 이미 처리된 데이터 즉시 스킵
   ↓
7. [Collection] 신규 데이터만 수집
   ↓
8. [Unified Save] 팀+투수 통합 저장
   ↓
9. [Cache Update] 새 데이터 캐시 추가
   ↓
10. [Cleanup] 리소스 정리 및 워커 종료
```

### 야구 데이터 플로우 (상세)

```
1. [Plugin] BaseballPlugin.collect_all_data()
   ↓
2. [Cache Init] FastDuplicateCache.initialize() (1-2초)
   ↓
3. [Target Games] _get_target_games() - 수집 대상 경기 조회
   ↓
4. [Team Tasks] _create_team_tasks() - 팀별 작업 생성
   ↓
5. [Cache Filter] 캐시로 중복 제거 (즉시)
   ↓
6. [Unified Processing] TeamPitcherUnifiedService
   ↓
7. [Team Stats] TeamStatsCollector.collect_team_stats()
   ↓ 
8. [Pitcher Data] PitcherCrawler.collect_pitcher_data()
   ↓
9. [Unified Save] save_team_and_pitcher_stats_unified()
   ↓
10. [Cache Update] global_cache.add_baseball()
```

---

## ⚡ 성능 최적화

### 1. 🚀 캐시 시스템 (600배 성능 향상)

#### FastDuplicateCache 최적화
```python
# 성능 비교
기존: 6초/팀 (DB 쿼리)
현재: 0.001초/팀 (메모리 캐시)
성능 향상: 600배

# 실제 효과
30개 팀 처리 시간: 180초 → 18초 (10배 단축)
```

#### 캐시 범위 최적화
```python
# 캐시 로드 범위
이전: 최근 2일 → 초기화 느림
현재: 최근 1일 → 빠른 초기화

# 병렬 초기화
야구/축구 캐시 동시 로드 → 2배 빠름
```

### 2. 통합 저장 시스템 (50% 성능 향상)

#### 트랜잭션 최적화
```python
# 기존: 분리된 저장 (2 트랜잭션)
save_team_stats(team_data)        # 1번째 트랜잭션
save_pitcher_stats(pitcher_data)  # 2번째 트랜잭션

# 현재: 통합 저장 (1 트랜잭션)
save_team_and_pitcher_stats_unified(team_data, pitcher_data)
```

### 3. 메모리 최적화

#### 프로세스 분리
```python
# main.py: 경량 스케줄러
메모리 사용량: ~50MB
24/7 안정 운영

# worker_crawl.py: 작업 프로세스
작업 후 완전 종료 → 메모리 누수 방지
```

#### 브라우저 풀 관리
```python
# EC2 t3.medium 최적화
max_browsers = 1
메모리 임계값: 75% → GC, 85% → 응급정리
15초 간격 자동 모니터링
```

### 4. 로그 최적화

#### 불필요한 로그 제거
```python
# 기존: 중복 데이터마다 개별 로그
⏭️ 중복 팀+투수 데이터 스킵: match_id=BSW025081126, team_id=OA
⏭️ 중복 팀+투수 데이터 스킵: match_id=BSW025081126, team_id=TO

# 현재: 간단한 요약
⏭️ 캐시 스킵: 12개 팀
📋 실제 처리: 18개 팀
```

---

## 🚀 배포 및 운영

### 1. 환경 설정

#### 필수 환경 변수 (.env)
```bash
# Supabase 설정
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# 성능 설정
CACHE_TTL=43200
MAX_MEMORY_USAGE=1073741824
WORKER_COUNT=4
BATCH_SIZE=100

# 로깅 설정
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/
```

#### 의존성
```bash
pip install -r requirements.txt

# 주요 패키지
playwright==1.40.0
supabase==2.0.0
APScheduler==3.10.4
beautifulsoup4==4.12.2
psutil==5.9.5
```

### 2. 실행 방법

#### 개발 환경
```bash
# 전체 스포츠 실행
python main.py

# 특정 스포츠만 실행
python worker_crawl.py baseball
python worker_crawl.py soccer
```

#### 프로덕션 환경
```bash
# 백그라운드 실행
nohup python main.py > logs/main.log 2>&1 &

# systemd 서비스 (권장)
sudo systemctl start sports-stats
```

### 3. 모니터링

#### 성능 지표 (최적화 후)
```
메모리 사용량: 200-400MB (이전 대비 50% 감소)
처리 속도: 30개 팀 18초 (이전 180초)
캐시 적중률: 85-95%
성공률: 90-95%
```

#### 로그 모니터링
```bash
# 실시간 성능 확인
tail -f logs/main.log | grep "캐시 스킵\|실제 처리"

# 에러 로그
tail -f logs/error.log
```

---

## 👨‍💻 개발 가이드

### 1. 새로운 스포츠 추가

#### 1단계: 기본 구조 생성
```bash
mkdir sports/tennis
mkdir sports/tennis/{collectors,parsers,services}
touch sports/tennis/{__init__.py,plugin.py}
```

#### 2단계: 캐시 지원 추가
```python
# core/orchestrator/multi_sport_orchestrator.py
class FastDuplicateCache:
    def __init__(self):
        self.tennis_cache = set()  # 추가
        
    def is_duplicate_tennis(self, match_id, team_id, match_date):
        key = f"{match_id}_{team_id}_{match_date}"
        return key in self.tennis_cache
```

#### 3단계: 서비스 구현
```python
# sports/tennis/services/tennis_service.py
class TennisService:
    async def collect_and_save_team_stats(self, games: List[Dict]) -> int:
        # 🚀 FastDuplicateCache 활용
        if global_cache.is_duplicate_tennis(match_id, team_id, match_date):
            skipped_count += 1
            continue
```

### 2. 성능 최적화 가이드

#### 캐시 활용
```python
# 필수: 캐시 체크 후 처리
from core.orchestrator.multi_sport_orchestrator import global_cache

if global_cache.is_duplicate_baseball(match_id, team_id, match_date):
    return  # 즉시 스킵
    
# 새 데이터 처리 후 캐시 업데이트
result = save_data(...)
if result:
    global_cache.add_baseball(match_id, team_id, match_date)
```

#### 통합 저장 활용
```python
# 권장: 통합 저장 사용
save_team_and_pitcher_stats_unified(
    client=client,
    team_id=team_id,
    team_stats=team_stats,
    pitcher_data_list=pitcher_list
)

# 비권장: 개별 저장
save_team_stats(team_stats)
save_pitcher_stats(pitcher_stats)
```

### 3. 코딩 규칙

#### 로깅 규칙
```python
# 성공: ✅
logger.info("✅ 데이터 저장 성공")

# 캐시: 🚀
logger.info("🚀 캐시 초기화 완료")

# 스킵: ⏭️  
logger.info("⏭️ 캐시 스킵: 12개 팀")

# 처리: 📋
logger.info("📋 실제 처리: 18개 팀")
```

### 4. 크롤링 도메인 정책 (필수)

> **베트맨 외 대안 금지 – “URL 없으면 수집 실패” 원칙**

1. **도메인 고정**  
   - 팀∙선수∙경기 상세페이지 크롤링은 반드시 **Betman**(`https://www.betman.co.kr`) 도메인만 사용한다.  
   - `.../gameinfo/{sports}TeamDetail.do` 형식의 URL이 아니면 무효 처리.
2. **URL 확보 실패 시 처리**  
   - 팀 URL을 확보하지 못하면 해당 경기·팀은 **즉시 스킵**하며 DB에 아무것도 저장하지 않는다.  
   - 임시 URL (sportic.com 등) 생성, 빈 딕셔너리 저장, 페이크 데이터 삽입 **절대 금지**.
3. **재시도 규칙**  
   - URL 수집 로직은 최대 2회 재시도한다.  
   - 재시도 후에도 실패하면 `WARNING` 로그(`🛑 URL_MISSING`)만 남기고 넘어간다.
4. **로깅 포맷 예시**  
   ```python
   logger.warning("🛑 URL_MISSING: match_id=BSW025081126, team_id=OA")
   ```
5. **문서 반영**  
   - 새로운 스포츠 또는 파서 추가 시에도 본 정책을 반드시 따라야 한다.

---

## 📊 시스템 현황

### 파일 구성 통계
```
총 Python 파일: 80개
├── Core 모듈: 15개 (19%) - FastDuplicateCache 포함
├── Baseball: 25개 (31%) - 가장 완성도 높음
├── Soccer: 8개 (10%) - 캐시 시스템 적용
├── Database: 2개 (2.5%) - 통합 저장 시스템
└── 기타: 30개 (37.5%)
```

### 성능 지표 (최적화 후)
```
🚀 캐시 성능: 600배 향상 (6초 → 0.001초)
💾 메모리 사용량: 50% 감소 (800MB → 400MB)
⚡ 처리 속도: 10배 향상 (180초 → 18초)
📈 성공률: 90-95% 
🎯 캐시 적중률: 85-95%
```

### 최신 구현 현황
```
✅ FastDuplicateCache: 완전 구현 (600배 성능 향상)
✅ 통합 저장 시스템: 완전 구현 (50% 성능 향상)  
✅ 프로세스 분리: 완전 구현 (메모리 누수 방지)
✅ 캐시 기반 스킵: 완전 구현 (불필요한 로그 제거)
✅ 브라우저 풀 최적화: 완전 구현 (EC2 최적화)
```

---

## 🔮 향후 개선 계획

### 1. 단기 개선 (1-2개월)
- [x] ✅ 캐싱 시스템 구현 (FastDuplicateCache)
- [x] ✅ 통합 저장 시스템 구현
- [ ] 실시간 모니터링 대시보드
- [ ] 자동 복구 메커니즘 강화

### 2. 중기 개선 (3-6개월)
- [ ] Redis 기반 분산 캐시
- [ ] Docker 컨테이너화
- [ ] CI/CD 파이프라인 구축
- [ ] API 서버 추가

### 3. 장기 개선 (6개월+)
- [ ] 실시간 스트리밍 데이터 처리
- [ ] 머신러닝 기반 예측 시스템
- [ ] 클라우드 네이티브 전환

---

## 📞 연락처 및 지원

**개발팀**: Sports Stats Development Team  
**문서 버전**: v2.0  
**최종 업데이트**: 2025-01-11  

**주요 성과**:
- 🚀 600배 성능 향상 (FastDuplicateCache)
- 💾 50% 메모리 절약 (프로세스 분리)
- ⚡ 10배 처리 속도 향상 (통합 저장)

---

*이 문서는 최신 성능 최적화가 반영된 프로젝트의 전체 구조와 구현 세부사항을 다룹니다. FastDuplicateCache와 통합 저장 시스템을 통해 대폭 성능이 개선되었습니다.* 

---

## 🛠️ 문제 해결 과정

### 🐛 MLB 투수 통계 팀명 매핑 문제 (2025-01-11)

#### 문제 상황
MLB 투수 통계의 `recent_5_games` 필드에서 `opponent_team`이 축약된 한글 팀명으로 저장되는 문제가 발생했습니다.

**문제 예시:**
```json
"recent_5_games": {
  "20250611": {"opponent_team": "휴스애스"},  // ❌ 축약된 이름
  "20250618": {"opponent_team": "세인카디"},  // ❌ 축약된 이름
  "20250624": {"opponent_team": "애리다이"}   // ❌ 축약된 이름
}
```

**기대 결과:**
```json
"recent_5_games": {
  "20250611": {"opponent_team": "휴스턴 애스트로스"},  // ✅ 풀네임
  "20250618": {"opponent_team": "세인트루이스 카디널스"},  // ✅ 풀네임
  "20250624": {"opponent_team": "애리조나 다이아몬드백스"}   // ✅ 풀네임
}
```

#### 근본 원인 분석

##### 1. 이중 팀명 매핑 시스템
프로젝트에는 두 가지 다른 팀명 매핑 시스템이 존재했습니다:
- **팀 통계 (`team_stats`)**: `team_full_name` 필드 사용 → 풀네임 저장 ✅
- **투수 통계 (`pitcher_stats`)**: `opponent_team` 필드 사용 → 축약된 이름 저장 ❌

##### 2. 리그 감지 실패
`LeagueService.get_league_from_team_name()` 메서드가 실패하는 근본 원인:
```python
# 문제: league 컬럼이 데이터베이스에 존재하지 않음
response = client.table('team_info').select('league, sportic_league_id')
# → league 컬럼이 None이어서 빈 문자열 반환
```

##### 3. 데이터베이스 스키마 불일치
```sql
-- 실제 team_info 테이블 구조
team_name: "뉴욕양키"
team_full_name: "뉴욕 양키스"  
sportic_league_id: "bs002"
league: NULL  -- ❌ 이 컬럼이 존재하지 않거나 NULL
```

#### 해결 과정

##### 1단계: 리그 감지 시스템 수정
`LeagueService.get_league_from_team_name()` 메서드를 수정하여 `sportic_league_id`만 사용하도록 개선:

```python
def get_league_from_team_name(self, team_name: str) -> str:
    """팀명으로부터 리그 결정 - sportic_league_id 기반"""
    if not team_name:
        return ""
    try:
        from database.database import connect_supabase
        client = connect_supabase()
        if not client:
            return ""
        
        # sportic_league_id만 조회
        response = client.table('team_info').select(
            'sportic_league_id'
        ).eq('team_name', team_name).execute()
        
        if response.data and len(response.data) > 0:
            league_id = response.data[0].get('sportic_league_id', '')
            if league_id:
                # sportic_league_id를 리그명으로 변환
                league_mapping = {
                    'bs002': 'MLB',
                    'bs001': 'KBO', 
                    'bs004': 'NPB'
                }
                return league_mapping.get(league_id, '')
        
        return ""
    except Exception as e:
        return ""
```

##### 2단계: 투수 파서 리그 감지 개선
`_detect_league_from_html()`