"""
Text Processor - Single Responsibility Principle (SRP)
Focused solely on text processing and cleaning operations
"""
from typing import List


def remove_duplication(text: str) -> str:
    """
    중복된 내용 제거
    
    Args:
        text: 원본 텍스트
        
    Returns:
        str: 중복이 제거된 텍스트
    """
    if not text:
        return ""
    
    # 콜론 뒤의 내용만 추출
    if ":" in text:
        text = text.split(":", 1)[1].strip()
    
    # 동일한 텍스트가 반복되는 경우 (예: "오티즈오티즈" -> "오티즈")
    text_len = len(text)
    if text_len % 2 == 0:
        half_len = text_len // 2
        first_half = text[:half_len]
        second_half = text[half_len:]
        if first_half == second_half:
            return first_half
    
    return text


def get_td_text(tds: List, index: int) -> str:
    """
    HTML td 요소에서 텍스트 추출
    
    Args:
        tds: td 요소 목록
        index: 가져올 요소의 인덱스
        
    Returns:
        str: 추출된 텍스트
    """
    if index < len(tds):
        return tds[index].get_text(strip=True)
    return ""


def log_duplicate_check_stats(
    total_checks: int,
    skipped_count: int,
    processed_count: int,
    data_type: str = "데이터"
) -> None:
    """
    중복 체크 통계를 로그로 출력
    
    Args:
        total_checks: 총 체크한 항목 수
        skipped_count: 중복으로 스킵한 항목 수  
        processed_count: 실제 처리한 항목 수
        data_type: 데이터 타입 설명
    """
    from utils.logger import Logger
    logger = Logger(__name__)
    
    skip_rate = (skipped_count / total_checks * 100) if total_checks > 0 else 0
    
    logger.info(
        f"📊 {data_type} 중복 체크 완료: "
        f"총 {total_checks}개 중 {skipped_count}개 스킵 ({skip_rate:.1f}%), "
        f"{processed_count}개 새로 처리"
    )


def get_duplicate_check_summary(
    team_stats_results: list = None,
    pitcher_stats_results: list = None
) -> dict:
    """
    중복 체크 결과 요약 생성
    
    Args:
        team_stats_results: 팀 통계 저장 결과 리스트
        pitcher_stats_results: 투수 통계 저장 결과 리스트
    
    Returns:
        dict: 중복 체크 요약 정보
    """
    summary = {
        'team_stats': {
            'total': 0,
            'skipped': 0,
            'processed': 0,
            'skip_rate': 0.0
        },
        'pitcher_stats': {
            'total': 0,
            'skipped': 0,
            'processed': 0,
            'skip_rate': 0.0
        }
    }
    
    # 팀 통계 결과 분석
    if team_stats_results:
        summary['team_stats']['total'] = len(team_stats_results)
        summary['team_stats']['processed'] = sum(
            1 for r in team_stats_results if r
        )
        summary['team_stats']['skipped'] = (
            summary['team_stats']['total'] - 
            summary['team_stats']['processed']
        )
        if summary['team_stats']['total'] > 0:
            summary['team_stats']['skip_rate'] = (
                summary['team_stats']['skipped'] / 
                summary['team_stats']['total']
            ) * 100
    
    # 투수 통계 결과 분석
    if pitcher_stats_results:
        summary['pitcher_stats']['total'] = len(pitcher_stats_results)
        summary['pitcher_stats']['processed'] = sum(
            1 for r in pitcher_stats_results if r
        )
        summary['pitcher_stats']['skipped'] = (
            summary['pitcher_stats']['total'] - 
            summary['pitcher_stats']['processed']
        )
        if summary['pitcher_stats']['total'] > 0:
            summary['pitcher_stats']['skip_rate'] = (
                summary['pitcher_stats']['skipped'] / 
                summary['pitcher_stats']['total']
            ) * 100
    
    return summary