"""
Date Parser - Single Responsibility Principle (SRP)
Focused solely on parsing various date formats
"""
import re
from datetime import datetime
from typing import Optional


def parse_date_from_string(date_string: str) -> str:
    """날짜 문자열을 파싱하여 YYYY-MM-DD 형태로 반환"""
    if not date_string:
        return ""
    
    # 일반적인 형식들을 처리
    patterns = [
        r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
        r'(\d{4})\.(\d{1,2})\.(\d{1,2})',  # YYYY.MM.DD
        r'(\d{4})/(\d{1,2})/(\d{1,2})',  # YYYY/MM/DD
        r'(\d{1,2})-(\d{1,2})-(\d{4})',  # MM-DD-YYYY
        r'(\d{1,2})\.(\d{1,2})\.(\d{4})',  # MM.DD.YYYY
        r'(\d{1,2})/(\d{1,2})/(\d{4})',  # MM/DD/YYYY
    ]
    
    for pattern in patterns:
        match = re.search(pattern, date_string)
        if match:
            groups = match.groups()
            if len(groups[0]) == 4:  # YYYY 형식
                year, month, day = groups
            else:  # MM/DD/YYYY 형식
                month, day, year = groups
            
            # 월과 일을 두 자리로 맞춤
            month = month.zfill(2)
            day = day.zfill(2)
            
            return f"{year}-{month}-{day}"
    
    # 파싱 실패 시 원본 반환
    return date_string


def convert_korean_date_to_ymd(date_str: str) -> str:
    """한국어 날짜 문자열을 YYYY-MM-DD 형식으로 변환"""
    if not date_str:
        return ""
    
    # "2024년 3월 15일" 형태를 파싱
    match = re.search(r'(\d{4})년\s*(\d{1,2})월\s*(\d{1,2})일', date_str)
    if match:
        year, month, day = match.groups()
        month = month.zfill(2)
        day = day.zfill(2)
        return f"{year}-{month}-{day}"
    
    # 파싱 실패 시 원본 반환
    return date_str


def extract_date_from_korean(text: str) -> str:
    """한국어 텍스트에서 날짜 추출 후 YYYY-MM-DD 형식으로 변환"""
    if not text:
        return ""
    
    # 다양한 한국어 날짜 패턴들
    patterns = [
        r'(\d{4})년\s*(\d{1,2})월\s*(\d{1,2})일',
        r'(\d{2,4})\.(\d{1,2})\.(\d{1,2})',
        r'(\d{2,4})-(\d{1,2})-(\d{1,2})',
        r'(\d{2,4})/(\d{1,2})/(\d{1,2})',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            year, month, day = match.groups()
            
            # 2자리 년도를 4자리로 변환 (20xx 가정)
            if len(year) == 2:
                year = f"20{year}"
            
            month = month.zfill(2)
            day = day.zfill(2)
            
            return f"{year}-{month}-{day}"
    
    return ""


def convert_date_str(date_str: str, year: Optional[int] = None) -> str:
    """'05.25(일)' -> '2025-05-25' 형식으로 변환"""
    if year is None:
        year = datetime.now().year
    
    match = re.match(r'(\d{2})\.(\d{2})', date_str)
    if not match:
        raise ValueError(f"날짜 형식이 올바르지 않습니다: {date_str}")
    
    month, day = match.groups()
    return f"{year}-{month}-{day}"