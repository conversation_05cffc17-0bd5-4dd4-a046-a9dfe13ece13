"""
Data Conversion Service - Single Responsibility Principle (SRP)
Focused solely on data type conversion operations
"""
import re
from typing import Any, Dict, List, Union

import pandas as pd


def safe_float(value: Any, default: float = 0.0) -> float:
    """안전하게 부동소수점으로 변환, 실패 시 기본값 반환"""
    if not value:
        return default
        
    try:
        if isinstance(value, str):
            # 쉼표 제거 및 숫자만 추출
            clean_value = re.sub(r'[^\d.+-]', '', value.replace(',', ''))
            if clean_value:
                return float(clean_value)
        elif isinstance(value, (int, float)):
            return float(value)
    except (ValueError, TypeError):
        pass
        
    return default


def to_json_safe(data: Union[Dict, List, Any]) -> Union[Dict, List, Any]:
    """데이터를 JSON 직렬화 가능한 형식으로 변환"""
    if isinstance(data, dict):
        return {k: to_json_safe(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [to_json_safe(item) for item in data]
    elif isinstance(data, (int, float, str, bool, type(None))):
        return data
    elif pd.isna(data):
        return None
    else:
        # 기타 객체는 문자열로 변환
        return str(data)


def format_pitcher_name(name: str) -> str:
    """투수 이름 형식 정리 (공백 제거, 일관된 형식으로 변환)"""
    if not name:
        return ""
        
    # 공백 제거 및 이름 정리
    cleaned = re.sub(r'\s+', ' ', name).strip()
    
    return cleaned