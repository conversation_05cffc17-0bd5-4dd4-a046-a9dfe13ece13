"""
URL Extraction Service - Single Responsibility Principle (SRP)
Focused solely on URL parsing and ID extraction operations
"""
import re


def extract_league_id(href: str) -> str:
    """URL에서 리그 ID 추출"""
    match = re.search(r'leagueId=([^&]+)', href)
    return match.group(1) if match else ""


def extract_team_id(href: str) -> str:
    """URL에서 팀 ID 추출"""
    match = re.search(r'teamId=([^&]+)', href)
    return match.group(1) if match else ""