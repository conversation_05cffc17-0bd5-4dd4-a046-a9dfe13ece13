"""
Date Processing Service - Single Responsibility Principle (SRP)
Focused solely on date parsing and formatting operations
"""
import re
from datetime import datetime
from typing import Optional


def extract_iso_date(date_text: str) -> str:
    """한글 날짜 텍스트에서 ISO 형식 날짜 추출 (YYYY-MM-DD)"""
    try:
        # '2023년 04월 12일 (수)' 형식을 '2023-04-12'로 변환
        pattern = r'(\d{4})년\s*(\d{1,2})월\s*(\d{1,2})일'
        match = re.search(pattern, date_text)
        if match:
            year, month, day = match.groups()
            return f"{year}-{int(month):02d}-{int(day):02d}"
        return ""
    except Exception:
        return ""


def format_date(date_str: Optional[str] = None,
                format_str: str = '%Y-%m-%d') -> str:
    """날짜 문자열 포맷팅, None이면 현재 날짜 사용"""
    if date_str:
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            return date_obj.strftime(format_str)
        except ValueError:
            pass
    
    # 날짜가 없거나 유효하지 않으면 현재 날짜 사용
    return datetime.now().strftime(format_str)


def create_file_name(prefix: str, date_str: Optional[str] = None,
                     suffix: str = 'json') -> str:
    """날짜를 포함한 파일명 생성"""
    date_part = format_date(date_str, '%Y%m%d')
    return f"{prefix}_{date_part}.{suffix}"