"""
크롤링 관련 예외 클래스 정의
"""
from typing import Optional

from utils.logger import Logger

# 로거 초기화
logger = Logger(__name__)


class CrawlerException(Exception):
    """크롤링 관련 기본 예외 클래스"""
    def __init__(self, message: str = "크롤링 작업 중 오류가 발생했습니다."):
        self.message = message
        super().__init__(self.message)


class NavigationError(CrawlerException):
    """페이지 탐색 관련 예외"""
    def __init__(self, url: str, message: Optional[str] = None):
        self.url = url
        msg = message or f"URL 탐색 중 오류 발생: {url}"
        super().__init__(msg)


class ParsingError(CrawlerException):
    """HTML 파싱 관련 예외"""
    def __init__(self, element: str, message: Optional[str] = None):
        self.element = element
        msg = message or f"HTML 요소 파싱 중 오류 발생: {element}"
        super().__init__(msg)


class TimeoutError(CrawlerException):
    """타임아웃 관련 예외"""
    def __init__(self, operation: str, timeout: int, message: Optional[str] = None):
        self.operation = operation
        self.timeout = timeout
        msg = message or f"작업 타임아웃: {operation}, {timeout}ms 초과"
        super().__init__(msg)


class DataExtractionError(CrawlerException):
    """데이터 추출 관련 예외"""
    def __init__(self, data_type: str, message: Optional[str] = None):
        self.data_type = data_type
        msg = message or f"{data_type} 데이터 추출 중 오류 발생"
        super().__init__(msg) 