"""
utils/process_utils.py - 서브프로세스 실행 및 운영시간 체크 유틸리티
"""
import subprocess
import sys
from datetime import datetime
import pytz

# Seoul 시간대 기준 작업 시간 (8am-8pm KST)
START_HOUR = 8
END_HOUR = 20  # 22에서 20으로 변경 (8pm)

def is_operating_hour():
    """Seoul 시간대 기준 작업 시간 체크 (8am-8pm KST)"""
    seoul_tz = pytz.timezone('Asia/Seoul')
    now = datetime.now(seoul_tz)
    return START_HOUR <= now.hour < END_HOUR

def run_worker(worker_path='worker_crawl.py'):
    """worker_crawl.py 등 워커 서브프로세스 실행"""
    result = subprocess.run([sys.executable, worker_path])
    return result.returncode