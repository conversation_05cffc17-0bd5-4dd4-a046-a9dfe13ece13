"""
Parser Factory - Factory Pattern Implementation
Creates appropriate parsers based on requirements
"""
from typing import Dict, Type

from utils.protocols import (
    DataParser, DateParser, GameResultParser, 
    InningsProcessor, TextProcessor
)
from utils.parsers.date_parser import (
    DateParserService, KoreanDateParser, 
    StandardDateParser, FlexibleDateParser
)
from utils.parsers.game_result_parser import (
    BaseballResultParser, SoccerResultParser, 
    GameResultParserFactory
)
from utils.parsers.innings_parser import InningsProcessorService
from utils.parsers.text_processor import TextProcessingService


class ParserFactory:
    """Factory for creating various parsers (Factory Pattern)"""
    
    # Date parsers registry
    _date_parsers: Dict[str, Type[DateParser]] = {
        'korean': KoreanDateParser,
        'standard': StandardDateParser,
        'flexible': FlexibleDateParser,
    }
    
    # Game result parsers registry
    _game_parsers: Dict[str, Type[GameResultParser]] = {
        'baseball': BaseballResultParser,
        'soccer': SoccerResultParser,
    }
    
    @classmethod
    def create_date_parser(cls, parser_type: str = 'standard') -> DateParser:
        """
        Create date parser by type
        
        Args:
            parser_type: Type of parser ('korean', 'standard', 'flexible')
            
        Returns:
            DateParser: Appropriate date parser instance
            
        Raises:
            ValueError: If parser type is not supported
        """
        parser_class = cls._date_parsers.get(parser_type.lower())
        if not parser_class:
            raise ValueError(
                f"Unknown date parser type: {parser_type}. "
                f"Supported types: {list(cls._date_parsers.keys())}"
            )
        return parser_class()
    
    @classmethod
    def create_game_result_parser(cls, sport_type: str) -> GameResultParser:
        """
        Create game result parser by sport type
        
        Args:
            sport_type: Type of sport ('baseball', 'soccer')
            
        Returns:
            GameResultParser: Appropriate game result parser instance
            
        Raises:
            ValueError: If sport type is not supported
        """
        return GameResultParserFactory.create_parser(sport_type)
    
    @classmethod
    def create_date_service(cls) -> DateParserService:
        """Create unified date parsing service"""
        return DateParserService()
    
    @classmethod
    def create_innings_service(cls) -> InningsProcessorService:
        """Create innings processing service"""
        return InningsProcessorService()
    
    @classmethod
    def create_text_service(cls) -> TextProcessingService:
        """Create text processing service"""
        return TextProcessingService()
    
    @classmethod
    def get_supported_date_parsers(cls) -> list:
        """Get list of supported date parser types"""
        return list(cls._date_parsers.keys())
    
    @classmethod
    def get_supported_game_parsers(cls) -> list:
        """Get list of supported game parser types"""
        return GameResultParserFactory.get_supported_sports()


class UnifiedParserService:
    """
    Unified service combining all parsers (Facade Pattern)
    Single entry point for all parsing operations
    """
    
    def __init__(self):
        self._date_service = ParserFactory.create_date_service()
        self._innings_service = ParserFactory.create_innings_service()
        self._text_service = ParserFactory.create_text_service()
        self._game_parsers = {}  # Cache for game parsers
    
    # Date parsing methods
    def parse_date(self, date_string: str, parser_type: str = 'auto') -> str:
        """Parse date using appropriate parser"""
        if parser_type == 'auto':
            return self._date_service.parse_date(date_string)
        else:
            parser = ParserFactory.create_date_parser(parser_type)
            return parser.parse_date(date_string)
    
    def convert_korean_date(self, date_str: str) -> str:
        """Convert Korean date format"""
        return self._date_service.convert_korean_date(date_str)
    
    def extract_date_from_text(self, text: str) -> str:
        """Extract date from text"""
        return self._date_service.extract_date_from_text(text)
    
    # Game result parsing methods
    def parse_game_result(self, result_text: str, sport_type: str) -> str:
        """Parse game result for specific sport"""
        if sport_type not in self._game_parsers:
            self._game_parsers[sport_type] = ParserFactory.create_game_result_parser(
                sport_type
            )
        
        parser = self._game_parsers[sport_type]
        return parser.parse_result(result_text)
    
    # Innings processing methods
    def normalize_innings(self, innings_value: str) -> str:
        """Normalize innings value"""
        return self._innings_service.normalize_innings(innings_value)
    
    def parse_innings_to_float(self, innings_str: str) -> float:
        """Parse innings to float"""
        return self._innings_service.parse_to_float(innings_str)
    
    # Text processing methods
    def remove_text_duplication(self, text: str) -> str:
        """Remove text duplications"""
        return self._text_service.remove_duplication(text)
    
    def extract_td_text(self, tds: list, index: int) -> str:
        """Extract text from HTML td element"""
        return self._text_service.get_td_text(tds, index)
    
    # Statistics and logging
    def log_duplicate_stats(self, total_checks: int, skipped_count: int,
                           processed_count: int, data_type: str = "데이터") -> None:
        """Log duplicate check statistics"""
        self._text_service.log_duplicate_stats(
            total_checks, skipped_count, processed_count, data_type
        )
    
    def get_duplicate_summary(self, team_stats_results: list = None,
                             pitcher_stats_results: list = None) -> dict:
        """Get duplicate check summary"""
        return self._text_service.get_duplicate_summary(
            team_stats_results, pitcher_stats_results
        )