"""
팀 매핑 서비스 - 팀명 매핑 공통 로직 (팀 서비스, 투수 서비스 공용)
Single Responsibility: 팀명 변환만 담당
"""
from typing import Dict, Set

from utils.logger import Logger

# 로거 설정
logger = Logger(__name__)


class OptimizedTeamMappingService:
    """최적화된 팀명 매핑 서비스 - ID/이름 set 기반 고속 검색"""
    
    def __init__(self):
        self._team_mappings: Dict[str, str] = {}
        self._team_id_set: Set[str] = set()
        self._team_name_set: Set[str] = set()
        self._loaded = False
    
    async def get_team_mappings(self) -> Dict[str, str]:
        """팀 매핑 정보 조회 (싱글톤 패턴)"""
        if not self._loaded:
            await self._load_optimized_mappings()
            self._loaded = True
        
        return self._team_mappings
    
    async def _load_optimized_mappings(self):
        """최적화된 팀 매핑 로드 - ID/이름 set 생성"""
        try:
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                logger.warning("Supabase 연결 실패 - 빈 매핑 사용")
                return
            
            # team_info 테이블에서 기본 필드만 조회
            response = client.table('team_info').select(
                'team_id, team_name, team_full_name'
            ).execute()
            
            if not response.data:
                logger.warning("팀 매핑 데이터 없음")
                return
            
            # 중복 제거 및 고속 검색을 위한 set 구성
            processed_teams = set()
            
            for team in response.data:
                team_id = team.get('team_id', '').strip()
                team_name = team.get('team_name', '').strip()
                team_full_name = team.get('team_full_name', '').strip()
                
                # 중복 방지용 키 생성
                team_key = (team_id, team_name, team_full_name)
                if team_key in processed_teams:
                    continue
                processed_teams.add(team_key)
                
                # ID set 구성
                if team_id:
                    self._team_id_set.add(team_id)
                
                # 이름 set 구성 (team_name, team_full_name만)
                for name in [team_name, team_full_name]:
                    if name:
                        self._team_name_set.add(name)
                
                # 매핑 딕셔너리 구성 (team_name -> team_full_name)
                target_name = team_full_name or team_name
                if target_name and team_name and team_name != target_name:
                    self._team_mappings[team_name] = target_name
            
        except Exception as e:
            logger.warning(f"팀 매핑 로드 실패: {e}")
    
    def has_team_id(self, team_id: str) -> bool:
        """팀 ID 존재 여부 고속 확인"""
        return team_id in self._team_id_set
    
    def has_team_name(self, team_name: str) -> bool:
        """팀명 존재 여부 고속 확인"""
        return team_name in self._team_name_set
    
    def get_team_full_name(self, team_name: str) -> str:
        """팀명을 풀네임으로 변환"""
        return self._team_mappings.get(team_name, team_name)


# 기존 호환성 유지
class TeamMappingService(OptimizedTeamMappingService):
    """기존 인터페이스 호환성 유지"""
    pass


# 싱글톤 인스턴스
_team_mapping_service = OptimizedTeamMappingService()


async def get_team_mappings() -> Dict[str, str]:
    """전역 팀 매핑 조회 함수"""
    return await _team_mapping_service.get_team_mappings()


def get_team_full_name(team_name: str, team_mappings: Dict[str, str]) -> str:
    """팀명을 풀네임으로 변환하는 유틸리티 함수"""
    return team_mappings.get(team_name, team_name)


def has_team_id(team_id: str) -> bool:
    """팀 ID 존재 여부 고속 확인"""
    return _team_mapping_service.has_team_id(team_id)


def has_team_name(team_name: str) -> bool:
    """팀명 존재 여부 고속 확인"""
    return _team_mapping_service.has_team_name(team_name) 