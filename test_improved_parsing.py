#!/usr/bin/env python3
"""
개선된 데이터 파싱 테스트 - 실제 HTML 구조 기반
"""
import asyncio
import sys
import os
from bs4 import BeautifulSoup
from typing import Dict, List, Optional

# 프로젝트 루트를 Python path에 추가
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class ImprovedSoccerParser:
    """개선된 축구 파서 - 실제 HTML 구조 기반"""
    
    def __init__(self, html: str):
        self.soup = BeautifulSoup(html, 'html.parser')
    
    def parse_team_profile(self) -> Dict[str, str]:
        """팀 프로필 파싱"""
        profile = {}
        
        # 팀명 추출 (실제 팀명은 다른 위치에 있을 수 있음)
        # 페이지 제목이나 특정 요소에서 팀명 찾기
        title_elem = self.soup.find('title')
        if title_elem:
            title_text = title_elem.get_text()
            if '팀' in title_text:
                profile['team_name'] = title_text.split('팀')[0].strip()
        
        return profile
    
    def parse_season_stats(self) -> Dict[str, List[Dict]]:
        """시즌 통계 파싱"""
        stats = {
            'season_overall': [],
            'recent_games': [],
            'historical': []
        }
        
        tables = self.soup.find_all('table')
        
        for i, table in enumerate(tables):
            caption = table.find('caption')
            caption_text = caption.get_text(strip=True) if caption else ""
            
            rows = table.find_all('tr')
            if len(rows) < 2:
                continue
            
            # 헤더 추출
            header_row = rows[0].find_all(['th', 'td'])
            headers = [cell.get_text(strip=True) for cell in header_row]
            
            # 데이터 행들 추출
            data_rows = []
            for row in rows[1:]:
                cells = row.find_all(['th', 'td'])
                if len(cells) == len(headers):
                    row_data = {}
                    for j, cell in enumerate(cells):
                        if j < len(headers):
                            row_data[headers[j]] = cell.get_text(strip=True)
                    if row_data:
                        data_rows.append(row_data)
            
            # 테이블 분류
            if '전체성적' in caption_text:
                stats['season_overall'] = data_rows
            elif '최근성적' in caption_text:
                stats['recent_games'] = data_rows
            elif '역대' in caption_text:
                stats['historical'] = data_rows
        
        return stats
    
    def parse_players(self) -> List[Dict[str, str]]:
        """선수 명단 파싱"""
        players = []
        
        # 선수 링크 찾기
        player_links = self.soup.find_all('a', href=lambda x: x and 'scPlayerDetail.do' in x)
        
        for link in player_links:
            player_name = link.get_text(strip=True)
            player_url = link.get('href', '')
            
            # URL에서 선수 ID 추출
            player_id = self._extract_player_id(player_url)
            
            if player_name and player_id:
                players.append({
                    'player_name': player_name,
                    'player_id': player_id,
                    'player_url': player_url
                })
        
        return players
    
    def _extract_player_id(self, url: str) -> Optional[str]:
        """URL에서 선수 ID 추출"""
        try:
            import re
            match = re.search(r'playerId=([^&]+)', url)
            return match.group(1) if match else None
        except:
            return None


class ImprovedSoccerPlayerParser:
    """개선된 축구 선수 파서"""
    
    def __init__(self, html: str):
        self.soup = BeautifulSoup(html, 'html.parser')
    
    def parse_player_profile(self) -> Dict[str, str]:
        """선수 프로필 파싱"""
        profile = {}
        
        # 선수명 추출
        title_elem = self.soup.find('title')
        if title_elem:
            title_text = title_elem.get_text()
            profile['player_name'] = title_text.split()[0] if title_text else ""
        
        return profile
    
    def parse_player_stats(self) -> Dict[str, List[Dict]]:
        """선수 통계 파싱"""
        stats = {
            'season_stats': [],
            'season_records': [],
            'career_stats': []
        }
        
        tables = self.soup.find_all('table')
        
        for table in tables:
            caption = table.find('caption')
            caption_text = caption.get_text(strip=True) if caption else ""
            
            rows = table.find_all('tr')
            if len(rows) < 2:
                continue
            
            # 헤더 추출
            header_row = rows[0].find_all(['th', 'td'])
            headers = [cell.get_text(strip=True) for cell in header_row]
            
            # 데이터 행들 추출
            data_rows = []
            for row in rows[1:]:
                cells = row.find_all(['th', 'td'])
                if len(cells) == len(headers):
                    row_data = {}
                    for j, cell in enumerate(cells):
                        if j < len(headers):
                            row_data[headers[j]] = cell.get_text(strip=True)
                    if row_data and '조회 결과가 없습니다' not in str(row_data):
                        data_rows.append(row_data)
            
            # 테이블 분류
            if '시즌 성적' in caption_text and '출전기록' not in caption_text:
                stats['season_stats'].extend(data_rows)
            elif '출전기록' in caption_text:
                stats['season_records'].extend(data_rows)
            elif '역대' in caption_text:
                stats['career_stats'].extend(data_rows)
        
        return stats


async def test_improved_soccer_parsing():
    """개선된 축구 파싱 테스트"""
    print("=" * 60)
    print("🚀 개선된 축구 파싱 테스트")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        
        # 울산 HD FC 팀 페이지 크롤링
        team_url = "https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=SC001&teamId=K01"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print(f"📄 팀 페이지 로딩: {team_url}")
            await page.goto(team_url, wait_until='networkidle')
            await asyncio.sleep(3)
            
            html = await page.content()
            await browser.close()
            
            # 개선된 파서로 데이터 추출
            parser = ImprovedSoccerParser(html)
            
            # 팀 프로필
            profile = parser.parse_team_profile()
            print(f"\n👤 팀 프로필: {profile}")
            
            # 시즌 통계
            season_stats = parser.parse_season_stats()
            print(f"\n📊 시즌 통계:")
            for stat_type, data in season_stats.items():
                print(f"   {stat_type}: {len(data)}개 항목")
                if data:
                    print(f"     첫 번째 항목: {list(data[0].keys())[:5]}...")
            
            # 선수 명단
            players = parser.parse_players()
            print(f"\n👥 선수 명단: {len(players)}명")
            for i, player in enumerate(players[:5]):
                print(f"   {i+1}. {player['player_name']} (ID: {player['player_id']})")
            
            # 선수 개별 페이지 테스트
            if players:
                first_player = players[0]
                player_url = f"https://www.betman.co.kr/main/mainPage/gameinfo/{first_player['player_url']}"
                
                print(f"\n🔍 선수 개별 페이지 테스트: {first_player['player_name']}")
                
                async with async_playwright() as p2:
                    browser2 = await p2.chromium.launch(headless=True)
                    page2 = await browser2.new_page()
                    
                    await page2.goto(player_url, wait_until='networkidle')
                    await asyncio.sleep(3)
                    
                    player_html = await page2.content()
                    await browser2.close()
                    
                    # 선수 파서로 데이터 추출
                    player_parser = ImprovedSoccerPlayerParser(player_html)
                    
                    # 선수 프로필
                    player_profile = player_parser.parse_player_profile()
                    print(f"     프로필: {player_profile}")
                    
                    # 선수 통계
                    player_stats = player_parser.parse_player_stats()
                    print(f"     통계:")
                    for stat_type, data in player_stats.items():
                        print(f"       {stat_type}: {len(data)}개 항목")
                        if data:
                            print(f"         샘플: {data[0]}")
            
            return True
            
    except Exception as e:
        print(f"❌ 개선된 축구 파싱 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """메인 테스트 함수"""
    print("🚀 개선된 데이터 파싱 테스트 시작")
    print("=" * 80)
    
    success = await test_improved_soccer_parsing()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ 개선된 파싱 테스트 성공!")
        print("🎯 실제 데이터 구조 파악 및 파싱 로직 완성")
    else:
        print("❌ 개선된 파싱 테스트 실패")
    print("=" * 80)


if __name__ == "__main__":
    asyncio.run(main())
