"""
팀 서비스 - 팀 통계 수집 및 관리 (통합된 버전)
Single Responsibility: 전체 프로세스 오케스트레이션만 담당
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List

import pytz
from supabase import Client

from collectors.browser_manager import TeamStatsBrowser
from config.config import League
from database.database import save_team_stats
from services.baseball.stats_collector import TeamStatsCollector
from utils.logger import Logger
from utils.service_utils import get_team_mappings

# 로거 설정
logger = Logger(__name__)


class TeamService:
    """팀 통계 수집 서비스 (통합된 버전)"""
    
    def __init__(self):
        self.browser = TeamStatsBrowser()
        self.collector = None
        self._team_mappings = {}
        self.KST = pytz.timezone('Asia/Seoul')

    async def save_multiple_team_stats(
        self,
        games: List[Dict],
        client: Client
    ) -> int:
        """
        여러 경기의 팀 통계를 5개씩 병렬로 수집 및 저장
        메모리 최적화: 매번 브라우저 인스턴스 생성/종료
        """
        teams_saved = 0
        try:
            # 컴포넌트 초기화 (브라우저 인스턴스 생성)
            if not await self._initialize_components():
                return 0
            # 팀 작업 수집
            team_tasks = self._collect_team_tasks(games)
            # 배치별 병렬 처리
            teams_saved = await self._process_batches(team_tasks, client)
        finally:
            # 브라우저와 playwright 확실히 종료 및 메모리 해제
            await self.browser.cleanup()
            import gc
            gc.collect()
        return teams_saved

    async def _initialize_components(self) -> bool:
        """컴포넌트 초기화 (팀 매핑, 브라우저, 수집기)"""
        # 팀 매핑 정보 로드 (공통 서비스 사용)
        self._team_mappings = await get_team_mappings()
        
        # 브라우저 초기화
        if not await self.browser.initialize():
            logger.error("브라우저 초기화 실패")
            return False
        
        # 통계 수집기 초기화
        self.collector = TeamStatsCollector(self.browser, self._team_mappings)
        return True

    def _collect_team_tasks(self, games: List[Dict]) -> List[Dict]:
        """모든 팀 작업 수집 (시간 필터링 포함)"""
        team_tasks = []
        processed_teams = set()
        
        for game in games:
            # 시간 범위 체크
            match_date = game.get('date')
            match_time = game.get('time')
            
            if not self._is_within_time_range(match_date, match_time):
                continue
            
            league = game.get('league')
            
            # 홈팀 작업 추가
            TeamService._add_team_task(
                team_tasks, processed_teams, game, 'home', 
                league, match_date, match_time
            )
            
            # 원정팀 작업 추가
            TeamService._add_team_task(
                team_tasks, processed_teams, game, 'away', 
                league, match_date, match_time
            )
        
        return team_tasks

    def _is_within_time_range(self, match_date, match_time, now=None):
        """시간 범위 검사 (24시간 이전 ~ 경기 시작)"""
        if not now:
            now = datetime.now(self.KST)

        try:
            # 날짜+시간 문자열 파싱
            match_datetime_str = f"{match_date} {match_time}"
            
            # 시간 형식 정규화 (시:분:초 -> 시:분)
            if ':' in match_time and len(match_time.split(':')) == 3:
                time_parts = match_time.split(':')
                match_time = f"{time_parts[0]}:{time_parts[1]}"
                match_datetime_str = f"{match_date} {match_time}"

            # datetime 객체로 변환
            match_dt = datetime.strptime(match_datetime_str, '%Y-%m-%d %H:%M')
            match_dt = self.KST.localize(match_dt)

            # 경기 24시간 전부터 경기 1시간 후까지 (범위 확장)
            start_time = match_dt - timedelta(hours=24)
            end_time = match_dt + timedelta(hours=1)

            # 현재 시간이 범위 내에 있는지 확인
            return start_time <= now <= end_time

        except Exception as e:
            logger.debug(f"시간 범위 검사 오류: {e}")
            return False

    @staticmethod
    def _add_team_task(
        team_tasks: List[Dict], 
        processed_teams: set, 
        game: Dict, 
        team_type: str,
        league: League, 
        match_date: str, 
        match_time: str
    ) -> None:
        """팀 작업을 목록에 추가 (중복 제거)"""
        team_id_key = f'{team_type}_team_id'
        team_url_key = f'{team_type}_team_url'
        team_name_key = f'{team_type}_team'
        
        team_id = game.get(team_id_key)
        team_url = game.get(team_url_key)
        team_name = game.get(team_name_key)
        
        team_key = f"{team_id}_{league}"
        if (team_key not in processed_teams and 
                team_id and team_url):
            team_display = (
                "🏠 [홈팀]" if team_type == 'home' else "✈️ [원정팀]"
            )
            
            task_info = {
                'team_id': team_id,
                'team_url': team_url,
                'team_name': team_name,
                'league': league,
                'match_date': match_date,
                'match_time': match_time,
                'display': f"{team_display} {team_name}"
            }
            team_tasks.append(task_info)
            processed_teams.add(team_key)

    async def _process_batches(
        self, 
        team_tasks: List[Dict], 
        client: Client
    ) -> int:
        """3개씩 배치로 팀 작업 병렬 처리 (메모리 안전)"""
        saved_count = 0
        batch_size = 3
        total_batches = (len(team_tasks) + batch_size - 1) // batch_size
        
        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(team_tasks))
            batch_tasks = team_tasks[start_idx:end_idx]
            
            # 배치 내 병렬 실행
            batch_coroutines = [
                self._process_single_team(task_info, client)
                for task_info in batch_tasks
            ]
            
            # 병렬 실행
            batch_results = await asyncio.gather(
                *batch_coroutines, return_exceptions=True
            )
            
            # 결과 처리
            for i, result in enumerate(batch_results):
                task_info = batch_tasks[i]
                if isinstance(result, Exception):
                    logger.warning(f"팀 처리 실패: {task_info['team_name']}")
                elif result:
                    saved_count += 1
            
            # 배치 간 최소 대기 (속도 최적화)
            if batch_idx < total_batches - 1:
                await asyncio.sleep(0.3)
        
        return saved_count

    async def _process_single_team(self, task_info: Dict, client: Client) -> bool:
        """단일 팀 처리 (기존 로직 그대로)"""
        try:
            stats = await self.collector.collect_team_stats(
                task_info['team_id'],
                task_info['team_url'],
                task_info['team_name'],
                task_info['league']
            )
            
            if stats:
                # 경기 시간 정보를 통계에 추가
                stats['match_date'] = task_info.get('match_date')
                stats['match_time'] = task_info.get('match_time')
                
                match_date = task_info.get('match_date', '')
                return TeamService._save_single_team_stats(
                    client, stats, task_info['league'], match_date
                )
            
        except Exception as e:
            logger.debug(f"팀 처리 오류: {task_info['team_name']} - {e}")
        
        return False
    
    @staticmethod
    def _save_single_team_stats(
        client: Client,
        stats: Dict,
        league: League,
        match_date: str
    ) -> bool:
        """단일 팀 통계 저장 (기존 로직 그대로)"""
        try:
            team_id = stats.get('team_id', '')
            if not team_id:
                return False
                
            return save_team_stats(client, team_id, stats, match_date)
            
        except Exception as e:
            logger.error(f"팀 통계 저장 오류: {e}")
            return False
