"""
팀 통계 수집기
Single Responsibility: 단일 팀의 통계 데이터 수집만 담당
"""
from typing import Any, Dict, Optional

from collectors.browser_manager import TeamStatsBrowser
from config.config import League, create_team_url, get_league_id
from parsers.baseball.base_stats_parser import BaseStatsParser
from parsers.baseball.kbo_stats_parser import KBOStatsParser
from parsers.baseball.mlb_stats_parser import MLBStatsParser
from parsers.baseball.npb_stats_parser import NPBStatsParser
from utils.logger import Logger

# 로거 설정
logger = Logger(__name__)


def _extract_games(record_str: str) -> int:
    """'3W 0D 4L' → 7"""
    import re
    nums = list(map(int, re.findall(r"(\d+)", record_str)))
    return sum(nums) if nums else 0


class TeamStatsCollector:
    """단일 팀 통계 수집"""
    
    def __init__(
        self, browser: TeamStatsBrowser, team_mappings: Dict[str, str]
    ) -> None:
        self.browser = browser
        self.team_mappings = team_mappings
        self.cache: Dict[str, Any] = {}
    
    async def collect_team_stats(
        self,
        team_id: str,
        team_url: str,
        team_name: str,
        league: League,
        max_retries: int = 3
    ) -> Dict[str, Any]:
        """
        팀 통계 수집 - 모든 카테고리(total/home/<USER>
        재시도 로직 포함: 주요 통계가 비어있을 때 재시도
        """
        # 향상된 캐싱 (날짜 포함)
        from datetime import datetime
        today = datetime.now().strftime('%Y%m%d')
        cache_key = f"{team_id}_{league}_{today}"
        if cache_key in self.cache:
            logger.debug(f"🔍 캐시 히트: {team_name}")
            return self.cache[cache_key]
        
        if not self.browser.is_initialized():
            logger.warning(f"❌ [DEBUG] {team_name} 브라우저 없음")
            return {}
        
        # 재시도 로직
        for retry_count in range(max_retries):
            page = await self.browser.create_page()
            if not page:
                if retry_count < max_retries - 1:
                    logger.warning(
                        f"⚠️ {team_name} 페이지 생성 실패, "
                        f"재시도 {retry_count + 1}/{max_retries}"
                    )
                    continue
                return {}
            
            try:
                # 새로운 팀 통계 URL 생성
                league_id = get_league_id(league)
                stats_url = create_team_url("BS", league_id, team_id)
                
                # 팀 페이지로 이동 (재시도 로직 포함)
                if not await TeamStatsBrowser.navigate_to_team_page(
                    page, stats_url, team_name
                ):
                    if retry_count < max_retries - 1:
                        logger.warning(
                            f"⚠️ {team_name} 페이지 이동 실패, "
                            f"재시도 {retry_count + 1}/{max_retries}"
                        )
                        await TeamStatsBrowser.close_page(page)
                        continue
                    await TeamStatsBrowser.close_page(page)
                    return {}
                
                # 전체/홈/원정 각각의 HTML 데이터 수집 (모든 카테고리 5경기)
                homeaway_data = await TeamStatsBrowser.collect_homeaway_data(
                    page, team_name
                )
                
                # 각각의 데이터를 파싱하여 합치기
                combined_stats = self._parse_and_combine_stats(
                    homeaway_data, league, league_id, team_name, team_id
                )
                
                # 통계 데이터 유효성 검증
                if self._is_stats_valid(combined_stats, team_name):
                    self.cache[cache_key] = combined_stats
                    await TeamStatsBrowser.close_page(page)
                    return combined_stats
                else:
                    # 데이터가 불완전한 경우 재시도
                    if retry_count < max_retries - 1:
                        logger.warning(
                            f"⚠️ {team_name} 통계 데이터 불완전, "
                            f"재시도 {retry_count + 1}/{max_retries}"
                        )
                        await TeamStatsBrowser.close_page(page)
                        import asyncio
                        await asyncio.sleep(1.0)  # 1초 대기 후 재시도
                        continue
                    else:
                        logger.error(
                            f"❌ {team_name} 최대 재시도 횟수 초과, "
                            f"불완전한 데이터 반환"
                        )
                        await TeamStatsBrowser.close_page(page)
                        return combined_stats
                        
            except Exception as e:
                logger.warning(
                    f"❌ [DEBUG] 팀 통계 수집 실패 - {team_name}: {e}"
                )
                if retry_count < max_retries - 1:
                    logger.warning(
                        f"⚠️ {team_name} 예외 발생, "
                        f"재시도 {retry_count + 1}/{max_retries}"
                    )
                    await TeamStatsBrowser.close_page(page)
                    import asyncio
                    await asyncio.sleep(1.0)  # 1초 대기 후 재시도
                    continue
                else:
                    await TeamStatsBrowser.close_page(page)
                    return {}
            
            finally:
                # page는 이미 위에서 close됨
                pass
        
        return {}
    
    def _parse_and_combine_stats(
        self,
        homeaway_data: Dict[str, str],
        league: League,
        league_id: str,
        team_name: str,
        team_id: str
    ) -> Dict[str, Any]:
        """
        전체/홈/원정 각각의 HTML 데이터를 파싱하여 합치기
        - 모든 카테고리: 5경기로 통일
        - 독립적인 recent_N_home/away_games vs 전체 내 last_N_home/away_games 구분
        """
        combined_stats = {
            'team_id': team_id,
            'team_name': team_name,
            'league': league.value,
            'seasons_summary': {},
            # 최근 경기 구조 (5경기로 통일)
            'recent_games': {},  # home/away 경기별 상세 목록
            'recent_games_summary': {},
            'season_stats': {},
            'sports': 'baseball'
        }
        
        # 각 카테고리별로 파싱
        for category, html in homeaway_data.items():
            if not html:
                continue
                
            parser = self._get_parser(league, league_id, html, team_name)
            if not parser:
                continue
            
            try:
                # 시즌 요약은 전체에서만 수집 (중복 방지)
                if category == 'total':
                    combined_stats['seasons_summary'] = (
                        parser.parse_season_summary()
                    )
                    
                    # 시즌 통계도 전체에서만 수집
                    try:
                        season_stats = parser.parse_season_stats()
                        combined_stats['season_stats'] = season_stats
                        logger.debug(
                            "시즌 통계 수집 완료: %s %d개", team_name, len(season_stats)
                        )
                    except Exception as e:
                        logger.error(
                            f"❌ [{team_name}] 시즌 통계 수집 실패: {e}"
                        )
                        combined_stats['season_stats'] = {}
                
                # 최근 경기는 카테고리별로 처리 (토탈 제외)
                recent_games = parser.parse_recent_games()
                if recent_games:
                    if category != 'total':
                        # 홈/원정 별 실제 경기 수(레코드 길이)로 키 생성
                        n_games_side = len(recent_games)
                        if category == 'home':
                            rm_key = f"recent_{n_games_side}_home_games"
                        else:  # away
                            rm_key = f"recent_{n_games_side}_away_games"
                        combined_stats['recent_games'][rm_key] = recent_games
                
                # 최근 경기 요약: 각 버튼별로 별도 저장
                recent_summary = parser.parse_recent_games_summary()
                if recent_summary:
                    if category != 'total':  # 토탈은 제외
                        # 홈/원정: 개별 저장 (키명 변경)
                        if category == 'home':
                            home_data = recent_summary.get('home', {})
                            if home_data:
                                n_home = _extract_games(
                                    home_data.get('record', '')
                                )
                                key_home = (
                                    f"recent_{n_home}_home_games" 
                                    or 'recent_home_games'
                                )
                                combined_stats['recent_games_summary'][
                                    key_home
                                ] = home_data
                        elif category == 'away':
                            away_data = recent_summary.get('away', {})
                            if away_data:
                                n_away = _extract_games(
                                    away_data.get('record', '')
                                )
                                key_away = (
                                    f"recent_{n_away}_away_games" 
                                    or 'recent_away_games'
                                )
                                combined_stats['recent_games_summary'][
                                    key_away
                                ] = away_data
                    if category == 'total':
                        # 전체 버튼: 최근 10경기 요약 → recent_total_games
                        # 하위 키는 last_N_home/away_games (전체 경기 중 서브셋)
                        total_summary = {}
                        home_n = 0
                        away_n = 0
                        
                        if 'home' in recent_summary:
                            home_data = recent_summary['home']
                            home_n = _extract_games(
                                home_data.get('record', '')
                            )
                            total_summary[
                                f'last_{home_n}_home_games'
                            ] = home_data
                        
                        if 'away' in recent_summary:
                            away_data = recent_summary['away']
                            away_n = _extract_games(
                                away_data.get('record', '')
                            )
                            total_summary[
                                f'last_{away_n}_away_games'
                            ] = away_data

                        # 전체 경기 수 (home + away)
                        n_games = home_n + away_n or 1
                        key_name = f"recent_{n_games}_games"
                        combined_stats['recent_games_summary'][
                            key_name
                        ] = total_summary
                
            except Exception as e:
                logger.warning(f"❌ {team_name} {category} 파싱 실패: {e}")
                continue
        
        return combined_stats
    
    def _is_stats_valid(self, stats: Dict[str, Any], team_name: str) -> bool:
        """
        통계 데이터의 유효성 검증
        주요 통계들이 비어있지 않은지 확인
        """
        if not stats:
            logger.debug(f"🔍 {team_name} 전체 통계 데이터 없음")
            return False
        
        # 1. 시즌 요약 (seasons_summary) 검증
        seasons_summary = stats.get('seasons_summary', {})
        if not seasons_summary or not any(seasons_summary.values()):
            logger.debug(f"🔍 {team_name} 시즌 요약 데이터 없음")
            return False
        
        # 2. 시즌 통계 (season_stats) 검증 - 중요도 높음
        season_stats = stats.get('season_stats', {})
        if not season_stats:
            logger.debug(f"🔍 {team_name} 시즌 통계 데이터 없음")
            return False
        
        # 3. 최근 경기 데이터 (recent_games) 검증
        recent_games = stats.get('recent_games', {})
        if not recent_games:
            logger.debug(f"🔍 {team_name} 최근 경기 데이터 없음")
            return False
        
        # 4. 최근 경기 요약 (recent_games_summary) 검증
        recent_summary = stats.get('recent_games_summary', {})
        if not recent_summary:
            logger.debug(f"🔍 {team_name} 최근 경기 요약 데이터 없음")
            return False
        
        # 모든 검증 통과
        logger.debug(f"✅ {team_name} 통계 데이터 유효성 검증 통과")
        return True
    
    def _get_parser(
        self, 
        league: League, 
        league_id: str, 
        html: str, 
        team_name: str
    ) -> Optional[BaseStatsParser]:
        """리그별 파서 선택 (기존 로직 그대로)"""
        if league.value == "NPB" or league_id == "BS004":
            return NPBStatsParser(html, self.team_mappings)
        elif league.value == "MLB" or league_id == "BS002":
            return MLBStatsParser(html, self.team_mappings)
        elif league.value == "KBO" or league_id == "BS001":
            return KBOStatsParser(html, self.team_mappings)
        else:
            logger.warning(f"❌ [DEBUG] {team_name} 지원하지 않는 리그: {league}")
            return None 