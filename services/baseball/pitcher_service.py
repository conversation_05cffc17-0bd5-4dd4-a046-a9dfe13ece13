"""
투수 통계 수집 서비스

주요 기능:
- 투수별 시즌 통계 및 프로필 수집
- 최근 경기 기록 (recent_N_games 동적 키)
- 배치 처리로 메모리 최적화
- 팀 매핑 및 리그 정보 연동
"""
import asyncio
import logging
from enum import Enum
from typing import Any, Dict, List, Optional

from services.baseball.pitcher_processor import PitcherDataProcessor
from utils.logger import Logger
from utils.service_utils import TeamMappingService

logger = Logger(__name__, level=logging.ERROR)


def safe_int(value, default=0):
    """안전하게 정수로 변환하는 함수 (float도 처리)"""
    if value is None:
        return default
    try:
        # 문자열이면서 숫자가 아닌 경우 (예: "kg" 포함)
        if isinstance(value, str):
            # 숫자가 아닌 문자 제거
            cleaned = ''.join(c for c in value if c.isdigit() or c == '.')
            if not cleaned:
                return default
            value = cleaned
        
        # float로 먼저 변환한 후 int로 변환
        return int(float(value))
    except (ValueError, TypeError):
        return default


def safe_float(value, default=0.0):
    """안전하게 실수로 변환하는 함수 (int도 처리)"""
    if value is None:
        return default
    try:
        # 문자열이면서 숫자가 아닌 경우 (예: "cm" 포함)
        if isinstance(value, str):
            # 숫자가 아닌 문자 제거
            cleaned = ''.join(c for c in value if c.isdigit() or c == '.')
            if not cleaned:
                return default
            value = cleaned
        
        # float로 먼저 변환한 후 반환
        return float(value)
    except (ValueError, TypeError):
        return default


def _convert_enum(obj):
    """Enum 타입을 처리하기 위한 재귀 함수"""
    if isinstance(obj, dict):
        return {k: _convert_enum(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [_convert_enum(v) for v in obj]
    elif isinstance(obj, Enum):
        return obj.value
    else:
        return obj


class PitcherService:
    """투수 통계 수집 서비스 (모듈화된 버전)"""
    
    def __init__(self) -> None:
        # 메모리 최적화: 매번 인스턴스 생성/종료를 위해 크롤러를 인스턴스 변수로 보관하지 않음
        self.processor = PitcherDataProcessor()
        self.team_mapping_service = TeamMappingService()
        self._team_mappings: Dict[str, str] = {}
    
    @staticmethod
    def _handle_batch_results(
        batch_results: List,
        batch_tasks: List[Dict],
        collected_data: Dict
    ) -> None:
        """배치 처리 결과 처리 (기존 로직)"""
        for i, result in enumerate(batch_results):
            task_info = batch_tasks[i]
            if isinstance(result, Exception):
                logger.warning(
                    f"투수 처리 실패: {task_info['pitcher_name']}"
                )
            elif result:
                collected_data[task_info['pitcher_name']] = result

    @staticmethod
    def _clean_season_stats(season_data: List) -> List:
        """시즌 통계 데이터 정리 (기존 로직)"""
        if not season_data:
            return []
        cleaned_season = []
        for stat in season_data:
            if isinstance(stat, dict):
                cleaned_stat = PitcherService._clean_data_dict(stat)
                cleaned_season.append(cleaned_stat)
        return cleaned_season

    async def save_multiple_pitcher_stats(
        self,
        games: List[Dict],
        client: Any
    ) -> List[Dict]:
        """
        여러 경기의 투수 통계를 병렬로 수집 및 저장
        메모리 최적화: 매번 크롤러 인스턴스 생성/종료
        """
        results = []
        from collectors.pitcher_crawler import PitcherCrawler
        try:
            # 매번 새로운 크롤러 인스턴스 생성
            crawler = PitcherCrawler()
            await crawler.initialize_browser()
            self.crawler = crawler  # fix: allow self.crawler usage
            # 컴포넌트 초기화
            await self._initialize_components()
            
            # team_info_map을 인스턴스 변수로 저장
            self.team_info_map = {}
            for game in games:
                for team_type in ["home", "away"]:
                    pitcher = game.get(f"{team_type}_pitcher")
                    if pitcher:
                        self.team_info_map[pitcher] = {
                            "team_name": game.get(f"{team_type}_team"),
                            "league": game.get("league", "")
                        }
            
            try:
                # 투수 작업 수집
                pitcher_tasks = PitcherService._collect_pitcher_tasks(
                    games
                )
                
                # 배치별 병렬 처리
                collected_data = await self._process_pitcher_batches(
                    pitcher_tasks
                )
                
                # 데이터베이스 저장
                await PitcherService._save_to_database(
                    collected_data, games, client
                )  # client 전달
                
                return list(collected_data.values())
                
            finally:
                # 크롤러/브라우저/Playwright 완전 종료 및 메모리 해제
                if 'crawler' in locals() and hasattr(crawler, 'close'):
                    await crawler.close()
                import gc
                gc.collect()
        except Exception as e:
            logger.error(f"투수 데이터 수집/저장 오류: {e}")
        return results

    async def _initialize_components(self) -> None:
        """컴포넌트 초기화 (팀 매핑, 브라우저)"""
        # 팀 매핑 정보 로드
        self._team_mappings = await (
            self.team_mapping_service.get_team_mappings()
        )
        
        # 브라우저 초기화
        await self.crawler.initialize_browser()
        
        # 크롤러에 팀 매핑 전달
        self.crawler.set_team_mappings(self._team_mappings)

    @staticmethod
    def _collect_pitcher_tasks(games: List[Dict]) -> List[Dict]:
        """모든 투수 작업 수집"""
        pitcher_tasks = []
        processed_pitchers = set()
        
        for game in games:
            # 홈팀 선발투수 추가
            PitcherService._add_pitcher_task(
                pitcher_tasks, processed_pitchers, game, 'home'
            )
            
            # 원정팀 선발투수 추가
            PitcherService._add_pitcher_task(
                pitcher_tasks, processed_pitchers, game, 'away'
            )
        
        return pitcher_tasks

    @staticmethod
    def _add_pitcher_task(
        pitcher_tasks: List[Dict], 
        processed_pitchers: set, 
        game: Dict, 
        team_type: str
    ) -> None:
        """투수 작업을 목록에 추가"""
        if not PitcherService._should_collect_pitcher_stats(game, team_type):
            return
        
        pitcher_key = f'{team_type}_pitcher'
        pitcher_name = game.get(pitcher_key)
        
        if not pitcher_name or pitcher_name in processed_pitchers:
            return
        
        team_url = game.get(f'{team_type}_team_url')
        team_display = "🏠 [홈팀]" if team_type == 'home' else "✈️ [원정팀]"
        
        task_info = {
            'pitcher_name': pitcher_name,
            'team_url': team_url,
            'position': "starting_pitcher",
            'game_role': f"{team_type}_starter",
            'display': f"{team_display} {pitcher_name}"
        }
        
        pitcher_tasks.append(task_info)
        processed_pitchers.add(pitcher_name)

    async def _process_pitcher_batches(
        self,
        pitcher_tasks: List[Dict]
    ) -> Dict:
        """1개씩 배치로 투수 작업 병렬 처리 (team_info 전달)"""
        import random

        from playwright.async_api import async_playwright
        collected_data = {}
        batch_size = 1  # 동시 처리 최소화
        total_batches = (len(pitcher_tasks) + batch_size - 1) // batch_size

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(pitcher_tasks))
            batch_tasks = pitcher_tasks[start_idx:end_idx]

            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                batch_coroutines = [
                    self._process_single_pitcher_with_delay(task_info, browser)
                    for task_info in batch_tasks
                ]
                batch_results = await asyncio.gather(
                    *batch_coroutines, return_exceptions=True
                )
                PitcherService._handle_batch_results(
                    batch_results, batch_tasks, collected_data
                )
                await browser.close()

            # 배치 간 대기시간을 2~3초로 늘림
            if batch_idx < total_batches - 1:
                await asyncio.sleep(random.uniform(4, 5))  # 2~3초에서 4~5초로 증가

        return collected_data

    async def _process_single_pitcher_with_delay(
        self, task_info: Dict, browser=None
    ) -> Optional[Dict]:
        """투수 파싱 전후에 랜덤 대기(1~2초) 추가"""
        import random

        # 처리 전 대기 시간 1~2초에서 3~4초로 증가
        await asyncio.sleep(random.uniform(3, 4))
        result = await self._process_single_pitcher(
            task_info, browser
        )
        # 처리 후 대기 시간 1~2초에서 3~4초로 증가
        await asyncio.sleep(random.uniform(3, 4))
        return result

    async def _process_single_pitcher(
        self, task_info: Dict, browser=None
    ) -> Optional[Dict]:
        """단일 투수 처리 (브라우저 인스턴스 주입, 빈값 저장 방지)"""
        context = None
        page = None
        try:
            context = await browser.new_context()
            page = await context.new_page()
            stats = await self.crawler._crawl_pitcher_stats(
                task_info['pitcher_name'],
                task_info['team_url']
            )
            # team_info 맵이 존재할 때만 안전하게 조회
            if hasattr(self, 'team_info_map'):
                team_info = self.team_info_map.get(task_info['pitcher_name'])
            else:
                team_info = None
            # 1. 선발투수 이름이 없으면(빈값) → 빈 데이터 저장 허용
            if not task_info['pitcher_name']:
                logger.info(f"No starter name: {task_info}")
                return PitcherService._structure_pitcher_data(
                    pitcher_name=task_info['pitcher_name'],
                    position=task_info['position'],
                    game_role=task_info['game_role'],
                    pitcher_stats=stats or {},
                    team_info=team_info
                )
            # 1-1. 시즌/리그 첫 출장 플래그 감지 시 빠르게 처리
            if stats and isinstance(stats, dict) and stats.get('first_appearance'):
                fa = stats['first_appearance']
                if fa == 'season':
                    logger.info(f"시즌 첫 출장: {task_info['pitcher_name']}")
                elif fa == 'league':
                    logger.info(f"리그 첫 출장: {task_info['pitcher_name']}")
                return PitcherService._structure_pitcher_data(
                    pitcher_name=task_info['pitcher_name'],
                    position=task_info['position'],
                    game_role=task_info['game_role'],
                    pitcher_stats=stats,
                    team_info=team_info
                )
            # 2. recent_games가 아예 없으면(최초 등판) → 빈 데이터 저장 허용
            if stats and (
                not stats.get('recent_games') or not stats['recent_games']
            ):
                logger.info(
                    f"First appearance (no recent_games): "
                    f"{task_info['pitcher_name']}"
                )
                return PitcherService._structure_pitcher_data(
                    pitcher_name=task_info['pitcher_name'],
                    position=task_info['position'],
                    game_role=task_info['game_role'],
                    pitcher_stats=stats,
                    team_info=team_info
                )
            # 3. 그 외에 데이터가 있는데 파싱이 안된 경우(No data 등)은 저장하지 않음
            # 더 관대하게: stats가 dict이고 name만 있으면 저장,
            # season_stats/recent_games 없어도 저장
            name_empty = stats.get('name', '') == '' if stats else True
            
            # 간단히 투수 이름만 로깅
            logger.info(f"투수 데이터 처리: {task_info['pitcher_name']}")
            
            if not stats or (isinstance(stats, dict) and name_empty):
                logger.warning(
                    f"No data or parse error for pitcher: "
                    f"{task_info['pitcher_name']}"
                )
                return None

            structured = PitcherService._structure_pitcher_data(
                pitcher_name=task_info['pitcher_name'],
                position=task_info['position'],
                game_role=task_info['game_role'],
                pitcher_stats=stats,
                team_info=team_info
            )

            # 디버그용 로그 제거됨
            return structured
        except Exception as e:
            logger.debug(
                f"투수 처리 오류: {task_info['pitcher_name']} - {e}"
            )
        finally:
            if page:
                try:
                    await page.close()
                except Exception:
                    pass
            if context:
                try:
                    await context.close()
                except Exception:
                    pass
        return None
    
    @staticmethod
    def _should_collect_pitcher_stats(
        game: Dict, team_type: str
    ) -> bool:
        """투수 통계 수집이 필요한지 확인"""
        pitcher_key = f'{team_type}_pitcher'
        team_url_key = f'{team_type}_team_url'
        
        pitcher_name = game.get(pitcher_key, '')
        team_url = game.get(team_url_key, '')
        
        return bool(pitcher_name and team_url and pitcher_name.strip())
    
    @staticmethod
    def _structure_pitcher_data(
        pitcher_name: str,
        position: str,
        game_role: str,
        pitcher_stats: Dict,
        team_info: dict = None
    ) -> Dict:
        """투수 데이터를 최종 구조로 정리"""
        team_name, league = PitcherService._extract_team_info(pitcher_stats)
        if (not team_name or not league) and team_info:
            team_name = team_name or team_info.get("team_name", "")
            league = league or team_info.get("league", "")
        # league는 무조건 str로 변환 (직렬화 오류 방지)
        pitcher_profile = PitcherService._build_starter_info(
            pitcher_stats, position, game_role, team_name, str(league)
        )
        pitcher_stats_data = PitcherService._build_pitching_stats(
            pitcher_stats
        )
        return {
            "pitcher_profile": pitcher_profile,
            "pitcher_stats": pitcher_stats_data
        }

    @staticmethod
    def _build_starter_info(
        pitcher_stats: Dict,
        position: str,
        game_role: str,
        team_name: str,
        league: str
    ) -> Dict:
        """투수 기본 정보 구성 (League Enum 처리 개선)"""
        from config.config import League

        # league 값을 올바르게 처리
        league_value = ""
        if isinstance(league, League):
            league_value = league.value  # Enum.value로 실제 값 추출
        elif isinstance(league, str):
            # 문자열이지만 'League.XXX' 형태인 경우 처리
            if league.startswith('League.'):
                league_name = league.replace('League.', '')
                # League enum에서 해당 값 찾기
                for league_enum in League:
                    if league_enum.name == league_name:
                        league_value = league_enum.value
                        break
                else:
                    league_value = league_name  # enum에서 찾지 못한 경우
            elif league in ("KBO", "NPB", "MLB"):
                league_value = league
            else:
                league_value = league
        else:
            league_value = str(league)
        
        # 기본 투수 정보 구성
        starter_info = {
            'profile': {
                'player_number': safe_int(pitcher_stats.get('no', 0)),
                'name': pitcher_stats.get('name', ''),
                'team_name': team_name,
                'birth_date': pitcher_stats.get('birth', ''),
                'height_cm': safe_float(pitcher_stats.get('height', 0)),
                'weight_kg': safe_int(
                    str(pitcher_stats.get('weight', '')).replace('kg', '')
                ),
                'league': league_value,  # 여기서 정리된 값 사용
                'position': position,
                'game_role': game_role,
                # 시즌 요약 통계 (데이터 없으면 빈 문자열 유지)
                'season_stats_summary': PitcherService._format_season_summary(
                    pitcher_stats.get('season_summary', {})
                )
            }
        }
        
        return starter_info

    @staticmethod
    def _format_season_summary(summary: Dict[str, Any]) -> Dict[str, Any]:
        """시즌 요약 통계를 포매팅한다. 값이 없으면 '' 유지"""
        def _normalize(val: Any, numeric: bool = False):
            if val in (None, '', '없음', 'No data'):
                return ''
            # 숫자 변환이 필요한 ERA 등만 처리
            if numeric:
                return safe_float(val, 0.0)
            return val

        return {
            'era': _normalize(summary.get('era'), numeric=True),
            'wins': _normalize(summary.get('wins')),
            'strikeouts': _normalize(summary.get('strikeouts')),
            'saves': _normalize(summary.get('saves')),
        }

    @staticmethod
    def _build_pitching_stats(pitcher_stats: Dict) -> Dict:
        """투구 통계 데이터 구성"""
        pitching_stats: Dict[str, Any] = {
            "season_stats": [],
        }

        if not isinstance(pitcher_stats, dict):
            return pitching_stats

        # 시즌 통계
        pitching_stats["season_stats"] = PitcherService._clean_season_stats(
            pitcher_stats.get("season_stats", [])
        )

        # ----- 최근 경기 -----
        raw_recent = pitcher_stats.get("recent_games", {})
        cleaned_recent = PitcherService._clean_recent_games(raw_recent)
        n_games = len(cleaned_recent)

        # 동적 키: recent_{n}_games
        recent_games_key = f"recent_{n_games}_games"
        pitching_stats[recent_games_key] = cleaned_recent

        # ----- 최근 경기 요약 -----
        raw_summary = pitcher_stats.get("recent_games_summary", {})
        cleaned_summary = PitcherService._clean_data_dict(raw_summary)
        summary_key = f"recent_{n_games}_games_summary"
        pitching_stats[summary_key] = cleaned_summary

        return pitching_stats

    @staticmethod
    def _clean_recent_games(recent_games_data: Dict) -> Dict:
        """최근 경기 데이터 정리"""
        if not isinstance(recent_games_data, dict):
            return {}
        cleaned_recent_games = {}
        for date_key, game_data in recent_games_data.items():
            if isinstance(game_data, dict):
                cleaned_recent_games[date_key] = (
                    PitcherService._clean_data_dict(game_data)
                )
        return cleaned_recent_games

    @staticmethod
    def _clean_data_dict(data: Dict) -> Dict:
        """데이터 딕셔너리에서 '없음' 값들을 빈 문자열로 정리"""
        cleaned_data = {}
        for key, value in data.items():
            if value == "없음" or value == "No data":
                cleaned_data[key] = ""
            else:
                cleaned_data[key] = value
        return cleaned_data

    @staticmethod
    def _extract_team_info(pitcher_stats: Dict) -> tuple:
        """투수 통계에서 팀 정보와 리그 추출"""
        team_name = ""
        league = ""
        
        if isinstance(pitcher_stats, dict) and "name" in pitcher_stats:
            team_name = pitcher_stats.get("team", "")
            league = PitcherService._get_league_from_team(team_name)
        
        return team_name, league

    @staticmethod
    def _get_league_from_team(team_name: str) -> str:
        """팀명으로부터 리그 결정 (DB 기반)"""
        try:
            # DB에서 팀 정보 조회하여 리그 결정
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                return ""
            
            # 1. team_info 테이블에서 team_id 조회
            response = client.table('team_info').select('team_id').eq(
                'team_name', team_name
            ).execute()
            
            if not response.data or not response.data[0].get('team_id'):
                # 팀명으로 찾을 수 없는 경우
                return ""
            
            team_id = response.data[0].get('team_id')
            
            # 2. target_games 테이블에서 league 조회 (필터 방식으로 수정)
            query = client.table('target_games').select('league')
            
            # home_team_id가 team_id인 경우 조회
            home_response = query.eq('home_team_id', team_id)\
                .limit(1).execute()
            if home_response.data and home_response.data[0].get('league'):
                return home_response.data[0].get('league')
            away_response = query.eq('away_team_id', team_id)\
                .limit(1).execute()
            if away_response.data and away_response.data[0].get('league'):
                return away_response.data[0].get('league')
            
            return ""
            
        except Exception as e:
            logger.debug(f"리그 조회 오류 ({team_name}): {e}")
            return ""

    @staticmethod
    async def _save_structured_data_to_db(
        client, 
        collected_data: Dict, 
        games: List[Dict]
    ) -> int:
        """
        새로운 구조의 투수 데이터를 별도 컬럼에 저장 (기존 검증된 로직)
        """
        from database.database import save_structured_pitcher_data
        
        saved_count = 0
        
        # 경기별 팀 정보 매핑 생성
        team_info_map = {}
        for game in games:
            if game.get('home_pitcher'):
                team_info_map[game['home_pitcher']] = {
                    'team_id': game.get('home_team_id', ''),
                    'match_date': game.get('date', ''),
                    'match_time': game.get('time', '')
                }
            if game.get('away_pitcher'):
                team_info_map[game['away_pitcher']] = {
                    'team_id': game.get('away_team_id', ''),
                    'match_date': game.get('date', ''),
                    'match_time': game.get('time', '')
                }
        
        # 각 투수 데이터를 pitcher_profile과 pitcher_stats로 분리하여 저장
        for pitcher_name, structured_data in collected_data.items():
            try:
                team_info = team_info_map.get(pitcher_name)
                if not team_info:
                    logger.warning(f"❌ [DB저장] {pitcher_name} 팀 정보 없음")
                    continue
                
                # pitcher_profile과 pitcher_stats 분리
                pitcher_profile = structured_data.get('pitcher_profile', {})
                pitcher_stats = structured_data.get('pitcher_stats', {})
                
                # 새로운 저장 함수 사용 (배열로 저장)
                success = save_structured_pitcher_data(
                    client=client,
                    pitcher_name=pitcher_name,
                    starter_info=pitcher_profile,
                    pitching_stats=pitcher_stats,
                    team_id=team_info['team_id'],
                    match_date=team_info['match_date'],
                    match_time=team_info.get('match_time', '')
                )
                
                if success:
                    saved_count += 1
                
            except Exception:
                continue
        
        return saved_count 

    @staticmethod
    async def _save_to_database(collected_data: dict, games: list, client=None) -> None:
        """수집된 투수 데이터를 DB에 저장 (team_stats 테이블의 JSONB 컬럼 사용)"""
        if not client:
            logger.warning("DB client 없음 - 투수 데이터 저장 스킵")
            return
        if not collected_data:
            logger.info("저장할 투수 데이터 없음")
            return
        try:
            # 모든 중첩 Enum을 value로 변환
            for key, rec in collected_data.items():
                collected_data[key] = _convert_enum(rec)
            
            # DB 저장 직전만 디버그 로그 유지
            logger.info(
                f"[DEBUG] pitcher_records to upsert: "
                f"{len(collected_data)} 건"
            )
            
            # save_structured_pitcher_data 함수 사용하여 저장
            saved_count = await PitcherService._save_structured_data_to_db(
                client=client,
                collected_data=collected_data,
                games=games
            )
            
            if saved_count > 0:
                logger.info(f"투수 데이터 {saved_count}건 저장 완료")
            else:
                logger.info("저장할 투수 데이터 없음 (모든 profile/stats 비어있음)")
                
        except Exception as e:
            import traceback
            logger.warning(f"투수 데이터 저장 중 오류: {e}\n{traceback.format_exc()}")

    def build_pitcher_data(
        self, pitcher_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """투수 데이터를 구조화합니다."""
        try:
            pitcher_stats = pitcher_data.get("pitcher_data", {})
            if not pitcher_stats:
                return {}

            # 프로필과 투구 통계 구성 (동적 recent_{n}_games 구조 사용)
            pitcher_profile = self._build_pitcher_profile(pitcher_stats)
            pitcher_stats_data = self._build_pitching_stats(pitcher_stats)

            return {
                "pitcher_profile": pitcher_profile,
                "pitcher_stats": pitcher_stats_data,
            }
        except Exception as e:
            logger.error(f"투수 데이터 구조화 오류: {e}")
            return {}

    @staticmethod
    def _build_pitcher_profile(
        pitcher_stats: Dict[str, Any]
    ) -> Dict[str, Any]:
        """투수 기본 정보 추출 (League Enum 처리 개선)"""
        from config.config import League
        
        league = pitcher_stats.get("league", "")
        
        # league 값을 올바르게 처리
        league_value = ""
        if isinstance(league, League):
            league_value = league.value  # Enum.value로 실제 값 추출
        elif isinstance(league, str):
            # 문자열이지만 'League.XXX' 형태인 경우 처리
            if league.startswith('League.'):
                league_name = league.replace('League.', '')
                # League enum에서 해당 값 찾기
                for league_enum in League:
                    if league_enum.name == league_name:
                        league_value = league_enum.value
                        break
                else:
                    league_value = league_name  # enum에서 찾지 못한 경우
            elif league in ("KBO", "NPB", "MLB"):
                league_value = league
            else:
                league_value = league
        else:
            league_value = str(league)
        
        pitcher_profile = {
            "no": pitcher_stats.get("no", ""),
            "name": pitcher_stats.get("name", ""),
            "team": pitcher_stats.get("team", ""),
            "birth": pitcher_stats.get("birth", ""),
            "height": pitcher_stats.get("height", ""),
            "weight": pitcher_stats.get("weight", ""),
            "position": "starting_pitcher",
            "game_role": "home_starter",
            "league": league_value  # 여기서 정리된 값 사용
        }
        
        # 시즌 요약 통계 추가
        if "season_summary" in pitcher_stats:
            pitcher_profile["season_summary"] = pitcher_stats["season_summary"]
        
        return pitcher_profile

    @staticmethod
    def _build_pitcher_stats(pitcher_stats: Dict) -> Dict:
        """(Deprecated) 이전 이름 호환용: _build_pitching_stats 래퍼"""
        return PitcherService._build_pitching_stats(pitcher_stats)