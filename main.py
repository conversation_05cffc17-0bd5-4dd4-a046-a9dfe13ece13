"""
메인 실행 파일 - 애플리케이션 엔트리포인트
"""
import subprocess
import time
from datetime import datetime
import os
import sys

INTERVAL_SECONDS = 600  # 10 minutes


def is_operating_hour():
    now = datetime.now()
    hour = now.hour
    # 8~9, 11~12, 14~15, 16~17
    return (
        (8 <= hour < 10) or
        (11 <= hour < 13) or
        (14 <= hour < 16) or
        (18 <= hour < 20)
    )


if __name__ == "__main__":
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    worker_path = os.path.join(BASE_DIR, "worker_crawl.py")
    first_run = True
    while True:
        if first_run or is_operating_hour():
            first_run = False
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[스케줄러] {timestamp} - 워커 실행")
            try:
                # 환경변수 전체 전달
                subprocess.run([sys.executable, worker_path], check=True, env=os.environ.copy())
            except subprocess.CalledProcessError as e:
                print(f"[스케줄러] 워커 예외 발생: {e}")
                # exit(1) 제거 → 스케줄러는 계속 동작
        else:
            print("[스케줄러] 운영시간 아님: 대기 중...")
        time.sleep(INTERVAL_SECONDS)