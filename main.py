"""
메인 스케줄러 - 초경량 버전 (최대 메모리 절약)
Target crawling: 08:01, 12:01, 14:01, 16:01, 22:01
Match results: 08:06, 12:06, 14:06, 16:06, 22:06
Program runs: 08:12, 11:12, 12:12, 14:12, 14:32, 16:12, 17:12
"""
import logging
import os
import subprocess
import sys
import time
from datetime import datetime

# ==============================================================================
# 로거 설정
# ==============================================================================
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)
# ==============================================================================


def install_playwright_chromium():
    """Checks if Playwright Chromium is installed and installs it if not."""
    try:
        # First check if Chromium is already working
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            browser.close()
            logger.info("✅ [Playwright] Chromium already working")
            return True
    except Exception:
        logger.info("🔍 [Playwright] Chromium not working, attempting installation...")
    
    try:
        # Simply install chromium
        subprocess.run(
            [sys.executable, "-m", "playwright", "install", "chromium"],
            check=True,
            capture_output=True,
            text=True,
        )
        logger.info("✅ [Playwright] Chromium installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error("❌ [Playwright] Failed to install Chromium.")
        logger.error(f"    Error: {e.stderr}")
        logger.info("💡 [Tip] You may need to run: ./deployment/scripts/setup_ec2_playwright.sh")
        return False
    except FileNotFoundError:
        logger.error(
            "❌ [Playwright] Playwright is not installed. "
            "Please run 'pip install playwright'."
        )
        return False


if __name__ == "__main__":
    # Ensure Playwright Chromium is ready before starting the scheduler.
    logger.info("🚀 [Startup] Initializing application...")
    
    chromium_installed = install_playwright_chromium()
    if not chromium_installed:
        logger.warning("⚠️ [Playwright] Chromium not installed, but continuing anyway...")
        logger.info("💡 [Tip] You can manually run: ./deployment/scripts/setup_ec2_playwright.sh")

    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    worker_path = os.path.join(BASE_DIR, "worker_crawl.py")

    # Initial run for all sports
    logger.info("[스케줄러] 초기 실행...")
    sports_to_run = [
        "baseball",
        "basketball",
        "soccer",
        "volleyball",
    ]

    for sport in sports_to_run:
        logger.info(f"[스케줄러] {sport} 워커 실행...")
        result = subprocess.run(
            [sys.executable, worker_path, sport],
            check=False,
            env=os.environ.copy(),
        )
        if result.returncode != 0:
            logger.error(f"[스케줄러] {sport} 워커 오류: {result.returncode}")
        else:
            logger.info(f"[스케줄러] {sport} 워커 완료")

    end_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.info(f"[스케줄러] {end_timestamp} - 첫 실행 완료")
    logger.info("-" * 50)

    # Schedule subsequent runs
try:
    import schedule
except ImportError:
    schedule = None

    def get_schedule_times():
        return [
            "08:12", "11:12", "12:12", "14:12", "14:32", "16:12", "17:12",
        ]

    def run_scheduled_job():
        for sport in sports_to_run:
            current_time = datetime.now().strftime("%H:%M:%S")
            logger.info(f"[{current_time}] {sport} 워커 실행...")
            result = subprocess.run(
                [sys.executable, worker_path, sport],
                check=False,
                env=os.environ.copy(),
            )
            if result.returncode != 0:
                logger.error(f"[스케줄러] {sport} 워커 오류: {result.returncode}")
            else:
                logger.info(f"[스케줄러] {sport} 워커 완료")

    if schedule:
        for time_str in get_schedule_times():
            schedule.every().day.at(time_str).do(run_scheduled_job)

        while True:
            schedule.run_pending()
            time.sleep(60)
    else:
        logger.warning("⚠️ schedule 모듈이 없어서 스케줄링을 건너뜁니다")
        logger.info("💡 pip install schedule 로 설치하세요")