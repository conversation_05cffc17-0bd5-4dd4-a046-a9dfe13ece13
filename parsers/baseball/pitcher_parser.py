"""
투수 파서 전략 모듈: Strategy 패턴을 사용한 HTML 파싱 전략 구현
SOLID 원칙 적용으로 효율화된 코드
"""
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Optional, Tuple, TypeVar

from bs4 import BeautifulSoup, Tag

from utils.common import extract_league_id, extract_team_id, safe_float
from utils.exceptions import ParsingError
from utils.helpers import normalize_innings

# 로거 설정
logger = logging.getLogger(__name__)

# 제네릭 타입 변수 정의
T = TypeVar('T')


class ParserStrategy(Generic[T], ABC):
    """
    파서 전략 추상 클래스
    
    Strategy 패턴 적용: 각 웹사이트나 데이터 형식에 따라 
    다양한 파싱 전략 구현 가능
    """
    
    @abstractmethod
    def parse(self, html: str, **kwargs) -> T:
        """HTML 문자열을 파싱하여 원하는 형식의 데이터로 변환"""
        pass


# SRP: 테이블 매핑 관리만 담당
class TableColumnMapper:
    """테이블 컬럼 매핑 관리 클래스"""
    
    @staticmethod
    def get_season_stats_mapping() -> Dict[int, str]:
        """시즌 성적 테이블 매핑"""
        return {
            0: "games",                  # 경기수 (12)
            1: "innings",                # 이닝 (69 1/3)
            2: "era",                    # 평균자책점 (2.34)
            3: "win_rate",               # 승률 (0.714)
            4: "wins",                   # 승 (5)
            5: "losses",                 # 패 (2)
            6: "saves",                  # 세이브 (0)
            7: "holds",                  # 홀드 (0)
            8: "runs_allowed",           # 실점 총합 (18)
            9: "earned_runs",            # 자책점 (18)
            10: "home_runs_allowed",     # 피홈런 (9)
            11: "hits_allowed",          # 피안타 (51)
            12: "strikeouts",            # 탈삼진 (66)
            13: "base_on_balls",         # 볼넷 (14)
            14: "hit_by_pitch",          # 사구 (0)
            15: "wild_pitch",            # 폭투 (1)
            16: "balk"                   # 보크 (0)
        }
    
    @staticmethod
    def get_recent_games_mapping() -> Dict[int, str]:
        """최근 경기 테이블 매핑"""
        return {
            0: "game_date",              # 경기일 (25.06.02(월))
            1: "opponent_team",          # 상대팀 (세인카디)
            2: "wins_losses",            # 승패 (승/패/없음)
            3: "innings",                # 이닝 (6)
            4: "pitch_count",            # 투구 (81)
            5: "batters_faced",          # 타자 (23)
            6: "at_bats",                # 타수 (20)
            7: "hits_allowed",           # 안타 (4)
            8: "home_runs_allowed",      # 홈런 (0)
            9: "base_on_balls",          # 4구 (3)
            10: "hit_by_pitch",          # 사구 (0)
            11: "strikeouts",            # 탈삼진 (4)
            12: "wild_pitch",            # 폭투 (0)
            13: "balk",                  # 보크 (0)
            14: "runs_allowed",          # 실점 (1)
            15: "earned_runs",           # 자책 (1)
            16: "era"                    # 평균자책점 (1.50)
        }


class TableParser(ParserStrategy[List[Dict[str, Any]]]):
    """HTML 테이블 파싱 전략"""
    
    def __init__(self):
        self.mapper = TableColumnMapper()
    
    def parse(self, html: str, **kwargs) -> List[Dict[str, Any]]:
        """HTML 테이블을 파싱하여 행 데이터의 리스트로 변환"""
        soup = BeautifulSoup(html, 'html.parser')
        table = soup.find('table')
        # 타입 안전성을 위한 캐스팅
        safe_table = table if isinstance(table, Tag) else None
        return self.parse_table(safe_table)
    
    def parse_table(self, table: Optional[Tag], 
                    table_type: str = 'season') -> List[Dict[str, Any]]:
        """HTML 테이블 태그를 파싱하여 행 데이터의 리스트로 변환"""
        if not table:
            return []
        
        # 테이블 타입에 따라 매핑 선택
        mapping = (self.mapper.get_season_stats_mapping()
                   if table_type == 'season'
                   else self.mapper.get_recent_games_mapping())
        
        try:
            return TableParser._extract_table_data(table, mapping)
        except Exception as e:
            raise ParsingError('table', f"테이블 파싱 오류: {str(e)}")
    
    @staticmethod
    def _extract_table_data(table: Tag,
                            mapping: Dict[int, str]) -> List[Dict[str, Any]]:
        """테이블 데이터 추출"""
        data_list = []
        tbody = table.find('tbody')
        
        if isinstance(tbody, Tag):
            for row in tbody.find_all('tr'):
                if isinstance(row, Tag):
                    row_data = TableParser._extract_row_data(row, mapping)
                    if row_data:
                        data_list.append(row_data)
        
        return data_list
    
    @staticmethod
    def _extract_row_data(row: Tag,
                          mapping: Dict[int, str]) -> Dict[str, Any]:
        """행 데이터 추출"""
        cols = row.find_all('td')
        row_data = {}
        
        for i, col in enumerate(cols):
            if isinstance(col, Tag):
                key = mapping.get(i, f'col_{i+1}')
                row_data[key] = col.text.strip()
        
        return row_data


# SRP: URL 생성만 담당
class URLBuilder:
    """URL 생성 클래스"""
    
    @staticmethod
    def build_team_url(league_id: str, team_id: str) -> str:
        """팀 URL 생성"""
        if not league_id or not team_id:
            return ''
        
        base = "https://www.betman.co.kr/main/mainPage/gameinfo"
        path = (f"/bsTeamDetail.do?item=BS&leagueId={league_id}"
                f"&teamId={team_id}")
        return base + path


class TeamInfoParser(ParserStrategy[Tuple[str, str, str, str]]):
    """팀 정보 파싱 전략"""
    
    def __init__(self):
        self.url_builder = URLBuilder()
    
    def parse(self, html: str, **kwargs) -> Tuple[str, str, str, str]:
        """팀 HTML을 파싱하여 팀명, 투수명, 팀 URL, 팀 ID 추출"""
        soup = BeautifulSoup(html, 'html.parser')
        tb_div = soup.find('div', class_='tb')
        # 타입 안전성을 위한 캐스팅
        safe_tb_div = tb_div if isinstance(tb_div, Tag) else None
        return self.extract_team_info(safe_tb_div)
    
    def extract_team_info(self,
                          tb_div: Optional[Tag]) -> Tuple[str, str, str, str]:
        """팀 블록에서 팀명, 투수명, 팀 URL, 팀 ID 추출"""
        if not tb_div:
            return ('', '', '', '')
            
        try:
            team_name, pitcher_name = TeamInfoParser._extract_names(tb_div)
            team_url, team_id = self._extract_url_and_id(tb_div)
            
            return team_name, pitcher_name, team_url, team_id
            
        except Exception as e:
            raise ParsingError('team_info', f"팀 정보 파싱 오류: {str(e)}")
    
    @staticmethod
    def _extract_names(tb_div: Tag) -> Tuple[str, str]:
        """팀명과 투수명 추출"""
        team_a = tb_div.find('a', class_='team')
        pitcher_tag = tb_div.find('span', class_='player')

        team_name = team_a.text.strip() if isinstance(team_a, Tag) else ''
        pitcher_name = ''
        if isinstance(pitcher_tag, Tag):
            pitcher_name = pitcher_tag.text.strip()
        
        return team_name, pitcher_name
    
    def _extract_url_and_id(self, tb_div: Tag) -> Tuple[str, str]:
        """팀 URL과 팀 ID 추출"""
        team_a = tb_div.find('a', class_='team')
        
        if not isinstance(team_a, Tag):
            return '', ''
        
        href_attr = team_a.get('href')
        href = href_attr if isinstance(href_attr, str) else ''
        
        league_id = extract_league_id(href)
        team_id = extract_team_id(href)
        
        team_url = self.url_builder.build_team_url(league_id, team_id)
        
        return team_url, team_id


# SRP: 날짜 변환만 담당
class DateConverter:
    """날짜 변환 유틸리티"""
    
    @staticmethod
    def convert_date_to_key(date_str: str) -> str:
        """날짜 문자열을 키 형태로 변환: '25.06.02(월)' -> '20250602'"""
        try:
            # 괄호와 요일 제거: "25.06.02(월)" -> "25.06.02"
            date_clean = date_str.split('(')[0].strip()
            
            # 점으로 분리: "25.06.02" -> ["25", "06", "02"]
            parts = date_clean.split('.')
            if len(parts) == 3:
                year, month, day = parts
                # 2자리 연도를 4자리로 변환 (25 -> 2025)
                if len(year) == 2:
                    year = f"20{year}"
                
                # 형태 변환: "2025" + "06" + "02" = "20250602"
                return f"{year}{month.zfill(2)}{day.zfill(2)}"
            
        except Exception:
            # 날짜 변환 실패 시 빈 문자열 반환
            pass
        
        return ""


# SRP: 리그 정보 관리만 담당
class LeagueService:
    """리그 정보 서비스"""
    
    @staticmethod
    def get_league_mapping() -> Dict[str, str]:
        """리그 매핑 정보"""
        return {
            'bs001': 'KBO',
            'bs002': 'MLB', 
            'bs004': 'NPB'
        }
    
    def get_league_from_team_name(self, team_name: str) -> str:
        """팀명으로부터 리그 결정 - 항상 문자열 반환"""
        if not team_name:
            return ""
        try:
            from database.database import connect_supabase
            client = connect_supabase()
            if not client:
                return ""
            response = client.table('team_info').select(
                'sportic_league_id'
            ).or_(
                f'team_name.ilike.%{team_name}%',
                f'team_full_name.ilike.%{team_name}%'
            ).limit(1).execute()
            if response.data and len(response.data) > 0:
                sportic_league_id = response.data[0].get(
                    'sportic_league_id', ''
                )
                league_map = self.get_league_mapping()
                league_str = league_map.get(sportic_league_id, '')
                if not isinstance(league_str, str):
                    if league_str is not None:
                        league_str = str(league_str)
                    else:
                        league_str = ''
                return league_str
        except Exception:
            pass
        return ""


# SRP: 팀명 매핑만 담당
class TeamNameMapper:
    """팀명 매핑 서비스"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        self.team_mappings = team_mappings or {}
    
    def map_team_name(self, original_team_name: str) -> str:
        """팀명을 풀네임으로 매핑"""
        if not self.team_mappings:
            return original_team_name
        
        # 정확한 매칭 우선
        if original_team_name in self.team_mappings:
            return self.team_mappings[original_team_name]
        
        # 부분 매칭 시도
        for key, value in self.team_mappings.items():
            if original_team_name in key or key in original_team_name:
                return value
        
        return original_team_name


# SRP: 기본 정보 추출만 담당
class BasicInfoExtractor:
    """기본 선수 정보 추출기"""
    
    def __init__(self, league_service: LeagueService):
        self.league_service = league_service
    
    def extract_basic_info(self, soup: BeautifulSoup, 
                          player_info: Dict[str, Any]) -> None:
        """기본 선수 정보 추출"""
        info_box = soup.find('div', class_='infoBox')
        if not isinstance(info_box, Tag):
            return
        
        BasicInfoExtractor._extract_player_number(soup, player_info)
        self._extract_team_info(info_box, player_info)
        BasicInfoExtractor._extract_birth_info(info_box, player_info)
        BasicInfoExtractor._extract_physical_info(info_box, player_info)
        BasicInfoExtractor._extract_season_summary(soup, player_info)
    
    @staticmethod
    def _extract_player_number(soup: BeautifulSoup,
                               player_info: Dict[str, Any]) -> None:
        """선수 번호 추출"""
        backnum_tag = soup.find('strong', id='player_backnum')
        if isinstance(backnum_tag, Tag):
            backnum_text = backnum_tag.text.strip()
            try:
                player_info['no'] = int(backnum_text)
            except (ValueError, TypeError):
                player_info['no'] = ""
        else:
            player_info['no'] = ""
    
    def _extract_team_info(self, info_box: Tag, 
                          player_info: Dict[str, Any]) -> None:
        """팀 정보 추출 (리그는 항상 문자열로)"""
        team_tag = info_box.find('strong', id='player_team_nm')
        team_name = team_tag.text.strip() if isinstance(team_tag, Tag) else ''
        player_info['team'] = team_name
        league = self.league_service.get_league_from_team_name(team_name)
        # Ensure league is always a string (final safety)
        player_info['league'] = str(league) if league is not None else ''
    
    @staticmethod
    def _extract_birth_info(info_box: Tag,
                            player_info: Dict[str, Any]) -> None:
        """생년월일 정보 추출"""
        birth_year = info_box.find('span', id='birth_year')
        birth_month = info_box.find('span', id='birth_month')
        birth_day = info_box.find('span', id='birth_day')
        
        birth_parts = []
        if isinstance(birth_year, Tag) and birth_year.text.strip():
            birth_parts.append(birth_year.text.strip())
        if isinstance(birth_month, Tag) and birth_month.text.strip():
            birth_parts.append(birth_month.text.strip().zfill(2))
        if isinstance(birth_day, Tag) and birth_day.text.strip():
            birth_parts.append(birth_day.text.strip().zfill(2))
        
        player_info['birth'] = (
            '-'.join(birth_parts) if len(birth_parts) == 3 else ''
        )
    
    @staticmethod
    def _extract_physical_info(info_box: Tag,
                               player_info: Dict[str, Any]) -> None:
        """신체 정보 추출"""
        height_tag = info_box.find('span', id='player_ht')
        height_text = (
            height_tag.text.strip() if isinstance(height_tag, Tag) else ''
        )
        player_info['height'] = safe_float(height_text)
        
        weight_tag = info_box.find('span', id='player_wt')
        player_info['weight'] = (
            weight_tag.text.strip() if isinstance(weight_tag, Tag) else ''
        )
    
    @staticmethod
    def _extract_season_summary(soup: BeautifulSoup,
                                player_info: Dict[str, Any]) -> None:
        """시즌 요약 정보 추출 (승, 패, 세이브, 삼진, ERA)"""
        season_summary = {}
        
        # ---- 시즌 성적 요소 추출 ----
        # 일부 페이지 구조 변경 대응: 기존 class 기반(span.win 등) + id 기반(em#player_*) 모두 지원

        def _find_stat_tag(primary: Dict[str, str],
                           fallback: Dict[str, str]) -> Tag:  # type: ignore[type-arg]
            """첫 번째 선택자로 검색 후 없으면 두 번째 선택자로 재검색"""
            tag = soup.find(**primary)
            if not isinstance(tag, Tag):
                tag = soup.find(**fallback)
            return tag

        # 승 수
        win_tag = _find_stat_tag(
            {"name": "span", "class_": "win"},
            {"name": "em", "id": "player_win"}
        )

        # 세이브 수
        save_tag = _find_stat_tag(
            {"name": "span", "class_": "save"},
            {"name": "em", "id": "player_save"}
        )

        # 탈삼진 수
        k_tag = _find_stat_tag(
            {"name": "span", "class_": "k"},
            {"name": "em", "id": "player_k"}
        )

        # ERA
        era_tag = _find_stat_tag(
            {"name": "span", "class_": "era"},
            {"name": "em", "id": "player_era"}
        )
        
        # 디버그: 실제로 어떤 값들이 추출되는지 확인
        logger.debug(f"[season_summary] win_tag: {win_tag}")
        logger.debug(f"[season_summary] save_tag: {save_tag}")
        logger.debug(f"[season_summary] k_tag: {k_tag}")
        logger.debug(f"[season_summary] era_tag: {era_tag}")
        
        season_summary['wins'] = (
            win_tag.text.strip() if isinstance(win_tag, Tag) else ''
        )
        season_summary['saves'] = (
            save_tag.text.strip() if isinstance(save_tag, Tag) else ''
        )
        season_summary['strikeouts'] = (
            k_tag.text.strip() if isinstance(k_tag, Tag) else ''
        )
        season_summary['era'] = (
            era_tag.text.strip() if isinstance(era_tag, Tag) else ''
        )
        
        # 디버그: 최종 추출된 값들 확인
        logger.debug(f"[season_summary] 최종 결과: {season_summary}")
        
        # 이닝 필드가 있다면 정규화
        if 'innings' in season_summary:
            season_summary['innings'] = normalize_innings(season_summary['innings'])
        
        player_info['season_summary'] = season_summary


# SRP: 최근 경기 추출만 담당
class GameLogExtractor:
    """최근 경기 추출기"""
    
    def __init__(self, date_converter: DateConverter, 
                 team_mapper: TeamNameMapper):
        self.date_converter = date_converter
        self.team_mapper = team_mapper
    
    def extract_appearance_records(self, soup: BeautifulSoup,
                                  player_info: Dict[str, Any]) -> None:
        """출장 기록 추출 - recent_games로 변환 (첫 출장 감지는 parse_pitcher_data에서)"""
        past_record_tbody = soup.find('tbody', id='pitcher_pastRecord')
        if isinstance(past_record_tbody, Tag):
            past_record_table = past_record_tbody.find_parent('table')
            if isinstance(past_record_table, Tag):
                recent_games = self._extract_recent_games(past_record_table)
                player_info['recent_games'] = recent_games
                GameLogExtractor._extract_recent_games_summary(
                    past_record_table, player_info
                )
            else:
                player_info['recent_games'] = {}
        else:
            player_info['recent_games'] = {}
        # do not set first_appearance here

    def _extract_recent_games(self, table: Tag) -> Dict[str, Dict[str, Any]]:
        """최근 경기 추출"""
        recent_games = {}
        tbody = table.find('tbody')
        
        if isinstance(tbody, Tag):
            for row in tbody.find_all('tr'):
                if isinstance(row, Tag):
                    game_data = self._extract_single_game_data(row)
                    if game_data:
                        date_key, data = game_data
                        recent_games[date_key] = data
        
        return recent_games
    
    def _extract_single_game_data(self, 
                                 row: Tag) -> Optional[Tuple[str, Dict[str, Any]]]:
        """단일 게임 데이터 추출"""
        cols = row.find_all('td')
        if len(cols) < 17:
            return None
        
        # 날짜 추출
        date_str = cols[0].text.strip()
        if '(' not in date_str:
            return None
        
        date_key = self.date_converter.convert_date_to_key(date_str)
        if not date_key:
            return None
        
        # 상대팀명 추출 및 매핑
        opponent_td = cols[1]
        original_team_name = GameLogExtractor._extract_team_name_from_cell(opponent_td)
        team_name = self.team_mapper.map_team_name(original_team_name)
        
        # 게임 데이터 구성
        game_data = {
            "opponent_team": team_name,
            "wins_losses": GameLogExtractor._convert_wins_losses(cols[2].text.strip()),
            "innings": cols[3].text.strip(),
            "pitch_count": cols[4].text.strip(),
            "batters_faced": cols[5].text.strip(),
            "at_bats": cols[6].text.strip(),
            "hits_allowed": cols[7].text.strip(),
            "home_runs_allowed": cols[8].text.strip(),
            "base_on_balls": cols[9].text.strip(),
            "hit_by_pitch": cols[10].text.strip(),
            "strikeouts": cols[11].text.strip(),
            "wild_pitch": cols[12].text.strip(),
            "balk": cols[13].text.strip(),
            "runs_allowed": cols[14].text.strip(),
            "earned_runs": cols[15].text.strip(),
            "era": cols[16].text.strip()
        }
        
        return date_key, game_data
    
    @staticmethod
    def _extract_team_name_from_cell(cell: Tag) -> str:
        """셀에서 팀명 추출"""
        # <strong> 태그가 있으면 그 안의 텍스트 우선
        strong_tag = cell.find('strong')
        if strong_tag and isinstance(strong_tag, Tag):
            return strong_tag.get_text(strip=True)
        
        # <a> 태그가 있으면 그 안의 텍스트
        a_tag = cell.find('a')
        if a_tag and isinstance(a_tag, Tag):
            return a_tag.get_text(strip=True)
        
        # 일반 텍스트
        return cell.get_text(strip=True)
    
    @staticmethod
    def _convert_wins_losses(wins_losses_text: str) -> str:
        """승패 텍스트를 영문으로 변환 (승->W, 패->L, 무->D)"""
        text = wins_losses_text.strip()
        
        # 한글 승패를 영문으로 변환
        if text == "승":
            return "W"
        elif text == "패":
            return "L"
        elif text == "무":
            return "D"
        else:
            # 이미 영문이거나 다른 형태인 경우 그대로 반환
            return text
    
    @staticmethod
    def _extract_recent_games_summary(table: Tag,
                                     player_info: Dict[str, Any]) -> None:
        """최근 경기 요약 통계 추출 (합계 행)"""
        try:
            # 디버깅용 로그
            logger.debug("최근 경기 요약 추출 시작")
            
            # 1. 가장 직접적인 방법 - CSS 선택자 사용
            sum_row = table.select_one("tfoot#pitcher_past_sumRecord tr")
            if sum_row:
                logger.debug("CSS 선택자로 요약 행 찾음")
                sum_cells = sum_row.find_all("td")
                sum_values = [cell.get_text(strip=True) for cell in sum_cells]
                
                # 데이터 확인용 로그
                logger.debug(f"추출된 셀 개수: {len(sum_cells)}")
                logger.debug(f"추출된 값들: {sum_values}")
                
                if len(sum_values) >= 17:
                    # 여기서 요약 데이터 매핑 (테이블 구조에 맞게)
                    player_info["recent_games_summary"] = {
                        "innings": sum_values[3],
                        "pitch_count": sum_values[4], 
                        "batters_faced": sum_values[5],
                        "at_bats": sum_values[6],
                        "hits_allowed": sum_values[7],
                        "home_runs_allowed": sum_values[8],
                        "base_on_balls": sum_values[9],
                        "hit_by_pitch": sum_values[10],
                        "strikeouts": sum_values[11],
                        "wild_pitch": sum_values[12],
                        "balk": sum_values[13],
                        "runs_allowed": sum_values[14],
                        "earned_runs": sum_values[15],
                        "era": sum_values[16]
                    }
                    logger.debug(f"recent_games_summary 추출 성공: {player_info['recent_games_summary']}")
                    return
                else:
                    logger.debug(f"충분한 셀이 없음 - 셀 개수: {len(sum_values)}")
            
            # 2. 모든 tfoot 확인
            all_tfoots = table.find_all("tfoot")
            logger.debug(f"모든 tfoot 태그 개수: {len(all_tfoots)}")
            
            for i, tfoot in enumerate(all_tfoots):
                logger.debug(f"tfoot[{i}] 확인")
                row = tfoot.find("tr")
                if row:
                    sum_cells = row.find_all("td")
                    if len(sum_cells) >= 17:
                        sum_values = [cell.get_text(strip=True) for cell in sum_cells]
                        player_info["recent_games_summary"] = {
                            "innings": sum_values[3],
                            "pitch_count": sum_values[4], 
                            "batters_faced": sum_values[5],
                            "at_bats": sum_values[6],
                            "hits_allowed": sum_values[7],
                            "home_runs_allowed": sum_values[8],
                            "base_on_balls": sum_values[9],
                            "hit_by_pitch": sum_values[10],
                            "strikeouts": sum_values[11],
                            "wild_pitch": sum_values[12],
                            "balk": sum_values[13],
                            "runs_allowed": sum_values[14],
                            "earned_runs": sum_values[15],
                            "era": sum_values[16]
                        }
                        logger.debug(f"tfoot[{i}]에서 요약 추출 성공")
                        return
            
            # 3. 아래와 같이 직접 HTML을 검색
            logger.debug("HTML 직접 검색으로 요약 행 찾기 시도")
            html_str = str(table)
            if "pitcher_past_sumRecord" in html_str:
                logger.debug("HTML에서 pitcher_past_sumRecord 문자열 발견")
            
            # 4. 마지막 방법: 최근 경기 데이터로부터 요약 계산
            if player_info.get('recent_games') and len(player_info['recent_games']) > 0:
                logger.debug("최근 경기 데이터에서 요약 계산")
                recent_games_summary = GameLogExtractor._calculate_summary_from_recent_games(
                    player_info['recent_games']
                )
                player_info['recent_games_summary'] = recent_games_summary
                logger.debug(f"계산된 요약: {recent_games_summary}")
                return
                
            # 모든 방법 실패 시 빈 딕셔너리 반환
            logger.debug("모든 요약 추출 방법 실패, 빈 요약 반환")
            player_info['recent_games_summary'] = {}
                
        except Exception as e:
            logger.error(f"최근 경기 요약 추출 오류: {e}")
            logger.error(f"오류 상세: {str(e)}")
            player_info['recent_games_summary'] = {}
    
    @staticmethod
    def _calculate_summary_from_recent_games(recent_games: Dict[str, Dict]) -> Dict[str, str]:
        """최근 경기 데이터로부터 요약 통계 계산"""
        if not recent_games:
            return {}
            
        try:
            # 합산할 필드들
            num_fields = ['earned_runs', 'runs_allowed', 'hits_allowed', 
                         'home_runs_allowed', 'base_on_balls', 'hit_by_pitch',
                         'strikeouts', 'wild_pitch', 'balk', 'pitch_count', 
                         'batters_faced', 'at_bats']
            
            # 합산 초기화
            totals = {field: 0 for field in num_fields}
            innings_total = 0  # 이닝 합계 (특수 처리 필요)
            
            # 최근 경기 순회하며 합산
            for date, game_data in recent_games.items():
                for field in num_fields:
                    value = game_data.get(field, '0')
                    if isinstance(value, str) and value.strip():
                        try:
                            totals[field] += int(value)
                        except ValueError:
                            pass
                
                # 이닝 합산 (4 2/3 형식 또는 11 2 형식 처리)
                innings = game_data.get('innings', '0')
                if innings and isinstance(innings, str):
                    if ' ' in innings:
                        parts = innings.split(' ')
                        whole = int(parts[0])
                        fraction = 0
                        if len(parts) > 1:
                            if '/' in parts[1]:
                                # "4 2/3" 형식
                                fraction_parts = parts[1].split('/')
                                if len(fraction_parts) == 2:
                                    try:
                                        fraction = float(int(fraction_parts[0]) / int(fraction_parts[1]))
                                    except (ValueError, ZeroDivisionError):
                                        pass
                            else:
                                # "11 2" 형식 (축약형) - 아웃 카운트를 분수로 변환
                                try:
                                    outs = int(parts[1])
                                    if outs == 1:
                                        fraction = 1.0 / 3.0
                                    elif outs == 2:
                                        fraction = 2.0 / 3.0
                                except ValueError:
                                    pass
                        innings_total += whole + fraction
                    else:
                        try:
                            innings_total += float(innings)
                        except ValueError:
                            pass
            
            # 이닝 형식 변환 (11.666... -> 11 2/3)
            whole_innings = int(innings_total)
            fraction_part = innings_total - whole_innings
            
            if abs(fraction_part) < 0.001:  # 거의 0인 경우
                innings_formatted = str(whole_innings)
            elif abs(fraction_part - 1.0/3.0) < 0.001:  # 1/3에 가까운 경우
                innings_formatted = f"{whole_innings} 1/3"
            elif abs(fraction_part - 2.0/3.0) < 0.001:  # 2/3에 가까운 경우
                innings_formatted = f"{whole_innings} 2/3"
            else:
                # 다른 소수점은 반올림하여 가장 가까운 1/3 단위로 변환
                if fraction_part < 1.0/6.0:
                    innings_formatted = str(whole_innings)
                elif fraction_part < 0.5:
                    innings_formatted = f"{whole_innings} 1/3"
                elif fraction_part < 5.0/6.0:
                    innings_formatted = f"{whole_innings} 2/3"
                else:
                    innings_formatted = str(whole_innings + 1)
            
            # 결과 요약 구성
            summary = {field: str(totals[field]) for field in num_fields}
            summary['innings'] = innings_formatted
            
            # ERA 계산
            if innings_total > 0:
                era = round((totals['earned_runs'] * 9) / innings_total, 2)
                summary['era'] = str(era)
            else:
                summary['era'] = '0.00'
                
            return summary
            
        except Exception as e:
            logger.error(f"최근 경기 요약 계산 오류: {e}")
            return {}

    @staticmethod
    def _find_summary_rows(table: Tag) -> List[Tag]:
        """요약 행 찾기"""
        # "합계" 행 찾기 (class="noBold"이거나 "합계" 텍스트 포함)
        summary_rows = table.find_all('tr', class_='noBold')
        if not summary_rows:
            # class가 없는 경우 텍스트로 찾기
            all_rows = table.find_all('tr')
            for row in all_rows:
                if isinstance(row, Tag):
                    first_td = row.find('td')
                    if (isinstance(first_td, Tag) and 
                        '합계' in first_td.get_text(strip=True)):
                        summary_rows = [row]
                        break
        return summary_rows

    @staticmethod
    def _build_summary_data(cols: List[Tag]) -> Dict[str, str]:
        """요약 데이터 구성"""
        return {
            "innings": cols[3].get_text(strip=True),
            "pitch_count": cols[4].get_text(strip=True), 
            "batters_faced": cols[5].get_text(strip=True),
            "at_bats": cols[6].get_text(strip=True),
            "hits_allowed": cols[7].get_text(strip=True),
            "home_runs_allowed": cols[8].get_text(strip=True),
            "base_on_balls": cols[9].get_text(strip=True),
            "hit_by_pitch": cols[10].get_text(strip=True),
            "strikeouts": cols[11].get_text(strip=True),
            "wild_pitch": cols[12].get_text(strip=True),
            "balk": cols[13].get_text(strip=True),
            "runs_allowed": cols[14].get_text(strip=True),
            "earned_runs": cols[15].get_text(strip=True),
            "era": cols[16].get_text(strip=True)
        }


class PitcherDataParser(ParserStrategy[Dict[str, Any]]):
    """투수 데이터 파싱 전략 - SOLID 원칙 적용"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        # DIP: 의존성 주입을 통한 느슨한 결합
        self.table_parser = TableParser()
        self.league_service = LeagueService()
        self.team_mapper = TeamNameMapper(team_mappings)
        self.date_converter = DateConverter()
        self.basic_info_extractor = BasicInfoExtractor(self.league_service)
        self.recent_games_extractor = GameLogExtractor(
            self.date_converter, 
            self.team_mapper
        )
    
    def parse(self, html: str, **kwargs) -> Dict[str, Any]:
        """투수 상세 페이지 HTML을 파싱하여 관련 통계 데이터 추출"""
        pitcher_name = kwargs.get('pitcher_name', '')
        return self.parse_pitcher_data(html, pitcher_name)
    
    def parse_pitcher_data(self, html: str, pitcher_name: str) -> Dict[str, Any]:
        """투수 상세 페이지 HTML에서 모든 관련 통계 데이터 추출"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            player_info: Dict[str, Any] = {'name': pitcher_name}
            # 각 추출기를 통한 데이터 수집
            self.basic_info_extractor.extract_basic_info(soup, player_info)
            self._extract_season_stats(soup, player_info)
            self.recent_games_extractor.extract_appearance_records(
                soup, player_info
            )
            # 진짜 데이터 없음 감지는 모든 주요 데이터가 비었을 때만
            season_stats = player_info.get('season_stats', [])
            recent_games = player_info.get('recent_games', {})
            # history_stats는 필요시 추가 구현, 예시로 빈 리스트 처리
            history_stats = player_info.get('history_stats', [])
            if not season_stats and not recent_games and not history_stats:
                player_info['first_appearance'] = 'league'
            elif not season_stats:
                player_info['first_appearance'] = 'season'
            # else: do not set first_appearance
            return player_info
        except Exception as e:
            raise ParsingError('pitcher_data', f"투수 데이터 파싱 오류: {str(e)}")

    def _extract_season_stats(self, soup: BeautifulSoup,
                              player_info: Dict[str, Any]) -> None:
        """시즌 통계 추출 (첫 출장 감지는 parse_pitcher_data에서)"""
        pitcher_area = soup.find('div', class_=['tblArea', 'pitcher_area'])
        if isinstance(pitcher_area, Tag):
            season_table = pitcher_area.find('table', class_='tbl')
            if isinstance(season_table, Tag):
                tbody = season_table.find('tbody')
                if tbody and tbody.find('tr', class_='noData'):
                    player_info['season_stats'] = []
                    return
                parsed_data = self.table_parser.parse_table(
                    season_table, 'season'
                )
                player_info['season_stats'] = parsed_data
            else:
                player_info['season_stats'] = []
        else:
            player_info['season_stats'] = []
