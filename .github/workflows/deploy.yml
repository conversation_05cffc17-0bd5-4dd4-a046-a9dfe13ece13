name: Deploy to EC2

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.13'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: Copy files to EC2
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USERNAME }}
          key: ${{ secrets.EC2_SSH_KEY }}
          port: 22
          source: "."
          target: "/home/<USER>/Stats"
          rm: true
          strip_components: 0

      - name: Deploy to EC2
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USERNAME }}
          key: ${{ secrets.EC2_SSH_KEY }}
          port: 22
          script: |
            cd /home/<USER>/Stats

            if [ ! -d "venv" ]; then
              python3 -m venv venv
            fi

            ./venv/bin/pip install --upgrade pip
            ./venv/bin/pip install -r requirements.txt

            if pm2 list | grep -q Stats; then
              pm2 restart Stats
            else
              pm2 start main.py --interpreter ./venv/bin/python3 --name proto_stats
            fi

            echo "배포가 완료되었습니다!"