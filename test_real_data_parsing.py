#!/usr/bin/env python3
"""
실제 웹사이트 데이터 파싱 테스트
"""
import asyncio
import sys
import os
from typing import Dict, List

# 프로젝트 루트를 Python path에 추가
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def test_soccer_team_parsing():
    """축구 팀 데이터 파싱 테스트"""
    print("=" * 60)
    print("🚀 축구 팀 데이터 파싱 테스트")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        from sports.soccer.parsers.soccer_parser import SoccerParser
        
        # 울산 HD FC 팀 페이지 크롤링
        team_url = "https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=SC001&teamId=K01"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print(f"📄 팀 페이지 로딩: {team_url}")
            await page.goto(team_url, wait_until='networkidle')
            await asyncio.sleep(3)  # 데이터 로딩 대기
            
            html = await page.content()
            await browser.close()
            
            print(f"✅ HTML 크기: {len(html)} bytes")
            
            # 파서로 데이터 추출
            parser = SoccerParser()
            team_data = parser.parse_team_data(
                html, 
                "울산 HD FC",
                league_id="SC001",
                league="K리그1"
            )
            
            print(f"\n📊 파싱 결과:")
            print(f"   팀명: {team_data.team_name}")
            print(f"   리그: {team_data.league}")
            print(f"   프로필: {len(team_data.profile)}개 항목")
            
            # 프로필 상세
            if team_data.profile:
                print(f"\n👤 팀 프로필:")
                for key, value in team_data.profile.items():
                    if value:
                        print(f"     {key}: {value}")
            
            # 시즌 통계
            if team_data.season_stats:
                print(f"\n📈 시즌 통계:")
                for stat_type, stats in team_data.season_stats.items():
                    if stats:
                        print(f"     {stat_type}: {len(stats)}개 항목")
                        # 주요 통계만 출력
                        for key in ['games', 'wins', 'draws', 'losses', 'points']:
                            if key in stats:
                                print(f"       {key}: {stats[key]}")
            
            # 최근 경기
            if team_data.recent_games:
                print(f"\n⚽ 최근 경기: {len(team_data.recent_games)}경기")
                for i, game in enumerate(team_data.recent_games[:3]):
                    print(f"     {i+1}. {game.get('date', '')} vs {game.get('opponent', '')} - {game.get('result', '')}")
            
            # 선수 명단
            if team_data.players:
                print(f"\n👥 선수 명단: {len(team_data.players)}명")
                positions = {}
                for player in team_data.players:
                    pos = player.get('position', 'Unknown')
                    if pos not in positions:
                        positions[pos] = []
                    positions[pos].append(player.get('player_name', ''))
                
                for pos, players in positions.items():
                    print(f"     {pos}: {len(players)}명 - {', '.join(players[:3])}{'...' if len(players) > 3 else ''}")
            
            return True
            
    except Exception as e:
        print(f"❌ 축구 팀 파싱 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_soccer_player_parsing():
    """축구 선수 데이터 파싱 테스트"""
    print("\n" + "=" * 60)
    print("🚀 축구 선수 데이터 파싱 테스트")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        from sports.soccer.parsers.soccer_parser import SoccerPlayerParser
        
        # 류성민 선수 페이지 크롤링
        player_url = "https://www.betman.co.kr/main/mainPage/gameinfo/scPlayerDetail.do?item=SC&leagueId=SC001&teamId=K01&playerId=20250023"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print(f"📄 선수 페이지 로딩: {player_url}")
            await page.goto(player_url, wait_until='networkidle')
            await asyncio.sleep(3)  # 데이터 로딩 대기
            
            html = await page.content()
            await browser.close()
            
            print(f"✅ HTML 크기: {len(html)} bytes")
            
            # 파서로 데이터 추출
            parser = SoccerPlayerParser(html)
            player_data = parser.parse_player_data(
                "류성민",
                league_id="SC001",
                league="K리그1",
                team_name="울산 HD FC",
                position="MF"
            )
            
            print(f"\n📊 파싱 결과:")
            print(f"   선수명: {player_data.player_name}")
            print(f"   팀: {player_data.team_name}")
            print(f"   포지션: {player_data.position}")
            print(f"   리그: {player_data.league}")
            
            # 프로필 상세
            if player_data.profile:
                print(f"\n👤 선수 프로필:")
                for key, value in player_data.profile.items():
                    if value:
                        print(f"     {key}: {value}")
            
            # 시즌 통계
            if player_data.season_stats:
                print(f"\n📈 시즌 통계: {len(player_data.season_stats)}개 항목")
                for key, value in list(player_data.season_stats.items())[:10]:
                    print(f"     {key}: {value}")
            
            # 커리어 통계
            if player_data.career_stats:
                print(f"\n🏆 커리어 통계:")
                career_seasons = player_data.career_stats.get('career_seasons', [])
                if career_seasons:
                    print(f"     총 {len(career_seasons)}시즌 데이터")
                    for i, season in enumerate(career_seasons[:3]):
                        print(f"     시즌 {i+1}: {season}")
            
            return True
            
    except Exception as e:
        print(f"❌ 축구 선수 파싱 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_basketball_team_parsing():
    """농구 팀 데이터 파싱 테스트"""
    print("\n" + "=" * 60)
    print("🚀 농구 팀 데이터 파싱 테스트")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        from sports.basketball.parsers.basketball_parser import BasketballParser
        
        # 휴스턴 로켓츠 팀 페이지 크롤링 (NBA)
        team_url = "https://www.betman.co.kr/main/mainPage/gameinfo/bkTeamDetail.do?item=BK&leagueId=BK002&teamId=HOU"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print(f"📄 팀 페이지 로딩: {team_url}")
            await page.goto(team_url, wait_until='networkidle')
            await asyncio.sleep(3)  # 데이터 로딩 대기
            
            html = await page.content()
            await browser.close()
            
            print(f"✅ HTML 크기: {len(html)} bytes")
            
            # 농구 시즌이 아닐 수 있으므로 데이터 확인
            if "데이터가 없습니다" in html or len(html) < 5000:
                print("⚠️ 농구 시즌이 아니거나 데이터가 없습니다")
                return True
            
            # 파서로 데이터 추출
            parser = BasketballParser()
            team_data = parser.parse_team_data(
                html, 
                "휴스턴 로켓츠",
                league_id="BK002",
                league="NBA"
            )
            
            print(f"\n📊 파싱 결과:")
            print(f"   팀명: {team_data.team_name}")
            print(f"   리그: {team_data.league}")
            print(f"   프로필: {len(team_data.profile)}개 항목")
            
            return True
            
    except Exception as e:
        print(f"❌ 농구 팀 파싱 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_volleyball_team_parsing():
    """배구 팀 데이터 파싱 테스트"""
    print("\n" + "=" * 60)
    print("🚀 배구 팀 데이터 파싱 테스트")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        from sports.volleyball.parsers.volleyball_parser import VolleyballParser
        
        # 현대캐피탈 스카이워커스 팀 페이지 크롤링
        team_url = "https://www.betman.co.kr/main/mainPage/gameinfo/vlTeamDetail.do?item=VL&leagueId=VL001&teamId=1005"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print(f"📄 팀 페이지 로딩: {team_url}")
            await page.goto(team_url, wait_until='networkidle')
            await asyncio.sleep(3)  # 데이터 로딩 대기
            
            html = await page.content()
            await browser.close()
            
            print(f"✅ HTML 크기: {len(html)} bytes")
            
            # 배구 시즌이 아닐 수 있으므로 데이터 확인
            if "데이터가 없습니다" in html or len(html) < 5000:
                print("⚠️ 배구 시즌이 아니거나 데이터가 없습니다")
                return True
            
            # 파서로 데이터 추출
            parser = VolleyballParser()
            team_data = parser.parse_team_data(
                html, 
                "현대캐피탈 스카이워커스",
                league_id="VL001",
                league="KOVO남"
            )
            
            print(f"\n📊 파싱 결과:")
            print(f"   팀명: {team_data.team_name}")
            print(f"   리그: {team_data.league}")
            print(f"   프로필: {len(team_data.profile)}개 항목")
            
            return True
            
    except Exception as e:
        print(f"❌ 배구 팀 파싱 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """메인 테스트 함수"""
    print("🚀 실제 웹사이트 데이터 파싱 테스트 시작")
    print("=" * 80)
    
    tests = [
        ("축구 팀", test_soccer_team_parsing),
        ("축구 선수", test_soccer_player_parsing),
        ("농구 팀", test_basketball_team_parsing),
        ("배구 팀", test_volleyball_team_parsing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 {test_name} 테스트 실행 중...")
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 테스트 통과")
            else:
                print(f"❌ {test_name} 테스트 실패")
        except Exception as e:
            print(f"❌ {test_name} 테스트 실행 실패: {e}")
    
    print("\n" + "=" * 80)
    print(f"🎉 실제 데이터 파싱 테스트 완료: {passed}/{total} 통과")
    print("=" * 80)
    
    if passed == total:
        print("✅ 모든 테스트 통과! 실제 데이터 파싱 성공")
    else:
        print(f"⚠️ {total - passed}개 테스트 실패")


if __name__ == "__main__":
    asyncio.run(main())
