"""
Unified Sports Parser Factory
Provides centralized access to all sport parsers with consistent interface
"""
from typing import Dict, Optional, Any
from sports.base.parsers.base_sports_parser import SportLeagueParserFactory
from utils.logger import Logger

logger = Logger(__name__)


class UnifiedSportsParserFactory:
    """Unified factory for all sports parsers"""

    def __init__(self):
        self.soccer_factory = SportLeagueParserFactory()
        self.basketball_factory = SportLeagueParserFactory()
        self.volleyball_factory = SportLeagueParserFactory()
        self.baseball_factory = SportLeagueParserFactory()

        self._register_parsers()

    def _register_parsers(self):
        """Register all sport parsers"""
        # Soccer parsers
        try:
            from sports.soccer.parsers.k_league_stats_parser import (
                KLeagueStatsParser
            )
            from sports.soccer.parsers.standard_league_stats_parser import (
                StandardLeagueStatsParser
            )

            # K-League
            self.soccer_factory.register_parser('SC001', KLeagueStatsParser)
            self.soccer_factory.register_parser('SC003', KLeagueStatsParser)

            # International leagues
            self.soccer_factory.register_parser('52', StandardLeagueStatsParser)
            self.soccer_factory.register_parser('53', StandardLeagueStatsParser)
            self.soccer_factory.register_parser('54', StandardLeagueStatsParser)
            self.soccer_factory.register_parser('56', StandardLeagueStatsParser)
            self.soccer_factory.register_parser('67', StandardLeagueStatsParser)
            self.soccer_factory.register_parser('2', StandardLeagueStatsParser)
            self.soccer_factory.register_parser('SC007',
                                                StandardLeagueStatsParser)
            self.soccer_factory.register_parser('SC017',
                                                StandardLeagueStatsParser)
            self.soccer_factory.register_parser('SC018',
                                                StandardLeagueStatsParser)

        except ImportError as e:
            logger.warning(f"Failed to register soccer parsers: {e}")

        # Basketball parsers
        try:
            from sports.basketball.parsers.basketball_parser import (
                KBLParser, NBAParser
            )

            self.basketball_factory.register_parser('BK001', KBLParser)
            self.basketball_factory.register_parser('BK003', KBLParser)
            self.basketball_factory.register_parser('BK002', NBAParser)

        except ImportError as e:
            logger.warning(f"Failed to register basketball parsers: {e}")

        # Volleyball parsers
        try:
            from sports.volleyball.parsers.volleyball_parser import KOVOParser

            self.volleyball_factory.register_parser('VL001', KOVOParser)
            self.volleyball_factory.register_parser('VL002', KOVOParser)

        except ImportError as e:
            logger.warning(f"Failed to register volleyball parsers: {e}")

        # Baseball parsers
        try:
            from sports.baseball.parsers.kbo_stats_parser import KBOStatsParser
            from sports.baseball.parsers.mlb_stats_parser import MLBStatsParser
            from sports.baseball.parsers.npb_stats_parser import NPBStatsParser

            self.baseball_factory.register_parser('KBO', KBOStatsParser)
            self.baseball_factory.register_parser('MLB', MLBStatsParser)
            self.baseball_factory.register_parser('NPB', NPBStatsParser)

        except ImportError as e:
            logger.warning(f"Failed to register baseball parsers: {e}")

    def get_parser(self, sport: str, league_id: str, html: str,
                   team_mappings: Optional[Dict[str, str]] = None):
        """Get parser for specific sport and league"""
        sport_lower = sport.lower()

        try:
            if sport_lower == 'soccer':
                return self.soccer_factory.get_parser(league_id, html,
                                                      team_mappings)
            if sport_lower == 'basketball':
                return self.basketball_factory.get_parser(league_id, html,
                                                          team_mappings)
            if sport_lower == 'volleyball':
                return self.volleyball_factory.get_parser(league_id, html,
                                                          team_mappings)
            if sport_lower == 'baseball':
                return self.baseball_factory.get_parser(league_id, html,
                                                        team_mappings)
            raise ValueError(f"Unsupported sport: {sport}")

        except Exception as e:
            logger.error(f"Failed to get parser for {sport}/{league_id}: {e}")
            raise

    def parse_team_data(self, sport: str, league_id: str, html: str,
                        team_name: str,
                        team_mappings: Optional[Dict[str, str]] = None,
                        **kwargs) -> Any:
        """Parse team data using appropriate parser"""
        try:
            parser = self.get_parser(sport, league_id, html, team_mappings)

            # Call the appropriate parsing method based on sport
            if sport.lower() == 'soccer':
                return self._parse_soccer_data(parser, team_name, league_id,
                                               kwargs.get('league', ''))
            if sport.lower() == 'basketball':
                return self._parse_basketball_data(parser, team_name,
                                                   league_id,
                                                   kwargs.get('league', ''))
            if sport.lower() == 'volleyball':
                return self._parse_volleyball_data(parser, team_name,
                                                   league_id,
                                                   kwargs.get('league', ''))
            if sport.lower() == 'baseball':
                return self._parse_baseball_data(parser, team_name, league_id,
                                                 kwargs.get('league', ''))
            raise ValueError(f"Unsupported sport: {sport}")

        except Exception as e:
            logger.error(f"Failed to parse team data for "
                         f"{sport}/{league_id}/{team_name}: {e}")
            raise

    def _parse_soccer_data(self, parser, team_name: str, league_id: str,
                           league: str):
        """Parse soccer-specific data"""
        # Soccer parsers have specific methods
        if hasattr(parser, 'parse_season_summary'):
            season_stats = parser.parse_season_summary()
        else:
            season_stats = {}

        if hasattr(parser, 'parse_recent_games'):
            recent_games = parser.parse_recent_games()
        else:
            recent_games = []

        # Create SoccerData structure
        from sports.soccer.parsers.soccer_parser import SoccerData
        return SoccerData(
            team_name=team_name,
            league=league,
            league_id=league_id,
            profile=parser.parse_team_profile(team_name, {
                'manager': '감독',
                'stadium': '구장',
                'season_record': '시즌성적'
            }) if hasattr(parser, 'parse_team_profile') else {},
            season_stats=season_stats,
            recent_games=recent_games,
            career_stats=season_stats.copy(),
            players=[]
        )

    def _parse_basketball_data(self, parser, team_name: str, league_id: str,
                               league: str):
        """Parse basketball-specific data"""
        season_stats = (parser.parse_season_summary()
                        if hasattr(parser, 'parse_season_summary') else {})
        recent_games = (parser.parse_recent_games()
                        if hasattr(parser, 'parse_recent_games') else [])

        from sports.basketball.parsers.basketball_parser import BasketballData
        return BasketballData(
            team_name=team_name,
            league=league,
            league_id=league_id,
            profile=parser.parse_team_profile(team_name, {
                'coach': '감독',
                'arena': '홈구장',
                'founded': '설립'
            }) if hasattr(parser, 'parse_team_profile') else {},
            season_stats=season_stats,
            recent_games=recent_games,
            career_stats=season_stats.copy(),
            players=[]
        )

    def _parse_volleyball_data(self, parser, team_name: str, league_id: str,
                               league: str):
        """Parse volleyball-specific data"""
        season_stats = (parser.parse_season_summary()
                        if hasattr(parser, 'parse_season_summary') else {})
        recent_games = (parser.parse_recent_games()
                        if hasattr(parser, 'parse_recent_games') else [])

        from sports.volleyball.parsers.volleyball_parser import VolleyballData
        return VolleyballData(
            team_name=team_name,
            league=league,
            league_id=league_id,
            profile=parser.parse_team_profile(team_name, {
                'coach': '감독',
                'home_court': '홈코트',
                'founded': '설립'
            }) if hasattr(parser, 'parse_team_profile') else {},
            season_stats=season_stats,
            recent_games=recent_games,
            career_stats=season_stats.copy(),
            players=[]
        )

    def _parse_baseball_data(self, parser, team_name: str, league_id: str,
                             league: str):
        """Parse baseball-specific data"""
        # Baseball parsers have different method signatures
        season_stats = (parser.parse_season_summary()
                        if hasattr(parser, 'parse_season_summary') else {})
        recent_games = (parser.parse_recent_games()
                        if hasattr(parser, 'parse_recent_games') else {})

        # Baseball has its own data structure
        return {
            'team_name': team_name,
            'league': league,
            'league_id': league_id,
            'season_stats': season_stats,
            'recent_games': recent_games,
            'recent_games_summary': (
                parser.parse_recent_games_summary()
                if hasattr(parser, 'parse_recent_games_summary') else {}),
            'season_history': (parser.parse_season_stats()
                               if hasattr(parser, 'parse_season_stats') else {})
        }

    def get_supported_leagues(self, sport: str) -> Dict[str, str]:
        """Get supported leagues for a sport"""
        sport_lower = sport.lower()

        if sport_lower == 'soccer':
            return {
                'SC001': 'K리그1', 'SC003': 'K리그2',
                '52': 'EPL', '53': '세리에A', '54': '프랑스리그',
                '56': '분데스리가', '67': '프리메라리가',
                '2': '에레디비시', 'SC007': 'J리그',
                'SC017': 'J2리그', 'SC018': 'MLS'
            }
        if sport_lower == 'basketball':
            return {
                'BK001': 'KBL', 'BK003': 'WKBL', 'BK002': 'NBA'
            }
        if sport_lower == 'volleyball':
            return {
                'VL001': 'KOVO남', 'VL002': 'KOVO여'
            }
        if sport_lower == 'baseball':
            return {
                'KBO': 'KBO리그', 'MLB': 'MLB', 'NPB': 'NPB'
            }
        return {}


# Global factory instance
unified_parser_factory = UnifiedSportsParserFactory()


def get_sports_parser(sport: str, league_id: str, html: str,
                      team_mappings: Optional[Dict[str, str]] = None):
    """Convenience function to get parser"""
    return unified_parser_factory.get_parser(sport, league_id, html,
                                             team_mappings)


def parse_sports_team_data(sport: str, league_id: str, html: str,
                           team_name: str,
                           team_mappings: Optional[Dict[str, str]] = None,
                           **kwargs):
    """Convenience function to parse team data"""
    return unified_parser_factory.parse_team_data(sport, league_id, html,
                                                  team_name, team_mappings,
                                                  **kwargs)