"""
Base Sports Parser - Common functionality across all sports
Consolidates duplicate logic from soccer, basketball, volleyball parsers
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from bs4 import BeautifulSoup, Tag

from sports.base.utils.text_parsing import (
    safe_int, safe_float, safe_get_cell_text, clean_text,
    parse_table_row, find_table_by_selectors, parse_team_profile_section,
    extract_season_record
)
from utils.logger import Logger

logger = Logger(__name__)


@dataclass
class SportData:
    """Base sport data structure"""
    team_name: str
    league: str
    league_id: str
    profile: Dict[str, Any]
    season_stats: Dict[str, Any]
    recent_games: List[Dict[str, Any]]
    career_stats: Dict[str, Any]
    players: List[Dict[str, Any]]


class BaseSportsParser(ABC):
    """Base parser class for all sports with common functionality"""
    
    def __init__(self, html: str, team_mappings: Optional[Dict[str, str]] = None):
        self.soup = BeautifulSoup(html, "html.parser")
        self.team_mappings = team_mappings or {}
        self.league_code = self.get_league_code()
    
    @abstractmethod
    def get_league_code(self) -> str:
        """Return league code"""
        pass
    
    @abstractmethod
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """Return season summary column mapping"""
        pass
    
    @abstractmethod
    def get_recent_games_mapping(self) -> Dict[str, int]:
        """Return recent games column mapping"""
        pass
    
    def parse_season_summary(self) -> Dict[str, Optional[Dict[str, str]]]:
        """Parse season summary statistics - common logic"""
        season_stats = {
            "home": None, 
            "away": None, 
            "total": None
        }
        
        mapping = self.get_season_summary_mapping()
        
        # Parse home statistics
        home_dict = self._parse_season_row("home_record", mapping.get("home", {}))
        if home_dict:
            home_dict["league_code"] = self.league_code
            season_stats["home"] = home_dict
        
        # Parse away statistics
        away_dict = self._parse_season_row("away_record", mapping.get("away", {}))
        if away_dict:
            away_dict["league_code"] = self.league_code
            season_stats["away"] = away_dict
        
        # Parse total statistics
        total_dict = self._parse_season_row("all_record", mapping.get("total", {}))
        if total_dict:
            total_dict["league_code"] = self.league_code
            season_stats["total"] = total_dict
        
        return season_stats
    
    def parse_recent_games(self, limit: int = 5) -> List[Dict[str, str]]:
        """Parse recent games - common logic"""
        try:
            games = []
            mapping = self.get_recent_games_mapping()
            
            # Find recent games table
            recent_table = self._find_recent_games_table()
            if not recent_table:
                return games
            
            rows = recent_table.find_all('tr')[1:]  # Skip header
            
            for row in rows[:limit]:
                game_data = parse_table_row(row, mapping)
                if game_data:
                    game_data["league_code"] = self.league_code
                    games.append(game_data)
            
            return games
            
        except Exception as e:
            logger.debug(f"Recent games parsing failed: {e}")
            return []
    
    def _parse_season_row(self, row_id: str, mapping: Dict[str, int]) -> Optional[Dict[str, str]]:
        """Parse season statistics row - common logic"""
        try:
            row = self.soup.find('tr', id=row_id)
            if not row:
                return None
            
            return parse_table_row(row, mapping)
            
        except Exception as e:
            logger.debug(f"Season row parsing failed {row_id}: {e}")
            return None
    
    def _find_recent_games_table(self) -> Optional[Tag]:
        """Find recent games table - common logic"""
        selectors = [
            'table.recent_games',
            'table.game_list',
            'div.recent_games table',
            'div.game_history table',
            'table[id*="recent"]',
            'table[class*="game"]'
        ]
        
        return find_table_by_selectors(self.soup, selectors)
    
    def parse_team_profile(self, team_name: str, profile_mapping: Dict[str, str]) -> Dict[str, Any]:
        """Parse team profile information - common logic"""
        try:
            profile = {'team_name': team_name}
            
            # Extract profile information using mapping
            profile_data = parse_team_profile_section(self.soup, profile_mapping)
            profile.update(profile_data)
            
            return profile
            
        except Exception as e:
            logger.debug(f"Team profile parsing failed {team_name}: {e}")
            return {'team_name': team_name}
    
    def map_team_name_to_full_name(self, team_name: str) -> str:
        """Map team name to full name using team mappings"""
        if not team_name or not self.team_mappings:
            return team_name
        
        return self.team_mappings.get(team_name, team_name)


class SportLeagueParserFactory:
    """Factory for creating sport-specific parsers"""
    
    def __init__(self):
        self.parsers = {}
    
    def register_parser(self, league_id: str, parser_class):
        """Register a parser for a specific league"""
        self.parsers[league_id] = parser_class
    
    def get_parser(self, league_id: str, html: str, team_mappings: Optional[Dict[str, str]] = None):
        """Get parser instance for league"""
        parser_class = self.parsers.get(league_id)
        if not parser_class:
            raise ValueError(f"No parser registered for league: {league_id}")
        
        return parser_class(html, team_mappings)


class BasePlayerParser:
    """Base player parser with common functionality"""
    
    def __init__(self, html: str):
        self.soup = BeautifulSoup(html, "html.parser")
    
    def parse_player_profile(self, player_name: str, profile_fields: Dict[str, str]) -> Dict[str, Any]:
        """Parse player profile information"""
        try:
            profile = {'player_name': player_name}
            
            # Extract player information from list
            info_list = self.soup.find('ul')
            if info_list:
                items = info_list.find_all('li')
                
                for item in items:
                    text = item.get_text(strip=True)
                    
                    for field_name, korean_label in profile_fields.items():
                        if korean_label in text:
                            profile[field_name] = text.replace(korean_label, '').strip()
            
            # Extract number and team
            number_team = self.soup.find('div', class_=lambda x: x and 'player_info' in str(x))
            if number_team:
                strong_tags = number_team.find_all('strong')
                for strong in strong_tags:
                    text = strong.get_text(strip=True)
                    if 'No.' in text:
                        profile['back_number'] = text.replace('No.', '').strip()
                    else:
                        profile['team'] = text
            
            return profile
            
        except Exception as e:
            logger.debug(f"Player profile parsing failed {player_name}: {e}")
            return {'player_name': player_name}
    
    def parse_player_statistics(self, table_caption_text: str) -> Dict[str, Any]:
        """Parse player statistics from tables"""
        try:
            stats = {}
            tables = self.soup.find_all('table')
            
            for table in tables:
                caption = table.find('caption')
                if caption and table_caption_text in caption.get_text():
                    rows = table.find_all('tr')
                    if len(rows) >= 3:
                        # Handle complex header structure (2 rows)
                        header_row1 = rows[0].find_all(['th', 'td'])
                        header_row2 = rows[1].find_all(['th', 'td'])
                        
                        # Combine headers
                        headers = []
                        for i, cell in enumerate(header_row1):
                            header1 = cell.get_text(strip=True)
                            if i < len(header_row2):
                                header2 = header_row2[i].get_text(strip=True)
                                if header2 and header2 != header1:
                                    headers.append(f"{header1}_{header2}")
                                else:
                                    headers.append(header1)
                            else:
                                headers.append(header1)
                        
                        # Parse data rows
                        for row in rows[2:]:
                            data_row = row.find_all(['th', 'td'])
                            if len(data_row) == len(headers):
                                row_data = {}
                                for i, cell in enumerate(data_row):
                                    if i < len(headers):
                                        header = headers[i]
                                        value = cell.get_text(strip=True)
                                        if header and value:
                                            row_data[header] = value
                                if row_data:
                                    if table_caption_text == '역대시즌':
                                        # Career stats - multiple seasons
                                        if 'career_seasons' not in stats:
                                            stats['career_seasons'] = []
                                        stats['career_seasons'].append(row_data)
                                    else:
                                        # Season stats - single row
                                        stats.update(row_data)
                                        break
            
            return stats
            
        except Exception as e:
            logger.debug(f"Player statistics parsing failed: {e}")
            return {}