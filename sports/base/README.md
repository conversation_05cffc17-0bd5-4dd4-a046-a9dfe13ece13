# Sports Base Modules

This directory contains consolidated base classes and utilities for all sports parsers.

## Overview

The sports directory has been consolidated to eliminate code duplication and provide a unified interface for all sports data parsing. This consolidation improves maintainability, reduces bugs, and provides consistent behavior across all sports.

## Directory Structure

```
sports/base/
├── README.md                           # This file
├── parsers/
│   ├── base_sports_parser.py          # Base parser class for all sports
│   └── sports_parser_factory.py       # Unified factory for all parsers
└── utils/
    └── text_parsing.py                # Common text parsing utilities
```

## Key Components

### BaseSportsParser
- **Location**: `sports/base/parsers/base_sports_parser.py`
- **Purpose**: Common functionality across all sports (soccer, basketball, volleyball)
- **Features**:
  - Abstract base class with required methods
  - Common season summary parsing
  - Recent games parsing
  - Team profile parsing
  - Team name mapping

### Text Parsing Utilities
- **Location**: `sports/base/utils/text_parsing.py`
- **Purpose**: Common text processing functions
- **Functions**:
  - `safe_int()`, `safe_float()` - Safe type conversion
  - `safe_get_cell_text()` - Safe HTML cell text extraction
  - `clean_text()` - Text normalization
  - `parse_table_row()` - Table row parsing with column mapping
  - `find_table_by_selectors()` - Table finding with multiple selectors
  - `parse_team_profile_section()` - Team profile information extraction
  - `extract_season_record()` - Win/draw/loss extraction from text

### Sports Parser Factory
- **Location**: `sports/base/parsers/sports_parser_factory.py`
- **Purpose**: Centralized access to all sport parsers
- **Features**:
  - Unified interface for all sports
  - Automatic parser selection based on sport and league
  - Consistent data structure handling
  - Error handling and logging

## Usage

### Basic Usage

```python
from sports.base.parsers.sports_parser_factory import parse_sports_team_data

# Parse soccer team data
soccer_data = parse_sports_team_data(
    sport='soccer',
    league_id='SC001',  # K-League 1
    html=html_content,
    team_name='FC서울',
    team_mappings=team_mappings
)

# Parse basketball team data
basketball_data = parse_sports_team_data(
    sport='basketball',
    league_id='BK001',  # KBL
    html=html_content,
    team_name='서울SK나이츠',
    team_mappings=team_mappings
)
```

### Advanced Usage

```python
from sports.base.parsers.sports_parser_factory import unified_parser_factory

# Get specific parser
parser = unified_parser_factory.get_parser(
    sport='soccer',
    league_id='SC001',
    html=html_content,
    team_mappings=team_mappings
)

# Use parser methods directly
season_stats = parser.parse_season_summary()
recent_games = parser.parse_recent_games(limit=5)
```

## Supported Sports and Leagues

### Soccer
- **K-League**: SC001 (K리그1), SC003 (K리그2)
- **International**: 52 (EPL), 53 (세리에A), 54 (프랑스리그), 56 (분데스리가), 67 (프리메라리가), 2 (에레디비시), SC007 (J리그), SC017 (J2리그), SC018 (MLS)

### Basketball
- **Korean**: BK001 (KBL), BK003 (WKBL)
- **International**: BK002 (NBA)

### Volleyball
- **Korean**: VL001 (KOVO남), VL002 (KOVO여)

### Baseball
- **Leagues**: KBO (KBO리그), MLB (MLB), NPB (NPB)

## Migration Guide

### Before Consolidation
```python
# Old way - duplicated code across sports
from sports.soccer.parsers.soccer_parser import SoccerParser
from sports.basketball.parsers.basketball_parser import BasketballParser

soccer_parser = SoccerParser(team_mappings)
basketball_parser = BasketballParser(team_mappings)

# Different method signatures and behavior
soccer_data = soccer_parser.parse_team_data(html, team_name, league_id=league_id)
basketball_data = basketball_parser.parse_team_data(html, team_name, league_id=league_id)
```

### After Consolidation
```python
# New way - unified interface
from sports.base.parsers.sports_parser_factory import parse_sports_team_data

# Consistent interface across all sports
soccer_data = parse_sports_team_data('soccer', league_id, html, team_name, team_mappings)
basketball_data = parse_sports_team_data('basketball', league_id, html, team_name, team_mappings)
```

## Benefits

1. **Reduced Code Duplication**: Common functionality is shared across all sports
2. **Consistent Interface**: All sports use the same method signatures and data structures
3. **Easier Maintenance**: Changes to common functionality only need to be made in one place
4. **Better Error Handling**: Centralized error handling and logging
5. **Type Safety**: Consistent data types and structures
6. **Extensibility**: Easy to add new sports by implementing the base interfaces

## Testing

The consolidation preserves all existing functionality while providing better organization. All sport-specific parsers continue to work as before, but now inherit common functionality from the base classes.

## Future Enhancements

1. **Additional Sports**: Easy to add new sports by implementing BaseSportsParser
2. **Advanced Analytics**: Common statistical calculations across sports
3. **Data Validation**: Centralized data validation and cleaning
4. **Caching**: Shared caching mechanisms for parsed data
5. **Configuration**: Centralized configuration management for all sports