"""
Common text parsing utilities for all sports
Consolidates duplicated utility methods across different sport parsers
"""
from typing import Union, Optional, Dict, Any


def safe_int(value: Union[str, int, float, None], default: int = 0) -> int:
    """
    Safely convert value to integer
    Handles negative signs, empty strings, and invalid values
    """
    if value is None:
        return default
    
    try:
        if isinstance(value, (int, float)):
            return int(value)
        
        if isinstance(value, str):
            # Handle empty or whitespace-only strings
            clean_value = value.strip()
            if not clean_value or clean_value in ['-', '.', 'N/A', '']:
                return default
            
            # Handle negative values with parentheses (accounting format)
            is_negative = clean_value.startswith('(') and clean_value.endswith(')')
            if is_negative:
                clean_value = clean_value[1:-1]
            
            # Remove commas and other formatting
            clean_value = clean_value.replace(',', '').replace('+', '')
            
            if not clean_value:
                return default
            
            result = int(float(clean_value))
            return -result if is_negative else result
            
    except (ValueError, TypeError, AttributeError):
        pass
    
    return default


def safe_float(value: Union[str, int, float, None], default: float = 0.0) -> float:
    """
    Safely convert value to float
    Handles negative signs, empty strings, and invalid values
    """
    if value is None:
        return default
    
    try:
        if isinstance(value, (int, float)):
            return float(value)
        
        if isinstance(value, str):
            # Handle empty or whitespace-only strings
            clean_value = value.strip()
            if not clean_value or clean_value in ['-', '.', 'N/A', '']:
                return default
            
            # Handle negative values with parentheses (accounting format)
            is_negative = clean_value.startswith('(') and clean_value.endswith(')')
            if is_negative:
                clean_value = clean_value[1:-1]
            
            # Remove commas and other formatting
            clean_value = clean_value.replace(',', '').replace('+', '')
            
            if not clean_value:
                return default
            
            result = float(clean_value)
            return -result if is_negative else result
            
    except (ValueError, TypeError, AttributeError):
        pass
    
    return default


def safe_get_cell_text(cells: list, index: int, default: str = "") -> str:
    """
    Safely get text from cell at given index
    """
    try:
        if index < len(cells) and cells[index]:
            cell = cells[index]
            if hasattr(cell, 'get_text'):
                return cell.get_text(strip=True)
            elif hasattr(cell, 'text'):
                return cell.text.strip()
            else:
                return str(cell).strip()
    except (IndexError, AttributeError, TypeError):
        pass
    
    return default


def clean_text(text: Union[str, None]) -> str:
    """
    Clean and normalize text content
    """
    if not text:
        return ""
    
    # Convert to string and strip whitespace
    clean = str(text).strip()
    
    # Remove extra whitespace
    clean = ' '.join(clean.split())
    
    # Remove common unwanted characters
    clean = clean.replace('\n', ' ').replace('\t', ' ').replace('\r', ' ')
    
    return clean


def extract_number_from_text(text: str) -> Optional[float]:
    """
    Extract first number found in text
    """
    import re
    
    if not text:
        return None
    
    # Look for numbers (including decimals)
    match = re.search(r'-?\d+\.?\d*', text)
    if match:
        try:
            return float(match.group())
        except ValueError:
            pass
    
    return None


def parse_table_row(row, column_mapping: Dict[str, int], default: str = "") -> Dict[str, str]:
    """
    Parse a table row using column mapping
    Common pattern across all sport parsers
    """
    if not row or not column_mapping:
        return {}
    
    cells = row.find_all(['td', 'th'])
    if not cells:
        return {}
    
    result = {}
    for field_name, column_index in column_mapping.items():
        if column_index < len(cells):
            value = safe_get_cell_text(cells, column_index, default)
            if value and value != default:
                result[field_name] = value
    
    return result


def find_table_by_selectors(soup, selectors: list) -> Optional[Any]:
    """
    Find table using multiple CSS selectors
    Common pattern across all sport parsers
    """
    for selector in selectors:
        table = soup.select_one(selector)
        if table:
            return table
    return None


def parse_team_profile_section(soup, profile_fields: Dict[str, str]) -> Dict[str, str]:
    """
    Parse team profile section with field mappings
    Common pattern for extracting manager, stadium, etc.
    """
    profile = {}
    
    # Look for profile section
    profile_section = soup.find('div', class_='team_profile')
    if not profile_section:
        # Try alternative selectors
        profile_section = soup.find('div', class_='profile') or soup.find('section', class_='team-info')
    
    if profile_section:
        for field_name, korean_label in profile_fields.items():
            elem = profile_section.find('span', string=korean_label)
            if elem and elem.next_sibling:
                profile[field_name] = clean_text(elem.next_sibling)
    
    return profile


def extract_season_record(text: str) -> Dict[str, int]:
    """
    Extract wins, draws, losses from season record text
    """
    import re
    
    result = {'wins': 0, 'draws': 0, 'losses': 0}
    
    if not text:
        return result
    
    # Pattern: "5승8무7패" or "5W8D7L"
    wins_match = re.search(r'(\d+)[승W]', text)
    draws_match = re.search(r'(\d+)[무D]', text)
    losses_match = re.search(r'(\d+)[패L]', text)
    
    if wins_match:
        result['wins'] = safe_int(wins_match.group(1))
    if draws_match:
        result['draws'] = safe_int(draws_match.group(1))
    if losses_match:
        result['losses'] = safe_int(losses_match.group(1))
    
    return result