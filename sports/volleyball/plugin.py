"""
배구 플러그인 - 기존 시스템과의 통합 인터페이스
다른 스포츠 플러그인 패턴을 배구에 적용
"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from sports.volleyball.collectors.volleyball_collector import VolleyballCollector
from sports.volleyball.parsers.volleyball_parser import VolleyballParser
from sports.volleyball.services.volleyball_service import VolleyballService
from core.sport_config import Sport, get_sport_config
from utils.logger import Logger

logger = Logger(__name__)


@dataclass
class VolleyballPlugin:
    """배구 플러그인 - 기존 UnifiedDataCollector 인터페이스 호환"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        self.collector = VolleyballCollector(team_mappings)
        self.parser = VolleyballParser(team_mappings)
        self.service = VolleyballService(team_mappings)
        self.sport_config = get_sport_config(Sport.VOLLEYBALL)
        self.team_mappings = team_mappings or {}
    
    async def collect_data(self, **kwargs) -> List[Dict]:
        """데이터 수집 진입점 - UnifiedDataCollector 호환"""
        logger.info("🚀 배구 데이터 수집 시작")
        
        try:
            # 타겟 게임 조회
            target_games = await self._get_target_games()
            
            if not target_games:
                logger.info("📭 수집할 배구 경기가 없습니다")
                return []
            
            # 스케줄 수집
            crawled_games = await self.collector.collect_schedule(target_games)
            
            if not crawled_games:
                logger.info("📭 매칭된 배구 경기가 없습니다")
                return []
            
            # 팀/선수 통계 수집 및 저장
            processed_games = await self._process_games_parallel(crawled_games)
            
            logger.info(f"✅ 배구 데이터 수집 완료: {len(processed_games)}개")
            return processed_games
            
        except Exception as e:
            logger.error(f"❌ 배구 데이터 수집 실패: {e}")
            return []
    
    async def collect_games(self) -> List[Dict]:
        """경기 데이터만 수집 - UnifiedDataCollector 호환"""
        try:
            target_games = await self._get_target_games()
            if not target_games:
                return []
            
            return await self.collector.collect_schedule(target_games)
            
        except Exception as e:
            logger.error(f"❌ 배구 경기 수집 실패: {e}")
            return []
    
    async def save_team_stats(self, games: List[Dict]) -> int:
        """팀 통계 저장 - UnifiedDataCollector 호환"""
        return await self.service.save_multiple_team_stats(games)
    
    async def save_player_stats(self, games: List[Dict]) -> List[Dict]:
        """선수 통계 저장 - UnifiedDataCollector 호환"""
        return await self.service.save_multiple_player_stats(games)
    
    async def _get_target_games(self) -> List[Dict]:
        """타겟 게임 조회 - 배구 리그만 필터링"""
        try:
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                return []
            
            # 배구 리그 ID 목록
            volleyball_league_ids = [league.id for league in self.sport_config.leagues]
            
            # target_games 테이블에서 배구 경기만 조회
            response = client.table('target_games').select('*').in_(
                'league_id', volleyball_league_ids
            ).execute()
            
            if not response.data:
                return []
            
            logger.info(f"📋 배구 타겟 경기: {len(response.data)}개")
            return response.data
            
        except Exception as e:
            logger.error(f"❌ 타겟 게임 조회 실패: {e}")
            return []
    
    async def _process_games_parallel(self, games: List[Dict]) -> List[Dict]:
        """게임 데이터 병렬 처리 - 팀/선수 통계 저장"""
        if not games:
            return []
        
        try:
            import asyncio
            
            # 팀 통계와 선수 통계를 병렬로 처리
            team_task = asyncio.create_task(
                self.service.collect_and_save_team_stats(games)
            )
            player_task = asyncio.create_task(
                self.service.collect_and_save_player_stats(games)
            )
            
            # 병렬 실행
            team_saved, player_results = await asyncio.gather(
                team_task, player_task, return_exceptions=True
            )
            
            # 결과 처리
            if isinstance(team_saved, Exception):
                logger.error(f"배구 팀 저장 실패: {team_saved}")
                team_count = 0
            else:
                team_count = team_saved
            
            if isinstance(player_results, Exception):
                logger.error(f"배구 선수 저장 실패: {player_results}")
                player_count = 0
            else:
                player_count = len(player_results) if player_results else 0
            
            logger.info(f"📊 배구 통계 저장: 팀 {team_count}개, 선수 {player_count}명")
            
            # 처리된 게임 정보에 통계 추가
            for game in games:
                game['team_stats_saved'] = team_count > 0
                game['player_stats_saved'] = player_count > 0
                game['sport'] = 'volleyball'
            
            return games
            
        except Exception as e:
            logger.error(f"❌ 배구 게임 병렬 처리 실패: {e}")
            return []


# 기존 시스템과의 호환성을 위한 별칭
class VolleyballDataCollector(VolleyballPlugin):
    """VolleyballPlugin의 별칭 - 명명 일관성"""
    pass


# 편의 함수들
async def collect_volleyball_data(**kwargs) -> List[Dict]:
    """배구 데이터 수집 편의 함수"""
    plugin = VolleyballPlugin()
    return await plugin.collect_data(**kwargs)


async def collect_volleyball_games() -> List[Dict]:
    """배구 경기 수집 편의 함수"""
    plugin = VolleyballPlugin()
    return await plugin.collect_games()


async def save_volleyball_team_stats(games: List[Dict]) -> int:
    """배구 팀 통계 저장 편의 함수"""
    plugin = VolleyballPlugin()
    return await plugin.save_team_stats(games)


async def save_volleyball_player_stats(games: List[Dict]) -> List[Dict]:
    """배구 선수 통계 저장 편의 함수"""
    plugin = VolleyballPlugin()
    return await plugin.save_player_stats(games)


# 팩토리 함수
def create_volleyball_collector(team_mappings: Optional[Dict[str, str]] = None) -> VolleyballPlugin:
    """배구 수집기 생성 팩토리"""
    return VolleyballPlugin(team_mappings)


def get_volleyball_leagues() -> List[str]:
    """배구 리그 목록 반환"""
    config = get_sport_config(Sport.VOLLEYBALL)
    return [league.id for league in config.leagues]


def get_volleyball_league_names() -> Dict[str, str]:
    """배구 리그 ID -> 이름 매핑"""
    config = get_sport_config(Sport.VOLLEYBALL)
    return {league.id: league.name for league in config.leagues}
