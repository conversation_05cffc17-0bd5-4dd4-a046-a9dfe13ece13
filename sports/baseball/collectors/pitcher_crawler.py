# flake8: noqa
"""
투수 크롤러 - 브라우저 관리 및 페이지 크롤링 전담 (기존 검증된 로직 적용)
"""
import asyncio
import json
import logging
import os
import time
from typing import Dict, Optional

import psutil
from playwright.async_api import Page, async_playwright

from core.utils.browser_utils import ensure_browser_ready
from utils.logger import Logger

# 로거 설정
logger = Logger(__name__, level=logging.ERROR)


class PitcherCrawler:
    """투수 페이지 크롤링 전담 클래스 (검증된 로직)"""
    
    def __init__(self):
        self.browser = None
        self.playwright = None
        self.team_mappings = {}
        self.pitcher_config = self._load_pitcher_config()
    
    async def initialize_browser(self):
        """브라우저 초기화"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=True)
    
    def _get_memory_usage(self) -> float:
        """현재 메모리 사용량 반환 (MB)"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def _log_browser_state(self, context: str):
        """브라우저 상태 로깅"""
        browser_state = "None" if self.browser is None else "Active"
        playwright_state = "None" if self.playwright is None else "Active"
        memory_mb = self._get_memory_usage()
        
        logger.debug(f"🔍 [{context}] 브라우저 상태: Browser={browser_state}, Playwright={playwright_state}, Memory={memory_mb:.1f}MB")
    
    async def cleanup(self):
        """브라우저 종료 - 메모리 누수 방지"""
        self._log_browser_state("CLEANUP_START")
        errors = []
        
        try:
            if self.browser:
                try:
                    await self.browser.close()
                except Exception as e:
                    errors.append(f"브라우저 종료 실패: {e}")
                finally:
                    self.browser = None
            
            if self.playwright:
                try:
                    await self.playwright.stop()
                except Exception as e:
                    errors.append(f"Playwright 종료 실패: {e}")
                finally:
                    self.playwright = None
            
            if errors:
                logger.warning(f"브라우저 정리 중 일부 오류 발생: {errors}")
                
        except Exception as e:
            logger.error(f"브라우저 정리 중 예상치 못한 오류: {e}")
            # 강제 정리
            self.browser = None
            self.playwright = None
        
        self._log_browser_state("CLEANUP_END")
    
    def _log_detailed_error(self, error_type: str, pitcher_name: str, error: Exception):
        """상세한 오류 정보 수집 및 로깅"""
        import traceback
        
        error_info = {
            'error_type': error_type,
            'pitcher_name': pitcher_name,
            'error_message': str(error),
            'error_class': type(error).__name__,
            'memory_mb': self._get_memory_usage(),
            'browser_state': 'None' if self.browser is None else 'Active',
            'playwright_state': 'None' if self.playwright is None else 'Active',
            'traceback': traceback.format_exc()
        }
        
        logger.error(f"💥 ERROR_DETAIL [{error_type}] {pitcher_name}: {error_info}")
    
    def _load_pitcher_config(self) -> Dict:
        """pitcher.json 설정 파일 로드"""
        try:
            config_path = os.path.join(
                os.path.dirname(__file__), 
                '../../../core/config/resources/pitcher.json'
            )
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.debug(f"pitcher.json 로드 실패: {e}")
        return {"manual_pitchers": {}, "direct_urls": {"enabled": True, "urls": []}}
    
    def set_team_mappings(self, team_mappings: Dict[str, str]):
        """팀 매핑 설정"""
        self.team_mappings = team_mappings
    
    async def crawl_pitcher_stats(
        self,
        pitcher_name: str,
        team_url: str
    ) -> Optional[Dict]:
        """투수 통계 크롤링 - 브라우저 초기화 상태 확인 포함"""
        if not pitcher_name:
            return None
        
        
        # 🔧 브라우저 초기화 상태 확인 및 재시도
        self._log_browser_state(f"BEFORE_ENSURE_READY_{pitcher_name}")
        if not await ensure_browser_ready(self):
            logger.warning(f"⚠️ {pitcher_name} 브라우저 초기화 실패, 재시도...")
            self._log_browser_state(f"ENSURE_READY_FAILED_{pitcher_name}")
            
            # 재초기화 시도
            try:
                await self.cleanup()  # 기존 브라우저 정리
                await asyncio.sleep(2)  # 대기
                await self.initialize_browser()  # 재초기화
                await asyncio.sleep(3)  # 안정화 대기
                
                if not self.browser:
                    logger.error(f"❌ {pitcher_name} 브라우저 재초기화 실패")
                    self._log_browser_state(f"REINIT_FAILED_{pitcher_name}")
                    return self._create_no_data_stats(pitcher_name)
                else:
                    self._log_browser_state(f"REINIT_SUCCESS_{pitcher_name}")
                    
            except Exception as e:
                logger.error(f"❌ {pitcher_name} 브라우저 재초기화 실패: {e}")
                self._log_browser_state(f"REINIT_EXCEPTION_{pitcher_name}")
                self._log_detailed_error("BROWSER_REINIT", pitcher_name, e)
                return self._create_no_data_stats(pitcher_name)
        
        # 🎯 1단계: pitcher.json에서 직접 URL 확인 (team_url 불필요)
        manual_pitchers = self.pitcher_config.get("manual_pitchers", {})
        
        if pitcher_name in manual_pitchers:
            pitcher_config = manual_pitchers[pitcher_name]
            direct_url = pitcher_config.get("direct_url", "")
            
            if direct_url and pitcher_config.get("active", True):
                logger.info(f"🎯 pitcher.json에서 {pitcher_name} 설정 발견")
                
                # 🔍 playerId가 FIND_ID인 경우, 실제 ID를 찾아야 함
                if "playerId=FIND_ID" in direct_url:
                    actual_player_id = await self._find_player_id_from_team_page(pitcher_name, team_url)
                    if actual_player_id:
                        direct_url = direct_url.replace("FIND_ID", actual_player_id)
                    else:
                        logger.warning(f"❌ {pitcher_name} playerId 찾기 실패 - 팀 URL 방식으로 fallback")
                        # playerId를 찾지 못했으면 일반 팀 URL 방식으로 진행
                        
                if "playerId=FIND_ID" not in direct_url:  # playerId가 있는 경우에만 직접 URL 시도
                    try:
                        stats = await self._crawl_pitcher_stats_direct_url(pitcher_name, direct_url)
                        if stats:
                            return stats
                    except Exception as e:
                        logger.debug(f"pitcher.json 직접 URL 크롤링 실패 - {pitcher_name}: {e}")
        else:
            pass  # No pitcher.json entry found, proceed to team URL method
        
        # 🚀 2단계: 팀 URL 기반 크롤링 (team_url 필요)
        if not team_url:
            logger.debug(f"🔄 {pitcher_name} team_url 없음 - 스케줄 기반 크롤링 생략")
            return self._create_no_data_stats(pitcher_name)
        
        # 🚀 스케줄 기반 크롤링 (fallback)
        logger.info(f"🔄 {pitcher_name} 스케줄 기반 크롤링으로 fallback")
        try:
            stats = await self._crawl_pitcher_stats(pitcher_name, team_url)
            if stats:
                # 성공적으로 데이터를 가져왔는지 확인
                season_stats = stats.get('season_stats', [])
                if season_stats and season_stats != [{'games': 'No data'}]:
                    logger.info(f"✅ {pitcher_name} 스케줄 기반 크롤링 성공")
                    
                    # 🚀 성공적으로 크롤링된 투수 정보를 pitcher.json에 자동 추가
                    if hasattr(self, '_last_page_url'):
                        await self._auto_add_to_pitcher_config(pitcher_name, self._last_page_url)
                    else:
                        logger.debug(f"❌ {pitcher_name} URL 정보 없어 pitcher.json 자동 추가 불가")
                    
                    return stats
                else:
                    logger.warning(f"⚠️ {pitcher_name} 스케줄 기반 크롤링 - 데이터 없음 (pitcher.json 추가 권장)")
                    return stats  # 'No data' 응답도 반환 (완전 실패가 아님)
            else:
                pass  # Failed to get valid stats
        except Exception as e:
            logger.error(f"❌ {pitcher_name} 투수 통계 수집 실패: {e}")
        
        logger.warning(f"🚫 {pitcher_name} 모든 수집 방법 실패 - pitcher.json 설정 필요")
        return None
    
    async def _crawl_pitcher_stats_direct_url(
        self,
        pitcher_name: str,
        direct_url: str
    ) -> Optional[Dict]:
        """직접 URL로 투수 통계 크롤링"""
        if not self.browser:
            return PitcherCrawler._create_no_data_stats(pitcher_name)
        
        page = None
        t0 = time.time()
        try:
            page = await self.browser.new_page()
            logger.info(f"🌐 {pitcher_name} 직접 URL: {direct_url}")
            
            # 직접 투수 페이지로 이동 (최근 등록 투수용 타임아웃 증가)
            await page.goto(direct_url, timeout=30000)  # 20s → 30s
            
            # 더 안정적인 대기 조건 추가 (최근 등록 투수용)
            try:
                # 1. 기본 infoBox 대기 (시간 증가)
                await page.wait_for_selector('.infoBox', timeout=20000)  # 10s → 20s
                
                # 2. 테이블 데이터가 로드될 때까지 추가 대기 (시간 증가)
                await page.wait_for_selector('table.tbl', timeout=15000)  # 5s → 15s
                
                # 3. 네트워크가 안정될 때까지 대기 (시간 증가)
                await page.wait_for_load_state('networkidle', timeout=15000)  # 5s → 15s
                
                # 4. 최근 등록 투수용 추가 안정화 대기
                await asyncio.sleep(3)  # 추가 안정화
                
            except Exception as wait_error:
                logger.debug(f"{pitcher_name} 요소 대기 중 일부 실패: {wait_error} - 계속 진행")
                # 기본 대기만으로도 진행 (시간 증가)
                await asyncio.sleep(5)  # 2s → 5s
            
            logger.info(f"{pitcher_name} 직접 페이지 로드 완료 - {time.time() - t0:.2f}s")
            
            # 통계 파싱
            for retry in range(3):
                try:
                    stats = await self._parse_pitcher_page(page, pitcher_name)
                    if stats and isinstance(stats, dict) and stats.get('name'):
                        logger.info(f"✅ {pitcher_name} 직접 URL 파싱 성공 - {time.time() - t0:.2f}s")
                        return stats
                    else:
                        raise ValueError('파싱 결과 이상 또는 name 없음')
                except Exception as e:
                    if retry == 2:
                        logger.warning(f"❌ {pitcher_name} 직접 URL 파싱 최종 실패: {e}")
                        return PitcherCrawler._create_no_data_stats(pitcher_name)
                    else:
                        logger.debug(f"⚠️ {pitcher_name} 직접 URL 파싱 재시도 {retry + 1}/3: {e}")
                        await asyncio.sleep(1)
                        
        except Exception as e:
            logger.debug(f"직접 URL 크롤링 오류 - {pitcher_name}: {e}")
            self._log_detailed_error("DIRECT_URL_CRAWL", pitcher_name, e)
            return PitcherCrawler._create_no_data_stats(pitcher_name)
        finally:
            if page:
                try:
                    await page.close()
                except Exception:
                    pass
    
    async def _crawl_pitcher_stats(
        self,
        pitcher_name: str,
        team_url: str
    ) -> Optional[Dict]:
        
        if not self.browser:
            self._log_browser_state(f"NO_BROWSER_{pitcher_name}")
            self._log_detailed_error("NO_BROWSER", pitcher_name, "Browser is None at start of _crawl_pitcher_stats")
            return PitcherCrawler._create_no_data_stats(pitcher_name)
            
        page = None
        t0 = time.time()
        try:
            page = await self.browser.new_page()
            
            # 페이지 로드 시도
            logger.info(f"🌐 {pitcher_name} 팀 페이지 URL: {team_url}")
            
            load_success = await PitcherCrawler._load_team_page(
                page, team_url, pitcher_name
            )
            if not load_success:
                logger.info(f"{pitcher_name} 팀페이지 로드 실패 - {time.time() - t0:.2f}s")
                return PitcherCrawler._create_no_data_stats(pitcher_name)
            
            
            # 투수 링크 찾기 및 클릭
            logger.info(f"🔍 {pitcher_name} 투수 링크 찾기 시작")
            
            click_success = await PitcherCrawler._find_and_click_pitcher_link(
                page, pitcher_name
            )
            
            if not click_success:
                logger.info(f"{pitcher_name} 투수링크 없음 - {time.time() - t0:.2f}s")
                return PitcherCrawler._create_no_data_stats(pitcher_name)
            
            
            # 클릭 후 현재 URL 확인 및 저장
            current_url = page.url
            self._last_page_url = current_url  # URL 저장
            logger.info(f"🔗 {pitcher_name} 클릭 후 URL: {current_url}")
            
            # 투수 페이지 로드 대기
            await PitcherCrawler._wait_for_pitcher_page_load(
                page, pitcher_name
            )
            # 통계 파싱 (파싱 실패도 3회 재시도)
            for retry in range(3):
                try:
                    stats = await self._parse_pitcher_page(page, pitcher_name)
                    
                    if stats and isinstance(stats, dict) and stats.get('name'):
                        logger.info(f"{pitcher_name} 전체 소요: {time.time() - t0:.2f}s")
                        return stats
                    else:
                        raise ValueError('파싱 결과 이상 또는 name 없음')
                except Exception as e:
                    if retry == 2:
                        logger.warning(
                            f"❌ {pitcher_name} 파싱 최종 실패 (3회 시도): {e}"
                        )
                        return PitcherCrawler._create_no_data_stats(
                            pitcher_name
                        )
                    else:
                        logger.debug(
                            f"⚠️ {pitcher_name} 파싱 재시도 {retry + 1}/3: {e}"
                        )
                        await asyncio.sleep(1)
        except Exception as e:
            logger.debug(f"크롤링 오류 - {pitcher_name}: {e}")
            self._log_browser_state(f"CRAWL_EXCEPTION_{pitcher_name}")
            self._log_detailed_error("CRAWLING", pitcher_name, e)
            return PitcherCrawler._create_no_data_stats(pitcher_name)
        finally:
            if page:
                try:
                    await page.close()
                except Exception:
                    pass

    @staticmethod
    async def _load_team_page(
        page: Page, team_url: str, pitcher_name: str
    ) -> bool:
        t0 = time.time()
        
        for retry in range(3):  # 3 retries
            try:
                await page.goto(team_url, timeout=30000)  # 20s → 30s (최근 등록 투수용)
                
                # 더 긴 대기 시간으로 테이블 로딩 대기
                await page.wait_for_selector("table.tbl", timeout=20000)  # 12s → 20s
                
                # 추가 안정화 대기 (최근 등록 투수의 경우 DOM 업데이트가 늦을 수 있음)
                await asyncio.sleep(3)
                
                logger.info(f"{pitcher_name} 팀페이지 로드 소요: {time.time() - t0:.2f}s")
                return True
            except Exception as e:
                if retry == 2:  # Final attempt
                    logger.warning(f"❌ {pitcher_name} 팀페이지 로드 최종 실패 (3회 시도): {e}")
                    return False
                else:
                    logger.debug(f"⚠️ {pitcher_name} 재시도 {retry + 1}/3: {e}")
                    wait_time = 5 * (retry + 1)  # 5s, 10s progressive delay
                    await asyncio.sleep(wait_time)  
        return False
    
    async def _detect_league_from_page(self, page: Page) -> str:
        """페이지 URL이나 내용을 기반으로 리그 감지"""
        try:
            url = page.url
            
            # URL에서 리그 ID 추출
            if 'leagueId=BS001' in url:
                return 'KBO'
            elif 'leagueId=BS002' in url:
                return 'MLB'
            elif 'leagueId=BS004' in url:
                return 'NPB'
            
            # 페이지 내용에서 리그 탭 확인
            content = await page.content()
            if 'NPB' in content and ('주니치' in content or '야쿠르트' in content or '히로시마' in content):
                return 'NPB'
            elif 'KBO' in content:
                return 'KBO'  
            elif 'MLB' in content:
                return 'MLB'
            
            # 기본값
            return 'KBO'
            
        except Exception:
            return 'KBO'  # 기본값
    
    @staticmethod
    async def _wait_for_pitcher_page_load(
        page: Page, pitcher_name: str
    ) -> None:
        t0 = time.time()
        
        try:
            # 네트워크 안정 대기시간 대폭 확대 (최근 등록 투수용)
            await page.wait_for_load_state('networkidle', timeout=30000)  # 18s → 30s
            
            # 핵심 요소(infoBox) 렌더링 대기 시간 증가
            await page.wait_for_selector('.infoBox', timeout=25000)  # 15s → 25s
            
            # 최근 등록 투수의 경우 데이터 로딩이 늦을 수 있으므로 추가 대기
            await asyncio.sleep(5)  # 추가 안정화 대기
            
            logger.info(
                f"{pitcher_name} 상세페이지 로드 소요: {time.time() - t0:.2f}s"
            )
        except Exception as e:
            logger.debug(f"페이지 로드 대기 중 오류: {pitcher_name} - {e}")
            # 실패 시에도 충분한 대기 시간 제공
            await asyncio.sleep(10)  # 6s → 10s longer failure recovery delay
    
    @staticmethod
    def _create_no_data_stats(pitcher_name: str) -> Dict:
        """No data 케이스를 위한 기본 통계 구조"""
        return {
            'name': pitcher_name,
            'team': '',
            'birth': '',
            'height': 0,
            'weight': '',
            'season_stats': [{"games": "No data"}],
            'recent_games': {}
        }

    async def _parse_pitcher_page(self, page: Page, pitcher_name: str) -> Dict:
        t0 = time.time()
        max_retries = 2
        
        for retry in range(max_retries):
            try:
                # 네트워크와 DOM이 완전히 로드될 때까지 추가 대기
                if retry > 0:
                    logger.debug(f"{pitcher_name} 파싱 재시도 {retry + 1}/{max_retries}")
                    await asyncio.sleep(4)  # 재시도 전 대기
                    await page.wait_for_load_state('networkidle', timeout=10000)
                
                html = await page.content()
                
                # 디버깅: HTML 길이 확인
                logger.debug(f"{pitcher_name} HTML 길이: {len(html)} 문자")
                
                # HTML이 완전히 비어있으면 재시도 (임계값을 1000자에서 100자로 낮춤)
                if len(html) < 100:  # 매우 작은 HTML만 재시도
                    if retry < max_retries - 1:
                        logger.warning(f"⚠️ {pitcher_name} HTML 너무 짧음 ({len(html)}자) - 재시도 예정")
                        continue
                    else:
                        logger.warning(f"❌ {pitcher_name} HTML 로딩 실패 - 최종 포기")
                        return PitcherCrawler._create_no_data_stats(pitcher_name)
                
                # HTML 내용 디버깅 (일부만 로깅)
                if 'infoBox' in html:
                    logger.debug(f"✅ {pitcher_name} infoBox 요소 존재")
                else:
                    logger.warning(f"⚠️ {pitcher_name} infoBox 요소 없음 - HTML 구조 다름")
                
                if 'table' in html:
                    logger.debug(f"✅ {pitcher_name} table 요소 존재")
                else:
                    logger.warning(f"⚠️ {pitcher_name} table 요소 없음")
                
                # 현재 페이지의 리그 감지
                league = await self._detect_league_from_page(page)
                
                # 🎯 betman.co.kr 데이터는 리그에 관계없이 일반 파서 사용
                url = page.url
                if 'betman.co.kr' in url:
                    # betman.co.kr HTML 구조는 리그에 관계없이 동일함
                    from sports.baseball.parsers.pitcher_parser import \
                        PitcherDataParser
                    parser = PitcherDataParser(team_mappings=self.team_mappings)
                    result = parser.parse_pitcher_data(html, pitcher_name)
                    logger.debug(f"{pitcher_name} betman.co.kr 일반 파서 사용 (리그: {league})")
                else:
                    from sports.baseball.parsers.pitcher_parser import \
                        PitcherDataParser
                    parser = PitcherDataParser(team_mappings=self.team_mappings)
                    result = parser.parse_pitcher_data(html, pitcher_name)
                    logger.debug(f"{pitcher_name} 일반 파서 사용 (리그: {league})")
                
                # 파싱 결과가 비어있는지 확인
                if self._is_empty_parsed_data(result):
                    if retry < max_retries - 1:
                        logger.warning(f"⚠️ {pitcher_name} 파싱 결과 비어있음 - 재시도 예정")
                        continue
                    else:
                        logger.warning(f"❌ {pitcher_name} 파싱 최종 실패 - 비어있는 데이터")
                        return PitcherCrawler._create_no_data_stats(pitcher_name)
                else:
                    logger.debug(f"✅ {pitcher_name} 파싱 성공: {result.get('season_stats', [])[:1]}")
                
                logger.info(f"{pitcher_name} ({league}) 파싱 완료 - {time.time() - t0:.2f}s")
                return result
                
            except Exception as e:
                if retry < max_retries - 1:
                    logger.debug(f"통계 파싱 오류 - {pitcher_name} (재시도 {retry + 1}/{max_retries}): {e}")
                    continue
                else:
                    logger.debug(f"통계 파싱 최종 실패 - {pitcher_name}: {e}")
                    return PitcherCrawler._create_no_data_stats(pitcher_name)
        
        return PitcherCrawler._create_no_data_stats(pitcher_name)
    
    @staticmethod
    def _is_empty_parsed_data(stats: Dict) -> bool:
        """파싱 결과가 비어있는 데이터인지 확인 (매우 관대한 버전)"""
        if not isinstance(stats, dict):
            return True
        
        # 이름이 있으면 기본적으로 성공으로 간주
        if stats.get('name'):
            logger.debug(f"이름 존재: '{stats.get('name')}' - 성공으로 판단")
            return False
        
        # 팀 정보가 있으면 성공
        if stats.get('team'):
            logger.debug(f"팀 정보 존재: '{stats.get('team')}' - 성공으로 판단")
            return False
        
        # 시즌 통계가 있으면 성공 (매우 관대하게)
        season_stats = stats.get('season_stats', [])
        if season_stats:
            logger.debug(f"시즌 통계 배열 존재 - 성공으로 판단")
            return False
        
        # 최근 경기 데이터가 있으면 성공
        recent_games = stats.get('recent_games', {})
        if recent_games:
            logger.debug(f"최근 경기 데이터 존재 - 성공으로 판단")
            return False
        
        # 신체 정보라도 있으면 성공
        if stats.get('height') and stats.get('height') != 0:
            logger.debug(f"신장 정보 존재: {stats.get('height')} - 성공으로 판단")
            return False
        
        if stats.get('birth'):
            logger.debug(f"생년월일 정보 존재: {stats.get('birth')} - 성공으로 판단") 
            return False
        
        # 선수 번호라도 있으면 성공
        if stats.get('no'):
            logger.debug(f"선수 번호 존재: {stats.get('no')} - 성공으로 판단")
            return False
        
        # 정말 아무것도 없을 때만 실패
        logger.warning(f"정말 아무 데이터도 없음 - 파싱 실패로 판단")
        return True


    @staticmethod
    async def _find_and_click_pitcher_link(
        page: 'Page', pitcher_name: str
    ) -> bool:
        """투수 링크 찾기 및 클릭"""
        pitcher_link = await PitcherCrawler._find_pitcher_link(
            page, pitcher_name
        )
        if not pitcher_link:
            logger.debug(f"투수 링크 찾기 실패: {pitcher_name}")
            
            # 🔍 디버깅: 페이지의 모든 선수 링크 출력
            await PitcherCrawler._debug_all_player_links(page, pitcher_name)
            return False
        try:
            await pitcher_link.click()
            return True
        except Exception as e:
            logger.debug(f"투수 링크 클릭 실패: {pitcher_name} - {e}")
            return False
    
    @staticmethod
    async def _debug_all_player_links(page: 'Page', pitcher_name: str):
        """디버깅용: 페이지의 모든 선수 링크 출력"""
        try:
            # 🔍 페이지 기본 정보 먼저 확인
            url = page.url
            title = await page.title()
            
            # 🔍 다양한 링크 셀렉터 시도
            selectors_to_try = [
                'a[href*="playerId="]',
                'a[href*="bsPlayerDetail"]', 
                'a[href*="player"]',
                'a',  # 모든 링크
            ]
            
            for selector in selectors_to_try:
                try:
                    links = await page.query_selector_all(selector)
                    
                    if selector == 'a[href*="playerId="]' and len(links) == 0:
                        # 주요 셀렉터에서 0개면 다른 셀렉터들도 확인
                        continue
                    elif len(links) > 0:
                        # 첫 번째 유효한 셀렉터의 링크들 출력
                        link_info = []
                        for link in links[:20]:  # 최대 20개
                            try:
                                link_text = await link.text_content()
                                href = await link.get_attribute('href')
                                if link_text and href:
                                    link_info.append(f"'{link_text.strip()}' -> {href}")
                            except Exception:
                                pass
                        
                        for i, info in enumerate(link_info[:10]):  # 처음 10개만
                            pass  # Debug output removed
                        break
                        
                except Exception as e:
                    pass  # Debug selector failed
            
            # 🔍 페이지 콘텐츠 일부 확인 (HTML 구조 파악용)
            try:
                content = await page.content()
                content_length = len(content)
                
                # 특정 키워드 검색
                keywords = ['투수', 'pitcher', 'playerId', 'bsPlayerDetail', 'table']
                for keyword in keywords:
                    count = content.lower().count(keyword.lower())
                    if count > 0:
                        pass  # Debug keyword count removed
                        
            except Exception as e:
                pass  # Content analysis failed
                
        except Exception as e:
            pass  # Debug all player links failed

    @staticmethod
    async def _find_pitcher_link(page: 'Page', pitcher_name: str):
        """투수 섹션에서만 선수 찾기 - 짧은 이름도 풀네임과 매칭"""
        
        # 이름 정규화 함수
        def normalize_name(name: str) -> str:
            """이름 정규화: 공백, 특수문자 제거 및 통일"""
            import re

            # 모든 종류의 공백(일반, 전각, 탭 등) 제거
            name = re.sub(r'[\s\u3000\u00A0\u2000-\u200F\u2028-\u202F]', '', name)
            # 중점(·) 및 특수문자 제거
            name = re.sub(r'[·\-\.\,]', '', name)
            return name.lower()
        
        normalized_pitcher_name = normalize_name(pitcher_name)
        logger.info(f"🔍 NPB 투수 찾기 시작: '{pitcher_name}' (정규화: '{normalized_pitcher_name}')")
        
        try:
            # 🎯 **1단계: 투수 섹션에서 정확한 이름 매칭**
            pitcher_section_selectors = [
                # 투수 섹션 내 정확 매칭
                f'div.plRow:has(span#pitcher_list) a:has-text("{pitcher_name}")',
                f'div:has(span:has-text("투수")) a:has-text("{pitcher_name}")',
                f'span#pitcher_list ~ a:has-text("{pitcher_name}")',
            ]
            
            for selector in pitcher_section_selectors:
                try:
                    link = await page.query_selector(selector)
                    if link:
                        href = await link.get_attribute('href')
                        link_text = await link.text_content()
                        if href and 'playerId=' in href:
                            logger.info(f"✅ 투수 섹션 정확 매칭: '{pitcher_name}' → '{link_text}'")
                            return link
                except Exception:
                    continue
            
            # 🚀 **2단계: 투수 섹션에서 부분 문자열 매칭 (핵심 개선)**
            try:
                # 투수 섹션의 모든 링크 가져오기
                pitcher_links = await page.query_selector_all(
                    'div.plRow:has(span#pitcher_list) a[href*="playerId="], '
                    'div:has(span:has-text("투수")) a[href*="playerId="]'
                )
                
                logger.info(f"투수 섹션에서 {len(pitcher_links)}개 링크 발견")
                
                # 디버깅: 팀 페이지의 모든 투수 이름 로깅
                all_pitcher_names = []
                for link in pitcher_links:
                    try:
                        link_text = await link.text_content()
                        if link_text and link_text.strip():
                            all_pitcher_names.append(link_text.strip())
                    except Exception:
                        pass
                
                if all_pitcher_names:
                    logger.info(f"팀 페이지 투수 목록: {all_pitcher_names}")
                    
                    # 🎯 특별 케이스: 찾는 투수명과 유사한 이름들 찾기
                    similar_names = []
                    for name in all_pitcher_names:
                        # 부분 매칭 체크
                        if pitcher_name in name or name in pitcher_name:
                            similar_names.append(name)
                        # 정규화된 이름으로 체크  
                        elif normalize_name(pitcher_name) in normalize_name(name):
                            similar_names.append(name)
                            
                    if similar_names:
                        logger.info(f"🎯 {pitcher_name} 유사 이름 발견: {similar_names}")
                    
                    # 특별히 이토가 포함된 경우 상세 로깅 (기존 로직 유지)
                    if any("이토" in name for name in all_pitcher_names):
                        matching_names = [name for name in all_pitcher_names if "이토" in name]
                        logger.info(f"🎯 이토 관련 투수들: {matching_names}")
                        logger.info(f"🔍 찾는 이름: '{pitcher_name}' vs 페이지 이름들")
                        
                        # 이토 관련 투수들의 URL도 로깅
                        for link in pitcher_links:
                            try:
                                link_text = await link.text_content()
                                if link_text and "이토" in link_text:
                                    href = await link.get_attribute('href')
                                    if href:
                                        # Fix URL construction for betman.co.kr relative links
                                        if href.startswith('/main/'):
                                            full_url = f'https://www.betman.co.kr{href}'
                                        elif href.startswith('/bsPlayerDetail') or href.startswith('bsPlayerDetail'):
                                            # Add missing path prefix for betman.co.kr
                                            clean_href = href.lstrip('/')
                                            full_url = f'https://www.betman.co.kr/main/mainPage/gameinfo/{clean_href}'
                                        elif not href.startswith('http'):
                                            full_url = f'https://www.betman.co.kr{href}'
                                        else:
                                            full_url = href
                                        logger.info(f"🔗 {link_text.strip()} → {full_url}")
                            except Exception:
                                pass
                else:
                    logger.warning("팀 페이지에서 투수 이름을 찾을 수 없음")
                
                for link in pitcher_links:
                    try:
                        link_text = await link.text_content()
                        if not link_text:
                            continue
                            
                        normalized_link_text = normalize_name(link_text)
                        
                        # 📍 **핵심: 짧은 이름이 풀네임에 포함되는지 확인**
                        if (normalized_pitcher_name in normalized_link_text or
                            normalized_link_text.startswith(normalized_pitcher_name)):
                            
                            href = await link.get_attribute('href')
                            if href and 'playerId=' in href:
                                # Fix URL construction for betman.co.kr relative links
                                if href.startswith('/main/'):
                                    full_url = f'https://www.betman.co.kr{href}'
                                elif href.startswith('/bsPlayerDetail') or href.startswith('bsPlayerDetail'):
                                    # Add missing path prefix for betman.co.kr
                                    clean_href = href.lstrip('/')
                                    full_url = f'https://www.betman.co.kr/main/mainPage/gameinfo/{clean_href}'
                                elif not href.startswith('http'):
                                    full_url = f'https://www.betman.co.kr{href}'
                                else:
                                    full_url = href
                                logger.info(
                                    f"✅ 투수 섹션 부분 매칭 성공: '{pitcher_name}' → '{link_text}'"
                                )
                                logger.info(f"🔗 투수 상세 URL: {full_url}")
                                return link
                                
                    except Exception as e:
                        logger.debug(f"투수 링크 처리 중 오류: {e}")
                        continue
                        
            except Exception as e:
                logger.debug(f"투수 섹션 부분 매칭 중 오류: {e}")
            
            # 🔍 **3단계: 이름 분할 매칭 (최후 수단)**
            name_parts = [part.strip() for part in pitcher_name.split() if part.strip()]
            if len(name_parts) >= 1:
                logger.debug(f"이름 분할 매칭 시도: {name_parts}")
                
                try:
                    pitcher_links = await page.query_selector_all(
                        'div.plRow:has(span#pitcher_list) a[href*="playerId="]'
                    )
                    
                    for link in pitcher_links:
                        try:
                            link_text = await link.text_content()
                            if not link_text:
                                continue
                            
                            # 모든 이름 부분이 포함되는지 확인
                            if all(part in link_text for part in name_parts):
                                href = await link.get_attribute('href')
                                if href and 'playerId=' in href:
                                    logger.info(
                                        f"✅ 투수 섹션 분할 매칭 성공: '{pitcher_name}' → '{link_text}'"
                                    )
                                    return link
                                    
                        except Exception:
                            continue
                            
                except Exception as e:
                    logger.debug(f"이름 분할 매칭 중 오류: {e}")
            
            # 🔍 **4단계: 전체 페이지에서 fallback 검색 (기존 로직)**
            logger.debug(f"투수 섹션에서 찾기 실패, 전체 페이지 검색...")
            
            # 일반 선수 링크 (정확 매칭)
            general_selectors = [
                f'a.player:has-text("{pitcher_name}")',
                f'a[href*="bsPlayerDetail"]:has-text("{pitcher_name}")',
                f'a[href*="playerId="]:has-text("{pitcher_name}")',
            ]

            for selector in general_selectors:
                try:
                    link = await page.query_selector(selector)
                    if link:
                        href = await link.get_attribute('href')
                        if href and 'playerId=' in href:
                            link_text = await link.text_content()
                            logger.info(f"✅ 전체 페이지 매칭: '{pitcher_name}' → '{link_text}'")
                            return link
                except Exception:
                    continue
                    
        except Exception as e:
            logger.debug(f"투수 링크 찾기 전체 오류: {e}")
            
        logger.warning(f"❌ 모든 매칭 방법 실패: {pitcher_name}")
        return None

    async def _auto_add_to_pitcher_config(self, pitcher_name: str, current_url: str = None) -> None:
        """성공적으로 크롤링된 투수를 pitcher.json에 자동 추가"""
        try:
            if not current_url or 'playerId=' not in current_url:
                logger.debug(f"❌ {pitcher_name} pitcher.json 자동 추가 실패: URL이 없거나 playerId 없음")
                return
            
            # playerId 추출
            import re
            player_id_match = re.search(r'playerId=([^&]+)', current_url)
            if not player_id_match:
                return
            
            player_id = player_id_match.group(1)
            
            # 팀 정보 추출 (URL에서)
            team_id_match = re.search(r'teamId=([^&]+)', current_url)
            league_id_match = re.search(r'leagueId=([^&]+)', current_url)
            
            team_id = team_id_match.group(1) if team_id_match else ""
            league_id = league_id_match.group(1) if league_id_match else ""
            
            # 리그 및 팀명 매핑
            league_map = {"BS001": "KBO", "BS002": "MLB", "BS004": "NPB"}
            team_map = {
                "OB": "두산", "SS": "삼성", "LG": "LG", "NC": "NC", "KT": "KT",
                "HH": "한화", "SK": "SSG", "LT": "롯데", "HT": "기아", "WO": "키움"
            }
            
            league = league_map.get(league_id, "KBO")
            team_name = team_map.get(team_id, team_id)
            
            # pitcher.json 업데이트
            config_path = os.path.join(
                os.path.dirname(__file__), 
                '../../../core/config/resources/pitcher.json'
            )
            
            import json
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 이미 존재하는지 확인
            if pitcher_name in config.get("manual_pitchers", {}):
                logger.debug(f"📋 {pitcher_name} 이미 pitcher.json에 존재함")
                return
            
            # 새 항목 추가
            if "manual_pitchers" not in config:
                config["manual_pitchers"] = {}
            
            config["manual_pitchers"][pitcher_name] = {
                "direct_url": current_url,
                "team": team_name,
                "league": league,
                "active": True,
                "notes": f"자동 추가됨 - playerId: {player_id}",
                "auto_added": True,
                "added_date": "2025-07-20"
            }
            
            # 파일 저장
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ {pitcher_name} pitcher.json에 자동 추가 완료 (playerId: {player_id})")
            
        except Exception as e:
            logger.debug(f"❌ {pitcher_name} pitcher.json 자동 추가 실패: {e}")

    async def _find_player_id_from_team_page(self, pitcher_name: str, team_url: str) -> str:
        """팀 페이지에서 투수의 실제 playerId 찾기"""
        if not self.browser:
            return "FIND_ID"
        
        page = None
        try:
            page = await self.browser.new_page()
            
            # 팀 페이지 로드
            await page.goto(team_url, timeout=30000)
            await page.wait_for_selector("table.tbl", timeout=20000)
            await asyncio.sleep(2)
            
            # 투수 링크 찾기
            link = await PitcherCrawler._find_pitcher_link(page, pitcher_name)
            if link:
                href = await link.get_attribute('href')
                if href and 'playerId=' in href:
                    import re
                    player_id_match = re.search(r'playerId=([^&]+)', href)
                    if player_id_match:
                        player_id = player_id_match.group(1)
                        logger.info(f"✅ {pitcher_name} playerId 찾기 성공: {player_id}")
                        return player_id
            
            logger.warning(f"❌ {pitcher_name} playerId 찾기 실패")
            return "FIND_ID"
            
        except Exception as e:
            logger.warning(f"❌ {pitcher_name} playerId 찾기 오류: {e}")
            return "FIND_ID"
        finally:
            if page:
                try:
                    await page.close()
                except Exception:
                    pass

    @staticmethod
    async def verify_pitcher_on_team_page(page: 'Page', player_name: str) -> Optional[str]:
        """팀 페이지에서 특정 선수가 실제로 투수인지 확인하고, 투수면 URL 반환"""
        
        def normalize_name(name: str) -> str:
            """이름 정규화: 공백, 특수문자 제거 및 통일"""
            import re
            name = re.sub(r'[\s\u3000\u00A0\u2000-\u200F\u2028-\u202F]', '', name)
            name = re.sub(r'[·\-\.\,]', '', name)
            return name.lower()
        
        normalized_player_name = normalize_name(player_name)
        logger.info(f"🔍 투수 검증 시작: '{player_name}' (정규화: '{normalized_player_name}')")
        
        try:
            # 투수 섹션의 모든 링크 가져오기
            pitcher_links = await page.query_selector_all(
                'div.plRow:has(span#pitcher_list) a[href*="playerId="], '
                'div:has(span:has-text("투수")) a[href*="playerId="]'
            )
            
            logger.info(f"팀 페이지에서 {len(pitcher_links)}개 투수 링크 발견")
            
            # 투수 목록에서 해당 선수 찾기
            for link in pitcher_links:
                try:
                    link_text = await link.text_content()
                    if link_text and link_text.strip():
                        link_name = link_text.strip()
                        normalized_link_name = normalize_name(link_name)
                        
                        # 정확한 매칭 또는 부분 매칭 확인
                        if (player_name == link_name or 
                            normalized_player_name == normalized_link_name or
                            player_name in link_name or 
                            link_name in player_name or
                            normalized_player_name in normalized_link_name):
                            
                            # 해당 선수의 URL 가져오기
                            href = await link.get_attribute('href')
                            if href:
                                # URL 구성
                                if href.startswith('/main/'):
                                    full_url = f'https://www.betman.co.kr{href}'
                                elif href.startswith('/bsPlayerDetail') or href.startswith('bsPlayerDetail'):
                                    clean_href = href.lstrip('/')
                                    full_url = f'https://www.betman.co.kr/main/mainPage/gameinfo/{clean_href}'
                                elif not href.startswith('http'):
                                    full_url = f'https://www.betman.co.kr{href}'
                                else:
                                    full_url = href
                                
                                logger.info(f"✅ 투수 확인됨: '{player_name}' → 팀 페이지 투수 목록의 '{link_name}' → URL: {full_url}")
                                return full_url
                            
                except Exception as e:
                    logger.debug(f"링크 텍스트 추출 실패: {e}")
                    continue
            
            logger.warning(f"❌ 투수 확인 실패: '{player_name}' - 팀 페이지 투수 목록에 없음")
            return None
            
        except Exception as e:
            logger.error(f"투수 검증 중 오류 발생: {e}")
            return None

    # 컨텍스트 매니저 지원 추가
    async def __aenter__(self):
        """비동기 컨텍스트 매니저 진입"""
        await self.initialize_browser()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """비동기 컨텍스트 매니저 종료"""
        await self.cleanup()