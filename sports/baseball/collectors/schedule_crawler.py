"""
Baseball Schedule Crawler
Extracts starting pitcher names from betman.co.kr schedule page by filtering on match_date
"""
import asyncio
from datetime import datetime
from typing import Dict, List, Optional
from playwright.async_api import Page

from utils.logger import Logger

logger = Logger(__name__)


class BaseballScheduleCrawler:
    """
    Crawls baseball schedule page to extract starting pitcher names for specific dates
    """
    
    def __init__(self):
        self.schedule_url = "https://www.betman.co.kr/main/mainPage/gameinfo/dataOfBaseballSchedule.do"
        self.page: Optional[Page] = None
    
    async def get_starting_pitchers_by_date(
        self, 
        page: Page, 
        match_date: str
    ) -> Dict[str, str]:
        """
        Get starting pitcher names for games on specific match_date
        
        Args:
            page: Playwright page instance
            match_date: Date string in format 'YYYY-MM-DD'
            
        Returns:
            Dict mapping team_id to starting pitcher name
            {
                'home_team_id': 'pitcher_name',
                'away_team_id': 'pitcher_name',
                ...
            }
        """
        pitcher_mapping = {}
        
        try:
            logger.debug(f"🔍 Getting starting pitchers for date: {match_date}")
            
            # Navigate to schedule page
            await page.goto(self.schedule_url, wait_until='networkidle')
            await asyncio.sleep(2)
            
            # Apply date filter - convert YYYY-MM-DD to required format
            formatted_date = self._format_date_for_filter(match_date)
            if not formatted_date:
                logger.warning(f"❌ Invalid date format: {match_date}")
                return pitcher_mapping
            
            # Set date filter
            await self._set_date_filter(page, formatted_date)
            await asyncio.sleep(3)
            
            # Extract pitcher names from filtered results
            pitcher_mapping = await self._extract_pitchers_from_schedule(page)
            
            logger.info(f"✅ Found {len(pitcher_mapping)} starting pitchers for {match_date}")
            return pitcher_mapping
            
        except Exception as e:
            logger.error(f"❌ Failed to get starting pitchers for {match_date}: {e}")
            return {}
    
    def _format_date_for_filter(self, match_date: str) -> Optional[str]:
        """
        Convert match_date from YYYY-MM-DD to format required by schedule page
        
        Args:
            match_date: Date in YYYY-MM-DD format
            
        Returns:
            Date in format required by betman schedule page, or None if invalid
        """
        try:
            # Parse the date
            date_obj = datetime.strptime(match_date, '%Y-%m-%d')
            
            # Convert to required format (may need adjustment based on actual site requirements)
            # Common formats: YYYYMMDD, YYYY.MM.DD, MM/DD/YYYY
            formatted = date_obj.strftime('%Y%m%d')  # Start with YYYYMMDD
            
            logger.debug(f"🔄 Date conversion: {match_date} → {formatted}")
            return formatted
            
        except ValueError as e:
            logger.error(f"❌ Date parsing failed for {match_date}: {e}")
            return None
    
    async def _set_date_filter(self, page: Page, formatted_date: str) -> None:
        """
        Set date filter on the schedule page
        
        Args:
            page: Playwright page instance
            formatted_date: Date in the format expected by the schedule page
        """
        try:
            # Look for date input field (common selectors)
            date_selectors = [
                'input[type="date"]',
                'input[name*="date"]',
                'input[id*="date"]',
                '.date-picker input',
                '#searchDate',
                'input.date-input'
            ]
            
            date_input = None
            for selector in date_selectors:
                try:
                    date_input = await page.wait_for_selector(selector, timeout=2000)
                    if date_input:
                        logger.debug(f"🎯 Found date input: {selector}")
                        break
                except:
                    continue
            
            if date_input:
                # Clear and set the date
                await date_input.clear()
                await date_input.fill(formatted_date)
                await page.keyboard.press('Enter')
                logger.debug(f"✅ Set date filter to: {formatted_date}")
            else:
                logger.warning("⚠️ Could not find date input field on schedule page")
                
        except Exception as e:
            logger.error(f"❌ Failed to set date filter: {e}")
    
    async def _extract_pitchers_from_schedule(self, page: Page) -> Dict[str, str]:
        """
        Extract starting pitcher names from the schedule page results
        
        Args:
            page: Playwright page instance with filtered schedule data
            
        Returns:
            Dict mapping team identifiers to pitcher names
        """
        pitcher_mapping = {}
        
        try:
            # Wait for schedule data to load
            await page.wait_for_selector('.game-list, .schedule-list, table', timeout=5000)
            
            # Extract game rows - common patterns for schedule tables
            game_selectors = [
                '.game-item',
                '.schedule-item', 
                'table tr',
                '.match-row',
                '.game-row'
            ]
            
            games = []
            for selector in game_selectors:
                try:
                    games = await page.query_selector_all(selector)
                    if games and len(games) > 1:  # Found actual game data
                        logger.debug(f"🎯 Found {len(games)} games using: {selector}")
                        break
                except:
                    continue
            
            if not games:
                logger.warning("⚠️ No game data found on schedule page")
                return pitcher_mapping
            
            # Process each game to extract pitcher info
            for i, game_element in enumerate(games):
                try:
                    game_data = await self._extract_single_game_pitchers(game_element)
                    if game_data:
                        pitcher_mapping.update(game_data)
                        logger.debug(f"✅ Game {i+1}: {game_data}")
                        
                except Exception as e:
                    logger.debug(f"⚠️ Failed to process game {i+1}: {e}")
                    continue
            
            return pitcher_mapping
            
        except Exception as e:
            logger.error(f"❌ Failed to extract pitchers from schedule: {e}")
            return {}
    
    async def _extract_single_game_pitchers(self, game_element) -> Optional[Dict[str, str]]:
        """
        Extract pitcher information from a single game element
        
        Args:
            game_element: Playwright element representing one game
            
        Returns:
            Dict with team-pitcher mapping for this game, or None if extraction fails
        """
        try:
            # Get all text content from the game element
            game_text = await game_element.inner_text()
            
            # Look for pitcher names - common patterns
            # "vs", "대", home/away indicators
            pitcher_patterns = await self._find_pitcher_patterns(game_element)
            
            if pitcher_patterns:
                return pitcher_patterns
            
            # Fallback: log the structure for debugging
            logger.debug(f"🔍 Game text sample: {game_text[:100]}...")
            
            return None
            
        except Exception as e:
            logger.debug(f"Failed to extract single game pitchers: {e}")
            return None
    
    async def _find_pitcher_patterns(self, game_element) -> Optional[Dict[str, str]]:
        """
        Find pitcher name patterns in game element
        
        Args:
            game_element: Game element to search in
            
        Returns:
            Dict with extracted pitcher names, or None
        """
        try:
            # Look for common pitcher indicators
            pitcher_indicators = [
                '.pitcher',
                '.starting-pitcher',
                '[class*="pitcher"]',
                'td:nth-child(3)',  # Common position for pitcher in tables
                'td:nth-child(4)'
            ]
            
            pitcher_data = {}
            
            for indicator in pitcher_indicators:
                try:
                    pitcher_elements = await game_element.query_selector_all(indicator)
                    for elem in pitcher_elements:
                        pitcher_name = await elem.inner_text()
                        if pitcher_name and len(pitcher_name.strip()) > 0:
                            # Use element position or other context to determine team
                            # This is a simplified approach - may need refinement
                            key = f"pitcher_{len(pitcher_data)}"
                            pitcher_data[key] = pitcher_name.strip()
                            
                except:
                    continue
            
            return pitcher_data if pitcher_data else None
            
        except Exception as e:
            logger.debug(f"Pattern search failed: {e}")
            return None