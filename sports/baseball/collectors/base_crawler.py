"""
베이스 크롤러 클래스 - 공통 기능 제공
"""
import asyncio
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Protocol

import pytz
from playwright.async_api import Page
from supabase import Client

from config.config import League
from utils.logger import Logger

logger = Logger(__name__)


class ProgressObserver(Protocol):
    """진행상황 관찰자 인터페이스"""
    
    def on_progress(self, current: int, total: int, message: str) -> None:
        """진행상황 업데이트"""
        ...
    
    def on_complete(self, message: str) -> None:
        """완료 알림"""
        ...
    
    def on_error(self, error: str) -> None:
        """에러 알림"""
        ...


class ConsoleObserver:
    """콘솔 출력 관찰자"""
    
    @staticmethod
    def on_progress(current: int, total: int, message: str) -> None:
        progress = min(100, int(current * 100 / total if total else 0))
        bar_length = 30
        filled = int(bar_length * current / total if total else 0)
        bar = '█' * filled + '░' * (bar_length - filled)
        
        logger.info(
            f"[진행] {current}/{total} - [{bar}] {progress}% - {message}"
        )
    
    @staticmethod
    def on_complete(message: str) -> None:
        logger.info(f"✅ {message}")
    
    @staticmethod
    def on_error(error: str) -> None:
        logger.error(f"❌ {error}")


class BaseCrawler(ABC):
    """베이스 크롤러 클래스"""
    
    def __init__(self, client: Optional[Client] = None):
        self.client = client
        self.observers: List[ProgressObserver] = []
        self.KST = pytz.timezone('Asia/Seoul')
        self.team_mappings: Dict[str, str] = {}
        self.league_teams_cache: Dict[League, set] = {}
    
    def add_observer(self, observer: ProgressObserver) -> None:
        """관찰자 추가"""
        self.observers.append(observer)
    
    def remove_observer(self, observer: ProgressObserver) -> None:
        """관찰자 제거"""
        if observer in self.observers:
            self.observers.remove(observer)
    
    def notify_progress(self, current: int, total: int, message: str) -> None:
        """진행상황 알림"""
        for observer in self.observers:
            observer.on_progress(current, total, message)
    
    def notify_complete(self, message: str) -> None:
        """완료 알림"""
        for observer in self.observers:
            observer.on_complete(message)
    
    def notify_error(self, error: str) -> None:
        """에러 알림"""
        for observer in self.observers:
            observer.on_error(error)
    
    def get_league_teams(self, league: League) -> set:
        """target_games에서 리그별 팀명 로드 (캐싱)"""
        if league in self.league_teams_cache:
            return self.league_teams_cache[league]
        
        if not self.client:
            return set()
        
        try:
            # 리그별 매핑
            league_id_map = {
                League.KBO: "BS001",
                League.MLB: "BS002", 
                League.NPB: "BS004"
            }
            
            league_id = league_id_map.get(league)
            if not league_id:
                return set()
            
            # target_games 테이블에서 해당 리그의 팀명 가져오기
            result = self.client.table("target_games").select(
                "home_team, away_team"
            ).eq("sports", "baseball").eq("game_type", "W").eq(
                "league_id", league_id
            ).execute()
            
            teams = set()
            for row in result.data:
                teams.add(row["home_team"])
                teams.add(row["away_team"])
            
            self.league_teams_cache[league] = teams
            logger.debug(
                f"🔍 {league.value}: target_games에서 {len(teams)}개 팀 로드"
            )
            return teams
            
        except Exception as e:
            logger.error(f"팀명 로드 실패 - {league.value}: {e}")
            return set()
    
    @staticmethod
    async def setup_browser_page(page: Page) -> None:
        """브라우저 페이지 초기 설정"""
        # 공통 설정 (타임아웃, 헤더 등)
        await page.set_viewport_size({"width": 1920, "height": 1080})
        await page.set_extra_http_headers({
            'User-Agent': (
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                'AppleWebKit/537.36'
            )
        })
    
    @abstractmethod
    async def collect_data(self, **kwargs) -> List[Dict[str, Any]]:
        """데이터 수집 (하위 클래스에서 구현)"""
        pass
    
    @staticmethod
    async def process_with_retry(func, *args, max_retries: int = 3,
                                 **kwargs):
        """재시도 로직이 포함된 함수 실행"""
        for attempt in range(max_retries):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                await asyncio.sleep(2 ** attempt)  # 지수 백오프
        return None 