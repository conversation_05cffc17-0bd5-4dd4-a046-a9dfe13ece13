"""
통합 데이터 수집기 - Strategy 패턴 적용
팀 통계, 투수 통계, 경기 스케줄을 통합 관리

# flake8: noqa  # 파일 전체에 대해 E501 등 길이 제한 검사 비활성화
"""
import asyncio
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import pytz
from playwright.async_api import Page, async_playwright

from config.config import SCHEDULE_URL, League
from sports.baseball.collectors.base_crawler import BaseCrawler
from sports.baseball.services.processors import \
    UnifiedBaseballService as TeamPitcherUnifiedService
from utils.logger import Logger

# 로거 설정
logger = Logger(__name__)


class LeagueStrategy:
    """리그별 처리 전략 (Strategy Pattern)"""

    @staticmethod
    async def click_league_tab(page: Page, league: League) -> bool:
        """리그 탭 클릭"""
        try:
            if league == League.KBO:
                await page.reload(wait_until="networkidle")
                await asyncio.sleep(2)
                kbo_tab = await page.query_selector('#ui-id-1')
                if kbo_tab:
                    await kbo_tab.click()
                    await asyncio.sleep(3)

            elif league == League.MLB:
                await page.reload(wait_until="networkidle")
                await asyncio.sleep(2)
                mlb_tab = await page.query_selector('#ui-id-2')
                if mlb_tab:
                    await mlb_tab.click()
                    await asyncio.sleep(3)

            elif league == League.NPB:
                await page.reload(wait_until="networkidle")
                await asyncio.sleep(2)
                await page.click('#ui-id-3', timeout=5000)
                await asyncio.sleep(3)

            return True

        except Exception:
            return False

    @staticmethod
    def get_tab_selector(league: League) -> str:
        """리그별 탭 선택자 반환"""
        return {
            League.KBO: '#tabs-1',
            League.MLB: '#tabs-2',
            League.NPB: '#tabs-3'
        }[league]


class UnifiedDataCollector(BaseCrawler):
    """통합 데이터 수집기"""

    def __init__(self, client=None, wait_time=2.0):
        super().__init__(client)
        self.unified_service = TeamPitcherUnifiedService()
        self.league_strategy = LeagueStrategy()
        self.KST = pytz.timezone('Asia/Seoul')
        self.wait_time = wait_time  # 대기 시간 설정
        # Playwright/브라우저 인스턴스 참조 (자원 정리를 위해 저장)
        self.playwright = None
        self.browser = None

    async def initialize_browser(self):
        """브라우저 초기화"""
        if not self.browser:
            from playwright.async_api import async_playwright
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(headless=True)

    async def collect_data(self, **kwargs) -> List[Dict]:
        """데이터 수집 진입점 - 리그별 순차 처리로 메모리 효율성 개선"""
        start_time = time.time()
        if __name__ != "__main__":
            logger.info("🚀 수집 시작")
        
        try:
            # 조건에 맞는 target_games 조회
            target_games = await self._get_target_games()

            if not target_games:
                logger.info("수집할 경기가 없습니다")
                return []

            # 리그별로 그룹화
            league_groups = self._group_games_by_league(target_games)
            
            if not league_groups:
                logger.info("유효한 리그 그룹이 없습니다")
                return []

            # 리그별 순차 처리 (크롤링 → 팀별 통합 처리만 사용)
            all_processed_games = []
            total_team_saved = 0
            total_pitcher_saved = 0
            
            for league_name, league_games in league_groups.items():
                logger.info(f"🏷️ {league_name} 리그 처리 시작 ({len(league_games)}개 경기)")
                
                # 1. 해당 리그 크롤링
                crawled_games = await self._crawl_league_games(league_name, league_games)
                
                if not crawled_games:
                    logger.info(f"🏷️ {league_name} 크롤링 결과 없음")
                    continue
                
                # 2. 팀별 통합 처리만 사용 (팀 통계 + 선발투수 함께)
                # 기존의 개별 저장 방식은 완전히 제거
                unified_results = await self._process_teams_unified(crawled_games)
                total_team_saved += unified_results.get('teams_saved', 0)
                total_pitcher_saved += unified_results.get('pitchers_saved', 0)
                
                # 3. 처리 완료 로그
                logger.info(
                    f"🏷️ {league_name} 완료: 팀 {unified_results.get('teams_saved', 0)}개, "
                    f"투수 {unified_results.get('pitchers_saved', 0)}개"
                )
                
                all_processed_games.extend(crawled_games)
            
            # 전체 결과 요약
            success_count = len(all_processed_games)
            total_time = time.time() - start_time
            
            if success_count > 0:
                logger.info(
                    "✅ 전체 완료: %s개 경기, 팀 %s개, 투수 %s개 (%.1f초)",
                    success_count,
                    total_team_saved,
                    total_pitcher_saved,
                    total_time,
                )
            else:
                logger.info(
                    "ℹ️ 수집 대상 없음 (%.1f초)",
                    total_time,
                )
            
            return all_processed_games
            
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"❌ 실패: {e} ({total_time:.1f}초)")
            return []

    async def collect_games(self) -> List[Dict]:
        """경기 데이터 수집"""
        # 조건에 맞는 target_games 조회
        target_games = await self._get_target_games()
        
        if not target_games:
            return []

        # 웹 크롤링
        crawled_games = await self._crawl_target_games(target_games)
        
        return crawled_games

    async def _get_target_games(self) -> List[Dict]:
        """조건에 맞는 target_games 조회 - DB 레벨에서 시간 필터링 최적화"""
        if not self.client:
            logger.debug("[get_target_games] No DB client available.")
            return []
        try:
            now = datetime.now(self.KST)
            end_date = (now + timedelta(days=1)).strftime('%Y-%m-%d')
            
            logger.info(f"🔍 DB 조회: {now.strftime('%Y-%m-%d')} ~ {end_date}")
            
            response = self.client.table("target_games").select(
                "match_id, match_date, match_time, league_id, "
                "home_team, away_team, home_team_id, away_team_id"
            ).eq("sports", "baseball").eq("game_type", "W").gte(
                "match_date", now.strftime('%Y-%m-%d')
            ).lte("match_date", end_date).order(
                "match_date, match_time"
            ).execute()

            if response and hasattr(response, 'data') and response.data:
                logger.info(f"📊 DB에서 {len(response.data)}개 경기 조회됨")
                for g in response.data:
                    logger.debug(f"  - {g['match_date']} {g['match_time']} {g['home_team']} vs {g['away_team']}")
            else:
                logger.info("[get_target_games] No games fetched from DB.")
                return []

            filtered_games = []
            for game in response.data:
                try:
                    date_str = game['match_date']
                    time_str = game['match_time']
                    if ':' in time_str and len(time_str.split(':')) == 3:
                        time_parts = time_str.split(':')
                        time_str = f"{time_parts[0]}:{time_parts[1]}"
                    match_dt = datetime.strptime(
                        f"{date_str} {time_str}",
                        "%Y-%m-%d %H:%M",
                    )
                    match_dt = self.KST.localize(match_dt)
                    cutoff_time = match_dt - timedelta(hours=24)
                    
                    logger.debug(f"경기 시간: {match_dt}, 현재 시간: {now}, 컷오프: {cutoff_time}")
                    
                    if cutoff_time <= now <= match_dt:
                        filtered_games.append(game)
                        logger.debug(f"✅ 포함: {game['home_team']} vs {game['away_team']}")
                    else:
                        logger.debug(f"❌ 제외: {game['home_team']} vs {game['away_team']} (시간 범위 밖)")
                except Exception as e:
                    logger.debug(f"❌ 파싱 실패: {game} - {e}")
                    continue
                    
            logger.info(f"🎯 필터링 후 {len(filtered_games)}개 경기")
            for g in filtered_games:
                logger.debug(f"  - {g['match_date']} {g['match_time']} {g['home_team']} vs {g['away_team']}")
            return filtered_games
        except Exception as e:
            logger.error(f"target_games 조회 실패: {e}")
            return []

    async def _crawl_target_games(
        self, target_games: List[Dict]
    ) -> List[Dict]:
        """target_games에 있는 경기들만 크롤링"""
        if not target_games:
            # logger.info("[crawl_target_games] No target games provided.")
            return []
        # logger.info(
        #     f"[crawl_target_games] Received {len(target_games)} games for crawling."
        # )  # noqa: E501
        for g in target_games:
            pass  # logger.debug(...)
        # 리그별로 그룹화
        league_groups = {}
        league_map = {
            "BS001": League.KBO,
            "BS002": League.MLB,
            "BS004": League.NPB
        }
        for game in target_games:
            league_id = game.get("league_id")
            if league_id in league_map:
                league = league_map[league_id]
                if league not in league_groups:
                    league_groups[league] = []
                league_groups[league].append(game)
        # logger.info("[crawl_target_games] League groups 로그")
        if not league_groups:
            # logger.info("[crawl_target_games] No valid league groups after grouping.")
            return []

        # 브라우저 설정
        playwright_instance = None
        browser = None
        page = None
        crawled_games = []

        try:
            playwright_instance = await async_playwright().start()
            browser = await playwright_instance.chromium.launch(headless=True)
            page = await browser.new_page()
            await self.setup_browser_page(page)
            # 페이지 로딩이 느린 경우를 대비해 타임아웃을 60초로 연장합니다.
            # 또한 네트워크가 idle 상태가 될 때까지 대기합니다.
            await page.goto(
                SCHEDULE_URL,
                timeout=60000,
                wait_until="networkidle",
            )

            # 리그별로 크롤링
            for league, games in league_groups.items():
                league_crawled = await self._crawl_league_target_games(
                    page,
                    league,
                    games,
                )
                crawled_games.extend(league_crawled)

            # 객체 속성에 저장해 close()에서 정리
            self.playwright = playwright_instance
            self.browser = browser

        except Exception as e:
            logger.error(f"크롤링 중 오류: {e}")
            
        finally:
            # 리소스 정리
            if page:
                try:
                    await page.close()
                except Exception:
                    pass
            
            if browser:
                try:
                    await browser.close()
                except Exception:
                    pass
            
            if playwright_instance:
                try:
                    await playwright_instance.stop()
                except Exception:
                    pass

        return crawled_games

    async def _crawl_league_target_games(
        self, page: Page, league: League, target_games: List[Dict]
    ) -> List[Dict]:
        """특정 리그의 target_games만 크롤링 - 팀 ID 기반 고속 매칭"""
        await self.league_strategy.click_league_tab(page, league)
        
        # 추가 대기 시간 적용
        await asyncio.sleep(self.wait_time)
        
        tab_selector = self.league_strategy.get_tab_selector(league)
        crawled_games = []
        
        for target_game in target_games:
            target_date = target_game['match_date']
            home_team_id = target_game['home_team_id']
            away_team_id = target_game['away_team_id']
            
            # 날짜에서 일(day) 추출 (예: "2025-06-01" -> "01")
            try:
                day = target_date.split('-')[2]  # "01"
            except (IndexError, AttributeError):
                continue
            
            # 해당 날짜의 모든 경기 행 찾기
            day_selector = f'{tab_selector} tbody tr[data-day="{day}"]'
            day_rows = await page.query_selector_all(day_selector)
            
            if not day_rows:
                continue
            
            # 각 행에서 팀 ID 매칭 확인
            matched_game = None
            for row in day_rows:
                # 홈팀 링크 찾기
                home_link = await row.query_selector(
                    f'a[href*="teamId={home_team_id}"]'
                )
                # 원정팀 링크 찾기  
                away_link = await row.query_selector(
                    f'a[href*="teamId={away_team_id}"]'
                )
                
                # 둘 다 있으면 매칭 성공
                if home_link and away_link:
                    matched_game = await self._build_matched_game_result(
                        row, league, target_game, home_link, away_link
                    )
                    break
            
            if matched_game:
                crawled_games.append(matched_game)
                
            # 각 게임 처리 후 짧은 대기 시간 추가
            await asyncio.sleep(self.wait_time / 2)
        
        return crawled_games

    async def _build_matched_game_result(
        self, row, league: League, target_game: Dict,
        home_link, away_link
    ) -> Optional[Dict]:
        """매칭된 경기의 결과 구성"""
        try:
            # 게임 datetime 생성
            target_date = target_game['match_date']
            target_time = target_game['match_time']
            
            try:
                if ':' in target_time and len(target_time.split(':')) == 3:
                    time_parts = target_time.split(':')
                    target_time_clean = f"{time_parts[0]}:{time_parts[1]}"
                else:
                    target_time_clean = target_time
                    
                game_dt = datetime.strptime(
                    f"{target_date} {target_time_clean}",
                    '%Y-%m-%d %H:%M'
                )
                game_dt = self.KST.localize(game_dt)
            except Exception:
                game_dt = datetime.now(self.KST)

            # vs_cell 가져오기
            vs_cell = await row.query_selector('td:nth-child(2)')
            if not vs_cell:
                return None

            # 경기 상태 확인 (경기전만)
            if not await self._validate_game_status(vs_cell):
                return None

            # 시간 확인 (추가 검증)
            date_cell = await row.query_selector('td:first-child')
            if date_cell:
                date_text = await date_cell.text_content()
                if date_text:
                    parsed_dt = self._parse_time(date_text)
                    if parsed_dt:
                        target_time = target_game['match_time']
                        if (':' in target_time and 
                                len(target_time.split(':')) == 3):
                            time_parts = target_time.split(':')
                            target_time = f"{time_parts[0]}:{time_parts[1]}"
                        
                        web_time = parsed_dt.strftime('%H:%M')
                        if web_time != target_time:
                            logger.debug(
                                f"시간 불일치: {web_time} vs {target_time}"
                            )
                            return None

            # 팀명 텍스트 추출
            home_team_text = await home_link.text_content()
            away_team_text = await away_link.text_content()

            if not home_team_text or not away_team_text:
                return None

            # URL 정보 구성
            home_href = await home_link.get_attribute('href')
            away_href = await away_link.get_attribute('href')
            
            home_url = f"https://www.betman.co.kr{home_href}"
            away_url = f"https://www.betman.co.kr{away_href}"

            # 투수 정보 수집
            pitcher_info = await self._extract_pitcher_info(vs_cell, game_dt, home_url, away_url, page)

            # 게임 상태 확인 (경기취소 체크)
            game_status = await self._get_game_status(vs_cell)
            
            # 최종 결과 구성
            return {
                'league': league,
                'date': game_dt.strftime('%Y-%m-%d'),
                'time': game_dt.strftime('%H:%M'),
                'match_date': target_game.get('match_date', ''),  # target_games에서 가져온 match_date
                'match_time': target_game.get('match_time', ''),  # target_games에서 가져온 match_time
                'home_team': home_team_text.strip(),
                'away_team': away_team_text.strip(),
                'home_team_url': home_url,
                'away_team_url': away_url,
                'home_team_id': target_game['home_team_id'],
                'away_team_id': target_game['away_team_id'],
                'match_id': target_game['match_id'],
                'home_pitcher': pitcher_info['home_pitcher'],
                'away_pitcher': pitcher_info['away_pitcher'],
                'game_status': game_status,
                'pitcher_available': pitcher_info['pitcher_available']
            }
            
        except Exception as e:
            logger.debug(f"경기 결과 구성 중 오류: {e}")
            return None

    async def _validate_date_time(
        self, row, target_game: Dict
    ) -> Optional[datetime]:
        """날짜/시간 검증"""
        date_cell = await row.query_selector('td:first-child')
        if not date_cell:
            logger.debug("date_cell 없음")
            return None

        date_text = await date_cell.text_content()
        if not date_text:
            logger.debug("date_text 없음")
            return None

        game_dt = self._parse_time(date_text)
        if not game_dt:
            logger.debug(f"시간 파싱 실패: {date_text}")
            return None

        # 시간 매칭 확인
        web_date = game_dt.strftime('%Y-%m-%d')
        web_time = game_dt.strftime('%H:%M')
        
        target_date = target_game['match_date']
        target_time = target_game['match_time']
        
        # DB 시간 형식이 HH:MM:SS인 경우 HH:MM으로 변환
        if ':' in target_time and len(target_time.split(':')) == 3:
            time_parts = target_time.split(':')
            target_time = f"{time_parts[0]}:{time_parts[1]}"
        
        logger.debug(f"시간 비교: 웹({web_date} {web_time}) vs "
                     f"타겟({target_date} {target_time})")
        
        if web_date != target_date or web_time != target_time:
            logger.debug("시간 불일치로 스킵")
            return None

        return game_dt

    async def _validate_game_status(self, vs_cell) -> bool:
        """경기 상태 검증 (경기전만 허용, 경기취소 제외)"""
        game_status = await self._get_game_status(vs_cell)
        if game_status == "경기취소":
            logger.debug(f"경기 취소로 스킵: {game_status}")
            return False
        if game_status != "경기전":
            logger.debug(f"경기 상태 불일치: {game_status}")
            return False
        return True

    async def _extract_and_validate_teams(
        self, vs_cell, target_game: Dict
    ) -> Optional[Dict]:
        """팀 정보 추출 및 검증"""
        # 팀 링크 추출
        home_link = await vs_cell.query_selector(
            '.vsDiv > div:first-child a.team'
        )
        away_link = await vs_cell.query_selector(
            '.vsDiv > div:last-child a.team'
        )

        if not home_link or not away_link:
            logger.debug("팀 링크 없음")
            return None

        # 팀명 텍스트 추출
        home_team_text = await home_link.text_content()
        away_team_text = await away_link.text_content()

        if not home_team_text or not away_team_text:
            logger.debug("팀명 텍스트 없음")
            return None

        web_home = home_team_text.strip()
        web_away = away_team_text.strip()

        logger.debug(f"팀명 비교: 웹({web_home} vs {web_away}) "
                     f"타겟({target_game['home_team']} vs "
                     f"{target_game['away_team']})")

        # 팀명 매칭 검증
        home_match = self._teams_match(web_home, target_game['home_team'])
        away_match = self._teams_match(web_away, target_game['away_team'])
        
        if not home_match or not away_match:
            logger.debug("팀명 불일치로 스킵")
            return None

        return {
            'home_team': web_home,
            'away_team': web_away,
            'home_link': home_link,
            'away_link': away_link
        }

    @staticmethod
    async def _extract_team_urls(vs_cell) -> Dict[str, Optional[str]]:
        """팀 URL 정보 추출"""
        home_link = await vs_cell.query_selector(
            '.vsDiv > div:first-child a.team'
        )
        away_link = await vs_cell.query_selector(
            '.vsDiv > div:last-child a.team'
        )

        home_url = None
        away_url = None

        if home_link:
            href = await home_link.get_attribute('href')
            if href:
                home_url = f"https://www.betman.co.kr{href}"

        if away_link:
            href = await away_link.get_attribute('href')
            if href:
                away_url = f"https://www.betman.co.kr{href}"

        return {
            'home_url': home_url,
            'away_url': away_url
        }

    async def _extract_pitcher_info(
        self, vs_cell, game_dt: datetime, home_url: str = None, away_url: str = None, page: 'Page' = None
    ) -> Dict[str, Optional[str] | bool]:
        """투수 정보 수집 - 스케줄 페이지에서 선발투수 이름 가져오고 팀 페이지에서 검증"""
        now = datetime.now(self.KST)
        pitcher_cutoff_time = game_dt - timedelta(hours=24)
        
        home_starter = None
        away_starter = None
        pitcher_available = now >= pitcher_cutoff_time
        
        if pitcher_available:
            # 스케줄 페이지에서 선발투수 이름 가져오기
            home_starter = await self._get_pitcher(vs_cell, 'first')
            away_starter = await self._get_pitcher(vs_cell, 'last')
            
            logger.debug(f"스케줄에서 선발투수 추출: 홈={home_starter}, 원정={away_starter}")
            
            # 선발투수 검증: 팀 페이지에서 실제 투수인지 확인
            if home_starter and home_url:
                home_starter = await self._verify_pitcher_on_team_page(home_starter, home_url, page)
            
            if away_starter and away_url:
                away_starter = await self._verify_pitcher_on_team_page(away_starter, away_url, page)

        return {
            'home_pitcher': home_starter,  # 검증된 투수만 반환
            'away_pitcher': away_starter,  # 검증된 투수만 반환
            'pitcher_available': pitcher_available
        }

    async def _verify_pitcher_on_team_page(self, pitcher_name: str, team_url: str, page: 'Page' = None) -> Optional[str]:
        """팀 페이지에서 선발투수가 실제 투수인지 확인하고, 투수면 URL 반환"""
        if not pitcher_name or not team_url:
            return None
            
        try:
            # 기존 페이지가 있으면 재사용, 없으면 새로 생성
            if page:
                # 기존 페이지에서 팀 URL로 이동
                await page.goto(team_url, timeout=30000, wait_until="networkidle")
                current_page = page
                should_close_page = False
            else:
                # 기존 브라우저가 없으면 새로 생성
                if not self.browser:
                    from playwright.async_api import async_playwright
                    self.playwright = await async_playwright().start()
                    self.browser = await self.playwright.chromium.launch(headless=True)
                
                # 새로운 페이지에서 팀 페이지 로드
                current_page = await self.browser.new_page()
                await self.setup_browser_page(current_page)
                await current_page.goto(team_url, timeout=30000, wait_until="networkidle")
                should_close_page = True
            
            try:
                # pitcher_crawler의 검증 메서드 사용
                from sports.baseball.collectors.pitcher_crawler import \
                    PitcherCrawler
                pitcher_url = await PitcherCrawler.verify_pitcher_on_team_page(current_page, pitcher_name)
                
                if pitcher_url:
                    logger.info(f"✅ 선발투수 검증 성공: '{pitcher_name}' - 실제 투수 확인됨, URL: {pitcher_url}")
                    return pitcher_url
                else:
                    logger.warning(f"❌ 선발투수 검증 실패: '{pitcher_name}' - 팀 페이지 투수 명단에 없음")
                    return None
                    
            finally:
                if should_close_page:
                    await current_page.close()
                
        except Exception as e:
            logger.error(f"선발투수 검증 중 오류 발생: {pitcher_name}, {e}")
            return None

    def _build_game_result(
        self,
        league: League,
        game_dt: datetime,
        target_game: Dict,
        team_info: Dict,
        urls: Dict,
        pitcher_info: Dict
    ) -> Dict:
        """최종 게임 결과 구성"""
        logger.debug("매칭 성공!")
        return {
            'league': league,
            'date': game_dt.strftime('%Y-%m-%d'),
            'time': game_dt.strftime('%H:%M'),
            'match_date': target_game.get('match_date', ''),  # target_games에서 가져온 match_date
            'match_time': target_game.get('match_time', ''),  # target_games에서 가져온 match_time
            'home_team': team_info['home_team'],
            'away_team': team_info['away_team'],
            'home_team_url': urls['home_url'],
            'away_team_url': urls['away_url'],
            'home_team_id': target_game['home_team_id'],
            'away_team_id': target_game['away_team_id'],
            'match_id': target_game['match_id'],
            'home_pitcher': pitcher_info['home_pitcher'],
            'away_pitcher': pitcher_info['away_pitcher'],
            'game_status': '경기전',
            'pitcher_available': pitcher_info['pitcher_available']
        }

    @staticmethod
    def _teams_match(web_team: str, target_team: str) -> bool:
        """팀명 유연 매칭 (공백, 특수문자 무시)"""
        import re

        # 공백과 특수문자 제거 후 비교
        web_clean = re.sub(r'[^가-힣a-zA-Z0-9]', '', web_team.lower())
        target_clean = re.sub(r'[^가-힣a-zA-Z0-9]', '', target_team.lower())
        
        return (web_clean == target_clean or 
                web_clean in target_clean or 
                target_clean in web_clean)

    @staticmethod
    def _parse_time(date_text: str) -> Optional[datetime]:
        """날짜 파싱"""
        try:
            if "(" not in date_text:
                return None

            date_part = date_text.split("(")[0].strip()
            time_part = "18:30"
            if ") " in date_text:
                time_part = date_text.split(") ")[1]

            year = 2025
            month, day = map(int, date_part.split(".")[1:])
            hour, minute = map(int, time_part.split(":"))

            game_dt = datetime(year, month, day, hour, minute)
            # KST 타임존 직접 생성
            from zoneinfo import ZoneInfo
            kst = ZoneInfo("Asia/Seoul")
            return game_dt.replace(tzinfo=kst)

        except Exception:
            return None

    @staticmethod
    def _get_team_id(url: str) -> Optional[str]:
        """URL에서 팀 ID 추출"""
        if not url:
            return None
        match = re.search(r'teamId=([^&]+)', url)
        return match.group(1) if match else None

    async def _get_game_status(self, vs_cell) -> str:
        """
        경기 상태 확인 (실제 HTML 구조 기반)
        - <span class="badge base">경기전</span>
        - <span class="badge end">종료</span>
        - <strong class="score">4</strong> (점수 유무)
        """
        try:
            # 방법 1: badge 클래스로 정확한 상태 확인
            badge_elem = await vs_cell.query_selector('.badge')
            if badge_elem:
                badge_text = await badge_elem.text_content()
                badge_class = await badge_elem.get_attribute('class')

                if badge_text:
                    badge_text = badge_text.strip()
                    if badge_text == "경기전":
                        return "경기전"
                    elif badge_text == "종료":
                        return "종료"
                    elif badge_text == "경기취소":
                        return "경기취소"
                    elif "경기중" in badge_text or "진행중" in badge_text:
                        return "경기중"

                # 클래스로도 확인
                if badge_class:
                    if "base" in badge_class:
                        return "경기전"
                    elif "end" in badge_class:
                        return "종료"
                    elif "live" in badge_class or "ing" in badge_class:
                        return "경기중"

            # 방법 2: 점수가 있으면 종료된 경기로 판단
            score_elements = await vs_cell.query_selector_all('strong.score')
            for score_elem in score_elements:
                score_text = await score_elem.text_content()
                if score_text and score_text.strip().isdigit():
                    # 실제 숫자 점수가 있으면 종료
                    return "종료"

            # 방법 3: 전체 텍스트에서 상태 키워드 확인 (fallback)
            status_text = await vs_cell.text_content()
            if status_text:
                status_text = status_text.strip()
                if "경기취소" in status_text:
                    return "경기취소"
                elif "종료" in status_text:
                    return "종료"
                elif "경기중" in status_text or "진행중" in status_text:
                    return "경기중"
                elif "경기전" in status_text:
                    return "경기전"

            # 기본값: 경기전 (badge가 없고 점수도 없으면 아직 시작 전)
            return "경기전"

        except Exception:
            # 에러 발생시 안전하게 경기전으로 처리
            return "경기전"

    @staticmethod
    async def _get_pitcher(
        vs_cell, position: str
    ) -> Optional[str]:
        """투수 정보 추출 - 실제 HTML 구조에 맞게 수정"""
        try:
            # 여러 가능한 선택자 시도
            selectors = [
                f'.vsDiv > div:{position}-child .player',  # 올바른 클래스명
                f'.vsDiv > div:{position}-child',
                f'.vsDiv div:{position}-child .player',
                f'.vsDiv div:{position}-child',
                f'.vsDIv > div:{position}-child .player',  # 기존 오타 버전
                f'.vsDIv > div:{position}-child',
                f'.vsDIv div:{position}-child .player',
                f'.vsDIv div:{position}-child',
                '.player',  # 일반적인 player 클래스
                '.pitcher',  # pitcher 클래스
                '.starter',  # starter 클래스
                '.pitcher-name',  # pitcher-name 클래스
                '.starter-name'   # starter-name 클래스
            ]
            
            for selector in selectors:
                elem = await vs_cell.query_selector(selector)
                if elem:
                    text = await elem.text_content()
                    if text and text.strip():
                        # "선발 :" 텍스트가 있으면 제거하고, 없으면 그대로 사용
                        if "선발 :" in text:
                            cleaned_name = text.replace("선발 :", "").strip()
                            logger.debug(f"선발투수 추출 ({position}): '{text}' → '{cleaned_name}' (selector: {selector})")
                            return cleaned_name
                        else:
                            logger.debug(f"선발투수 추출 ({position}): '{text}' (selector: {selector})")
                            return text.strip()
            
            # 전체 HTML 구조 디버깅
            html_content = await vs_cell.inner_html()
            logger.debug(f"투수 요소 없음 ({position}). 전체 HTML: {html_content}")
            
        except Exception as e:
            logger.debug(f"투수 추출 오류 ({position}): {e}")
        return None

    @staticmethod
    def _filter_games_by_priority(games: List[Dict]) -> List[Dict]:
        """팀별로 가장 가까운 경기 하나씩만 선택"""
        if not games:
            return []

        team_games = {}

        for game in games:
            home_team_id = game['home_team_id']
            away_team_id = game['away_team_id']
            game_datetime = f"{game['date']} {game['time']}"

            # 홈팀 처리
            if home_team_id:
                if (home_team_id not in team_games or
                        game_datetime < team_games[home_team_id]['datetime']):
                    team_games[home_team_id] = {
                        'game': game,
                        'datetime': game_datetime
                    }

            # 원정팀 처리
            if away_team_id:
                if (away_team_id not in team_games or
                        game_datetime < team_games[away_team_id]['datetime']):
                    team_games[away_team_id] = {
                        'game': game,
                        'datetime': game_datetime
                    }

        # 중복 제거된 경기들 반환
        unique_games = {}
        for team_data in team_games.values():
            game = team_data['game']
            game_key = (f"{game['date']}_{game['time']}_"
                        f"{game['home_team']}_{game['away_team']}")
            unique_games[game_key] = game

        return list(unique_games.values())

    async def _process_teams_unified(self, games: List[Dict]) -> Dict[str, int]:
        """팀별 통합 처리 (팀 통계 + 선발투수)"""
        try:
            return await self.unified_service.process_teams_with_pitchers(
                games, self.client
            )
        except Exception as e:
            logger.error(f"❌ 팀별 통합 처리 실패: {e}")
            return {'teams_saved': 0, 'pitchers_saved': 0}

    async def close(self):
        """
        Playwright/브라우저/컨텍스트 완전 종료 및 메모리 해제
        """
        try:
            if hasattr(self, 'browser') and self.browser:
                try:
                    await self.browser.close()
                except Exception:
                    pass
                self.browser = None
            if hasattr(self, 'playwright') and self.playwright:
                try:
                    await self.playwright.stop()
                except Exception:
                    pass
                self.playwright = None
        except Exception:
            pass

    def _group_games_by_league(self, target_games: List[Dict]) -> Dict[str, List[Dict]]:
        """target_games를 리그별로 그룹화"""
        league_groups = {}
        league_map = {
            "BS001": "KBO",
            "BS002": "MLB", 
            "BS004": "NPB"
        }
        
        for game in target_games:
            league_id = game.get("league_id")
            if league_id in league_map:
                league_name = league_map[league_id]
                if league_name not in league_groups:
                    league_groups[league_name] = []
                league_groups[league_name].append(game)
        
        return league_groups

    async def _crawl_league_games(self, league_name: str, target_games: List[Dict]) -> List[Dict]:
        """특정 리그의 게임들만 크롤링"""
        # 리그 이름을 League enum으로 변환
        league_enum_map = {
            "KBO": League.KBO,
            "MLB": League.MLB,
            "NPB": League.NPB
        }
        
        league_enum = league_enum_map.get(league_name)
        if not league_enum:
            logger.error(f"❌ 알 수 없는 리그: {league_name}")
            return []

        # 브라우저 설정
        playwright_instance = None
        browser = None
        page = None
        crawled_games = []

        try:
            playwright_instance = await async_playwright().start()
            browser = await playwright_instance.chromium.launch(headless=True)
            page = await browser.new_page()
            await self.setup_browser_page(page)
            
            await page.goto(
                SCHEDULE_URL,
                timeout=60000,
                wait_until="networkidle",
            )

            # 해당 리그만 크롤링
            crawled_games = await self._crawl_league_target_games(
                page, league_enum, target_games
            )

        except Exception as e:
            logger.error(f"❌ {league_name} 크롤링 실패: {e}")
            
        finally:
            # 리소스 정리
            if page:
                try:
                    await page.close()
                except Exception:
                    pass
            
            if browser:
                try:
                    await browser.close()
                except Exception:
                    pass
            
            if playwright_instance:
                try:
                    await playwright_instance.stop()
                except Exception:
                    pass

        return crawled_games