"""
Baseball Skip Logic Service
Single Responsibility: Determining what data collection should be skipped
"""
from __future__ import annotations

from typing import Any, Dict, List, Optional

from sports.baseball.utils.database_service import db_service
from sports.baseball.constants import SKIP_FIELDS
from utils.logger import Logger

logger = Logger(__name__)


class SkipLogicService:
    """
    Manages skip logic for baseball data collection.
    
    This service determines whether team or pitcher data collection
    should be skipped based on existing data in the database.
    Extracted from UnifiedBaseballService to follow SRP.
    
    Responsibilities:
    - Check existing team data
    - Check existing pitcher data
    - Manage skip policies by league/data type
    - Provide skip statistics and reporting
    """
    
    def __init__(self) -> None:
        """Initialize the skip logic service."""
        self._skip_stats = {
            'team_skips': 0,
            'pitcher_skips': 0,
            'total_checks': 0
        }
        
        # Configurable fields to check for different data types (from constants)
        self._team_skip_fields = SKIP_FIELDS['TEAM_SKIP_FIELDS'].copy()
        self._pitcher_skip_fields = SKIP_FIELDS['PITCHER_SKIP_FIELDS'].copy()
    
    async def should_skip_team_collection(self, task: Dict[str, Any]) -> bool:
        """
        Determine if team data collection should be skipped.
        
        Checks if team data already exists for the given match, team, and date.
        Only checks betman-collected fields, excluding team_batting, team_pitching, 
        and boxscores as specified.
        
        Args:
            task: Team task dictionary with identification info
            
        Returns:
            bool: True if collection should be skipped, False otherwise
        """
        self._skip_stats['total_checks'] += 1
        
        try:
            exists = db_service.check_team_data_exists(
                task['match_id'],
                task['team_id'],
                task['match_date'],
                self._team_skip_fields
            )
            
            if exists:
                self._skip_stats['team_skips'] += 1
                logger.debug(
                    f"⏭️ Skipping team collection for {task['team_name']} - "
                    f"data exists ({self._team_skip_fields})"
                )
                return True
            
            return False
            
        except Exception as e:
            logger.debug(f"Failed to check team skip condition for {task.get('team_name')}: {e}")
            return False
    
    async def should_skip_pitcher_collection(self, task: Dict[str, Any]) -> bool:
        """
        Determine if pitcher data collection should be skipped.
        
        Checks if pitcher data already exists for the given match, team, and date.
        
        Args:
            task: Team task dictionary with pitcher identification info
            
        Returns:
            bool: True if collection should be skipped, False otherwise
        """
        self._skip_stats['total_checks'] += 1
        
        try:
            exists = db_service.check_pitcher_data_exists(
                task['match_id'],
                task['team_id'],
                task['match_date'],
                self._pitcher_skip_fields
            )
            
            if exists:
                self._skip_stats['pitcher_skips'] += 1
                pitcher_name = task.get('pitcher_name', 'Unknown')
                logger.debug(
                    f"⏭️ Skipping pitcher collection for {pitcher_name} - "
                    f"data exists ({self._pitcher_skip_fields})"
                )
                return True
            
            return False
            
        except Exception as e:
            pitcher_name = task.get('pitcher_name', 'Unknown')
            logger.debug(f"Failed to check pitcher skip condition for {pitcher_name}: {e}")
            return False
    
    async def should_skip_both(self, task: Dict[str, Any]) -> tuple[bool, bool]:
        """
        Check skip conditions for both team and pitcher data.
        
        Args:
            task: Team task dictionary
            
        Returns:
            Tuple of (skip_team, skip_pitcher) boolean flags
        """
        skip_team = await self.should_skip_team_collection(task)
        skip_pitcher = await self.should_skip_pitcher_collection(task)
        
        return skip_team, skip_pitcher
    
    def configure_team_skip_fields(self, fields: List[str]) -> None:
        """
        Configure which fields to check for team skip logic.
        
        Args:
            fields: List of database field names to check
        """
        if not fields:
            logger.warning("Empty fields list provided for team skip configuration")
            return
        
        self._team_skip_fields = fields.copy()
        logger.debug(f"Team skip fields configured: {self._team_skip_fields}")
    
    def configure_pitcher_skip_fields(self, fields: List[str]) -> None:
        """
        Configure which fields to check for pitcher skip logic.
        
        Args:
            fields: List of database field names to check
        """
        if not fields:
            logger.warning("Empty fields list provided for pitcher skip configuration")
            return
        
        self._pitcher_skip_fields = fields.copy()
        logger.debug(f"Pitcher skip fields configured: {self._pitcher_skip_fields}")
    
    def get_skip_stats(self) -> Dict[str, Any]:
        """
        Get skip logic statistics.
        
        Returns:
            Dictionary with skip statistics and ratios
        """
        total_checks = self._skip_stats['total_checks']
        team_skips = self._skip_stats['team_skips']
        pitcher_skips = self._skip_stats['pitcher_skips']
        
        return {
            'total_checks': total_checks,
            'team_skips': team_skips,
            'pitcher_skips': pitcher_skips,
            'team_skip_ratio': team_skips / max(total_checks, 1),
            'pitcher_skip_ratio': pitcher_skips / max(total_checks, 1),
            'total_skips': team_skips + pitcher_skips,
            'current_team_fields': self._team_skip_fields.copy(),
            'current_pitcher_fields': self._pitcher_skip_fields.copy()
        }
    
    def reset_stats(self) -> None:
        """Reset skip statistics counters."""
        self._skip_stats = {
            'team_skips': 0,
            'pitcher_skips': 0,
            'total_checks': 0
        }
        logger.debug("Skip logic statistics reset")
    
    def log_skip_summary(self) -> None:
        """Log a summary of skip logic performance."""
        stats = self.get_skip_stats()
        
        if stats['total_checks'] > 0:
            logger.info(
                f"📊 Skip Logic Summary: "
                f"{stats['total_skips']}/{stats['total_checks']} skipped "
                f"(Team: {stats['team_skips']}, Pitcher: {stats['pitcher_skips']})"
            )
        else:
            logger.debug("No skip logic checks performed")


class SkipPolicy:
    """
    Configurable skip policies for different scenarios.
    
    Allows for different skip behavior based on league, data type,
    or other contextual factors.
    """
    
    def __init__(self, name: str):
        """
        Initialize skip policy.
        
        Args:
            name: Policy name for identification
        """
        self.name = name
        self.team_fields = ['season_summary', 'recent_games_summary']
        self.pitcher_fields = ['pitcher_profile', 'pitcher_stats']
        self.enabled = True
        self.league_specific = {}
    
    def set_team_fields(self, fields: List[str]) -> 'SkipPolicy':
        """Set team fields to check. Returns self for chaining."""
        self.team_fields = fields.copy()
        return self
    
    def set_pitcher_fields(self, fields: List[str]) -> 'SkipPolicy':
        """Set pitcher fields to check. Returns self for chaining."""
        self.pitcher_fields = fields.copy()
        return self
    
    def enable(self) -> 'SkipPolicy':
        """Enable this policy. Returns self for chaining."""
        self.enabled = True
        return self
    
    def disable(self) -> 'SkipPolicy':
        """Disable this policy. Returns self for chaining."""
        self.enabled = False
        return self
    
    def for_league(self, league: str, team_fields: List[str], pitcher_fields: List[str]) -> 'SkipPolicy':
        """Set league-specific field configuration. Returns self for chaining."""
        self.league_specific[league] = {
            'team_fields': team_fields.copy(),
            'pitcher_fields': pitcher_fields.copy()
        }
        return self
    
    def get_fields_for_league(self, league: str, data_type: str) -> List[str]:
        """
        Get fields to check for specific league and data type.
        
        Args:
            league: League name (KBO, MLB, NPB)
            data_type: 'team' or 'pitcher'
            
        Returns:
            List of field names to check
        """
        if not self.enabled:
            return []
        
        league_config = self.league_specific.get(league)
        if league_config:
            return league_config.get(f'{data_type}_fields', [])
        
        return getattr(self, f'{data_type}_fields', [])


# Predefined skip policies
DEFAULT_SKIP_POLICY = SkipPolicy('default').set_team_fields([
    'season_summary', 'recent_games_summary'
]).set_pitcher_fields([
    'pitcher_profile', 'pitcher_stats'
])

CONSERVATIVE_SKIP_POLICY = SkipPolicy('conservative').set_team_fields([
    'season_summary', 'recent_games_summary', 'team_batting', 'team_pitching'
]).set_pitcher_fields([
    'pitcher_profile', 'pitcher_stats', 'season_stats'
])

AGGRESSIVE_SKIP_POLICY = SkipPolicy('aggressive').set_team_fields([
    'season_summary'
]).set_pitcher_fields([
    'pitcher_profile'
])