"""
Baseball Orchestration Service
Single Responsibility: High-level coordination of baseball data collection workflow
"""
from __future__ import annotations

import asyncio
from typing import Any, Dict, List, Optional

from supabase import Client

from database.database import save_team_and_pitcher_stats_unified
from sports.baseball.services.task_manager import BaseballTaskManager
from sports.baseball.services.skip_logic_service import Skip<PERSON>ogicService
from sports.baseball.services.processors.team_data_processor import TeamDataProcessor
from sports.baseball.services.processors.pitcher_data_processor import PitcherDataProcessor
from sports.baseball.collectors.browser_manager import TeamStatsBrowser
from sports.baseball.collectors.pitcher_crawler import PitcherCrawler
from sports.baseball.services.stats_collector import TeamStatsCollector
from core.utils.memory_monitor import MemoryMonitor
from utils.service_utils import get_team_mappings
from utils.logger import Logger

logger = Logger(__name__)


class BaseballOrchestrator:
    """
    Orchestrates the complete baseball data collection workflow.
    
    This service coordinates all aspects of baseball data collection by
    delegating specific responsibilities to focused services. Follows
    the Orchestrator pattern and Single Responsibility Principle.
    
    Responsibilities:
    - Coordinate the overall workflow
    - Manage component initialization
    - Handle resource cleanup
    - Provide workflow statistics
    - Manage error recovery
    """
    
    def __init__(self) -> None:
        """Initialize the baseball orchestrator."""
        # Core services
        self.task_manager = BaseballTaskManager()
        self.skip_service = SkipLogicService()
        
        # Data processors
        self.team_processor: Optional[TeamDataProcessor] = None
        self.pitcher_processor: Optional[PitcherDataProcessor] = None
        
        # Browser and crawler components
        self.browser: Optional[TeamStatsBrowser] = None
        self.pitcher_crawler: Optional[PitcherCrawler] = None
        
        # Utilities
        self.memory_monitor = MemoryMonitor(threshold_mb=400.0)
        self._team_mappings: Dict[str, str] = {}
        self._initialized = False
        
        # Statistics
        self._workflow_stats = {
            'total_games': 0,
            'total_tasks': 0,
            'teams_processed': 0,
            'pitchers_processed': 0,
            'teams_saved': 0,
            'pitchers_saved': 0,
            'errors': 0
        }
    
    async def process_games(
        self, 
        games: List[Dict[str, Any]], 
        client: Client
    ) -> Dict[str, int]:
        """
        Main entry point for processing baseball games.
        
        Coordinates the complete workflow from game data to database storage.
        
        Args:
            games: List of game data dictionaries
            client: Authenticated Supabase client
            
        Returns:
            Dictionary with processing results: {'teams_saved': int, 'pitchers_saved': int}
        """
        try:
            # Initialize components
            await self._initialize_components()
            
            if not games:
                logger.warning("No games provided for processing")
                return {'teams_saved': 0, 'pitchers_saved': 0}
            
            # Update statistics
            self._workflow_stats['total_games'] = len(games)
            
            # Create tasks from games
            tasks = self.task_manager.create_team_tasks(games)
            self._workflow_stats['total_tasks'] = len(tasks)
            
            if not tasks:
                logger.warning("No valid tasks created from games")
                return {'teams_saved': 0, 'pitchers_saved': 0}
            
            # Log task statistics
            task_stats = self.task_manager.get_task_stats(tasks)
            logger.info(
                f"🔄 [Baseball] Processing {task_stats['total_tasks']} tasks "
                f"across {len(task_stats['leagues'])} leagues"
            )
            
            # Process tasks sequentially (simplified approach)
            results = await self._process_tasks_sequential(tasks, client)
            
            # Log final statistics
            await self._log_final_stats()
            
            return results
            
        except Exception as e:
            self._workflow_stats['errors'] += 1
            logger.error(f"Baseball workflow failed: {e}")
            return {'teams_saved': 0, 'pitchers_saved': 0}
        finally:
            await self._cleanup_resources()
    
    async def _process_tasks_sequential(
        self, 
        tasks: List[Dict[str, Any]], 
        client: Client
    ) -> Dict[str, int]:
        """
        Process tasks sequentially with skip logic optimization.
        
        Args:
            tasks: List of team tasks
            client: Supabase client
            
        Returns:
            Dictionary with processing results
        """
        teams_saved = 0
        pitchers_saved = 0
        
        for i, task in enumerate(tasks, 1):
            try:
                team_name = task.get('team_name', 'Unknown')
                pitcher_name = task.get('pitcher_name', '')
                
                logger.debug(f"🔄 Processing team ({i}/{len(tasks)}): {team_name}")
                self._workflow_stats['teams_processed'] += 1
                
                # Check skip conditions
                skip_team, skip_pitcher = await self.skip_service.should_skip_both(task)
                
                # Collect team data if needed
                team_stats = None
                if not skip_team and self.team_processor:
                    team_stats = await self.team_processor.collect_team_stats(task)
                
                # If team collection was skipped, get existing data for pitcher extraction
                if skip_team and not team_stats:
                    try:
                        result = client.table('baseball_stats').select('recent_games').eq('team_id', task['team_id']).limit(1).execute()
                        if result.data and result.data[0].get('recent_games'):
                            team_stats = {'recent_games': result.data[0]['recent_games']}
                            logger.debug(f"🔄 Retrieved existing team data for pitcher extraction: {task['team_name']}")
                    except Exception as e:
                        logger.debug(f"Could not retrieve existing team data: {e}")
                
                # Extract pitcher names from team data and collect pitcher stats
                pitcher_data = None
                if team_stats and not skip_pitcher and self.pitcher_processor:
                    pitcher_names = self._extract_pitcher_names_from_team_data(team_stats)
                    
                    if pitcher_names:
                        team_url = task.get('team_url') or self._generate_team_url(
                            task['league'], task['team_id']
                        )
                        
                        # Collect data for the most recent starting pitcher
                        recent_pitcher_name = pitcher_names[0]  # Use most recent
                        logger.debug(f"🎯 Collecting pitcher data for: {recent_pitcher_name}")
                        
                        pitcher_data = await self.pitcher_processor.collect_pitcher_data(
                            recent_pitcher_name, team_url, task
                        )
                        
                        if pitcher_data:
                            self._workflow_stats['pitchers_processed'] += 1
                
                # Save data if we have any new information
                if (team_stats and 'error' not in team_stats) or skip_team:
                    success = await self._save_unified_data(
                        client, task, team_stats, pitcher_data, skip_team
                    )
                    
                    if success:
                        teams_saved += 1
                        if pitcher_data:
                            pitchers_saved += 1
                
                # Rate limiting between teams
                if i < len(tasks):
                    await asyncio.sleep(1)
                    
            except Exception as e:
                self._workflow_stats['errors'] += 1
                logger.error(f"❌ Failed to process team {task.get('team_name')}: {e}")
        
        # Update final statistics
        self._workflow_stats['teams_saved'] = teams_saved
        self._workflow_stats['pitchers_saved'] = pitchers_saved
        
        return {
            'teams_saved': teams_saved,
            'pitchers_saved': pitchers_saved
        }
    
    async def _save_unified_data(
        self,
        client: Client,
        task: Dict[str, Any],
        team_stats: Optional[Dict[str, Any]],
        pitcher_data: Optional[Dict[str, Any]],
        skip_team_collection: bool
    ) -> bool:
        """
        Save team and pitcher data to database.
        
        Args:
            client: Supabase client
            task: Task information
            team_stats: Team statistics data
            pitcher_data: Pitcher data
            skip_team_collection: Whether team collection was skipped
            
        Returns:
            True if save was successful, False otherwise
        """
        try:
            # Initialize team_stats if skipping collection
            if skip_team_collection and team_stats is None:
                team_stats = {}
            
            # Wrap pitcher_data in list for unified function
            pitcher_data_list = [pitcher_data] if pitcher_data else None
            
            # Save to database
            success = save_team_and_pitcher_stats_unified(
                client, 
                task['team_id'], 
                team_stats, 
                pitcher_data_list, 
                task['match_date']
            )
            
            if success:
                logger.debug(f"✅ Saved data for {task['team_name']}")
            else:
                logger.warning(f"⚠️ Failed to save data for {task['team_name']}")
            
            return success
            
        except Exception as e:
            logger.error(f"Database save failed for {task.get('team_name')}: {e}")
            return False
    
    def _generate_team_url(self, league_enum: Any, team_id: str) -> str:
        """
        Generate team URL for data collection.
        
        Args:
            league_enum: League enumeration
            team_id: Team identifier
            
        Returns:
            Team URL string
        """
        try:
            from config.config import create_team_url, get_league_id
            league_id = get_league_id(league_enum)
            return create_team_url("BS", league_id, team_id)
        except Exception as e:
            logger.error(f"Failed to generate team URL for {team_id}: {e}")
            return f"https://betman.co.kr/team/{team_id}"  # Fallback
    
    async def _initialize_components(self) -> bool:
        """
        Initialize all required components for data collection.
        
        Returns:
            True if initialization successful, False otherwise
        """
        if self._initialized:
            return True
        
        try:
            # Initialize browser and crawler
            self.browser = TeamStatsBrowser()
            await self.browser.initialize()
            
            self.pitcher_crawler = PitcherCrawler()
            
            # Get team mappings
            self._team_mappings = await get_team_mappings()
            
            # Initialize collectors
            team_collector = TeamStatsCollector(self.browser, self._team_mappings)
            
            # Initialize processors
            self.team_processor = TeamDataProcessor(team_collector)
            self.pitcher_processor = PitcherDataProcessor(self.pitcher_crawler)
            
            self._initialized = True
            logger.info("✅ Baseball orchestrator components initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize baseball orchestrator: {e}")
            return False
    
    async def _cleanup_resources(self) -> None:
        """Clean up all resources and connections."""
        try:
            if self.browser:
                await self.browser.cleanup()
            if self.pitcher_crawler:
                await self.pitcher_crawler.cleanup()
            
            logger.debug("🧹 Baseball orchestrator cleanup completed")
            
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")
    
    async def _log_final_stats(self) -> None:
        """Log final workflow statistics."""
        stats = self._workflow_stats
        skip_stats = self.skip_service.get_skip_stats()
        
        logger.info(
            f"✅ [Baseball] Workflow Complete: "
            f"Games: {stats['total_games']}, "
            f"Tasks: {stats['total_tasks']}, "
            f"Teams Saved: {stats['teams_saved']}, "
            f"Pitchers Saved: {stats['pitchers_saved']}"
        )
        
        if skip_stats['total_checks'] > 0:
            logger.info(
                f"📊 [Baseball] Skip Logic: "
                f"{skip_stats['total_skips']}/{skip_stats['total_checks']} skipped "
                f"({skip_stats['team_skips']} teams, {skip_stats['pitcher_skips']} pitchers)"
            )
        
        if stats['errors'] > 0:
            logger.warning(f"⚠️ [Baseball] Errors encountered: {stats['errors']}")
    
    def get_workflow_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive workflow statistics.
        
        Returns:
            Dictionary with workflow and skip statistics
        """
        stats = self._workflow_stats.copy()
        stats['skip_stats'] = self.skip_service.get_skip_stats()
        stats['memory_stats'] = {
            'current_mb': self.memory_monitor.get_current_usage(),
            'threshold_mb': self.memory_monitor.threshold_mb
        }
        return stats
    
    def _extract_pitcher_names_from_team_data(self, team_stats: Dict[str, Any]) -> List[str]:
        """
        Extract starting pitcher names from team statistics data.
        
        Searches through nested recent games JSONB structure to find starting pitcher names
        that were parsed during team data collection.
        
        Data structure: recent_games -> recent_N_home/away_games -> date -> home/away -> starting_pitcher
        
        Args:
            team_stats: Team statistics dictionary containing recent games
            
        Returns:
            List of pitcher names found, in order of most recent first
        """
        pitcher_names = []
        
        try:
            recent_games = team_stats.get('recent_games', {})
            
            # Search through recent games categories (recent_5_home_games, recent_5_away_games, etc.)
            for category, games_by_date in recent_games.items():
                if isinstance(games_by_date, dict):
                    logger.debug(f"🔍 Searching {category} ({len(games_by_date)} games)")
                    
                    # Each category contains games indexed by date
                    for date, game_data in games_by_date.items():
                        if isinstance(game_data, dict):
                            # Each game has 'home' and 'away' data
                            for side in ['home', 'away']:
                                side_data = game_data.get(side, {})
                                if isinstance(side_data, dict):
                                    pitcher_name = side_data.get('starting_pitcher', '').strip()
                                    if pitcher_name and pitcher_name not in pitcher_names:
                                        pitcher_names.append(pitcher_name)
                                        logger.debug(f"🎯 Found pitcher: {pitcher_name} ({date}, {side})")
            
            logger.info(f"🎯 Extracted {len(pitcher_names)} unique pitcher names: {pitcher_names}")
            return pitcher_names
            
        except Exception as e:
            logger.warning(f"Failed to extract pitcher names: {e}")
            return []
    
    def reset_stats(self) -> None:
        """Reset all statistics counters."""
        self._workflow_stats = {
            'total_games': 0,
            'total_tasks': 0,
            'teams_processed': 0,
            'pitchers_processed': 0,
            'teams_saved': 0,
            'pitchers_saved': 0,
            'errors': 0
        }
        self.skip_service.reset_stats()
        logger.debug("Workflow statistics reset")