"""
Team Data Processor
Single Responsibility: Team statistics collection and processing only
"""
import asyncio
from typing import Dict, List, Optional, Any

from sports.baseball.services.stats_collector import TeamStatsCollector
from utils.logger import Logger

logger = Logger(__name__)


class TeamDataProcessor:
    """
    Handles team statistics collection and processing for baseball teams.
    
    This processor focuses solely on collecting and validating team statistics
    from various baseball leagues (KBO, MLB, NPB). Extracted from the larger
    unified service for better separation of concerns and maintainability.
    
    Attributes:
        team_collector: TeamStatsCollector instance for data collection
    """
    
    def __init__(self, team_collector: TeamStatsCollector) -> None:
        """Initialize the team data processor with a statistics collector.
        
        Args:
            team_collector: Configured TeamStatsCollector for data collection
        """
        self.team_collector: TeamStatsCollector = team_collector
    
    async def collect_team_stats(self, task: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Collect comprehensive team statistics for a single team.
        
        Fetches team performance data including season summary, recent games,
        batting/pitching statistics, and other relevant metrics.
        
        Args:
            task: Team task dictionary containing:
                - team_id: Unique identifier for the team
                - team_name: Display name of the team
                - team_url: Optional URL for team statistics page
                - league: League enum (KBO, MLB, NPB)
            
        Returns:
            Dictionary containing team statistics with keys like:
            - season_summary: Overall season performance
            - recent_games_summary: Recent 5 games performance
            - team_batting: Batting statistics
            - team_pitching: Pitching statistics
            Returns None if collection fails
        """
        try:
            team_id = task['team_id']
            team_name = task['team_name']
            team_url = task.get('team_url')
            league_enum = task['league']
            
            # Generate team URL if not provided
            if not team_url:
                team_url = self._generate_team_url(league_enum, team_id)
                logger.debug(f"🔗 {team_name} URL generated: {team_url}")
            
            # Collect team statistics
            team_stats = await self.team_collector.collect_team_stats(
                team_id=team_id,
                team_url=team_url,
                team_name=team_name,
                league=league_enum
            )
            
            # Add delay for rate limiting
            await asyncio.sleep(2)
            
            return team_stats
            
        except Exception as e:
            logger.error(f"Team data collection failed for {task.get('team_name', 'Unknown')}: {e}")
            return None
    
    def _generate_team_url(self, league_enum: Any, team_id: str) -> str:
        """
        Generate team statistics URL for the given league and team.
        
        Creates a properly formatted URL for accessing team statistics
        on the betman.co.kr platform based on league and team identifiers.
        
        Args:
            league_enum: League enumeration (KBO, MLB, NPB)
            team_id: Unique team identifier
            
        Returns:
            str: Formatted URL for team statistics or empty string if failed
        """
        try:
            from config.config import create_team_url, get_league_id
            league_id = get_league_id(league_enum)
            return create_team_url("BS", league_id, team_id)
        except Exception as e:
            logger.error(f"Failed to generate team URL for {team_id}: {e}")
            return ""
    
    def is_team_stats_valid(self, team_stats: Dict[str, Any]) -> bool:
        """
        Validate team statistics data for completeness and correctness.
        
        Checks if the team statistics dictionary contains all required
        fields and that the data is not empty or in an error state.
        
        Args:
            team_stats: Dictionary containing team statistics data
            
        Returns:
            bool: True if statistics are valid and complete, False otherwise
        """
        if not team_stats or 'error' in team_stats:
            return False
        
        # Check for required fields
        required_fields = ['season_summary', 'recent_games_summary']
        return all(
            team_stats.get(field) and len(team_stats.get(field, {})) > 0
            for field in required_fields
        )
    
    def should_skip_team_collection(self, team_stats: Optional[Dict[str, Any]]) -> bool:
        """
        Determine if team collection should be skipped based on existing data.
        
        Evaluates whether existing team statistics are recent and valid enough
        to skip re-collection, helping to optimize performance and reduce
        unnecessary API calls.
        
        Args:
            team_stats: Existing team statistics dictionary or None
            
        Returns:
            bool: True if collection should be skipped, False if collection needed
        """
        return team_stats is not None and self.is_team_stats_valid(team_stats)