"""
Unified Baseball Service - Legacy Compatibility Wrapper
Maintains backward compatibility while delegating to new modular services
"""
from typing import Any, Dict, List

from supabase import Client

from sports.baseball.services.baseball_orchestrator import BaseballOrchestrator
from utils.logger import Logger

logger = Logger(__name__)


class UnifiedBaseballService:
    """
    Legacy compatibility wrapper for the new modular baseball system.
    
    This service maintains backward compatibility with existing code
    while delegating all functionality to the new BaseballOrchestrator
    and focused service classes.
    
    DEPRECATED: New code should use BaseballOrchestrator directly.
    This wrapper will be removed in a future version.
    """

    def __init__(self) -> None:
        """Initialize the unified baseball service wrapper."""
        self.orchestrator = BaseballOrchestrator()
        
        # Legacy compatibility attributes
        self._cache = None
        logger.warning(
            "UnifiedBaseballService is deprecated. "
            "Use BaseballOrchestrator directly for new code."
        )
    
    async def process_teams_with_pitchers(
        self,
        games: List[Dict[str, Any]],
        client: Client
    ) -> Dict[str, int]:
        """
        Legacy wrapper for processing teams and pitchers.
        
        DEPRECATED: Use BaseballOrchestrator.process_games() directly.
        
        Args:
            games: List of game data dictionaries
            client: Authenticated Supabase client
            
        Returns:
            Dictionary with counts: {'teams_saved': int, 'pitchers_saved': int}
        """
        logger.info("🔄 [야구] Using legacy UnifiedBaseballService (consider migrating to BaseballOrchestrator)")
        return await self.orchestrator.process_games(games, client)
    
    # Legacy compatibility methods that delegate to orchestrator
    def get_workflow_stats(self) -> Dict[str, Any]:
        """Get workflow statistics (legacy compatibility)."""
        return self.orchestrator.get_workflow_stats()
    
    def reset_stats(self) -> None:
        """Reset statistics (legacy compatibility)."""
        self.orchestrator.reset_stats()
    
    async def cleanup(self) -> None:
        """Cleanup resources (legacy compatibility)."""
        await self.orchestrator._cleanup_resources()