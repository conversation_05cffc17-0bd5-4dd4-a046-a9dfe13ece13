"""
Pitcher Data Processor
Single Responsibility: Pitcher statistics collection and processing only
"""
from typing import Any, Dict, Optional, Union

from utils.logger import Logger

logger = Logger(__name__)


class PitcherDataProcessor:
    """
    Handles pitcher statistics collection and processing for baseball pitchers.
    
    This processor focuses solely on collecting, validating, and structuring
    pitcher-specific data including profiles, statistics, and performance metrics.
    Extracted from the larger unified service for better separation of concerns.
    
    Attributes:
        pitcher_crawler: PitcherCrawler instance for data collection
    """
    
    def __init__(self, pitcher_crawler: Any) -> None:
        """Initialize the pitcher data processor with a crawler.
        
        Args:
            pitcher_crawler: Configured PitcherCrawler for data collection
        """
        self.pitcher_crawler: Any = pitcher_crawler
    
    async def collect_pitcher_data(
        self,
        pitcher_name: str,
        team_url: str,
        task: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """선발투수 데이터 수집 (old 코드 그대로 복사)"""
        if not pitcher_name or not pitcher_name.strip():
            return None
        
        pitcher_crawler = None
        try:
            # 독립적인 투수 크롤러 생성
            import asyncio

            from sports.baseball.collectors.pitcher_crawler import \
                PitcherCrawler
            
            pitcher_crawler = PitcherCrawler()
            await pitcher_crawler.initialize_browser()
            
            # 투수 크롤러 초기화 후 충분한 대기 시간
            await asyncio.sleep(3)
            
            # 투수 데이터 수집
            pitcher_stats = await pitcher_crawler.crawl_pitcher_stats(
                pitcher_name=pitcher_name,
                team_url=team_url
            )
            
            # 투수 데이터 수집 후 충분한 대기 시간
            await asyncio.sleep(2)
            
            # 🎯 투수 데이터 검증 개선: 더 관대한 검증 (빈 데이터도 허용)
            has_name = bool(pitcher_stats.get('name'))
            has_season_stats = bool(
                pitcher_stats.get('season_stats') and 
                len(pitcher_stats.get('season_stats', [])) > 0
            )
            has_recent_games = bool(
                pitcher_stats.get('recent_5_games') or 
                pitcher_stats.get('recent_games') or
                any(k.startswith('recent_') and k.endswith('_games') 
                    for k in pitcher_stats.keys())
            )
            
            # 🚀 NPB/KBO 투수는 투수명만 있어도 유효 데이터로 처리
            league_obj = task.get('league', '')
            if hasattr(league_obj, 'value'):
                league = league_obj.value.upper()
            else:
                league = str(league_obj).upper()
            if league in ['NPB', 'KBO']:
                is_valid_data = bool(pitcher_name.strip())  # 투수명만 있으면 OK
                logger.debug(
                    f"🎯 {league} 투수 {pitcher_name}: 투수명 기반 처리 "
                    f"(name={has_name}, season={has_season_stats}, "
                    f"recent={has_recent_games})"
                )
            else:
                # MLB는 기존 검증 유지
                is_valid_data = has_name or has_season_stats or has_recent_games
            
            if is_valid_data:
                structured_data = self._structure_pitcher_data(
                    pitcher_name, pitcher_stats, task
                )
                return structured_data
            else:
                name = (
                    pitcher_stats.get('name', 'No Name') 
                    if pitcher_stats else 'No Name'
                )
                recent = (
                    bool(pitcher_stats.get('recent_5_games')) 
                    if pitcher_stats else False
                )
                season = (
                    bool(pitcher_stats.get('season_stats')) 
                    if pitcher_stats else False
                )
                logger.warning(
                    f"❌ {pitcher_name} 투수 데이터 없음 - "
                    f"name: {name}, recent_games: {recent}, "
                    f"season_stats: {season}"
                )
                return None
                
        except Exception as e:
            logger.warning(f"❌ {pitcher_name} 투수 데이터 수집 실패: {e}")
            return None
        finally:
            if pitcher_crawler:
                try:
                    await pitcher_crawler.cleanup()
                except Exception:
                    pass
    
    def _safe_int(self, value: Any) -> int:
        """안전하게 정수로 변환"""
        try:
            if isinstance(value, str):
                # kg 제거
                value = value.replace('kg', '').strip()
            return int(float(value)) if value else 0
        except (ValueError, TypeError):
            return 0

    def _safe_float(self, value: Any) -> float:
        """안전하게 실수로 변환"""
        try:
            return float(value) if value else 0.0
        except (ValueError, TypeError):
            return 0.0

    def _structure_pitcher_data(
        self,
        pitcher_name: str,
        pitcher_stats: Dict[str, Any],
        task: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Structure raw pitcher data into standardized format.
        
        Transforms raw crawler data into a consistent, clean format
        suitable for database storage and API responses.
        
        Args:
            pitcher_name: Official pitcher name for identification
            pitcher_stats: Raw pitcher data dictionary from crawler
            task: Complete task dictionary with team and league info
            
        Returns:
            Dictionary with structured pitcher data containing:
            - pitcher_name: Standardized pitcher name
            - pitcher_profile: Cleaned profile information
            - pitcher_stats: Cleaned statistical data
        """
        try:
            position = task.get('position', 'starting_pitcher')
            game_role = task.get('game_role', 'starter')
            team_name = task.get('team_name', '')
            
            # 🎯 리그 정보 추출
            league_obj = task.get('league', '')
            if hasattr(league_obj, 'value'):
                league = league_obj.value
            else:
                league = str(league_obj)
            
            # 🎯 팀 이름 매핑 적용 (리그별 정책: KBO/NPB=짧은이름, MLB=풀네임)
            from sports.baseball.utils.database_service import db_service
            from database.database import get_team_display_name
            try:
                client = db_service.get_client()
                if client and team_name:
                    mapped_team_name = get_team_display_name(
                        client, team_name, league
                    )
                    if mapped_team_name != team_name:
                        logger.debug(
                            f"🔄 팀명 매핑 ({league}): {team_name} → {mapped_team_name}"
                        )
                        team_name = mapped_team_name
            except Exception as e:
                logger.debug(f"팀명 매핑 실패: {e}")
            
            # 🎯 투수 프로필 구성 (old 코드와 같은 중첩 구조 사용)
            pitcher_profile = {
                'profile': {
                    'player_number': self._safe_int(pitcher_stats.get('no', 0)),
                    'name': pitcher_stats.get('name', pitcher_name),
                    'team_name': team_name,  # 매핑된 팀명 사용
                    'birth_date': pitcher_stats.get('birth', ''),
                    'height_cm': self._safe_float(pitcher_stats.get('height', 0)),
                    'weight_kg': self._safe_int(
                        str(pitcher_stats.get('weight', '')).replace('kg', '')
                    ),
                    'league': league,
                    'position': position,
                    'game_role': game_role,
                    'season_stats_summary': pitcher_stats.get('season_summary', {})
                }
            }
            
            # 🚀 시즌 통계 검증 및 처리 개선
            season_stats = pitcher_stats.get("season_stats", [])
            
            # 시즌 통계가 빈 배열이거나 무효한 경우 처리
            if not season_stats or (
                isinstance(season_stats, list) and len(season_stats) == 0
            ):
                logger.warning(
                    f"⚠️ {pitcher_name} 시즌 통계가 비어있음 - 기본 구조 생성"
                )
                season_stats = [{
                    "year": "2025",
                    "games": "데이터 없음",
                    "wins": "0",
                    "losses": "0", 
                    "era": "0.00",
                    "innings": "0.0"
                }]
            
            # 🎯 투수 통계 구성 (season_stats 맨 앞 배치)
            pitcher_stats_data = {}
            
            # 1. 시즌 통계 (맨 앞)
            pitcher_stats_data["season_stats"] = season_stats
            
            # 2. 최근 경기 데이터 처리 (중복 완전 제거)
            # 2-1. 동적 키 우선 처리 (recent_N_games, recent_N_games_summary)
            dynamic_keys = [
                k for k in pitcher_stats.keys() 
                if k.startswith('recent_') and ('_games' in k) 
                and k not in ['recent_games', 'recent_games_summary']  # 기존 키 제외
            ]
            
            # 동적 키 데이터 복사
            for key in dynamic_keys:
                pitcher_stats_data[key] = pitcher_stats[key]
            
            # 2-2. 기존 recent_games 처리 (중복 방지)
            if 'recent_games' in pitcher_stats:
                recent_games = pitcher_stats["recent_games"]
                n_games = len(recent_games) if isinstance(recent_games, dict) else 0
                if recent_games and n_games > 0:
                    recent_games_key = f"recent_{n_games}_games"
                    # 동적 키로 이미 처리된 경우 스킵
                    if recent_games_key not in pitcher_stats_data:
                        pitcher_stats_data[recent_games_key] = recent_games
            
            # 2-3. recent_games_summary 처리 (recent_5_games_summary 등)
            if 'recent_games_summary' in pitcher_stats:
                recent_summary = pitcher_stats["recent_games_summary"]
                # recent_games와 매칭하여 N 값 계산
                if 'recent_games' in pitcher_stats:
                    recent_games = pitcher_stats["recent_games"]
                    n_games = len(recent_games) if isinstance(recent_games, dict) else 5
                    summary_key = f"recent_{n_games}_games_summary"
                else:
                    summary_key = "recent_5_games_summary"  # 기본값
                
                # 동적 키로 이미 처리된 경우가 아니면 추가
                if summary_key not in pitcher_stats_data and recent_summary:
                    pitcher_stats_data[summary_key] = recent_summary
            
            return {
                'pitcher_name': pitcher_name,
                'pitcher_profile': pitcher_profile,
                'pitcher_stats': pitcher_stats_data
            }
            
        except Exception as e:
            logger.error(f"Failed to structure pitcher data for {pitcher_name}: {e}")
            return {
                'pitcher_name': pitcher_name,
                'pitcher_profile': {},
                'pitcher_stats': {}
            }
    
    def _clean_profile_data(self, profile_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Clean and validate pitcher profile data.
        
        Removes empty, null, or invalid entries and standardizes
        string formatting for consistent data storage.
        
        Args:
            profile_data: Raw profile data dictionary
            
        Returns:
            Dictionary with cleaned profile data as string values
        """
        if not isinstance(profile_data, dict):
            return {}
        
        # Remove empty or invalid entries
        cleaned = {}
        for key, value in profile_data.items():
            if value and str(value).strip() not in ['', 'None', 'null', '없음']:
                cleaned[key] = str(value).strip()
        
        return cleaned
    
    def _clean_stats_data(self, stats_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Clean and validate pitcher statistics data.
        
        Processes numeric statistics, handles nested data structures,
        and ensures consistent formatting for all statistical values.
        
        Args:
            stats_data: Raw statistics data dictionary
            
        Returns:
            Dictionary with cleaned and validated statistics
        """
        if not isinstance(stats_data, dict):
            return {}
        
        cleaned = {}
        for key, value in stats_data.items():
            # Handle numeric values
            if isinstance(value, (int, float)):
                cleaned[key] = self._safe_numeric_conversion(value)
            elif isinstance(value, str) and value.strip():
                # Try to convert string numbers
                cleaned[key] = self._safe_numeric_conversion(value)
            elif isinstance(value, dict):
                # Recursively clean nested dictionaries
                nested_clean = self._clean_stats_data(value)
                if nested_clean:
                    cleaned[key] = nested_clean
        
        return cleaned
    
    def _safe_numeric_conversion(self, value: Union[str, int, float, Any]) -> str:
        """
        Safely convert numeric values to clean string format.
        
        Handles various input types and edge cases while ensuring
        consistent string output for statistics storage.
        
        Args:
            value: Numeric value in any format (string, int, float)
            
        Returns:
            Clean string representation of the numeric value
        """
        try:
            if isinstance(value, str):
                value = value.strip()
                if not value or value.lower() in ['none', 'null', '없음', '-']:
                    return "0"
            
            # Convert to float first, then to clean string
            float_val = float(value)
            if float_val.is_integer():
                return str(int(float_val))
            else:
                return f"{float_val:.2f}".rstrip('0').rstrip('.')
                
        except (ValueError, TypeError):
            return "0"
    
    def is_pitcher_stats_complete(self, pitcher_stats: Dict[str, Any]) -> bool:
        """
        Check if pitcher statistics data is complete and valid.
        
        Validates that the pitcher data contains sufficient information
        for storage and analysis purposes.
        
        Args:
            pitcher_stats: Dictionary containing pitcher statistics and profile
            
        Returns:
            bool: True if statistics are complete and valid, False otherwise
        """
        if not pitcher_stats:
            return False
        
        # Check for required pitcher fields
        profile = pitcher_stats.get('pitcher_profile', {})
        stats = pitcher_stats.get('pitcher_stats', {})
        
        # At least profile or stats should have some data
        return bool(profile or stats)
    
    def is_pitcher_profile_complete(self, structured_data: Dict[str, Any]) -> bool:
        """
        Check if pitcher profile information is complete and usable.
        
        Validates that essential profile fields are present and contain
        meaningful data for player identification and analysis.
        
        Args:
            structured_data: Dictionary containing structured pitcher data
            
        Returns:
            bool: True if profile is complete with essential fields, False otherwise
        """
        if not structured_data:
            return False
        
        profile = structured_data.get('pitcher_profile', {})
        
        # Check if profile has essential fields
        essential_fields = ['team', 'position', 'age']
        return any(profile.get(field) for field in essential_fields)