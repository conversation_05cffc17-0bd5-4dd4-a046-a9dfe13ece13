"""
Baseball Task Manager
Single Responsibility: Task creation and management for baseball data collection
"""
from __future__ import annotations

import json
import os
from typing import Any, Dict, List, Optional
from enum import Enum

from sports.baseball.constants import LEAGUE_PRIORITIES, VALIDATION_RULES, FILE_PATHS
from sports.baseball.exceptions import Configuration<PERSON>rror, TaskError, ValidationError
from sports.baseball.utils.error_handler import <PERSON><PERSON>r<PERSON><PERSON>ler
from utils.logger import Logger

logger = Logger(__name__)


class BaseballTaskManager:
    """
    Manages task creation and organization for baseball data collection.
    
    This service handles the creation of team tasks from game data,
    pitcher information lookup, and task validation. Extracted from
    UnifiedBaseballService to follow Single Responsibility Principle.
    
    Responsibilities:
    - Create team tasks from game data
    - Load and manage pitcher configuration
    - Validate task data
    - Organize tasks by priority/league
    """
    
    def __init__(self) -> None:
        """Initialize the task manager."""
        self._pitcher_config: Optional[Dict[str, Any]] = None
        self._league_priorities = LEAGUE_PRIORITIES.copy()
    
    def create_team_tasks(self, games: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create team tasks from game data.
        
        Processes game data and creates individual team tasks for both
        home and away teams, including pitcher information lookup.
        
        Args:
            games: List of game data dictionaries
            
        Returns:
            List of team task dictionaries with team and pitcher info
        """
        if not games:
            logger.warning("No games provided for task creation")
            return []
        
        team_tasks = []
        
        for game in games:
            try:
                # Create home team task
                home_task = self._create_team_task(game, 'home')
                if home_task:
                    team_tasks.append(home_task)
                
                # Create away team task
                away_task = self._create_team_task(game, 'away')
                if away_task:
                    team_tasks.append(away_task)
                    
            except Exception as e:
                game_id = game.get('match_id', 'unknown')
                ErrorHandler.handle_error(
                    e, 
                    operation='create_team_tasks',
                    context={'game_id': game_id, 'game_data': game}
                )
        
        logger.info(f"🔄 [TaskManager] Created {len(team_tasks)} team tasks from {len(games)} games")
        return self._prioritize_tasks(team_tasks)
    
    def _create_team_task(self, game: Dict[str, Any], team_type: str) -> Optional[Dict[str, Any]]:
        """
        Create a single team task from game data.
        
        Args:
            game: Game data dictionary
            team_type: 'home' or 'away'
            
        Returns:
            Team task dictionary or None if creation fails
        """
        try:
            team_id_key = f'{team_type}_team_id'
            team_name_key = f'{team_type}_team'
            
            team_id = game.get(team_id_key)
            team_name = game.get(team_name_key)
            
            if not team_id or not team_name:
                logger.debug(f"Missing team data for {team_type} team in game {game.get('match_id')}")
                return None
            
            # Get league information and convert to enum
            league = game.get('league', 'Unknown')
            league_enum = self._get_league_enum(league)
            if not league_enum:
                logger.warning(f"Unknown league: {league}")
                return None
            
            # Get pitcher information
            pitcher_name = self._get_pitcher_name(game, team_type, team_id, league)
            
            return {
                'team_id': team_id,
                'team_name': team_name,
                'league': league_enum,
                'match_id': game.get('match_id'),
                'match_date': game.get('match_date'),
                'match_time': game.get('match_time'),
                'pitcher_name': pitcher_name,
                'team_role': team_type,
                'priority': self._league_priorities.get(league, 999)
            }
            
        except Exception as e:
            ErrorHandler.handle_error(
                e,
                operation='create_team_task',
                context={
                    'team_type': team_type,
                    'game_id': game.get('match_id'),
                    'team_id': team_id,
                    'team_name': team_name
                }
            )
            return None
    
    def _get_league_enum(self, league: Any) -> Optional[Any]:
        """
        Convert league string or enum to enum.
        
        Args:
            league: League name string or League enum object
            
        Returns:
            League enum or None if invalid
        """
        try:
            from config.config import League
            
            # Handle enum objects
            if hasattr(league, 'value'):
                # It's already an enum, return as-is
                return league
            
            # Handle string values
            league_str = str(league).upper()
            if league_str == 'KBO':
                return League.KBO
            elif league_str == 'MLB':
                return League.MLB
            elif league_str == 'NPB':
                return League.NPB
            else:
                return None
        except ImportError as e:
            raise ConfigurationError(
                "Failed to import League enum - configuration module not available",
                missing_key='League',
                cause=e
            )
    
    def _get_pitcher_name(
        self, 
        game: Dict[str, Any], 
        team_type: str, 
        team_id: str, 
        league: str
    ) -> str:
        """
        Get pitcher name from game data or pitcher configuration.
        
        Args:
            game: Game data dictionary
            team_type: 'home' or 'away'
            team_id: Team identifier
            league: League name
            
        Returns:
            Pitcher name or empty string if not found
        """
        # First try to get pitcher from game data
        pitcher_name = game.get(f'{team_type}_pitcher', '')
        
        if not pitcher_name:
            # Look up in pitcher configuration
            pitcher_info = self._get_pitcher_from_config(team_id, league)
            if pitcher_info:
                pitcher_name = pitcher_info['name']
                logger.debug(f"🎯 pitcher.json에서 선발투수 찾음: {team_id} -> {pitcher_name}")
        
        return pitcher_name
    
    def _get_pitcher_from_config(self, team_id: str, league: str) -> Optional[Dict[str, Any]]:
        """
        Get pitcher information from pitcher.json configuration.
        
        Args:
            team_id: Team identifier
            league: League name
            
        Returns:
            Pitcher information dictionary or None if not found
        """
        try:
            config = self._load_pitcher_config()
            if not config:
                return None
            
            manual_pitchers = config.get('manual_pitchers', {})
            
            for pitcher_name, pitcher_info in manual_pitchers.items():
                if (pitcher_info.get('team') == team_id and 
                    pitcher_info.get('league') == league and
                    pitcher_info.get('active', False)):
                    return {
                        'name': pitcher_name,
                        'direct_url': pitcher_info.get('direct_url'),
                        'team': pitcher_info.get('team'),
                        'league': pitcher_info.get('league')
                    }
            
            return None
            
        except Exception as e:
            ErrorHandler.handle_error(
                e,
                operation='pitcher_config_lookup',
                context={'team_id': team_id, 'league': league}
            )
            return None
    
    def _load_pitcher_config(self) -> Optional[Dict[str, Any]]:
        """
        Load pitcher configuration from pitcher.json file.
        
        Returns:
            Pitcher configuration dictionary or None if loading fails
        """
        if self._pitcher_config is not None:
            return self._pitcher_config
        
        try:
            # Build path to pitcher.json (from task_manager.py to project root)
            # Current file: /sports/baseball/services/task_manager.py
            # Target: /core/config/resources/pitcher.json
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            pitcher_json_path = os.path.join(project_root, 'core', 'config', 'resources', 'pitcher.json')
            
            if not os.path.exists(pitcher_json_path):
                logger.warning(f"pitcher.json not found at {pitcher_json_path} - pitcher lookups will be skipped")
                self._pitcher_config = {'manual_pitchers': {}}
                return self._pitcher_config
            
            with open(pitcher_json_path, 'r', encoding='utf-8') as f:
                self._pitcher_config = json.load(f)
            
            logger.debug("✅ pitcher.json configuration loaded")
            return self._pitcher_config
            
        except json.JSONDecodeError as e:
            raise ConfigurationError(
                "pitcher.json file contains invalid JSON",
                config_file=pitcher_json_path,
                cause=e
            )
        except PermissionError as e:
            raise ConfigurationError(
                "Permission denied reading pitcher.json",
                config_file=pitcher_json_path,
                cause=e
            )
        except Exception as e:
            raise ConfigurationError(
                f"Failed to load pitcher.json configuration: {str(e)}",
                config_file=pitcher_json_path,
                cause=e
            )
    
    def _prioritize_tasks(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Prioritize tasks based on league priority and other factors.
        
        Args:
            tasks: List of team tasks
            
        Returns:
            Sorted list of tasks by priority
        """
        return sorted(tasks, key=lambda task: (
            task.get('priority', 999),
            task.get('team_name', 'ZZZ')
        ))
    
    def validate_task(self, task: Dict[str, Any]) -> bool:
        """
        Validate that a task has all required fields.
        
        Args:
            task: Team task dictionary
            
        Returns:
            True if task is valid, False otherwise
            
        Raises:
            ValidationError: For invalid task structure or missing required fields
        """
        if not isinstance(task, dict):
            raise ValidationError(
                "Task must be a dictionary",
                field='task',
                value=type(task).__name__,
                expected_type='dict'
            )
        
        required_fields = VALIDATION_RULES['REQUIRED_TEAM_FIELDS']
        
        for field in required_fields:
            if field not in task:
                raise ValidationError(
                    f"Task validation failed: missing required field",
                    field=field,
                    value='missing'
                )
            
            if not task.get(field):
                raise ValidationError(
                    f"Task validation failed: empty required field",
                    field=field,
                    value=task.get(field)
                )
        
        # Validate specific field types
        if not isinstance(task['team_id'], str):
            raise ValidationError(
                "team_id must be a string",
                field='team_id',
                value=type(task['team_id']).__name__,
                expected_type='str'
            )
        
        if not isinstance(task['team_name'], str):
            raise ValidationError(
                "team_name must be a string",
                field='team_name',
                value=type(task['team_name']).__name__,
                expected_type='str'
            )
        
        return True
    
    def get_task_stats(self, tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Get statistics about the task list.
        
        Args:
            tasks: List of team tasks
            
        Returns:
            Dictionary with task statistics
        """
        stats = {
            'total_tasks': len(tasks),
            'leagues': {},
            'teams_with_pitchers': 0,
            'teams_without_pitchers': 0
        }
        
        for task in tasks:
            league = str(task.get('league', 'Unknown'))
            stats['leagues'][league] = stats['leagues'].get(league, 0) + 1
            
            if task.get('pitcher_name'):
                stats['teams_with_pitchers'] += 1
            else:
                stats['teams_without_pitchers'] += 1
        
        return stats