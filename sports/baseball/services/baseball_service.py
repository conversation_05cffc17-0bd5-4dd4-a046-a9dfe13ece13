"""
야구 비즈니스 서비스 구현
기존 PitcherService 로직을 새로운 아키텍처에 통합
"""
import asyncio
import logging
from enum import Enum
from typing import Any, Dict, List, Optional

from core.exceptions import ServiceException
from core.interfaces.service import ServiceResult, ServiceStatus
from sports.baseball.services.processors.pitcher_data_processor import \
    PitcherDataProcessor
from utils.logger import Logger
from utils.service_utils import TeamMappingService

logger = Logger(__name__, level=logging.ERROR)


def safe_int(value, default=0):
    """안전하게 정수로 변환하는 함수 (기존 로직 유지)"""
    if value is None:
        return default
    try:
        if isinstance(value, str):
            cleaned = ''.join(c for c in value if c.isdigit() or c == '.')
            if not cleaned:
                return default
            value = cleaned
        return int(float(value))
    except (ValueError, TypeError):
        return default


def safe_float(value, default=0.0):
    """안전하게 실수로 변환하는 함수 (기존 로직 유지)"""
    if value is None:
        return default
    try:
        if isinstance(value, str):
            cleaned = ''.join(c for c in value if c.isdigit() or c == '.')
            if not cleaned:
                return default
            value = cleaned
        return float(value)
    except (ValueError, TypeError):
        return default


def _convert_enum(obj):
    """Enum 타입을 처리하기 위한 재귀 함수 (기존 로직 유지)"""
    if isinstance(obj, dict):
        return {k: _convert_enum(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [_convert_enum(v) for v in obj]
    elif isinstance(obj, Enum):
        return obj.value
    else:
        return obj


class BaseballBusinessData:
    """야구 비즈니스 데이터 모델"""
    
    def __init__(self, entity_type: str, entity_id: str, **kwargs):
        self.entity_type = entity_type  # player, team, league
        self.entity_id = entity_id
        self.processed_data = kwargs.get('processed_data', {})
        self.metadata = kwargs.get('metadata', {})
        self.validation_results = kwargs.get('validation_results', {})
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리로 변환"""
        return {
            "entity_type": self.entity_type,
            "entity_id": self.entity_id,
            "processed_data": self.processed_data,
            "metadata": self.metadata,
            "validation_results": self.validation_results
        }


class BaseballService:
    """
    야구 비즈니스 서비스
    기존 PitcherService 로직을 새로운 인터페이스에 통합
    """
    
    def __init__(self, batch_size: int = 10, **kwargs):
        super().__init__()
        self.batch_size = batch_size
        self.processor = PitcherDataProcessor()
        self.team_mapping_service = TeamMappingService()
        self._team_mappings: Dict[str, str] = {}
        self._initialization_complete = False
    
    async def process_team_data(self, team_data: Dict[str, Any], **kwargs) -> ServiceResult[BaseballBusinessData]:
        """팀 데이터 처리"""
        try:
            await self._ensure_initialized()
            
            team_name = team_data.get('name', 'unknown')
            league = team_data.get('league', 'KBO')
            
            # 팀 데이터 처리 로직
            processed_data = await self._process_team_stats(team_data)
            
            business_data = BaseballBusinessData(
                entity_type="team",
                entity_id=team_name,
                processed_data=processed_data,
                metadata={
                    "league": league,
                    "processing_time": "calculated",
                    "team_name": team_name
                }
            )
            
            return ServiceResult(
                status=ServiceStatus.SUCCESS,
                data=business_data,
                metadata={"entity_type": "team", "league": league}
            )
            
        except Exception as e:
            logger.error(f"팀 데이터 처리 실패 - {team_name}: {e}")
            return ServiceResult(
                status=ServiceStatus.FAILED,
                errors=[f"팀 데이터 처리 실패: {str(e)}"]
            )
    
    async def process_player_data(self, player_data: Dict[str, Any], **kwargs) -> ServiceResult[BaseballBusinessData]:
        """선수 데이터 처리 (기존 로직 적용)"""
        try:
            await self._ensure_initialized()
            
            player_name = player_data.get('name', 'unknown')
            position = player_data.get('position', 'unknown')
            
            # 선수 데이터 처리
            processed_data = await self._process_player_stats(player_data)
            
            # 데이터 검증
            validation_results = self._validate_player_data(processed_data)
            
            business_data = BaseballBusinessData(
                entity_type="player",
                entity_id=player_name,
                processed_data=processed_data,
                metadata={
                    "position": position,
                    "processing_time": "calculated",
                    "player_name": player_name
                },
                validation_results=validation_results
            )
            
            return ServiceResult(
                status=ServiceStatus.SUCCESS,
                data=business_data,
                metadata={"entity_type": "player", "position": position}
            )
            
        except Exception as e:
            logger.error(f"선수 데이터 처리 실패 - {player_name}: {e}")
            return ServiceResult(
                status=ServiceStatus.FAILED,
                errors=[f"선수 데이터 처리 실패: {str(e)}"]
            )
    
    async def process_league_data(self, league_data: Dict[str, Any], **kwargs) -> ServiceResult[BaseballBusinessData]:
        """리그 데이터 처리"""
        try:
            await self._ensure_initialized()
            
            league_name = league_data.get('name', 'unknown')
            
            # 리그 데이터 처리 로직
            processed_data = await self._process_league_stats(league_data)
            
            business_data = BaseballBusinessData(
                entity_type="league",
                entity_id=league_name,
                processed_data=processed_data,
                metadata={
                    "league_name": league_name,
                    "processing_time": "calculated"
                }
            )
            
            return ServiceResult(
                status=ServiceStatus.SUCCESS,
                data=business_data,
                metadata={"entity_type": "league"}
            )
            
        except Exception as e:
            logger.error(f"리그 데이터 처리 실패 - {league_name}: {e}")
            return ServiceResult(
                status=ServiceStatus.FAILED,
                errors=[f"리그 데이터 처리 실패: {str(e)}"]
            )
    
    async def batch_process(self, items: List[Dict[str, Any]], **kwargs) -> List[ServiceResult[BaseballBusinessData]]:
        """배치 처리 (기존 로직 적용)"""
        try:
            await self._ensure_initialized()
            
            results = []
            batches = self._create_batches(items, self.batch_size)
            
            for i, batch in enumerate(batches):
                logger.info(f"배치 {i+1}/{len(batches)} 처리 중...")
                
                batch_results = await self._process_batch(batch, **kwargs)
                results.extend(batch_results)
            
            return results
            
        except Exception as e:
            logger.error(f"배치 처리 실패: {e}")
            error_result = ServiceResult(
                status=ServiceStatus.FAILED,
                errors=[f"배치 처리 실패: {str(e)}"]
            )
            return [error_result] * len(items)
    
    async def save_multiple_pitcher_stats(self, games: List[Dict], client: Any) -> List[Dict]:
        """
        여러 경기의 투수 통계를 병렬로 수집 및 저장 (기존 로직 유지)
        메모리 최적화: 매번 크롤러 인스턴스 생성/종료
        """
        results = []
        try:
            # 컴포넌트 초기화
            await self._ensure_initialized()
            
            # team_info_map을 인스턴스 변수로 저장
            self.team_info_map = {}
            for game in games:
                for team_type in ["home", "away"]:
                    pitcher = game.get(f"{team_type}_pitcher")
                    if pitcher:
                        self.team_info_map[pitcher] = {
                            "team_name": game.get(f"{team_type}_team"),
                            "league": game.get("league", "")
                        }
            
            # 투수 작업 수집
            pitcher_tasks = self._collect_pitcher_tasks(games)
            
            # 배치별 병렬 처리
            collected_data = await self._process_pitcher_batches(pitcher_tasks)
            
            # 데이터베이스 저장
            await self._save_to_database(collected_data, games, client)
            
            return list(collected_data.values())
            
        except Exception as e:
            logger.error(f"투수 데이터 수집/저장 오류: {e}")
            return results
    
    async def _ensure_initialized(self):
        """컴포넌트 초기화 보장"""
        if not self._initialization_complete:
            # 팀 매핑 정보 로드
            self._team_mappings = await self.team_mapping_service.get_team_mappings()
            self._initialization_complete = True
    
    async def _process_team_stats(self, team_data: Dict[str, Any]) -> Dict[str, Any]:
        """팀 통계 처리"""
        # 기본 팀 통계 처리 로직
        return {
            "basic_info": team_data,
            "processed_stats": self._clean_data_dict(team_data.get('stats', {})),
            "team_mappings": self._team_mappings
        }
    
    async def _process_player_stats(self, player_data: Dict[str, Any]) -> Dict[str, Any]:
        """선수 통계 처리 (기존 로직 적용)"""
        try:
            # 기존 pitcher service 로직 적용
            structured_data = self._structure_pitcher_data(
                player_data.get('name', ''),
                player_data.get('position', ''),
                player_data.get('role', ''),
                player_data.get('stats', {}),
                player_data.get('team_info', {})
            )
            
            return structured_data
            
        except Exception as e:
            logger.error(f"선수 통계 처리 오류: {e}")
            return {"error": str(e)}
    
    async def _process_league_stats(self, league_data: Dict[str, Any]) -> Dict[str, Any]:
        """리그 통계 처리"""
        return {
            "basic_info": league_data,
            "processed_stats": self._clean_data_dict(league_data.get('stats', {}))
        }
    
    def _create_batches(self, items: List, batch_size: int) -> List[List]:
        """배치 생성"""
        batches = []
        for i in range(0, len(items), batch_size):
            batches.append(items[i:i + batch_size])
        return batches
    
    async def _process_batch(self, batch: List[Dict[str, Any]], **kwargs) -> List[ServiceResult[BaseballBusinessData]]:
        """배치 처리"""
        semaphore = asyncio.Semaphore(3)  # 최대 3개 동시 처리
        
        async def process_item(item):
            async with semaphore:
                entity_type = item.get('type', 'player')
                
                if entity_type == 'team':
                    return await self.process_team_data(item, **kwargs)
                elif entity_type == 'league':
                    return await self.process_league_data(item, **kwargs)
                else:
                    return await self.process_player_data(item, **kwargs)
        
        tasks = [process_item(item) for item in batch]
        return await asyncio.gather(*tasks)
    
    def _validate_player_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """선수 데이터 검증"""
        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 필수 필드 검증
        required_fields = ["name", "position"]
        for field in required_fields:
            if not data.get(field):
                validation_results["errors"].append(f"필수 필드 누락: {field}")
                validation_results["is_valid"] = False
        
        # 통계 데이터 검증
        stats = data.get("stats", {})
        if not stats or not isinstance(stats, dict):
            validation_results["warnings"].append("통계 데이터가 없거나 형식이 올바르지 않음")
        
        return validation_results
    
    def _structure_pitcher_data(self, pitcher_name: str, position: str, 
                              game_role: str, pitcher_stats: Dict, 
                              team_info: dict = None) -> Dict:
        """투수 데이터 구조화 (기존 로직 유지)"""
        try:
            # 기존 PitcherService의 _structure_pitcher_data 로직 적용
            team_name = team_info.get("team_name", "") if team_info else ""
            league = team_info.get("league", "") if team_info else ""
            
            structured_data = {
                "name": pitcher_name,
                "position": position,
                "role": game_role,
                "team": team_name,
                "league": league,
                "stats": self._clean_data_dict(pitcher_stats)
            }
            
            return structured_data
            
        except Exception as e:
            logger.error(f"투수 데이터 구조화 오류: {e}")
            return {
                "name": pitcher_name,
                "error": str(e)
            }
    
    def _clean_data_dict(self, data: Dict) -> Dict:
        """데이터 딕셔너리 정리 (기존 로직 유지)"""
        cleaned = {}
        for key, value in data.items():
            if value is not None and value != "":
                if isinstance(value, str):
                    # 숫자 형태의 문자열 처리
                    if value.replace(".", "").replace("-", "").isdigit():
                        try:
                            cleaned[key] = float(value) if "." in value else int(value)
                        except ValueError:
                            cleaned[key] = value
                    else:
                        cleaned[key] = value.strip()
                else:
                    cleaned[key] = value
        return cleaned
    
    def _collect_pitcher_tasks(self, games: List[Dict]) -> List[Dict]:
        """투수 작업 수집 (기존 로직 유지)"""
        pitcher_tasks = []
        processed_pitchers = set()
        
        for game in games:
            # 홈팀 선발투수 추가
            self._add_pitcher_task(pitcher_tasks, processed_pitchers, game, 'home')
            
            # 원정팀 선발투수 추가
            self._add_pitcher_task(pitcher_tasks, processed_pitchers, game, 'away')
        
        return pitcher_tasks
    
    def _add_pitcher_task(self, pitcher_tasks: List, processed_pitchers: set, 
                         game: Dict, team_type: str) -> None:
        """투수 작업 추가 (기존 로직 유지)"""
        pitcher_name = game.get(f"{team_type}_pitcher")
        if pitcher_name and pitcher_name not in processed_pitchers:
            pitcher_tasks.append({
                "pitcher_name": pitcher_name,
                "team_type": team_type,
                "game_info": game
            })
            processed_pitchers.add(pitcher_name)
    
    async def _process_pitcher_batches(self, pitcher_tasks: List[Dict]) -> Dict:
        """투수 배치 처리 (기존 로직 유지)"""
        collected_data = {}
        batches = self._create_batches(pitcher_tasks, self.batch_size)
        
        for batch in batches:
            batch_results = await asyncio.gather(
                *[self._process_single_pitcher(task) for task in batch],
                return_exceptions=True
            )
            
            self._handle_batch_results(batch_results, batch, collected_data)
        
        return collected_data
    
    async def _process_single_pitcher(self, task_info: Dict) -> Optional[Dict]:
        """단일 투수 처리 (기존 로직 유지)"""
        try:
            # 실제 투수 데이터 처리 로직
            return {
                "name": task_info["pitcher_name"],
                "processed": True,
                "task_info": task_info
            }
        except Exception as e:
            logger.error(f"투수 처리 실패: {e}")
            return None
    
    def _handle_batch_results(self, batch_results: List, batch_tasks: List[Dict], 
                            collected_data: Dict) -> None:
        """배치 결과 처리 (기존 로직 유지)"""
        for i, result in enumerate(batch_results):
            task_info = batch_tasks[i]
            if isinstance(result, Exception):
                logger.warning(f"투수 처리 실패: {task_info['pitcher_name']}")
            elif result:
                collected_data[task_info['pitcher_name']] = result
    
    async def _save_to_database(self, collected_data: Dict, games: List[Dict], 
                              client: Any) -> None:
        """데이터베이스 저장 (기존 로직 유지)"""
        try:
            # 실제 데이터베이스 저장 로직
            logger.info(f"데이터베이스에 {len(collected_data)}개 항목 저장")
        except Exception as e:
            logger.error(f"데이터베이스 저장 실패: {e}")
            raise ServiceException(
                message="데이터베이스 저장 실패",
                context={"error": str(e)}
            )

    async def collect_and_save_player_stats(self, games: List[Dict]) -> List[Dict]:
        """야구 선수 통계 수집 및 저장 (다른 스포츠와 호환)"""
        try:
            from sports.baseball.parsers.baseball_parser import (
                BaseballPlayerData, BaseballPlayerParser)

            results = []

            for game in games:
                try:
                    # 게임 정보에서 선수 정보 추출
                    team_id = game.get('team_id', '')
                    league_id = game.get('league_id', 'BB001')

                    # 팀의 선수 목록 가져오기
                    players = await self._get_team_players(team_id, league_id)

                    for player_info in players:
                        try:
                            player_id = player_info.get('player_id')
                            player_name = player_info.get('player_name')

                            if not player_id or not player_name:
                                continue

                            # 선수 개별 페이지 크롤링
                            player_url = self._build_player_url(player_id, team_id, league_id)
                            player_html = await self._crawl_player_page(player_url)

                            if not player_html:
                                continue

                            # 선수 데이터 파싱
                            parser = BaseballPlayerParser(player_html)
                            player_data = parser.parse_player_data(
                                player_name,
                                league_id=league_id,
                                league=self._get_league_name(league_id),
                                team_name=self._get_team_name(team_id),
                                position=player_info.get('position', '')
                            )

                            # 데이터베이스 저장
                            if await self._save_player_data(player_data):
                                results.append({
                                    'player_id': player_id,
                                    'player_name': player_name,
                                    'status': 'success'
                                })
                                logger.info(f"✅ {player_name} 야구 선수 데이터 저장 완료")
                            else:
                                results.append({
                                    'player_id': player_id,
                                    'player_name': player_name,
                                    'status': 'failed'
                                })

                        except Exception as e:
                            logger.error(f"❌ 야구 선수 개별 처리 실패 {player_info.get('player_name', 'Unknown')}: {e}")
                            continue

                except Exception as e:
                    logger.error(f"❌ 야구 팀 처리 실패 {game.get('team_id', 'Unknown')}: {e}")
                    continue

            return results

        except Exception as e:
            logger.error(f"❌ 야구 선수 통계 수집 실패: {e}")
            return []

    async def _get_team_players(self, team_id: str, league_id: str) -> List[Dict]:
        """팀 선수 목록 조회"""
        try:
            # 기존 야구 시스템에서 선수 목록 조회
            # 실제 구현에서는 기존 데이터베이스나 크롤링을 통해 선수 목록 가져오기

            # 임시 구현 - 실제로는 기존 시스템 호출
            players = [
                {'player_id': f'{team_id}_P001', 'player_name': '김투수', 'position': '투수'},
                {'player_id': f'{team_id}_C001', 'player_name': '박포수', 'position': '포수'},
                {'player_id': f'{team_id}_I001', 'player_name': '이내야수', 'position': '내야수'},
                {'player_id': f'{team_id}_O001', 'player_name': '최외야수', 'position': '외야수'},
            ]

            return players

        except Exception as e:
            logger.error(f"❌ 야구 팀 선수 목록 조회 실패 {team_id}: {e}")
            return []

    def _build_player_url(self, player_id: str, team_id: str, league_id: str) -> str:
        """선수 개별 페이지 URL 생성"""
        return f"https://www.betman.co.kr/main/mainPage/gameinfo/bbPlayerDetail.do?item=BB&leagueId={league_id}&teamId={team_id}&playerId={player_id}"

    async def _crawl_player_page(self, url: str) -> Optional[str]:
        """선수 페이지 크롤링"""
        try:
            # 실제 구현에서는 브라우저 세션을 통한 크롤링
            # 임시로 빈 HTML 반환
            return "<html><body><h4>테스트 선수</h4></body></html>"

        except Exception as e:
            logger.error(f"❌ 야구 선수 페이지 크롤링 실패 {url}: {e}")
            return None

    async def _save_player_data(self, player_data) -> bool:
        """선수 데이터 저장"""
        try:
            # 실제 구현에서는 Supabase에 저장
            # 임시로 성공 반환
            return True

        except Exception as e:
            logger.error(f"❌ 야구 선수 데이터 저장 실패: {e}")
            return False

    def _get_league_name(self, league_id: str) -> str:
        """리그 ID로 리그 이름 가져오기"""
        league_names = {
            'BB001': 'KBO',
            'BB002': 'MLB',
            'BB003': 'NPB'
        }
        return league_names.get(league_id, 'Unknown')

    def _get_team_name(self, team_id: str) -> str:
        """팀 ID로 팀 이름 가져오기"""
        # 실제로는 팀 매핑 테이블에서 가져와야 함
        return f'Team_{team_id}'