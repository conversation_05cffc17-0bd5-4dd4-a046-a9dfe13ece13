"""
Baseball Module Constants
Centralized configuration constants to eliminate hard-coded values
"""
from typing import Dict, List, Final

# Database Configuration
DATABASE_TABLES: Final[Dict[str, str]] = {
    'BASEBALL_STATS': 'baseball_stats',
    'TEAM_INFO': 'team_info',
    'TARGET_GAMES': 'target_games'
}

# Browser Configuration
BROWSER_CONFIG: Final[Dict[str, any]] = {
    'HEADLESS': True,
    'TIMEOUT': 30000,  # 30 seconds in milliseconds
    'USER_AGENT': (
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) '
        'AppleWebKit/537.36 (KHTML, like Gecko) '
        'Chrome/120.0.0.0 Safari/537.36'
    ),
    'VIEWPORT': {'width': 1920, 'height': 1080},
    'NAVIGATION_TIMEOUT': 30000,
    'ELEMENT_TIMEOUT': 10000
}

# Browser Launch Arguments
BROWSER_ARGS: Final[List[str]] = [
    '--no-sandbox',
    '--disable-dev-shm-usage',
    '--disable-gpu',
    '--disable-web-security',
    '--disable-features=VizDisplayCompositor',
    '--disable-background-timer-throttling',
    '--disable-backgrounding-occluded-windows',
    '--disable-renderer-backgrounding',
    '--no-first-run',
    '--no-default-browser-check'
]

# Data Collection Configuration
DATA_COLLECTION: Final[Dict[str, any]] = {
    'MAX_RETRIES': 3,
    'RETRY_DELAY': 1.0,
    'BACKOFF_FACTOR': 2.0,
    'RATE_LIMIT_DELAY': 1.0,  # Delay between team processing
    'MAX_CONCURRENT_BROWSERS': 3,
    'MEMORY_THRESHOLD_MB': 400.0
}

# League Configuration
LEAGUE_PRIORITIES: Final[Dict[str, int]] = {
    'KBO': 1,
    'MLB': 2,
    'NPB': 3
}

LEAGUE_CODES: Final[Dict[str, str]] = {
    'KBO': 'BS001',
    'MLB': 'BS002',
    'NPB': 'BS004'
}

# Skip Logic Configuration
SKIP_FIELDS: Final[Dict[str, List[str]]] = {
    'TEAM_SKIP_FIELDS': [
        'season_summary',
        'recent_games_summary'
    ],
    'PITCHER_SKIP_FIELDS': [
        'pitcher_profile',
        'pitcher_stats'
    ]
}

# Data Validation Rules
VALIDATION_RULES: Final[Dict[str, any]] = {
    'REQUIRED_TEAM_FIELDS': [
        'team_id',
        'team_name', 
        'league',
        'match_id',
        'match_date'
    ],
    'REQUIRED_PITCHER_FIELDS': [
        'pitcher_name',
        'team_id',
        'league'
    ],
    'MAX_TEAM_NAME_LENGTH': 50,
    'MAX_PITCHER_NAME_LENGTH': 50,
    'MIN_TEAM_ID_LENGTH': 1,
    'MAX_TEAM_ID_LENGTH': 10
}

# Error Handling Configuration  
ERROR_CONFIG: Final[Dict[str, any]] = {
    'MAX_CONNECTION_ATTEMPTS': 3,
    'CONNECTION_RETRY_DELAY': 2.0,
    'CRITICAL_OPERATIONS': {
        'database_connection',
        'browser_initialization',
        'component_initialization'
    },
    'RETRYABLE_OPERATIONS': {
        'database_query',
        'browser_navigation',
        'data_collection',
        'page_load',
        'element_interaction'
    }
}

# Logging Configuration
LOGGING_CONFIG: Final[Dict[str, any]] = {
    'LEVEL': 'INFO',
    'ENABLE_DEBUG': False,
    'LOG_DATABASE_QUERIES': False,
    'LOG_BROWSER_ACTIONS': False,
    'MAX_LOG_CONTEXT_LENGTH': 200
}

# File Paths
FILE_PATHS: Final[Dict[str, str]] = {
    'PITCHER_CONFIG': 'core/config/resources/pitcher.json',
    'TEAM_MAPPINGS': 'core/config/resources/team_mappings.json',
    'URL_PATTERNS': 'core/config/resources/url_patterns.json'
}

# URL Patterns
URL_PATTERNS: Final[Dict[str, str]] = {
    'TEAM_STATS_BASE': 'https://www.betman.co.kr/main/mainPage/gameinfo/bsTeamAnalysis.do',
    'PITCHER_STATS_BASE': 'https://www.betman.co.kr/main/mainPage/gameinfo/bsPlayerDetail.do',
    'FALLBACK_BASE': 'https://betman.co.kr'
}

# Performance Monitoring
PERFORMANCE: Final[Dict[str, any]] = {
    'MONITOR_MEMORY_USAGE': True,
    'LOG_SLOW_OPERATIONS': True,
    'SLOW_OPERATION_THRESHOLD': 10.0,  # seconds
    'MEMORY_WARNING_THRESHOLD': 300.0,  # MB
    'MEMORY_CRITICAL_THRESHOLD': 500.0,  # MB
}

# Development/Testing Configuration
DEV_CONFIG: Final[Dict[str, any]] = {
    'ENABLE_MOCK_DATA': False,
    'SKIP_BROWSER_TESTS': False,
    'ENABLE_PERFORMANCE_PROFILING': False,
    'MAX_TEST_TEAMS': 5,
    'ENABLE_DEBUG_SCREENSHOTS': False
}

# Data Formats and Patterns
DATA_FORMATS: Final[Dict[str, str]] = {
    'DATE_FORMAT': '%Y-%m-%d',
    'DATETIME_FORMAT': '%Y-%m-%d %H:%M:%S',
    'TIME_FORMAT': '%H:%M',
    'INNINGS_PATTERN': r'(\d+)\s*(\d*/\d*)',
    'ERA_PATTERN': r'(\d+\.\d{2})',
    'PERCENTAGE_PATTERN': r'(\d+\.\d{3})'
}

# Cache Configuration
CACHE_CONFIG: Final[Dict[str, any]] = {
    'ENABLE_CACHING': True,
    'CACHE_TTL_SECONDS': 3600,  # 1 hour
    'MAX_CACHE_SIZE': 1000,
    'CACHE_PITCHER_CONFIG': True,
    'CACHE_TEAM_MAPPINGS': True
}

# API Rate Limits
RATE_LIMITS: Final[Dict[str, any]] = {
    'REQUESTS_PER_MINUTE': 60,
    'CONCURRENT_REQUESTS': 3,
    'BACKOFF_ON_RATE_LIMIT': True,
    'RATE_LIMIT_BACKOFF_SECONDS': 60
}