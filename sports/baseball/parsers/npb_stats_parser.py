"""
NPB 통계 파서 - 홀드, 볼넷, 실책 컬럼 없음 (도루도 숨겨짐)
"""
from typing import Dict

from bs4 import Tag

from sports.baseball.parsers.base_stats_parser import BaseStatsParser
from utils.logger import Logger

# 로거 설정
logger = Logger(__name__)


class NPBStatsParser(BaseStatsParser):
    """NPB 통계 파서 (홀드, 볼넷, 실책, 도루 없음)"""

    def get_league_code(self) -> str:
        return "NPB"

    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """NPB 시즌 요약 컬럼 매핑 (홀드 없음)"""
        return {
            "home": {  # 16개 컬럼
                "rank": 0,
                # "games_behind": 1,
                # [2] "홈" 텍스트 - 스킵
                "games": 3,
                "win_rate": 4,
                "win": 5,
                "draw": 6,
                "lose": 7,
                "saves": 8,
                # 홀드 없음
                "batting_average": 9,
                "era": 10,
                "runs": 11,
                "runs_allowed": 12,
                "home_runs": 13,
                "hits": 14,
                "strikeouts": 15
                # 출루율, 도루, 삼진 누락 (HTML 구조상)
            },
            "away": {  # 14개 컬럼 (원정)
                # [0] "원정" 텍스트 - 스킵
                "games": 1,
                "win_rate": 2,
                "win": 3,
                "draw": 4,
                "lose": 5,
                "saves": 6,
                # 홀드 없음
                "batting_average": 7,
                "era": 8,
                "runs": 9,
                "runs_allowed": 10,
                "home_runs": 11,
                "hits": 12,
                "strikeouts": 13
                # 출루율, 도루, 삼진 누락
            },
            "total": {  # 14개 컬럼 (전체)
                # [0] "전체" 텍스트 - 스킵
                "games": 1,
                "win_rate": 2,
                "win": 3,
                "draw": 4,
                "lose": 5,
                "saves": 6,
                # 홀드 없음
                "batting_average": 7,
                "era": 8,
                "runs": 9,
                "runs_allowed": 10,
                "home_runs": 11,
                "hits": 12,
                "strikeouts": 13
                # 출루율, 도루, 삼진 누락
            }
        }

    def get_recent_games_mapping(self) -> Dict[str, Dict[str, int]]:
        """NPB 최근 경기 컬럼 매핑 - 도루/실책은 DOM에서 제거됨 (pitcher_stats 추가)"""
        return {
            "home": {  # 11개 컬럼 (pitcher_stats 추가)
                # [0] 날짜 - 별도 처리
                # [1] "홈" 텍스트 - 스킵  
                "team_full_name": 2,
                "starting_pitcher": 3,
                "result": 4,
                "runs": 5,
                "home_runs": 6,
                "hits": 7,
                # "strikeouts": 8,  # 도루 컬럼이 없으므로 8번
                "walks": 9,       # 볼넷 9번
                "pitcher_stats": 10  # 투수기록 10번
            },
            "away": {  # 10개 컬럼 (pitcher_stats 추가)
                # [0] "원정" 텍스트 - 스킵
                "team_full_name": 1,
                "starting_pitcher": 2,
                "result": 3,
                "runs": 4,
                "home_runs": 5,
                "hits": 6,
                # "strikeouts": 7,  # 도루 컬럼이 없으므로 7번
                "walks": 8,       # 볼넷 8번
                "pitcher_stats": 9  # 투수기록 9번
            }
        }

    def get_recent_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """NPB 최근 경기 요약 컬럼 매핑 - 도루/실책 제거됨 (pitcher_stats 제거)"""
        return {
            "home": {  # 8개 컬럼 (pitcher_stats 제거)
                # [0] "최근N경기평균" 텍스트 - 스킵
                # [1] "홈" 텍스트 - 스킵
                "record": 2,
                "avg_runs": 3,
                "avg_home_runs": 4,
                "avg_hits": 5,
                # "avg_strikeouts": 6,  # 도루 없으므로 6번
                "avg_walks": 7        # 볼넷 평균 7번
                # pitcher_stats 제거 (원래 8번)
            },
            "away": {  # 7개 컬럼 (pitcher_stats 제거)
                # [0] "원정" 텍스트 - 스킵
                "record": 1,
                "avg_runs": 2,
                "avg_home_runs": 3,
                "avg_hits": 4,
                # "avg_strikeouts": 5,  # 도루 없으므로 5번
                "avg_walks": 6        # 볼넷 평균 6번
                # pitcher_stats 제거 (원래 7번)
            }
        }

    def get_season_stats_mapping(self) -> Dict[str, int]:
        """NPB 역대 시즌 통계 컬럼 매핑 (20개 컬럼, 홀드 없음)"""
        return {
            "rank": 1,
            "batting_average": 2,
            "win_rate": 3,
            "era": 4,
            "win": 5,
            "draw": 6,
            "lose": 7,
            "saves": 8,
            # 홀드 없음 (인덱스 9 건너뜀)
            "runs": 9,          # KBO/MLB의 [10]이 NPB에서는 [9]
            "runs_allowed": 10,
            "earned_runs": 11,
            "home_runs": 12,
            "home_runs_allowed": 13,
            "hits": 14,
            "hits_allowed": 15,
            "slugging_pct": 16,
            "on_base_pct": 17,
            "stolen_bases": 18,
            "strikeouts": 19
        }

    def parse_season_stats(self) -> Dict[str, Dict[str, str]]:
        """NPB 역대 시즌 통계 파싱 (가장 최근 1개 시즌만)"""
        historical_stats = {}

        # 테이블 찾기
        table = self.soup.find("table", id="tbl3")
        if not isinstance(table, Tag):
            return historical_stats

        tbody = table.find("tbody", id="history_record")
        if not isinstance(tbody, Tag):
            return historical_stats

        rows = tbody.find_all("tr")
        mapping = self.get_season_stats_mapping()

        # 🚀 가장 최근 시즌만 파싱 (첫 번째 행)
        if rows:
            row = rows[0]  # 첫 번째 행이 가장 최근 시즌
            if isinstance(row, Tag):
                tds = row.find_all("td")
                if len(tds) >= 20:  # NPB는 20개 컬럼 (홀드 없음)
                    stats_dict = {}
                    try:
                        # 시즌 정보 추출 (첫 번째 컬럼, 키로만 사용)
                        season_text = tds[0].get_text(strip=True)
                        if season_text:
                            # 매핑된 데이터 추출
                            for field_name, column_index in mapping.items():
                                if column_index < len(tds):
                                    value = tds[column_index].get_text(strip=True)
                                    if value:  # 빈 값이 아닌 경우만 추가
                                        stats_dict[field_name] = value

                            # 리그 코드 추가
                            stats_dict["league_code"] = self.league_code

                            # 시즌을 키로 사용
                            historical_stats[season_text] = stats_dict
                            logger.debug(
                                f"✅ NPB 최근 시즌 {season_text} 파싱 완료"
                            )

                    except Exception as e:
                        logger.warning(
                            f"❌ NPB 최근 시즌 통계 파싱 오류: {str(e)}"
                        )

        return historical_stats


# 하위 호환성을 위한 별칭
TeamStatsParserNPB = NPBStatsParser 