"""
KBO 통계 파서 - 홀드, 볼넷, 실책 포함
"""
from typing import Dict

from sports.baseball.parsers.base_stats_parser import BaseStatsParser


class KBOStatsParser(BaseStatsParser):
    """KBO 통계 파서"""

    def get_league_code(self) -> str:
        return "KBO"

    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """KBO 시즌 요약 컬럼 매핑"""
        return {
            "home": {  # 20개 컬럼
                "rank": 0,
                # "games_behind": 1,
                # [2] "홈" 텍스트 - 스킵
                "games": 3,
                "win_rate": 4,
                "win": 5,
                "draw": 6,
                "lose": 7,
                "saves": 8,
                "holds": 9,
                "batting_average": 10,
                "era": 11,
                "runs": 12,
                "runs_allowed": 13,
                "home_runs": 14,
                "hits": 15,
                "slugging_pct": 16,
                "on_base_pct": 17,
                "stolen_bases": 18,
                "strikeouts": 19
            },
            "away": {  # 18개 컬럼 (원정)
                # [0] "원정" 텍스트 - 스킵
                "games": 1,
                "win_rate": 2,
                "win": 3,
                "draw": 4,
                "lose": 5,
                "saves": 6,
                "holds": 7,
                "batting_average": 8,
                "era": 9,
                "runs": 10,
                "runs_allowed": 11,
                "home_runs": 12,
                "hits": 13,
                "slugging_pct": 14,
                "on_base_pct": 15,
                "stolen_bases": 16,
                "strikeouts": 17
            },
            "total": {  # 18개 컬럼 (전체)
                # [0] "전체" 텍스트 - 스킵
                "games": 1,
                "win_rate": 2,
                "win": 3,
                "draw": 4,
                "lose": 5,
                "saves": 6,
                "holds": 7,
                "batting_average": 8,
                "era": 9,
                "runs": 10,
                "runs_allowed": 11,
                "home_runs": 12,
                "hits": 13,
                "slugging_pct": 14,
                "on_base_pct": 15,
                "stolen_bases": 16,
                "strikeouts": 17
            }
        }

    def get_recent_games_mapping(self) -> Dict[str, Dict[str, int]]:
        """KBO 최근 경기 컬럼 매핑 (pitcher_stats 추가)"""
        return {
            "home": {  # 13개 컬럼 (pitcher_stats 추가)
                # [0] 날짜 - 별도 처리
                # [1] "홈" 텍스트 - 스킵  
                "team_full_name": 2,
                "starting_pitcher": 3,
                "result": 4,
                "runs": 5,
                "home_runs": 6,
                "hits": 7,
                "stolen_bases": 8,
                "strikeouts": 9,
                "walks": 10,
                "errors": 11,
                "pitcher_stats": 12
            },
            "away": {  # 12개 컬럼 (pitcher_stats 추가)
                # [0] "원정" 텍스트 - 스킵
                "team_full_name": 1,
                "starting_pitcher": 2,
                "result": 3,
                "runs": 4,
                "home_runs": 5,
                "hits": 6,
                "stolen_bases": 7,
                "strikeouts": 8,
                "walks": 9,
                "errors": 10,
                "pitcher_stats": 11
            }
        }

    def get_recent_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """KBO 최근 경기 요약 컬럼 매핑 (pitcher_stats 제거)"""
        return {
            "home": {  # 9개 컬럼 (pitcher_stats 제거)
                # [0] "최근N경기평균" 텍스트 - 스킵
                # [1] "홈" 텍스트 - 스킵
                "record": 2,
                "avg_runs": 3,
                "avg_home_runs": 4,
                "avg_hits": 5,
                "avg_stolen_bases": 6,
                "avg_strikeouts": 7,
                "avg_walks": 8,
                "avg_errors": 9
                # pitcher_stats 제거
            },
            "away": {  # 8개 컬럼 (pitcher_stats 제거)
                # [0] "원정" 텍스트 - 스킵
                "record": 1,
                "avg_runs": 2,
                "avg_home_runs": 3,
                "avg_hits": 4,
                "avg_stolen_bases": 5,
                "avg_strikeouts": 6,
                "avg_walks": 7,
                "avg_errors": 8
                # pitcher_stats 제거
            }
        }

    def get_season_stats_mapping(self) -> Dict[str, int]:
        """KBO 역대 시즌 통계 컬럼 매핑 (21개 컬럼)"""
        return {
            "rank": 1,
            "batting_average": 2,
            "win_rate": 3,
            "era": 4,
            "win": 5,
            "draw": 6,
            "lose": 7,
            "saves": 8,
            "holds": 9,
            "runs": 10,
            "runs_allowed": 11,
            "earned_runs": 12,
            "home_runs": 13,
            "home_runs_allowed": 14,
            "hits": 15,
            "hits_allowed": 16,
            "slugging_pct": 17,
            "on_base_pct": 18,
            "stolen_bases": 19,
            "strikeouts": 20
        }


# 하위 호환성을 위한 별칭
TeamStatsParserKBO = KBOStatsParser