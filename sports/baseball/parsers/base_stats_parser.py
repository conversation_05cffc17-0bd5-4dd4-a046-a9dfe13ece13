# flake8: noqa
"""
베이스 통계 파서 클래스 - 모든 리그 공통 로직
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Tuple

from bs4 import BeautifulSoup, Tag

from utils.helpers import (convert_date_str, map_pitcher_stats, map_result,
                           remove_duplication)
from utils.logger import Logger

# 로거 설정
logger = Logger(__name__)


class BaseStatsParser(ABC):
    """모든 리그 파서의 베이스 클래스"""

    def __init__(
        self, html: str, team_mappings: Optional[Dict[str, str]] = None
    ):
        """
        BaseStatsParser 초기화

        Args:
            html: 파싱할 HTML 문자열
            team_mappings: 팀 이름 약어 -> 풀네임 매핑 (DB에서 가져옴)
        """
        self.soup = BeautifulSoup(html, "lxml")
        self.team_mappings = team_mappings or {}
        self.league_code = self.get_league_code()

    @abstractmethod
    def get_league_code(self) -> str:
        """리그 코드 반환 (KBO, MLB, NPB)"""
        pass

    @abstractmethod
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """시즌 요약 컬럼 매핑 반환"""
        pass

    @abstractmethod
    def get_recent_games_mapping(self) -> Dict[str, Dict[str, int]]:
        """최근 경기 컬럼 매핑 반환"""
        pass

    @abstractmethod
    def get_recent_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """최근 경기 요약 컬럼 매핑 반환"""
        pass

    @abstractmethod
    def get_season_stats_mapping(self) -> Dict[str, int]:
        """역대 시즌 통계 컬럼 매핑 반환"""
        pass

    def parse_season_summary(self) -> Dict[str, Optional[Dict[str, str]]]:
        """시즌 전체 성적 파싱 (공통 로직)"""
        season_stats: Dict[str, Optional[Dict[str, str]]] = {
            "home": None, "away": None, "total": None
        }

        try:
            mapping = self.get_season_summary_mapping()
            if not mapping:
                logger.warning(f"❌ {self.league_code} 시즌 요약 매핑 정보 없음")
                return season_stats

            # 홈 통계 파싱
            if "home" in mapping and mapping["home"]:
                home_dict = self._parse_season_row("home_record", mapping["home"])
                if home_dict:
                    home_dict["league_code"] = self.league_code
                    season_stats["home"] = home_dict

            # 원정 통계 파싱
            if "away" in mapping and mapping["away"]:
                away_dict = self._parse_season_row("away_record", mapping["away"])
                if away_dict:
                    away_dict["league_code"] = self.league_code
                    season_stats["away"] = away_dict

            # 전체 통계 파싱 - null 체크 추가
            if "total" in mapping and mapping["total"]:
                total_dict = self._parse_season_row("all_record", mapping["total"])
                if total_dict:
                    total_dict["league_code"] = self.league_code
                    season_stats["total"] = total_dict
            else:
                logger.debug(f"🔍 {self.league_code} total 매핑 정보 없음")

        except Exception as e:
            logger.warning(f"❌ {self.league_code} 시즌 요약 파싱 전체 실패: {str(e)}")

        return season_stats

    def _parse_season_row(
        self, row_id: str, column_mapping: Dict[str, int]
    ) -> Optional[Dict[str, str]]:
        """시즌 통계 행 파싱 (공통 로직)"""
        try:
            if not column_mapping:
                logger.debug(f"🔍 {self.league_code} [{row_id}] 컬럼 매핑 정보 없음")
                return None

            row = self.soup.find("tr", id=row_id)
            if not isinstance(row, Tag):
                logger.debug(f"🔍 {self.league_code} [{row_id}] HTML 요소 찾을 수 없음")
                return None

            tds = row.find_all("td")
            if not tds:
                logger.debug(f"🔍 {self.league_code} [{row_id}] td 요소 없음")
                return None

            stats_dict = {}
            for field_name, column_index in column_mapping.items():
                if column_index < len(tds):
                    value = tds[column_index].get_text(strip=True)
                    if value:  # 빈 값이 아닌 경우만 추가
                        stats_dict[field_name] = value

            if not stats_dict:
                logger.debug(f"🔍 {self.league_code} [{row_id}] 추출된 데이터 없음")
                return None

            return stats_dict

        except Exception as e:
            logger.warning(f"❌ {self.league_code} 시즌 통계 파싱 오류 [{row_id}]: {str(e)}")
            return None

    def parse_recent_games(self) -> Dict[str, Dict[str, Dict[str, str]]]:
        """최근 경기 성적 파싱 (공통 로직)"""
        recent_stats = {}
        tbody = self.soup.find("tbody", id="seasonLatestTeamRecord")
        if not isinstance(tbody, Tag):
            return recent_stats

        rows = tbody.find_all("tr")
        if not rows:
            return recent_stats

        mapping = self.get_recent_games_mapping()
        i = 0

        while i < len(rows):
            # 경기 데이터 파싱 시도
            match_data = self._parse_match_rows(rows, i, mapping)
            if match_data:
                date, home_dict, away_dict, rows_processed = match_data

                # team_full_name을 away_team_name, home_team_name으로 변경
                if "team_full_name" in home_dict:
                    home_dict["home_team_name"] = home_dict.pop("team_full_name")
                if "team_full_name" in away_dict:
                    away_dict["away_team_name"] = away_dict.pop("team_full_name")

                recent_stats[date] = {"home": home_dict, "away": away_dict}
                i += rows_processed
            else:
                i += 1

        return recent_stats

    def _parse_match_rows(
        self, rows: Any, idx: int, mapping: Dict[str, Dict[str, int]]
    ) -> Optional[Tuple[str, Dict[str, str], Dict[str, str], int]]:
        """경기 데이터(홈/원정 행) 파싱 (공통 로직)"""
        if idx >= len(rows) or idx + 1 >= len(rows):
            return None

        home_row = rows[idx]
        away_row = rows[idx + 1]

        if not isinstance(home_row, Tag) or not isinstance(away_row, Tag):
            return None

        try:
            home_tds = home_row.find_all("td")
            away_tds = away_row.find_all("td")

            if not home_tds or not away_tds:
                return None

            # 날짜 추출 (홈팀 첫 번째 컬럼)
            date = home_tds[0].get_text(strip=True) if home_tds else ""
            
            # "원정", "홈" 같은 텍스트가 있으면 스킵
            if date in ["원정", "홈", ""]:
                # 다음 컬럼들에서 날짜 찾기 시도
                for i in range(1, min(3, len(home_tds))):
                    potential_date = home_tds[i].get_text(strip=True)
                    if potential_date and potential_date not in ["원정", "홈"]:
                        date = potential_date
                        break
                else:
                    # 날짜를 찾을 수 없으면 기본값 사용
                    date = "unknown_date"
            
            # 날짜 변환 (에러 처리 추가)
            try:
                date = convert_date_str(date)
            except ValueError:
                # 날짜 변환 실패 시 원본 사용
                pass

            # 홈팀 데이터 파싱 - 타입 변환
            home_dict = self._parse_match_row_data(
                list(home_tds), mapping["home"]
            )

            # 원정팀 데이터 파싱 - 타입 변환
            away_dict = self._parse_match_row_data(
                list(away_tds), mapping["away"]
            )

            return date, home_dict, away_dict, 2

        except Exception as e:
            logger.warning(f"❌ 경기 데이터 파싱 오류: {str(e)}")
            return None

    def _parse_match_row_data(
        self, tds: Any, column_mapping: Dict[str, int]
    ) -> Dict[str, str]:
        """경기 행 데이터 파싱 (pitcher_stats 제거)"""
        match_dict = {}

        try:
            for field_name, column_index in column_mapping.items():
                if column_index < len(tds):
                    value = tds[column_index].get_text(strip=True)
                    
                    # 특별 처리
                    if field_name == "result":
                        match_dict[field_name] = map_result(value)
                    elif field_name == "starting_pitcher":
                        # 투수 이름 중복 제거 및 특수문자 변환
                        match_dict[field_name] = map_pitcher_stats(
                            remove_duplication(value)
                        )
                    elif field_name == "pitcher_stats":
                        # 투수 기록: 팀 이름 제거 및 특수문자 변환
                        match_dict[field_name] = map_pitcher_stats(value)
                    elif field_name == "match_time":
                        # match_time 처리: 빈 문자열이면 None으로 변환
                        if value and value.strip():
                            match_dict[field_name] = value.strip()
                        else:
                            match_dict[field_name] = None
                    elif field_name in ["team_full_name", "team"]:
                        # 팀 이름 처리: DB 매핑만 사용 (expand_team_name 제거)
                        a_tag = tds[column_index].find('a')
                        if a_tag:
                            team_name = a_tag.get_text(strip=True)
                        else:
                            team_name = value
                        
                        # 🚀 KBO, NPB 리그는 매핑을 적용하지 않고 HTML에서 추출한 팀명을 그대로 사용
                        if self.league_code in ["NPB", "KBO"]:
                            match_dict[field_name] = team_name
                            logger.debug(f"🔍 {self.league_code} 팀명 원본 사용: {team_name}")
                        else:
                            # MLB는 DB 매핑(team_info의 team_full_name)을 우선 적용
                            mapped_name = (
                                self.team_mappings.get(team_name, team_name)
                                if self.team_mappings else team_name
                            )
                            match_dict[field_name] = mapped_name
                            if mapped_name != team_name:
                                logger.debug(f"🔍 {self.league_code} 팀명 매핑: {team_name} → {mapped_name}")
                    elif value:  # 빈 값이 아닌 경우만 추가
                        match_dict[field_name] = value

        except Exception as e:
            logger.warning(f"❌ 매치 데이터 파싱 오류: {str(e)}")

        return match_dict

    def parse_recent_games_summary(self) -> Dict[str, Optional[Dict[str, str]]]:  # noqa: E501
        """최근 10경기 평균 파싱 (디폴트)"""
        return self.parse_recent_n_games(10)

    def parse_recent_n_games(
        self, n: int = 10
    ) -> Dict[str, Optional[Dict[str, str]]]:
        """최근 n경기 평균 파싱 (공통 로직)"""
        recent_n_games: Dict[str, Optional[Dict[str, str]]] = {
            "home": None, "away": None
        }

        tfoot = self.soup.find("tfoot", id="seasonLatestTeamRecordSum")
        if not isinstance(tfoot, Tag):
            return recent_n_games

        rows = tfoot.find_all("tr")
        if len(rows) < 2:
            return recent_n_games

        mapping = self.get_recent_summary_mapping()

        # 홈팀 평균 파싱 - 타입 확인
        if isinstance(rows[0], Tag):
            home_dict = BaseStatsParser._parse_avg_row(
                rows[0], mapping["home"]
            )
            if home_dict:
                recent_n_games["home"] = home_dict

        # 원정팀 평균 파싱 - 타입 확인
        if isinstance(rows[1], Tag):
            away_dict = BaseStatsParser._parse_avg_row(
                rows[1], mapping["away"]
            )
            if away_dict:
                recent_n_games["away"] = away_dict

        return recent_n_games

    @staticmethod
    def _parse_avg_row(
        row: Any, column_mapping: Dict[str, int]
    ) -> Optional[Dict[str, str]]:
        """평균 행 파싱 (공통 로직)"""
        if not isinstance(row, Tag):
            return None

        tds = row.find_all("td")
        if not tds:
            return None

        avg_dict = {}
        try:
            for field_name, column_index in column_mapping.items():
                if column_index < len(tds):
                    value = tds[column_index].get_text(strip=True)
                    
                    # 특별 처리
                    if field_name == "record":
                        avg_dict[field_name] = map_result(value)
                    elif value:  # 빈 값이 아닌 경우만 추가
                        avg_dict[field_name] = value

        except Exception as e:
            logger.warning(f"❌ 평균 데이터 파싱 오류: {str(e)}")
            return None

        return avg_dict if avg_dict else None

    def parse_season_stats(self) -> Dict[str, Dict[str, str]]:
        """역대 시즌 통계 파싱 (가장 최근 1개 시즌만)"""
        historical_stats = {}

        # 테이블 찾기
        table = self.soup.find("table", id="tbl3")
        if not isinstance(table, Tag):
            logger.warning(f"🔍 [{self.league_code}] tbl3 테이블을 찾을 수 없음")
            return historical_stats

        tbody = table.find("tbody", id="history_record")
        if not isinstance(tbody, Tag):
            logger.warning(f"🔍 [{self.league_code}] history_record tbody를 찾을 수 없음")
            return historical_stats

        rows = tbody.find_all("tr")
        
        mapping = self.get_season_stats_mapping()
        
        # 🚀 가장 최근 시즌만 파싱 (첫 번째 행)
        if rows:
            row = rows[0]  # 첫 번째 행이 가장 최근 시즌
            if isinstance(row, Tag):
                tds = row.find_all("td")
                
                if len(tds) >= 21:  # 최소 컬럼 수 확인
                    stats_dict = {}
                    try:
                        # 시즌 정보 추출 (첫 번째 컬럼, 키로만 사용)
                        season_text = tds[0].get_text(strip=True)
                        if season_text:
                            # 매핑된 데이터 추출
                            for field_name, column_index in mapping.items():
                                if column_index < len(tds):
                                    value = tds[column_index].get_text(strip=True)
                                    if value:  # 빈 값이 아닌 경우만 추가
                                        stats_dict[field_name] = value

                            # 리그 코드 추가
                            stats_dict["league_code"] = self.league_code

                            # 시즌을 키로 사용
                            historical_stats[season_text] = stats_dict
                            logger.debug(f"✅ [{self.league_code}] 최근 시즌 {season_text} 파싱 완료")

                    except Exception as e:
                        logger.warning(f"❌ [{self.league_code}] 최근 시즌 통계 파싱 오류: {str(e)}")
                else:
                    logger.warning(f"🔍 [{self.league_code}] 첫 번째 행: 컬럼 수 부족 ({len(tds)}/21)")
        
        return historical_stats 