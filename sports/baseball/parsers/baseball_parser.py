"""
야구 데이터 파서 구현
기존 파싱 로직을 새로운 아키텍처에 통합
"""
from typing import Dict, Any, Optional, List, Union
from abc import ABC, abstractmethod

from bs4 import BeautifulSoup, Tag

from core.interfaces.parser import SportParser, ParseResult, ParseStatus
from core.exceptions import ParsingException as DataParsingException
from utils.helpers import (convert_date_str, map_pitcher_stats, map_result,
                           remove_duplication)
from utils.logger import Logger

logger = Logger(__name__)


class BaseballData:
    """야구 데이터 모델"""
    
    def __init__(self, player_name: str, league: str, **kwargs):
        self.player_name = player_name
        self.league = league
        self.profile = kwargs.get('profile', {})
        self.season_stats = kwargs.get('season_stats', {})
        self.recent_games = kwargs.get('recent_games', {})
        self.career_stats = kwargs.get('career_stats', [])
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리로 변환"""
        return {
            "player_name": self.player_name,
            "league": self.league,
            "profile": self.profile,
            "season_stats": self.season_stats,
            "recent_games": self.recent_games,
            "career_stats": self.career_stats
        }


class BaseStatsParserCore(ABC):
    """리그별 파서의 베이스 코어 로직 (기존 코드 유지)"""

    def __init__(self, html: str, team_mappings: Optional[Dict[str, str]] = None):
        self.soup = BeautifulSoup(html, "html.parser")
        self.team_mappings = team_mappings or {}
        self.league_code = self.get_league_code()

    @abstractmethod
    def get_league_code(self) -> str:
        """리그 코드 반환"""
        pass

    @abstractmethod
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """시즌 요약 컬럼 매핑 반환"""
        pass

    @abstractmethod
    def get_recent_games_mapping(self) -> Dict[str, Dict[str, int]]:
        """최근 경기 컬럼 매핑 반환"""
        pass

    @abstractmethod
    def get_recent_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """최근 경기 요약 컬럼 매핑 반환"""
        pass

    @abstractmethod
    def get_season_stats_mapping(self) -> Dict[str, int]:
        """역대 시즌 통계 컬럼 매핑 반환"""
        pass
    
    def parse_season_summary(self) -> Dict[str, Optional[Dict[str, str]]]:
        """시즌 전체 성적 파싱 (기존 로직 유지)"""
        season_stats = {
            "home": None, "away": None, "total": None
        }

        mapping = self.get_season_summary_mapping()

        # 홈 통계 파싱
        home_dict = self._parse_season_row("home_record", mapping["home"])
        if home_dict:
            home_dict["league_code"] = self.league_code
            season_stats["home"] = home_dict

        # 원정 통계 파싱
        away_dict = self._parse_season_row("away_record", mapping["away"])
        if away_dict:
            away_dict["league_code"] = self.league_code
            season_stats["away"] = away_dict

        # 전체 통계 파싱
        total_dict = self._parse_season_row("all_record", mapping["total"])
        if total_dict:
            total_dict["league_code"] = self.league_code
            season_stats["total"] = total_dict

        return season_stats
    
    def _parse_season_row(self, row_id: str, column_mapping: Dict[str, int]) -> Optional[Dict[str, str]]:
        """시즌 통계 행 파싱 (기존 로직 유지)"""
        row = self.soup.find("tr", id=row_id)
        if not isinstance(row, Tag):
            return None

        tds = row.find_all("td")
        if not tds:
            return None

        stats_dict = {}
        try:
            for field_name, column_index in column_mapping.items():
                if column_index < len(tds):
                    value = tds[column_index].get_text(strip=True)
                    if value:
                        stats_dict[field_name] = value

        except Exception as e:
            logger.warning(f"❌ 시즌 통계 파싱 오류 [{row_id}]: {str(e)}")
            return None

        return stats_dict if stats_dict else None


class KBOStatsParser(BaseStatsParserCore):
    """KBO 리그 파서 (기존 로직 유지)"""
    
    def get_league_code(self) -> str:
        return "KBO"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {"wins": 1, "losses": 2, "draws": 3, "era": 4, "whip": 5},
            "away": {"wins": 1, "losses": 2, "draws": 3, "era": 4, "whip": 5},
            "total": {"wins": 1, "losses": 2, "draws": 3, "era": 4, "whip": 5}
        }
    
    def get_recent_games_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {"result": 1, "opponent": 2, "score": 3},
            "away": {"result": 1, "opponent": 2, "score": 3}
        }
    
    def get_recent_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "recent_10": {"wins": 1, "losses": 2, "era": 3}
        }
    
    def get_season_stats_mapping(self) -> Dict[str, int]:
        return {"year": 0, "team": 1, "games": 2, "wins": 3, "losses": 4}


class MLBStatsParser(BaseStatsParserCore):
    """MLB 리그 파서"""
    
    def get_league_code(self) -> str:
        return "MLB"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {"wins": 1, "losses": 2, "era": 3, "whip": 4},
            "away": {"wins": 1, "losses": 2, "era": 3, "whip": 4},
            "total": {"wins": 1, "losses": 2, "era": 3, "whip": 4}
        }
    
    def get_recent_games_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {"result": 1, "opponent": 2, "score": 3},
            "away": {"result": 1, "opponent": 2, "score": 3}
        }
    
    def get_recent_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "recent_10": {"wins": 1, "losses": 2, "era": 3}
        }
    
    def get_season_stats_mapping(self) -> Dict[str, int]:
        return {"year": 0, "team": 1, "games": 2, "wins": 3, "losses": 4}


class NPBStatsParser(BaseStatsParserCore):
    """NPB 리그 파서"""
    
    def get_league_code(self) -> str:
        return "NPB"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {"wins": 1, "losses": 2, "era": 3, "whip": 4},
            "away": {"wins": 1, "losses": 2, "era": 3, "whip": 4},
            "total": {"wins": 1, "losses": 2, "era": 3, "whip": 4}
        }
    
    def get_recent_games_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {"result": 1, "opponent": 2, "score": 3},
            "away": {"result": 1, "opponent": 2, "score": 3}
        }
    
    def get_recent_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "recent_10": {"wins": 1, "losses": 2, "era": 3}
        }
    
    def get_season_stats_mapping(self) -> Dict[str, int]:
        return {"year": 0, "team": 1, "games": 2, "wins": 3, "losses": 4}


class BaseballParser(SportParser[BaseballData]):
    """
    야구 데이터 파서
    기존 파싱 로직을 새로운 인터페이스에 통합
    """
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None, **kwargs):
        super().__init__()
        self.team_mappings = team_mappings or {}
        self.league_parsers = {
            "KBO": KBOStatsParser,
            "MLB": MLBStatsParser,
            "NPB": NPBStatsParser
        }
    
    def parse_team_data(self, html: str, team_name: str, **kwargs) -> ParseResult[BaseballData]:
        """팀 데이터 파싱"""
        try:
            # 팀 데이터 파싱 로직 구현
            league = kwargs.get('league', 'KBO')
            
            # 기본 팀 정보 파싱
            team_data = BaseballData(
                player_name=team_name,
                league=league,
                profile={"type": "team", "name": team_name}
            )
            
            return ParseResult(
                status=ParseStatus.SUCCESS,
                data=team_data,
                metadata={"type": "team", "league": league}
            )
            
        except Exception as e:
            return ParseResult(
                status=ParseStatus.FAILED,
                errors=[f"팀 데이터 파싱 실패: {str(e)}"]
            )
    
    def parse_player_data(self, html: str, player_name: str, **kwargs) -> ParseResult[BaseballData]:
        """선수 데이터 파싱 (기존 로직 적용)"""
        try:
            league = kwargs.get('league', 'KBO')
            
            # 리그별 파서 선택
            parser_class = self.league_parsers.get(league, KBOStatsParser)
            parser = parser_class(html, self.team_mappings)
            
            # 프로필 파싱
            profile = self._parse_player_profile(html, player_name)
            
            # 시즌 통계 파싱
            season_stats = parser.parse_season_summary()
            
            # 최근 경기 파싱
            recent_games = self._parse_recent_games(parser)
            
            # 커리어 통계 파싱
            career_stats = self._parse_career_stats(parser)
            
            player_data = BaseballData(
                player_name=player_name,
                league=league,
                profile=profile,
                season_stats=season_stats,
                recent_games=recent_games,
                career_stats=career_stats
            )
            
            return ParseResult(
                status=ParseStatus.SUCCESS,
                data=player_data,
                metadata={
                    "league": league,
                    "player_name": player_name,
                    "parsing_time": "calculated"
                }
            )
            
        except Exception as e:
            logger.error(f"선수 데이터 파싱 실패 - {player_name}: {e}")
            return ParseResult(
                status=ParseStatus.FAILED,
                errors=[f"선수 데이터 파싱 실패: {str(e)}"]
            )
    
    def parse_league_data(self, html: str, league_name: str, **kwargs) -> ParseResult[BaseballData]:
        """리그 데이터 파싱"""
        try:
            # 리그 전체 데이터 파싱 로직
            league_data = BaseballData(
                player_name=league_name,
                league=league_name,
                profile={"type": "league", "name": league_name}
            )
            
            return ParseResult(
                status=ParseStatus.SUCCESS,
                data=league_data,
                metadata={"type": "league"}
            )
            
        except Exception as e:
            return ParseResult(
                status=ParseStatus.FAILED,
                errors=[f"리그 데이터 파싱 실패: {str(e)}"]
            )
    
    def _parse_player_profile(self, html: str, player_name: str) -> Dict[str, Any]:
        """선수 프로필 파싱 (기존 로직 적용)"""
        soup = BeautifulSoup(html, "html.parser")
        profile = {"name": player_name}
        
        try:
            # 기본 정보 파싱
            info_box = soup.find("div", class_="infoBox")
            if info_box:
                # 프로필 정보 추출 로직
                profile.update(self._extract_profile_info(info_box))
            
        except Exception as e:
            logger.debug(f"프로필 파싱 오류 - {player_name}: {e}")
        
        return profile
    
    def _extract_profile_info(self, info_box) -> Dict[str, Any]:
        """프로필 정보 추출"""
        profile_info = {}
        
        try:
            # 기본 정보 테이블 파싱
            rows = info_box.find_all("tr")
            for row in rows:
                cells = row.find_all(["th", "td"])
                if len(cells) >= 2:
                    key = cells[0].get_text(strip=True)
                    value = cells[1].get_text(strip=True)
                    if key and value:
                        profile_info[key] = value
                        
        except Exception as e:
            logger.debug(f"프로필 정보 추출 오류: {e}")
        
        return profile_info
    
    def _parse_recent_games(self, parser: BaseStatsParserCore) -> Dict[str, Any]:
        """최근 경기 파싱"""
        try:
            return parser.parse_recent_games() if hasattr(parser, 'parse_recent_games') else {}
        except Exception as e:
            logger.debug(f"최근 경기 파싱 오류: {e}")
            return {}
    
    def _parse_career_stats(self, parser: BaseStatsParserCore) -> List[Dict[str, Any]]:
        """커리어 통계 파싱"""
        try:
            if hasattr(parser, 'parse_season_stats'):
                return parser.parse_season_stats()
            return []
        except Exception as e:
            logger.debug(f"커리어 통계 파싱 오류: {e}")
            return []
    
    def validate_data(self, data: BaseballData) -> bool:
        """데이터 유효성 검증"""
        if not data.player_name:
            return False
        
        if not data.league:
            return False
        
        # 필수 프로필 정보 확인
        if not data.profile.get("name"):
            return False
        
        return True


class BaseballPlayerData:
    """야구 선수 데이터 모델"""

    def __init__(self, player_name: str, team_name: str, league: str, league_id: str, position: str, **kwargs):
        self.player_name = player_name
        self.team_name = team_name
        self.league = league
        self.league_id = league_id
        self.position = position
        self.profile = kwargs.get('profile', {})
        self.season_stats = kwargs.get('season_stats', {})
        self.career_stats = kwargs.get('career_stats', [])

    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리로 변환"""
        return {
            "player_name": self.player_name,
            "team_name": self.team_name,
            "league": self.league,
            "league_id": self.league_id,
            "position": self.position,
            "profile": self.profile,
            "season_stats": self.season_stats,
            "career_stats": self.career_stats
        }


class BaseballPlayerParser:
    """야구 선수 개별 파서"""

    def __init__(self, html: str):
        self.soup = BeautifulSoup(html, "html.parser")

    def parse_player_data(self, player_name: str, **kwargs) -> BaseballPlayerData:
        """선수 데이터 파싱"""
        try:
            league_id = kwargs.get('league_id', 'BB001')
            league = kwargs.get('league', 'KBO')
            team_name = kwargs.get('team_name', '')
            position = kwargs.get('position', '')

            # 프로필 파싱
            profile = self._parse_player_profile(player_name)

            # 시즌 통계 파싱
            season_stats = self._parse_player_season_stats()

            # 커리어 통계 파싱
            career_stats = self._parse_player_career_stats()

            return BaseballPlayerData(
                player_name=player_name,
                team_name=team_name,
                league=league,
                league_id=league_id,
                position=position,
                profile=profile,
                season_stats=season_stats,
                career_stats=career_stats
            )

        except Exception as e:
            logger.error(f"야구 선수 데이터 파싱 실패 {player_name}: {e}")
            return BaseballPlayerData(
                player_name=player_name,
                team_name=kwargs.get('team_name', ''),
                league=kwargs.get('league', ''),
                league_id=kwargs.get('league_id', ''),
                position=kwargs.get('position', ''),
                profile={},
                season_stats={},
                career_stats={}
            )

    def _parse_player_profile(self, player_name: str) -> Dict[str, Any]:
        """선수 프로필 파싱"""
        try:
            profile = {
                'player_name': player_name,
                'birth_date': '',
                'height': '',
                'weight': '',
                'position': '',
                'back_number': '',
                'team': '',
                'batting_hand': '',
                'throwing_hand': ''
            }

            # 선수 정보 리스트에서 추출
            info_list = self.soup.find('ul')
            if info_list:
                items = info_list.find_all('li')

                for item in items:
                    text = item.get_text(strip=True)

                    if '생년월일' in text:
                        profile['birth_date'] = text.replace('생년월일', '').strip()
                    elif '신장' in text:
                        profile['height'] = text.replace('신장', '').strip()
                    elif '체중' in text:
                        profile['weight'] = text.replace('체중', '').strip()
                    elif '포지션' in text:
                        profile['position'] = text.replace('포지션', '').strip()
                    elif '등번호' in text:
                        profile['back_number'] = text.replace('등번호', '').strip()
                    elif '타석' in text:
                        profile['batting_hand'] = text.replace('타석', '').strip()
                    elif '투구' in text:
                        profile['throwing_hand'] = text.replace('투구', '').strip()

            return profile

        except Exception as e:
            logger.debug(f"야구 선수 프로필 파싱 실패 {player_name}: {e}")
            return {'player_name': player_name}

    def _parse_player_season_stats(self) -> Dict[str, Any]:
        """선수 시즌 통계 파싱"""
        try:
            stats = {}

            # 시즌 통계 테이블 찾기
            tables = self.soup.find_all('table')

            for table in tables:
                caption = table.find('caption')
                if caption and '시즌 성적' in caption.get_text():
                    rows = table.find_all('tr')
                    if len(rows) >= 2:
                        # 헤더와 데이터 행
                        header_row = rows[0].find_all(['th', 'td'])
                        data_row = rows[1].find_all(['th', 'td'])

                        if len(header_row) == len(data_row):
                            for i, header in enumerate(header_row):
                                header_text = header.get_text(strip=True)
                                data_text = data_row[i].get_text(strip=True)
                                if header_text and data_text:
                                    stats[header_text] = data_text

            return stats

        except Exception as e:
            logger.debug(f"야구 선수 시즌 통계 파싱 실패: {e}")
            return {}

    def _parse_player_career_stats(self) -> Dict[str, Any]:
        """선수 커리어 통계 파싱"""
        try:
            stats = {}

            # 커리어 통계 테이블 찾기
            tables = self.soup.find_all('table')

            for table in tables:
                caption = table.find('caption')
                if caption and ('통산' in caption.get_text() or '커리어' in caption.get_text() or '역대시즌' in caption.get_text()):
                    rows = table.find_all('tr')
                    if len(rows) >= 2:
                        # 헤더와 데이터 행
                        header_row = rows[0].find_all(['th', 'td'])

                        # 여러 시즌 데이터가 있을 수 있음
                        career_data = []
                        for row in rows[1:]:
                            data_row = row.find_all(['th', 'td'])
                            if len(data_row) == len(header_row):
                                season_data = {}
                                for i, header in enumerate(header_row):
                                    header_text = header.get_text(strip=True)
                                    data_text = data_row[i].get_text(strip=True)
                                    if header_text and data_text:
                                        season_data[header_text] = data_text
                                if season_data:
                                    career_data.append(season_data)

                        stats['career_seasons'] = career_data

            return stats

        except Exception as e:
            logger.debug(f"야구 선수 커리어 통계 파싱 실패: {e}")
            return {}