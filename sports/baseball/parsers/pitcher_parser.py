# flake8: noqa
"""
투수 파서 전략 모듈: Strategy 패턴을 사용한 HTML 파싱 전략 구현
SOLID 원칙 적용으로 효율화된 코드
"""
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Generic, List, Optional, Tuple, TypeVar

from bs4 import BeautifulSoup, Tag

from utils.common import extract_league_id, extract_team_id, safe_float
from utils.exceptions import ParsingError
from utils.helpers import normalize_innings

# 로거 설정
logger = logging.getLogger(__name__)

# 제네릭 타입 변수 정의
T = TypeVar('T')


class ParserStrategy(Generic[T], ABC):
    """
    파서 전략 추상 클래스
    
    Strategy 패턴 적용: 각 웹사이트나 데이터 형식에 따라 
    다양한 파싱 전략 구현 가능
    """
    
    @abstractmethod
    def parse(self, html: str, **kwargs) -> T:
        """HTML 문자열을 파싱하여 원하는 형식의 데이터로 변환"""
        pass


# SRP: 테이블 매핑 관리만 담당
class TableColumnMapper:
    """테이블 컬럼 매핑 관리 클래스"""
    
    @staticmethod
    def get_season_stats_mapping() -> Dict[int, str]:
        """시즌 성적 테이블 매핑"""
        return {
            0: "games",                  # 경기수 (12)
            1: "innings",                # 이닝 (69 1/3)
            2: "era",                    # 평균자책점 (2.34)
            3: "win_rate",               # 승률 (0.714)
            4: "wins",                   # 승 (5)
            5: "losses",                 # 패 (2)
            6: "saves",                  # 세이브 (0)
            7: "holds",                  # 홀드 (0)
            8: "runs_allowed",           # 실점 총합 (18)
            9: "earned_runs",            # 자책점 (18)
            10: "home_runs_allowed",     # 피홈런 (9)
            11: "hits_allowed",          # 피안타 (51)
            12: "strikeouts",            # 탈삼진 (66)
            13: "base_on_balls",         # 볼넷 (14)
            14: "hit_by_pitch",          # 사구 (0)
            15: "wild_pitch",            # 폭투 (1)
            16: "balk"                   # 보크 (0)
        }
    
    @staticmethod
    def get_recent_games_mapping() -> Dict[int, str]:
        """최근 경기 테이블 매핑"""
        return {
            0: "game_date",              # 경기일 (25.06.02(월))
            1: "opponent_team",          # 상대팀 (세인카디)
            2: "wins_losses",            # 승패 (승/패/없음)
            3: "innings",                # 이닝 (6)
            4: "pitch_count",            # 투구 (81)
            5: "batters_faced",          # 타자 (23)
            6: "at_bats",                # 타수 (20)
            7: "hits_allowed",           # 안타 (4)
            8: "home_runs_allowed",      # 홈런 (0)
            9: "base_on_balls",          # 4구 (3)
            10: "hit_by_pitch",          # 사구 (0)
            11: "strikeouts",            # 탈삼진 (4)
            12: "wild_pitch",            # 폭투 (0)
            13: "balk",                  # 보크 (0)
            14: "runs_allowed",          # 실점 (1)
            15: "earned_runs",           # 자책 (1)
            16: "era"                    # 평균자책점 (1.50)
        }


class TableParser(ParserStrategy[List[Dict[str, Any]]]):
    """HTML 테이블 파싱 전략"""
    
    def __init__(self):
        self.mapper = TableColumnMapper()
    
    def parse(self, html: str, **kwargs) -> List[Dict[str, Any]]:
        """HTML 테이블을 파싱하여 행 데이터의 리스트로 변환"""
        soup = BeautifulSoup(html, 'html.parser')
        table = soup.find('table')
        # 타입 안전성을 위한 캐스팅
        safe_table = table if isinstance(table, Tag) else None
        return self.parse_table(safe_table)
    
    def parse_table(self, table: Optional[Tag], 
                    table_type: str = 'season') -> List[Dict[str, Any]]:
        """HTML 테이블 태그를 파싱하여 행 데이터의 리스트로 변환"""
        if not table:
            return []
        
        # 테이블 타입에 따라 매핑 선택
        mapping = (self.mapper.get_season_stats_mapping()
                   if table_type == 'season'
                   else self.mapper.get_recent_games_mapping())
        
        try:
            return TableParser._extract_table_data(table, mapping)
        except Exception as e:
            raise ParsingError('table', f"테이블 파싱 오류: {str(e)}")
    
    @staticmethod
    def _extract_table_data(table: Tag,
                            mapping: Dict[int, str]) -> List[Dict[str, Any]]:
        """테이블 데이터 추출"""
        data_list = []
        tbody = table.find('tbody')
        
        if isinstance(tbody, Tag):
            for row in tbody.find_all('tr'):
                if isinstance(row, Tag):
                    row_data = TableParser._extract_row_data(row, mapping)
                    if row_data:
                        data_list.append(row_data)
        
        return data_list
    
    @staticmethod
    def _extract_row_data(row: Tag,
                          mapping: Dict[int, str]) -> Dict[str, Any]:
        """행 데이터 추출"""
        cols = row.find_all('td')
        row_data = {}
        
        for i, col in enumerate(cols):
            if isinstance(col, Tag):
                key = mapping.get(i, f'col_{i+1}')
                row_data[key] = col.text.strip()
        
        return row_data


# SRP: URL 생성만 담당
class URLBuilder:
    """URL 생성 클래스"""
    
    @staticmethod
    def build_team_url(league_id: str, team_id: str) -> str:
        """팀 URL 생성"""
        if not league_id or not team_id:
            return ''
        
        base = "https://www.betman.co.kr/main/mainPage/gameinfo"
        path = (f"/bsTeamDetail.do?item=BS&leagueId={league_id}"
                f"&teamId={team_id}")
        return base + path


class TeamInfoParser(ParserStrategy[Tuple[str, str, str, str]]):
    """팀 정보 파싱 전략"""
    
    def __init__(self):
        self.url_builder = URLBuilder()
    
    def parse(self, html: str, **kwargs) -> Tuple[str, str, str, str]:
        """팀 HTML을 파싱하여 팀명, 투수명, 팀 URL, 팀 ID 추출"""
        soup = BeautifulSoup(html, 'html.parser')
        tb_div = soup.find('div', class_='tb')
        # 타입 안전성을 위한 캐스팅
        safe_tb_div = tb_div if isinstance(tb_div, Tag) else None
        return self.extract_team_info(safe_tb_div)
    
    def extract_team_info(self,
                          tb_div: Optional[Tag]) -> Tuple[str, str, str, str]:
        """팀 블록에서 팀명, 투수명, 팀 URL, 팀 ID 추출"""
        if not tb_div:
            return ('', '', '', '')
            
        try:
            team_name, pitcher_name = TeamInfoParser._extract_names(tb_div)
            team_url, team_id = self._extract_url_and_id(tb_div)
            
            return team_name, pitcher_name, team_url, team_id
            
        except Exception as e:
            raise ParsingError('team_info', f"팀 정보 파싱 오류: {str(e)}")
    
    @staticmethod
    def _extract_names(tb_div: Tag) -> Tuple[str, str]:
        """팀명과 투수명 추출"""
        team_a = tb_div.find('a', class_='team')
        pitcher_tag = tb_div.find('span', class_='player')

        team_name = team_a.text.strip() if isinstance(team_a, Tag) else ''
        pitcher_name = ''
        if isinstance(pitcher_tag, Tag):
            pitcher_name = pitcher_tag.text.strip()
        
        return team_name, pitcher_name
    
    def _extract_url_and_id(self, tb_div: Tag) -> Tuple[str, str]:
        """팀 URL과 팀 ID 추출"""
        team_a = tb_div.find('a', class_='team')
        
        if not isinstance(team_a, Tag):
            return '', ''
        
        href_attr = team_a.get('href')
        href = href_attr if isinstance(href_attr, str) else ''
        
        league_id = extract_league_id(href)
        team_id = extract_team_id(href)
        
        team_url = self.url_builder.build_team_url(league_id, team_id)
        
        return team_url, team_id


# SRP: 날짜 변환만 담당
class DateConverter:
    """날짜 변환 유틸리티"""
    
    @staticmethod
    def convert_date_to_key(date_str: str) -> str:
        """날짜 문자열을 키 형태로 변환: '25.06.02(월)' -> '20250602'"""
        try:
            # 괄호와 요일 제거: "25.06.02(월)" -> "25.06.02"
            date_clean = date_str.split('(')[0].strip()
            
            # 점으로 분리: "25.06.02" -> ["25", "06", "02"]
            parts = date_clean.split('.')
            if len(parts) == 3:
                year, month, day = parts
                # 2자리 연도를 4자리로 변환 (25 -> 2025)
                if len(year) == 2:
                    year = f"20{year}"
                
                # 형태 변환: "2025" + "06" + "02" = "20250602"
                return f"{year}{month.zfill(2)}{day.zfill(2)}"
            
        except Exception:
            # 날짜 변환 실패 시 빈 문자열 반환
            pass
        
        return ""


# SRP: 리그 정보 관리만 담당
class LeagueService:
    """리그 정보 서비스 - DB 기반 동적 매핑"""
    
    def get_league_from_team_name(self, team_name: str) -> str:
        """팀명으로부터 리그 결정 - team_info 테이블에서 직접 조회"""
        if not team_name:
            return ""
        try:
            from database.database import connect_supabase
            client = connect_supabase()
            if not client:
                return ""
            
            # 🚀 team_info에서 sportic_league_id 조회
            response = client.table('team_info').select(
                'sportic_league_id'
            ).eq('team_name', team_name).execute()
            
            if response.data and len(response.data) > 0:
                # sportic_league_id를 리그명으로 변환
                league_id = response.data[0].get('sportic_league_id', '')
                if league_id:
                    # bs002 -> MLB, bs001 -> KBO, bs004 -> NPB
                    league_mapping = {
                        'bs002': 'MLB',
                        'bs001': 'KBO', 
                        'bs004': 'NPB'
                    }
                    mapped_league = league_mapping.get(league_id, '')
                    if mapped_league:
                        logger.debug(f"팀명으로 리그 매핑: {team_name} -> {league_id} -> {mapped_league}")
                        return mapped_league
                    
        except Exception as e:
            logger.debug(f"리그 조회 실패: {e}")
        return ""


# SRP: 팀명 매핑만 담당
class TeamNameMapper:
    """팀명 매핑 서비스 - 리그별 매핑 전략"""
    
    def __init__(
        self,
        team_mappings: Optional[Dict[str, str]] = None,
        league: str = "",
        use_dynamic_mapping: bool = True,
    ):
        """TeamNameMapper

        Args:
            team_mappings: 외부에서 주입한 매핑 딕셔너리(테스트용/오버라이드용)
            league: 호출 컨텍스트의 리그 (MLB 등). 일부 리그는 매핑이 반드시 필요할 수 있음.
            use_dynamic_mapping: True 이면 Supabase `team_info` 테이블에서 매핑을 즉시 로드.
        """

        # 1) 우선 주입받은 매핑 사용
        self.team_mappings = team_mappings or {}
        self.league = league
        self.use_dynamic_mapping = use_dynamic_mapping

        # 2) 동적 매핑 활성화 & 아직 비어 있다면 Supabase 조회
        if use_dynamic_mapping and not self.team_mappings:
            try:
                # 지연 로딩(import cycle 방지)
                from database.database import (connect_supabase,
                                               get_team_mappings)

                client = connect_supabase()
                if client:
                    self.team_mappings = get_team_mappings(client)
            except Exception as exc:
                logger.debug(
                    "TeamNameMapper 동적 매핑 로드 실패: %s", exc
                )

        # 3) 역매핑(full_name -> short_name) 생성
        self._reverse_mappings: Dict[str, str] = {
            full_name: short_name
            for short_name, full_name in self.team_mappings.items()
        }
    
    def map_team_name(self, original_team_name: str) -> str:
        """리그별 팀명 매핑 전략 (데이터베이스 기반)"""
        # 🎯 데이터베이스 기반 팀명 매핑 사용 (KBO/NPB: 짧은이름, MLB: 풀네임)
        if self.use_dynamic_mapping and self.league:
            try:
                from database.database import (connect_supabase,
                                               get_team_display_name)
                client = connect_supabase()
                if client:
                    mapped_name = get_team_display_name(
                        client, original_team_name, self.league
                    )
                    if mapped_name != original_team_name:
                        logger.debug(
                            f"🔄 상대팀 매핑 ({self.league}): "
                            f"{original_team_name} → {mapped_name}"
                        )
                        return mapped_name
            except Exception as e:
                logger.debug(f"데이터베이스 팀명 매핑 실패: {e}")
        
        # 🚀 기존 로직 유지 (백업용)
        # 1) KBO, NPB: 역매핑 우선 적용 (full_name → short_name)
        if (self.league in ["KBO", "NPB"] and 
            original_team_name in self._reverse_mappings):
            return self._reverse_mappings[original_team_name]

        # 2) (MLB 전용) 짧은 → 긴 매핑 유지 호환 (기존 로직)
        if self.league == "MLB" and self.team_mappings:
            return self._apply_team_mapping(original_team_name)

        # 3) 매핑 불가 시 기존 명칭 유지
        return original_team_name
    
    def _apply_team_mapping(self, original_team_name: str) -> str:
        """MLB용 팀명 매핑 적용"""
        # 정확한 매칭 우선
        if original_team_name in self.team_mappings:
            return self.team_mappings[original_team_name]
        
        # 부분 매칭 시도
        for key, value in self.team_mappings.items():
            if original_team_name in key or key in original_team_name:
                return value
        
        return original_team_name


# SRP: 기본 정보 추출만 담당
class BasicInfoExtractor:
    """기본 선수 정보 추출기"""
    
    def __init__(self, league_service: LeagueService):
        self.league_service = league_service
    
    def extract_basic_info(self, soup: BeautifulSoup, 
                          player_info: Dict[str, Any]) -> None:
        """기본 선수 정보 추출 - 실제 betman HTML 구조에 맞춰 수정"""
        
        # 실제 betman 구조에서 기본 정보 추출
        self._extract_player_number_betman(soup, player_info)
        self._extract_team_info_betman(soup, player_info)
        self._extract_birth_info_betman(soup, player_info)
        self._extract_physical_info_betman(soup, player_info)
        self._extract_season_summary_betman(soup, player_info)
    
    def _extract_player_number_betman(self, soup: BeautifulSoup, player_info: Dict[str, Any]) -> None:
        """실제 betman 구조에서 선수 번호 추출"""
        # "No. 75" 패턴 찾기
        for element in soup.find_all(text=True):
            if 'No.' in str(element):
                try:
                    # "No. 75" -> 75 추출
                    no_text = str(element).strip()
                    if 'No.' in no_text:
                        number = no_text.split('No.')[1].strip()
                        player_info['no'] = int(number)
                        logger.debug(f"선수 번호 추출: {number}")
                        return
                except (ValueError, IndexError):
                    continue
        player_info['no'] = ""
    
    def _extract_team_info_betman(self, soup: BeautifulSoup, player_info: Dict[str, Any]) -> None:
        """실제 betman 구조에서 팀 정보 추출"""
        # 올바른 방법: HTML ID로 직접 추출
        team_tag = soup.find('strong', id='player_team_nm')
        if team_tag:
            team_name = team_tag.get_text().strip()
            player_info['team'] = team_name
            league = self.league_service.get_league_from_team_name(team_name)
            player_info['league'] = league.upper() if league else ''
            logger.debug(f"팀 정보 추출: {team_name}, 리그: {player_info['league']}")
            return
        else:
            # 디버깅: 팀 요소를 찾지 못한 경우
            logger.warning(f"⚠️ PARSER: strong#player_team_nm 요소 없음")
            # 모든 strong 태그 확인
            all_strong = soup.find_all('strong')
            logger.debug(f"모든 strong 태그: {[(s.get('id', 'no-id'), s.get_text(strip=True)) for s in all_strong[:10]]}")
        
        # 기본값
        player_info['team'] = ''
        player_info['league'] = ''
    
    def _extract_birth_info_betman(self, soup: BeautifulSoup, player_info: Dict[str, Any]) -> None:
        """실제 betman 구조에서 생년월일 추출"""
        # 방법 1: 구조화된 birth 요소에서 추출 (profile section)
        try:
            birth_year = soup.find('span', id='birth_year')
            birth_month = soup.find('span', id='birth_month')
            birth_day = soup.find('span', id='birth_day')
            
            if birth_year and birth_month and birth_day:
                year = birth_year.get_text().strip()
                month = birth_month.get_text().strip().zfill(2)
                day = birth_day.get_text().strip().zfill(2)
                
                if year and month and day:
                    birth_str = f"{year}-{month}-{day}"
                    player_info['birth'] = birth_str
                    logger.debug(f"생년월일 추출 (구조화): {birth_str}")
                    return
        except Exception as e:
            logger.debug(f"구조화된 생년월일 추출 실패: {e}")
        
        # 방법 2: 텍스트에서 "1996.01.30" 패턴 찾기 (fallback)
        for element in soup.find_all(text=True):
            text = str(element).strip()
            # YYYY.MM.DD 패턴 매칭
            if '.' in text and len(text) >= 8:
                parts = text.split('.')
                if len(parts) >= 3:
                    try:
                        year = int(parts[0])
                        month = int(parts[1])
                        day = int(parts[2])
                        if 1900 <= year <= 2010 and 1 <= month <= 12 and 1 <= day <= 31:
                            birth_str = f"{year}-{month:02d}-{day:02d}"
                            player_info['birth'] = birth_str
                            logger.debug(f"생년월일 추출 (텍스트): {birth_str}")
                            return
                    except (ValueError, IndexError):
                        continue
        
        # NPB 선수는 betman.co.kr에서 생년월일 정보를 제공하지 않음
        player_info['birth'] = ""
        logger.debug("생년월일 정보 없음 (NPB는 데이터 미제공)")
    
    def _extract_physical_info_betman(self, soup: BeautifulSoup, player_info: Dict[str, Any]) -> None:
        """실제 betman 구조에서 신체 정보 추출"""
        # "187.0cm", "109.0kg" 패턴 찾기
        for element in soup.find_all(text=True):
            text = str(element).strip()
            if 'cm' in text:
                try:
                    height_str = text.replace('cm', '').strip()
                    player_info['height'] = float(height_str)
                    logger.debug(f"신장 추출: {height_str}cm")
                except ValueError:
                    pass
            elif 'kg' in text:
                try:
                    weight_str = text.replace('kg', '').strip()
                    player_info['weight'] = weight_str
                    logger.debug(f"체중 추출: {weight_str}kg")
                except ValueError:
                    pass
        
        # 기본값 설정
        if 'height' not in player_info:
            player_info['height'] = 0
        if 'weight' not in player_info:
            player_info['weight'] = ""
    
    def _extract_season_summary_betman(self, soup: BeautifulSoup, player_info: Dict[str, Any]) -> None:
        """실제 betman 구조에서 시즌 요약 정보 추출"""
        season_summary = {}
        
        # emphasis 태그에서 주요 성적 추출 (ERA, 승수, 세이브, 탈삼진)
        emphasis_elements = soup.find_all('emphasis')
        for element in emphasis_elements:
            text = element.get_text().strip()
            # 부모 요소에서 라벨 확인
            parent = element.parent
            if parent:
                parent_text = parent.get_text().strip()
                if '평균자책점' in parent_text and text.replace('.', '').isdigit():
                    season_summary['era'] = text
                elif ('다승' in parent_text or '승' in parent_text) and text.isdigit():
                    season_summary['wins'] = text
                elif '세이브' in parent_text and text.isdigit():
                    season_summary['saves'] = text  
                elif '탈삼진' in parent_text and text.isdigit():
                    season_summary['strikeouts'] = text
        
        player_info['season_summary'] = season_summary
        logger.debug(f"시즌 요약 추출: {season_summary}")
    
    @staticmethod
    def _extract_player_number(soup: BeautifulSoup,
                               player_info: Dict[str, Any]) -> None:
        """선수 번호 추출"""
        backnum_tag = soup.find('strong', id='player_backnum')
        if isinstance(backnum_tag, Tag):
            backnum_text = backnum_tag.text.strip()
            try:
                player_info['no'] = int(backnum_text)
            except (ValueError, TypeError):
                player_info['no'] = ""
        else:
            player_info['no'] = ""
    
    def _extract_team_info(self, info_box: Tag, 
                          player_info: Dict[str, Any]) -> None:
        """팀 정보 추출 (리그는 항상 문자열로)"""
        team_tag = info_box.find('strong', id='player_team_nm')
        team_name = team_tag.text.strip() if isinstance(team_tag, Tag) else ''
        player_info['team'] = team_name
        league = self.league_service.get_league_from_team_name(team_name)
        # Ensure league is always a clean string (remove "League." prefix)
        if league:
            # "League.MLB" -> "MLB", "League.KBO" -> "KBO", "League.NPB" -> "NPB"
            clean_league = str(league).replace("League.", "").upper()
            player_info['league'] = clean_league
        else:
            player_info['league'] = ''
    
    @staticmethod
    def _extract_birth_info(info_box: Tag,
                            player_info: Dict[str, Any]) -> None:
        """생년월일 정보 추출"""
        birth_year = info_box.find('span', id='birth_year')
        birth_month = info_box.find('span', id='birth_month')
        birth_day = info_box.find('span', id='birth_day')
        
        birth_parts = []
        if isinstance(birth_year, Tag) and birth_year.text.strip():
            birth_parts.append(birth_year.text.strip())
        if isinstance(birth_month, Tag) and birth_month.text.strip():
            birth_parts.append(birth_month.text.strip().zfill(2))
        if isinstance(birth_day, Tag) and birth_day.text.strip():
            birth_parts.append(birth_day.text.strip().zfill(2))
        
        player_info['birth'] = (
            '-'.join(birth_parts) if len(birth_parts) == 3 else ''
        )
    
    @staticmethod
    def _extract_physical_info(info_box: Tag,
                               player_info: Dict[str, Any]) -> None:
        """신체 정보 추출"""
        height_tag = info_box.find('span', id='player_ht')
        height_text = (
            height_tag.text.strip() if isinstance(height_tag, Tag) else ''
        )
        player_info['height'] = safe_float(height_text)
        
        weight_tag = info_box.find('span', id='player_wt')
        player_info['weight'] = (
            weight_tag.text.strip() if isinstance(weight_tag, Tag) else ''
        )
    
    @staticmethod
    def _extract_season_summary(soup: BeautifulSoup,
                                player_info: Dict[str, Any]) -> None:
        """시즌 요약 정보 추출 (승, 패, 세이브, 삼진, ERA)"""
        season_summary = {}
        
        # ---- 시즌 성적 요소 추출 ----
        # 일부 페이지 구조 변경 대응: 기존 class 기반(span.win 등) + id 기반(em#player_*) 모두 지원

        def _find_stat_tag(primary: Dict[str, str],
                           fallback: Dict[str, str]) -> Tag:  # type: ignore[type-arg]
            """첫 번째 선택자로 검색 후 없으면 두 번째 선택자로 재검색"""
            tag = soup.find(**primary)
            if not isinstance(tag, Tag):
                tag = soup.find(**fallback)
            return tag

        # 승 수
        win_tag = _find_stat_tag(
            {"name": "span", "class_": "win"},
            {"name": "em", "id": "player_win"}
        )

        # 세이브 수
        save_tag = _find_stat_tag(
            {"name": "span", "class_": "save"},
            {"name": "em", "id": "player_save"}
        )

        # 탈삼진 수
        k_tag = _find_stat_tag(
            {"name": "span", "class_": "k"},
            {"name": "em", "id": "player_k"}
        )

        # ERA
        era_tag = _find_stat_tag(
            {"name": "span", "class_": "era"},
            {"name": "em", "id": "player_era"}
        )
        
        # 디버그: 실제로 어떤 값들이 추출되는지 확인
        logger.debug(f"[season_summary] win_tag: {win_tag}")
        logger.debug(f"[season_summary] save_tag: {save_tag}")
        logger.debug(f"[season_summary] k_tag: {k_tag}")
        logger.debug(f"[season_summary] era_tag: {era_tag}")
        
        season_summary['wins'] = (
            win_tag.text.strip() if isinstance(win_tag, Tag) else ''
        )
        season_summary['saves'] = (
            save_tag.text.strip() if isinstance(save_tag, Tag) else ''
        )
        season_summary['strikeouts'] = (
            k_tag.text.strip() if isinstance(k_tag, Tag) else ''
        )
        season_summary['era'] = (
            era_tag.text.strip() if isinstance(era_tag, Tag) else ''
        )
        
        # 디버그: 최종 추출된 값들 확인
        logger.debug(f"[season_summary] 최종 결과: {season_summary}")
        
        # 이닝 필드가 있다면 정규화
        if 'innings' in season_summary:
            season_summary['innings'] = normalize_innings(season_summary['innings'])
        
        player_info['season_summary'] = season_summary


# SRP: 최근 경기 추출만 담당
class GameLogExtractor:
    """최근 경기 추출기"""
    
    def __init__(self, date_converter: DateConverter, 
                 team_mapper: TeamNameMapper):
        self.date_converter = date_converter
        self.team_mapper = team_mapper
    
    def extract_appearance_records(self, soup: BeautifulSoup,
                                  player_info: Dict[str, Any]) -> None:
        """출장 기록 추출 - 실제 betman HTML 구조에 맞춰 수정"""
        
        # 실제 betman 구조: 출전기록 테이블 찾기
        games_table = None
        
        # 방법 1: 캡션으로 출전기록 테이블 찾기
        for table in soup.find_all('table'):
            caption = table.find('caption')
            if caption and '출전기록' in caption.get_text():
                games_table = table
                logger.debug("캡션으로 출전기록 테이블 발견")
                break
        
        # 방법 2: 테이블 구조로 출전기록 테이블 찾기
        if not games_table:
            for table in soup.find_all('table'):
                headers = table.find_all(['th', 'columnheader'])
                header_texts = [th.get_text().strip() for th in headers]
                # 출전기록 테이블의 특징적인 컬럼들
                if any(col in ' '.join(header_texts) for col in ['경기일', '상대팀', '승패', '이닝', '투구']):
                    tbody = table.find('tbody')
                    if tbody and tbody.find_all('tr'):
                        games_table = table
                        logger.debug("구조 분석으로 출전기록 테이블 발견")
                        break
        
        if games_table:
            recent_games = self._extract_recent_games_betman(games_table)
            player_info['recent_games'] = recent_games
            logger.debug(f"최근 경기 추출 성공: {len(recent_games)}개 경기")
            
            # 요약 정보도 추출
            self._extract_recent_games_summary_betman(games_table, player_info)
        else:
            logger.warning("출전기록 테이블을 찾을 수 없음")
            player_info['recent_games'] = {}
    
    def _extract_recent_games_betman(self, table: Tag) -> Dict[str, Dict[str, Any]]:
        """실제 betman 구조에서 최근 경기 추출"""
        recent_games = {}
        
        tbody = table.find('tbody')
        if not tbody:
            return recent_games
        
        rows = tbody.find_all('tr')
        for row in rows:
            cells = row.find_all(['td', 'cell'])
            if len(cells) >= 17:  # 충분한 데이터 컬럼이 있어야 함
                try:
                    # 날짜 추출 및 변환
                    date_str = cells[0].get_text().strip()
                    if '(' in date_str:
                        # "25.07.06(일)" -> "20250706" 변환
                        date_part = date_str.split('(')[0].strip()
                        date_parts = date_part.split('.')
                        if len(date_parts) == 3:
                            year = f"20{date_parts[0]}"
                            month = date_parts[1].zfill(2)
                            day = date_parts[2].zfill(2)
                            date_key = f"{year}{month}{day}"
                            
                            # 상대팀 추출
                            team_cell = cells[1]
                            team_name = team_cell.get_text().strip()
                            
                            # 게임 데이터 구성
                            game_data = {
                                "opponent_team": team_name,
                                "wins_losses": cells[2].get_text().strip(),
                                "innings": cells[3].get_text().strip(),
                                "pitch_count": cells[4].get_text().strip(),
                                "batters_faced": cells[5].get_text().strip(),
                                "at_bats": cells[6].get_text().strip(),
                                "hits_allowed": cells[7].get_text().strip(),
                                "home_runs_allowed": cells[8].get_text().strip(),
                                "base_on_balls": cells[9].get_text().strip(),
                                "hit_by_pitch": cells[10].get_text().strip(),
                                "strikeouts": cells[11].get_text().strip(),
                                "wild_pitch": cells[12].get_text().strip(),
                                "balk": cells[13].get_text().strip(),
                                "runs_allowed": cells[14].get_text().strip(),
                                "earned_runs": cells[15].get_text().strip(),
                                "era": cells[16].get_text().strip()
                            }
                            
                            recent_games[date_key] = game_data
                            
                except (IndexError, ValueError) as e:
                    logger.debug(f"경기 데이터 추출 오류: {e}")
                    continue
        
        return recent_games
    
    def _extract_recent_games_summary_betman(self, table: Tag, player_info: Dict[str, Any]) -> None:
        """실제 betman 구조에서 최근 경기 요약 추출"""
        try:
            # tfoot에서 합계 행 찾기
            tfoot = table.find('tfoot')
            if tfoot:
                summary_row = tfoot.find('tr')
                if summary_row:
                    cells = summary_row.find_all(['td', 'cell'])
                    if len(cells) >= 17:
                        summary_data = {
                            "innings": cells[2].get_text().strip(),
                            "pitch_count": cells[3].get_text().strip(),
                            "batters_faced": cells[4].get_text().strip(),
                            "at_bats": cells[5].get_text().strip(),
                            "hits_allowed": cells[6].get_text().strip(),
                            "home_runs_allowed": cells[7].get_text().strip(),
                            "base_on_balls": cells[8].get_text().strip(),
                            "hit_by_pitch": cells[9].get_text().strip(),
                            "strikeouts": cells[10].get_text().strip(),
                            "wild_pitch": cells[11].get_text().strip(),
                            "balk": cells[12].get_text().strip(),
                            "runs_allowed": cells[13].get_text().strip(),
                            "earned_runs": cells[14].get_text().strip(),
                            "era": cells[15].get_text().strip()
                        }
                        
                        player_info['recent_games_summary'] = summary_data
                        logger.debug("최근 경기 요약 추출 성공")
                        return
            
            # 합계 행을 찾지 못한 경우 계산으로 대체
            recent_games = player_info.get('recent_games', {})
            if recent_games:
                logger.debug("최근 경기 데이터에서 요약 계산")
                player_info['recent_games_summary'] = self._calculate_summary_from_games(recent_games)
            else:
                player_info['recent_games_summary'] = {}
                
        except Exception as e:
            logger.warning(f"최근 경기 요약 추출 오류: {e}")
            player_info['recent_games_summary'] = {}
    
    def _calculate_summary_from_games(self, recent_games: Dict[str, Dict]) -> Dict[str, str]:
        """최근 경기 데이터로부터 요약 통계 계산"""
        if not recent_games:
            return {}
        
        try:
            # 숫자 필드들 합산
            totals = {
                'pitch_count': 0, 'batters_faced': 0, 'at_bats': 0,
                'hits_allowed': 0, 'home_runs_allowed': 0, 'base_on_balls': 0,
                'hit_by_pitch': 0, 'strikeouts': 0, 'wild_pitch': 0,
                'balk': 0, 'runs_allowed': 0, 'earned_runs': 0
            }
            
            innings_total = 0
            
            for game_data in recent_games.values():
                for field in totals.keys():
                    value = game_data.get(field, '0')
                    if isinstance(value, str) and value.strip().isdigit():
                        totals[field] += int(value)
                
                # 이닝 합산 처리
                innings = game_data.get('innings', '0')
                if innings:
                    try:
                        if ' ' in innings:
                            parts = innings.split(' ')
                            whole = int(parts[0])
                            fraction = 0
                            if len(parts) > 1 and '/' in parts[1]:
                                frac_parts = parts[1].split('/')
                                if len(frac_parts) == 2:
                                    fraction = int(frac_parts[0]) / int(frac_parts[1])
                            innings_total += whole + fraction
                        else:
                            innings_total += float(innings)
                    except (ValueError, ZeroDivisionError):
                        pass
            
            # 이닝 형식 변환
            whole_innings = int(innings_total)
            fraction_part = innings_total - whole_innings
            
            if abs(fraction_part) < 0.001:
                innings_formatted = str(whole_innings)
            elif abs(fraction_part - 1.0/3.0) < 0.001:
                innings_formatted = f"{whole_innings} 1/3"
            elif abs(fraction_part - 2.0/3.0) < 0.001:
                innings_formatted = f"{whole_innings} 2/3"
            else:
                innings_formatted = f"{whole_innings} {fraction_part:.1f}"
            
            # ERA 계산
            era = round((totals['earned_runs'] * 9) / innings_total, 2) if innings_total > 0 else 0.00
            
            summary = {field: str(totals[field]) for field in totals.keys()}
            summary['innings'] = innings_formatted
            summary['era'] = str(era)
            
            return summary
            
        except Exception as e:
            logger.error(f"최근 경기 요약 계산 오류: {e}")
            return {}

    def _extract_recent_games(self, table: Tag) -> Dict[str, Dict[str, Any]]:
        """최근 경기 추출"""
        recent_games = {}
        tbody = table.find('tbody')
        
        if isinstance(tbody, Tag):
            for row in tbody.find_all('tr'):
                if isinstance(row, Tag):
                    game_data = self._extract_single_game_data(row)
                    if game_data:
                        date_key, data = game_data
                        recent_games[date_key] = data
        
        return recent_games
    
    def _extract_single_game_data(self, 
                                 row: Tag) -> Optional[Tuple[str, Dict[str, Any]]]:
        """단일 게임 데이터 추출"""
        cols = row.find_all('td')
        if len(cols) < 17:
            return None
        
        # 날짜 추출
        date_str = cols[0].text.strip()
        if '(' not in date_str:
            return None
        
        date_key = self.date_converter.convert_date_to_key(date_str)
        if not date_key:
            return None
        
        # 상대팀명 추출 및 매핑
        opponent_td = cols[1]
        original_team_name = GameLogExtractor._extract_team_name_from_cell(opponent_td)
        team_name = self.team_mapper.map_team_name(original_team_name)
        
        # 게임 데이터 구성
        game_data = {
            "opponent_team": team_name,
            "wins_losses": GameLogExtractor._convert_wins_losses(cols[2].text.strip()),
            "innings": cols[3].text.strip(),
            "pitch_count": cols[4].text.strip(),
            "batters_faced": cols[5].text.strip(),
            "at_bats": cols[6].text.strip(),
            "hits_allowed": cols[7].text.strip(),
            "home_runs_allowed": cols[8].text.strip(),
            "base_on_balls": cols[9].text.strip(),
            "hit_by_pitch": cols[10].text.strip(),
            "strikeouts": cols[11].text.strip(),
            "wild_pitch": cols[12].text.strip(),
            "balk": cols[13].text.strip(),
            "runs_allowed": cols[14].text.strip(),
            "earned_runs": cols[15].text.strip(),
            "era": cols[16].text.strip()
        }
        
        return date_key, game_data
    
    @staticmethod
    def _extract_team_name_from_cell(cell: Tag) -> str:
        """셀에서 팀명 추출"""
        # <strong> 태그가 있으면 그 안의 텍스트 우선
        strong_tag = cell.find('strong')
        if strong_tag and isinstance(strong_tag, Tag):
            return strong_tag.get_text(strip=True)
        
        # <a> 태그가 있으면 그 안의 텍스트
        a_tag = cell.find('a')
        if a_tag and isinstance(a_tag, Tag):
            return a_tag.get_text(strip=True)
        
        # 일반 텍스트
        return cell.get_text(strip=True)
    
    @staticmethod
    def _convert_wins_losses(wins_losses_text: str) -> str:
        """승패 텍스트를 영문으로 변환 (승->W, 패->L, 무->D)"""
        text = wins_losses_text.strip()
        
        # 한글 승패를 영문으로 변환
        if text == "승":
            return "W"
        elif text == "패":
            return "L"
        elif text == "무":
            return "D"
        else:
            # 이미 영문이거나 다른 형태인 경우 그대로 반환
            return text
    
    @staticmethod
    def _extract_recent_games_summary(table: Tag,
                                     player_info: Dict[str, Any]) -> None:
        """최근 경기 요약 통계 추출 (합계 행)"""
        try:
            # 디버깅용 로그
            logger.debug("최근 경기 요약 추출 시작")
            
            # 1. 가장 직접적인 방법 - CSS 선택자 사용
            sum_row = table.select_one("tfoot#pitcher_past_sumRecord tr")
            if sum_row:
                logger.debug("CSS 선택자로 요약 행 찾음")
                sum_cells = sum_row.find_all("td")
                sum_values = [cell.get_text(strip=True) for cell in sum_cells]
                
                # 데이터 확인용 로그
                logger.debug(f"추출된 셀 개수: {len(sum_cells)}")
                logger.debug(f"추출된 값들: {sum_values}")
                
                if len(sum_values) >= 17:
                    # 여기서 요약 데이터 매핑 (테이블 구조에 맞게)
                    player_info["recent_games_summary"] = {
                        "innings": sum_values[3],
                        "pitch_count": sum_values[4], 
                        "batters_faced": sum_values[5],
                        "at_bats": sum_values[6],
                        "hits_allowed": sum_values[7],
                        "home_runs_allowed": sum_values[8],
                        "base_on_balls": sum_values[9],
                        "hit_by_pitch": sum_values[10],
                        "strikeouts": sum_values[11],
                        "wild_pitch": sum_values[12],
                        "balk": sum_values[13],
                        "runs_allowed": sum_values[14],
                        "earned_runs": sum_values[15],
                        "era": sum_values[16]
                    }
                    logger.debug(f"recent_games_summary 추출 성공: {player_info['recent_games_summary']}")
                    return
                else:
                    logger.debug(f"충분한 셀이 없음 - 셀 개수: {len(sum_values)}")
            
            # 2. 모든 tfoot 확인
            all_tfoots = table.find_all("tfoot")
            logger.debug(f"모든 tfoot 태그 개수: {len(all_tfoots)}")
            
            for i, tfoot in enumerate(all_tfoots):
                logger.debug(f"tfoot[{i}] 확인")
                row = tfoot.find("tr")
                if row:
                    sum_cells = row.find_all("td")
                    if len(sum_cells) >= 17:
                        sum_values = [cell.get_text(strip=True) for cell in sum_cells]
                        player_info["recent_games_summary"] = {
                            "innings": sum_values[3],
                            "pitch_count": sum_values[4], 
                            "batters_faced": sum_values[5],
                            "at_bats": sum_values[6],
                            "hits_allowed": sum_values[7],
                            "home_runs_allowed": sum_values[8],
                            "base_on_balls": sum_values[9],
                            "hit_by_pitch": sum_values[10],
                            "strikeouts": sum_values[11],
                            "wild_pitch": sum_values[12],
                            "balk": sum_values[13],
                            "runs_allowed": sum_values[14],
                            "earned_runs": sum_values[15],
                            "era": sum_values[16]
                        }
                        logger.debug(f"tfoot[{i}]에서 요약 추출 성공")
                        return
            
            # 3. 아래와 같이 직접 HTML을 검색
            logger.debug("HTML 직접 검색으로 요약 행 찾기 시도")
            html_str = str(table)
            if "pitcher_past_sumRecord" in html_str:
                logger.debug("HTML에서 pitcher_past_sumRecord 문자열 발견")
            
            # 4. 마지막 방법: 최근 경기 데이터로부터 요약 계산
            if player_info.get('recent_games') and len(player_info['recent_games']) > 0:
                logger.debug("최근 경기 데이터에서 요약 계산")
                recent_games_summary = GameLogExtractor._calculate_summary_from_recent_games(
                    player_info['recent_games']
                )
                player_info['recent_games_summary'] = recent_games_summary
                logger.debug(f"계산된 요약: {recent_games_summary}")
                return
                
            # 모든 방법 실패 시 빈 딕셔너리 반환
            logger.debug("모든 요약 추출 방법 실패, 빈 요약 반환")
            player_info['recent_games_summary'] = {}
                
        except Exception as e:
            logger.error(f"최근 경기 요약 추출 오류: {e}")
            logger.error(f"오류 상세: {str(e)}")
            player_info['recent_games_summary'] = {}
    
    @staticmethod
    def _calculate_summary_from_recent_games(recent_games: Dict[str, Dict]) -> Dict[str, str]:
        """최근 경기 데이터로부터 요약 통계 계산"""
        if not recent_games:
            return {}
            
        try:
            # 합산할 필드들
            num_fields = ['earned_runs', 'runs_allowed', 'hits_allowed', 
                         'home_runs_allowed', 'base_on_balls', 'hit_by_pitch',
                         'strikeouts', 'wild_pitch', 'balk', 'pitch_count', 
                         'batters_faced', 'at_bats']
            
            # 합산 초기화
            totals = {field: 0 for field in num_fields}
            innings_total = 0  # 이닝 합계 (특수 처리 필요)
            
            # 최근 경기 순회하며 합산
            for date, game_data in recent_games.items():
                for field in num_fields:
                    value = game_data.get(field, '0')
                    if isinstance(value, str) and value.strip():
                        try:
                            totals[field] += int(value)
                        except ValueError:
                            pass
                
                # 이닝 합산 (4 2/3 형식 또는 11 2 형식 처리)
                innings = game_data.get('innings', '0')
                if innings and isinstance(innings, str):
                    if ' ' in innings:
                        parts = innings.split(' ')
                        whole = int(parts[0])
                        fraction = 0
                        if len(parts) > 1:
                            if '/' in parts[1]:
                                # "4 2/3" 형식
                                fraction_parts = parts[1].split('/')
                                if len(fraction_parts) == 2:
                                    try:
                                        fraction = float(int(fraction_parts[0]) / int(fraction_parts[1]))
                                    except (ValueError, ZeroDivisionError):
                                        pass
                            else:
                                # "11 2" 형식 (축약형) - 아웃 카운트를 분수로 변환
                                try:
                                    outs = int(parts[1])
                                    if outs == 1:
                                        fraction = 1.0 / 3.0
                                    elif outs == 2:
                                        fraction = 2.0 / 3.0
                                except ValueError:
                                    pass
                        innings_total += whole + fraction
                    else:
                        try:
                            innings_total += float(innings)
                        except ValueError:
                            pass
            
            # 이닝 형식 변환 (11.666... -> 11 2/3)
            whole_innings = int(innings_total)
            fraction_part = innings_total - whole_innings
            
            if abs(fraction_part) < 0.001:  # 거의 0인 경우
                innings_formatted = str(whole_innings)
            elif abs(fraction_part - 1.0/3.0) < 0.001:  # 1/3에 가까운 경우
                innings_formatted = f"{whole_innings} 1/3"
            elif abs(fraction_part - 2.0/3.0) < 0.001:  # 2/3에 가까운 경우
                innings_formatted = f"{whole_innings} 2/3"
            else:
                # 다른 소수점은 반올림하여 가장 가까운 1/3 단위로 변환
                if fraction_part < 1.0/6.0:
                    innings_formatted = str(whole_innings)
                elif fraction_part < 0.5:
                    innings_formatted = f"{whole_innings} 1/3"
                elif fraction_part < 5.0/6.0:
                    innings_formatted = f"{whole_innings} 2/3"
                else:
                    innings_formatted = str(whole_innings + 1)
            
            # 결과 요약 구성
            summary = {field: str(totals[field]) for field in num_fields}
            summary['innings'] = innings_formatted
            
            # ERA 계산
            if innings_total > 0:
                era = round((totals['earned_runs'] * 9) / innings_total, 2)
                summary['era'] = str(era)
            else:
                summary['era'] = '0.00'
                
            return summary
            
        except Exception as e:
            logger.error(f"최근 경기 요약 계산 오류: {e}")
            return {}

    @staticmethod
    def _find_summary_rows(table: Tag) -> List[Tag]:
        """요약 행 찾기"""
        # "합계" 행 찾기 (class="noBold"이거나 "합계" 텍스트 포함)
        summary_rows = table.find_all('tr', class_='noBold')
        if not summary_rows:
            # class가 없는 경우 텍스트로 찾기
            all_rows = table.find_all('tr')
            for row in all_rows:
                if isinstance(row, Tag):
                    first_td = row.find('td')
                    if (isinstance(first_td, Tag) and 
                        '합계' in first_td.get_text(strip=True)):
                        summary_rows = [row]
                        break
        return summary_rows

    @staticmethod
    def _build_summary_data(cols: List[Tag]) -> Dict[str, str]:
        """요약 데이터 구성"""
        return {
            "innings": cols[3].get_text(strip=True),
            "pitch_count": cols[4].get_text(strip=True), 
            "batters_faced": cols[5].get_text(strip=True),
            "at_bats": cols[6].get_text(strip=True),
            "hits_allowed": cols[7].get_text(strip=True),
            "home_runs_allowed": cols[8].get_text(strip=True),
            "base_on_balls": cols[9].get_text(strip=True),
            "hit_by_pitch": cols[10].get_text(strip=True),
            "strikeouts": cols[11].get_text(strip=True),
            "wild_pitch": cols[12].get_text(strip=True),
            "balk": cols[13].get_text(strip=True),
            "runs_allowed": cols[14].get_text(strip=True),
            "earned_runs": cols[15].get_text(strip=True),
            "era": cols[16].get_text(strip=True)
        }


class PitcherDataParser(ParserStrategy[Dict[str, Any]]):
    """투수 데이터 파싱 전략 - SOLID 원칙 적용"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        # DIP: 의존성 주입을 통한 느슨한 결합
        self.table_parser = TableParser()
        self.league_service = LeagueService()
        self.team_mapper = TeamNameMapper(team_mappings)
        self.date_converter = DateConverter()
        self.basic_info_extractor = BasicInfoExtractor(self.league_service)
        self.recent_games_extractor = GameLogExtractor(
            self.date_converter, 
            self.team_mapper
        )
    
    def parse(self, html: str, **kwargs) -> Dict[str, Any]:
        """투수 상세 페이지 HTML을 파싱하여 관련 통계 데이터 추출"""
        pitcher_name = kwargs.get('pitcher_name', '')
        return self.parse_pitcher_data(html, pitcher_name)
    
    def parse_pitcher_data(self, html: str, pitcher_name: str) -> Dict[str, Any]:
        """투수 상세 페이지 HTML에서 모든 관련 통계 데이터 추출"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            player_info: Dict[str, Any] = {'name': pitcher_name if pitcher_name else ''}
            
            # 리그 감지 및 팀 매퍼 업데이트
            league = self._detect_league_from_html(html)
            self.team_mapper.league = league
            
            # GameLogExtractor의 team_mapper도 업데이트
            self.recent_games_extractor.team_mapper.league = league
            
            # 🎯 선수 이름 추출 (HTML에서 직접 추출)
            name_element = soup.find('h4', id='player_name')
            if name_element:
                extracted_name = name_element.get_text(strip=True)
                if extracted_name:
                    player_info['name'] = extracted_name
                    logger.info(f"🔍 PARSER: HTML에서 이름 추출 - '{extracted_name}'")
            else:
                # 디버깅: 이름 요소를 찾지 못한 경우 대안 시도
                logger.warning(f"⚠️ PARSER: h4#player_name 요소 없음 - HTML 구조 분석")
                # h4 태그들 확인
                h4_elements = soup.find_all('h4')
                logger.debug(f"발견된 h4 태그들: {[h4.get_text(strip=True) for h4 in h4_elements[:3]]}")
                # strong 태그들 확인  
                strong_elements = soup.find_all('strong')[:5]
                logger.debug(f"발견된 strong 태그들: {[s.get_text(strip=True) for s in strong_elements]}")
            
            # 각 추출기를 통한 데이터 수집
            self.basic_info_extractor.extract_basic_info(soup, player_info)
            self._extract_season_stats(soup, player_info)
            self._extract_season_stats_summary(soup, player_info)
            self.recent_games_extractor.extract_appearance_records(
                soup, player_info
            )
            # 진짜 데이터 없음 감지는 모든 주요 데이터가 비었을 때만
            season_stats = player_info.get('season_stats', [])
            recent_games = player_info.get('recent_games', {})
            # history_stats는 필요시 추가 구현, 예시로 빈 리스트 처리
            history_stats = player_info.get('history_stats', [])
            if not season_stats and not recent_games and not history_stats:
                player_info['first_appearance'] = 'league'
            elif not season_stats:
                player_info['first_appearance'] = 'season'
            # else: do not set first_appearance
            return player_info
        except Exception as e:
            raise ParsingError('pitcher_data', f"투수 데이터 파싱 오류: {str(e)}")

    def _detect_league_from_html(self, html: str) -> str:
        """HTML에서 리그 감지 (개선된 버전)"""
        try:
            # 1. URL에서 리그 ID 추출 (우선순위)
            if 'leagueId=BS001' in html:
                return 'KBO'
            elif 'leagueId=BS002' in html:
                return 'MLB'
            elif 'leagueId=BS004' in html:
                return 'NPB'
            
            # 2. HTML 내용에서 리그 감지
            if 'NPB' in html:
                return 'NPB'
            elif 'MLB' in html:
                return 'MLB'
            elif 'KBO' in html:
                return 'KBO'
            
            # 3. 팀명으로 리그 감지 (새로 추가)
            soup = BeautifulSoup(html, 'html.parser')
            team_tag = soup.find('strong', id='player_team_nm')
            if isinstance(team_tag, Tag):
                team_name = team_tag.get_text(strip=True)
                if team_name:
                    league = self.league_service.get_league_from_team_name(team_name)
                    if league:
                        # "League.MLB" -> "MLB" 형태로 정리
                        clean_league = str(league).replace("League.", "").upper()
                        logger.debug(f"팀명으로 리그 감지: {team_name} -> {clean_league}")
                        return clean_league
            
            # 4. 기본값
            logger.debug("리그 감지 실패, 기본값 KBO 사용")
            return 'KBO'
            
        except Exception as e:
            logger.debug(f"리그 감지 오류: {e}, 기본값 KBO 사용")
            return 'KBO'  # 기본값

    def _extract_season_stats(self, soup: BeautifulSoup,
                              player_info: Dict[str, Any]) -> None:
        """시즌 통계 추출 - 실제 betman HTML 구조에 맞춰 수정"""
        # 실제 betman 구조: 시즌 성적 테이블 찾기
        season_table = None
        
        # 방법 1: 캡션으로 시즌 성적 테이블 찾기
        tables_with_caption = soup.find_all('table')
        for table in tables_with_caption:
            caption = table.find('caption')
            if caption and '시즌 성적' in caption.get_text():
                season_table = table
                logger.debug("캡션으로 시즌 성적 테이블 발견")
                break
        
        # 방법 2: 테이블 구조로 시즌 성적 테이블 찾기 (경기수, 이닝, 평균자책점 등)
        if not season_table:
            for table in soup.find_all('table'):
                headers = table.find_all('th') + table.find_all('columnheader')
                header_texts = [th.get_text().strip() for th in headers]
                # 투수 시즌 통계 테이블의 특징적인 컬럼들
                if any(col in ' '.join(header_texts) for col in ['경기수', '이닝', '평균', '승률', '승', '패']):
                    # tbody에 실제 데이터가 있는지 확인
                    tbody = table.find('tbody')
                    if tbody and tbody.find_all('tr'):
                        rows = tbody.find_all('tr')
                        # 첫 번째 행에 실제 통계 데이터가 있는지 확인 (숫자나 소수점)
                        if rows:
                            first_row_cells = rows[0].find_all(['td', 'cell'])
                            first_row_text = ' '.join([cell.get_text().strip() for cell in first_row_cells])
                            if any(char.isdigit() or char == '.' for char in first_row_text):
                                season_table = table
                                logger.debug("구조 분석으로 시즌 성적 테이블 발견")
                                break
        
        if season_table:
            # 실제 데이터 추출
            tbody = season_table.find('tbody')
            if tbody:
                rows = tbody.find_all('tr')
                if rows:
                    # 첫 번째 데이터 행 추출
                    first_row = rows[0]
                    cells = first_row.find_all(['td', 'cell'])
                    
                    if len(cells) >= 8:  # 최소한의 통계 데이터가 있어야 함
                        season_data = {
                            'games': cells[0].get_text().strip() if len(cells) > 0 else '',
                            'innings': cells[1].get_text().strip() if len(cells) > 1 else '',
                            'era': cells[2].get_text().strip() if len(cells) > 2 else '',
                            'win_rate': cells[3].get_text().strip() if len(cells) > 3 else '',
                            'wins': cells[4].get_text().strip() if len(cells) > 4 else '',
                            'losses': cells[5].get_text().strip() if len(cells) > 5 else '',
                            'saves': cells[6].get_text().strip() if len(cells) > 6 else '',
                            'holds': cells[7].get_text().strip() if len(cells) > 7 else '',
                            'runs_allowed': cells[8].get_text().strip() if len(cells) > 8 else '',
                            'earned_runs': cells[9].get_text().strip() if len(cells) > 9 else '',
                            'home_runs_allowed': cells[10].get_text().strip() if len(cells) > 10 else '',
                            'hits_allowed': cells[11].get_text().strip() if len(cells) > 11 else '',
                            'strikeouts': cells[12].get_text().strip() if len(cells) > 12 else '',
                            'base_on_balls': cells[13].get_text().strip() if len(cells) > 13 else '',
                            'hit_by_pitch': cells[14].get_text().strip() if len(cells) > 14 else '',
                            'wild_pitch': cells[15].get_text().strip() if len(cells) > 15 else '',
                            'balk': cells[16].get_text().strip() if len(cells) > 16 else ''
                        }
                        
                        player_info['season_stats'] = [season_data]
                        logger.debug(f"시즌 통계 추출 성공: {season_data['games']}경기, ERA {season_data['era']}")
                        return
                    
        # 데이터를 찾지 못한 경우
        logger.warning("시즌 통계 테이블을 찾을 수 없음")
        player_info['season_stats'] = []
    
    def _extract_season_stats_summary(self, soup: BeautifulSoup, player_info: Dict[str, Any]) -> None:
        """시즌 성적 요약 추출 - 프로필 섹션에서 주요 통계 추출"""
        try:
            # 프로필 섹션 찾기
            profile_section = soup.find('div', class_='leagueInfoArea player')
            if not profile_section:
                logger.debug("프로필 섹션을 찾을 수 없음")
                player_info['season_summary'] = {}
                return
            
            season_summary = {}
            
            # 평균자책점 (ERA)
            era_elem = profile_section.find('em', id='player_era')
            if era_elem:
                season_summary['era'] = era_elem.get_text().strip()
            
            # 다승 (Wins)
            win_elem = profile_section.find('em', id='player_win')
            if win_elem:
                season_summary['wins'] = win_elem.get_text().strip()
            
            # 세이브 (Saves)
            save_elem = profile_section.find('em', id='player_save')
            if save_elem:
                season_summary['saves'] = save_elem.get_text().strip()
            
            # 탈삼진 (Strikeouts)
            k_elem = profile_section.find('em', id='player_k')
            if k_elem:
                season_summary['strikeouts'] = k_elem.get_text().strip()
            
            # 게임수는 시즌 스탯에서 가져올 수 있으면 추가
            season_stats = player_info.get('season_stats', [])
            if season_stats and len(season_stats) > 0:
                first_stat = season_stats[0]
                if 'games' in first_stat:
                    season_summary['games'] = first_stat['games']
                if 'innings' in first_stat:
                    season_summary['innings'] = first_stat['innings']
            
            player_info['season_summary'] = season_summary
            logger.debug(f"시즌 성적 요약 추출 성공: {season_summary}")
            
        except Exception as e:
            logger.warning(f"시즌 성적 요약 추출 실패: {e}")
            player_info['season_summary'] = {}
