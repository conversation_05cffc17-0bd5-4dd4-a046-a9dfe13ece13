"""
야구 플러그인 구현
기존 야구 로직을 새로운 아키텍처에 통합
"""
from typing import Dict, List, Any

from core.factories.sport_factory import SportPlugin, SportType
from core.interfaces.collector import SportCollector
from core.interfaces.parser import SportParser
from core.interfaces.service import SportService

from .collectors.baseball_collector import BaseballCollector
from .parsers.baseball_parser import BaseballParser
from .services.baseball_service import BaseballService


class BaseballPlugin(SportPlugin):
    """
    야구 플러그인 구현
    KBO, MLB, NPB 리그 지원
    """
    
    @property
    def sport_type(self) -> SportType:
        """스포츠 타입 반환"""
        return SportType.BASEBALL
    
    @property
    def display_name(self) -> str:
        """표시용 스포츠 이름"""
        return "야구"
    
    @property
    def supported_leagues(self) -> List[str]:
        """지원하는 리그 목록"""
        return ["KBO", "MLB", "NPB"]
    
    def create_collector(self, **kwargs) -> SportCollector:
        """야구 데이터 수집기 생성"""
        return BaseballCollector(**kwargs)
    
    def create_parser(self, **kwargs) -> SportParser:
        """야구 데이터 파서 생성"""
        return BaseballParser(**kwargs)
    
    def create_service(self, **kwargs) -> SportService:
        """야구 비즈니스 서비스 생성"""
        return BaseballService(**kwargs)
    
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """야구 플러그인 설정 검증"""
        required_keys = ["leagues", "batch_size"]
        
        # 필수 키 검증
        for key in required_keys:
            if key not in config:
                return False
        
        # 리그 설정 검증
        leagues = config.get("leagues", [])
        if not isinstance(leagues, list):
            return False
        
        # 지원하는 리그인지 확인
        supported = set(self.supported_leagues)
        for league in leagues:
            if league not in supported:
                return False
        
        # 배치 크기 검증
        batch_size = config.get("batch_size", 10)
        if not isinstance(batch_size, int) or batch_size <= 0:
            return False
        
        return True
    
    def get_default_configuration(self) -> Dict[str, Any]:
        """기본 설정 반환"""
        return {
            "leagues": ["KBO", "MLB", "NPB"],
            "batch_size": 10,
            "max_concurrent": 3,
            "timeout_seconds": 30,
            "retry_attempts": 3,
            "browser_pool_size": 3,
            "cache_enabled": True,
            "cache_ttl_seconds": 3600,
            "supported_positions": [
                "pitcher", "catcher", "first_base", "second_base", 
                "third_base", "shortstop", "left_field", 
                "center_field", "right_field", "designated_hitter"
            ]
        } 