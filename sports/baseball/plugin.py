"""
야구 플러그인 구현
기존 야구 로직을 새로운 아키텍처에 통합
"""
from typing import Dict, List, Any

from core.factories.sport_factory import SportPlugin, SportType
from core.interfaces.collector import SportCollector
from core.interfaces.parser import SportParser
from core.interfaces.service import SportService

from .collectors.baseball_collector import BaseballCollector
from .parsers.baseball_parser import BaseballParser
from .services.baseball_service import BaseballService


class BaseballPlugin(SportPlugin):
    """
    야구 플러그인 구현
    KBO, MLB, NPB 리그 지원
    """

    def __init__(self):
        """초기화"""
        from core.config.sport_config import SportConfig

        self.sport_config = SportConfig(
            sport_name="baseball",
            sport_code="BB",
            leagues={
                'BB001': 'KBO',
                'BB002': 'MLB',
                'BB003': 'NPB'
            },
            team_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/bbTeamDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}",
            player_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/bbPlayerDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}&playerId={playerId}",
            time_slots=[
                {"start": "02:00", "end": "04:00"},  # 새벽 2-4시
                {"start": "14:00", "end": "16:00"}   # 오후 2-4시
            ],
            max_concurrent=2,
            delay_between_requests=3.0
        )

    @property
    def sport_type(self) -> SportType:
        """스포츠 타입 반환"""
        return SportType.BASEBALL

    @property
    def display_name(self) -> str:
        """표시용 스포츠 이름"""
        return "야구"

    @property
    def supported_leagues(self) -> List[str]:
        """지원하는 리그 목록"""
        return ["KBO", "MLB", "NPB"]

    def get_sport_config(self):
        """스포츠 설정 반환"""
        return self.sport_config

    async def collect_all_data(self, browser_session=None) -> Dict[str, Any]:
        """모든 야구 데이터 수집"""
        try:
            import logging
            logger = logging.getLogger(__name__)
            logger.info("⚾ 야구 데이터 수집 시작")

            # 기존 시스템 호출
            collector = self.create_collector()

            # 팀 데이터 수집
            teams_result = await collector.collect_all_teams()

            # 선수 데이터 수집
            players_result = await collector.collect_all_players()

            return {
                'teams_processed': teams_result.get('teams_processed', 0),
                'players_processed': players_result.get('players_processed', 0),
                'games_processed': 0,  # 기존 시스템에서 별도 처리
                'errors': teams_result.get('errors', []) + players_result.get('errors', [])
            }

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"❌ 야구 데이터 수집 실패: {e}")
            return {
                'teams_processed': 0,
                'players_processed': 0,
                'games_processed': 0,
                'errors': [str(e)]
            }

    def create_collector(self, **kwargs) -> SportCollector:
        """야구 데이터 수집기 생성"""
        return BaseballCollector(**kwargs)

    def create_parser(self, **kwargs) -> SportParser:
        """야구 데이터 파서 생성"""
        return BaseballParser(**kwargs)

    def create_service(self, **kwargs) -> SportService:
        """야구 비즈니스 서비스 생성"""
        return BaseballService(**kwargs)
    
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """야구 플러그인 설정 검증"""
        required_keys = ["leagues", "batch_size"]
        
        # 필수 키 검증
        for key in required_keys:
            if key not in config:
                return False
        
        # 리그 설정 검증
        leagues = config.get("leagues", [])
        if not isinstance(leagues, list):
            return False
        
        # 지원하는 리그인지 확인
        supported = set(self.supported_leagues)
        for league in leagues:
            if league not in supported:
                return False
        
        # 배치 크기 검증
        batch_size = config.get("batch_size", 10)
        if not isinstance(batch_size, int) or batch_size <= 0:
            return False
        
        return True
    
    def get_default_configuration(self) -> Dict[str, Any]:
        """기본 설정 반환"""
        return {
            "leagues": ["KBO", "MLB", "NPB"],
            "batch_size": 10,
            "max_concurrent": 3,
            "timeout_seconds": 30,
            "retry_attempts": 3,
            "browser_pool_size": 3,
            "cache_enabled": True,
            "cache_ttl_seconds": 3600,
            "supported_positions": [
                "pitcher", "catcher", "first_base", "second_base", 
                "third_base", "shortstop", "left_field", 
                "center_field", "right_field", "designated_hitter"
            ]
        } 