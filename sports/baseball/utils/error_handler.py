"""
Error Handler Utility
Provides consistent error handling patterns and retry logic
"""
import asyncio
import functools
import traceback
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union

from sports.baseball.exceptions import (
    <PERSON>Error, <PERSON>rowserError, DatabaseError, DataCollectionError,
    RetryableError, CriticalError, ResourceError, ValidationError
)
from utils.logger import Logger

logger = Logger(__name__)

T = TypeVar('T')


class ErrorHandler:
    """
    Centralized error handling for baseball module operations.
    
    Provides consistent error classification, logging, and recovery
    strategies across all baseball services.
    """
    
    # Map common exception types to our custom exceptions
    EXCEPTION_MAPPING = {
        'playwright._impl._api_types.Error': BrowserError,
        'playwright._impl._api_types.TimeoutError': BrowserError,
        'supabase.lib.client_options.ClientOptions': DatabaseError,
        'requests.exceptions.RequestException': DataCollectionError,
        'urllib.error.URLError': DataCollectionError,
        'json.JSONDecodeError': ValidationError,
        'KeyError': ValidationError,
        'ValueError': ValidationError,
        'TypeError': ValidationError,
        'ConnectionError': RetryableError,
        'TimeoutError': RetryableError,
    }
    
    # Operations that should be retried on failure
    RETRYABLE_OPERATIONS = {
        'database_query',
        'browser_navigation',
        'data_collection',
        'page_load',
        'element_interaction'
    }
    
    # Critical operations that should trigger alerts
    CRITICAL_OPERATIONS = {
        'database_connection',
        'browser_initialization',
        'component_initialization'
    }
    
    @classmethod
    def classify_error(
        self, 
        error: Exception, 
        operation: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> BaseballError:
        """
        Classify a generic exception into a specific baseball error type.
        
        Args:
            error: Original exception
            operation: Operation being performed when error occurred
            context: Additional context information
            
        Returns:
            Classified BaseballError subclass instance
        """
        error_type_name = f"{error.__class__.__module__}.{error.__class__.__name__}"
        message = str(error)
        context = context or {}
        context['original_error'] = error_type_name
        context['operation'] = operation
        
        # Check for specific exception mappings
        for pattern, baseball_error_class in self.EXCEPTION_MAPPING.items():
            if pattern in error_type_name:
                return baseball_error_class(
                    message=message,
                    cause=error,
                    **context
                )
        
        # Check if operation is critical
        if operation in self.CRITICAL_OPERATIONS:
            return CriticalError(
                message=f"Critical operation failed: {message}",
                operation=operation,
                cause=error,
                **context
            )
        
        # Check if operation is retryable
        if operation in self.RETRYABLE_OPERATIONS:
            return RetryableError(
                message=message,
                operation=operation,
                cause=error,
                **context
            )
        
        # Default to generic BaseballError
        return BaseballError(
            message=message,
            context=context,
            cause=error
        )
    
    @classmethod
    def handle_error(
        self,
        error: Exception,
        operation: str,
        context: Optional[Dict[str, Any]] = None,
        reraise: bool = False
    ) -> Optional[BaseballError]:
        """
        Handle an error with appropriate logging and classification.
        
        Args:
            error: Exception to handle
            operation: Operation that failed
            context: Additional context
            reraise: Whether to re-raise the classified error
            
        Returns:
            Classified BaseballError or None if not reraising
        """
        classified_error = self.classify_error(error, operation, context)
        
        # Log based on error severity
        if isinstance(classified_error, CriticalError):
            logger.error(
                f"💥 CRITICAL ERROR in {operation}: {classified_error}"
            )
            logger.error(f"Stack trace: {traceback.format_exc()}")
        elif isinstance(classified_error, RetryableError):
            retry_count = classified_error.context.get('retry_count', 0)
            logger.warning(
                f"⚠️ Retryable error in {operation} (attempt {retry_count + 1}): {classified_error}"
            )
        else:
            logger.error(f"❌ Error in {operation}: {classified_error}")
        
        if reraise:
            raise classified_error
        
        return classified_error
    
    @classmethod
    def with_retry(
        self,
        max_retries: int = 3,
        delay: float = 1.0,
        backoff_factor: float = 2.0,
        retryable_errors: Optional[List[Type[Exception]]] = None
    ):
        """
        Decorator for adding retry logic to functions.
        
        Args:
            max_retries: Maximum number of retry attempts
            delay: Initial delay between retries in seconds
            backoff_factor: Factor to multiply delay by for each retry
            retryable_errors: List of exception types that should trigger retry
            
        Returns:
            Decorated function with retry logic
        """
        if retryable_errors is None:
            retryable_errors = [RetryableError, BrowserError, DatabaseError]
        
        def decorator(func: Callable[..., T]) -> Callable[..., T]:
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs) -> T:
                current_delay = delay
                last_error = None
                
                for attempt in range(max_retries + 1):
                    try:
                        return await func(*args, **kwargs)
                    except Exception as e:
                        last_error = e
                        
                        # Check if error is retryable
                        classified_error = self.classify_error(
                            e, 
                            operation=func.__name__,
                            context={'attempt': attempt + 1, 'max_retries': max_retries}
                        )
                        
                        should_retry = (
                            attempt < max_retries and (
                                isinstance(classified_error, tuple(retryable_errors)) or
                                (isinstance(classified_error, RetryableError) and classified_error.should_retry)
                            )
                        )
                        
                        if not should_retry:
                            self.handle_error(e, func.__name__, reraise=True)
                        
                        logger.warning(
                            f"🔄 Retrying {func.__name__} in {current_delay:.1f}s "
                            f"(attempt {attempt + 1}/{max_retries})"
                        )
                        
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff_factor
                
                # If we get here, all retries failed
                if last_error:
                    self.handle_error(last_error, func.__name__, reraise=True)
                
                raise RuntimeError("Unexpected retry failure")
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs) -> T:
                import time
                current_delay = delay
                last_error = None
                
                for attempt in range(max_retries + 1):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        last_error = e
                        
                        classified_error = self.classify_error(
                            e, 
                            operation=func.__name__,
                            context={'attempt': attempt + 1, 'max_retries': max_retries}
                        )
                        
                        should_retry = (
                            attempt < max_retries and (
                                isinstance(classified_error, tuple(retryable_errors)) or
                                (isinstance(classified_error, RetryableError) and classified_error.should_retry)
                            )
                        )
                        
                        if not should_retry:
                            self.handle_error(e, func.__name__, reraise=True)
                        
                        logger.warning(
                            f"🔄 Retrying {func.__name__} in {current_delay:.1f}s "
                            f"(attempt {attempt + 1}/{max_retries})"
                        )
                        
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                
                if last_error:
                    self.handle_error(last_error, func.__name__, reraise=True)
                
                raise RuntimeError("Unexpected retry failure")
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        
        return decorator
    
    @classmethod
    def safe_execute(
        self,
        operation: str,
        func: Callable[[], T],
        default: Optional[T] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> T:
        """
        Safely execute a function with error handling.
        
        Args:
            operation: Name of operation being performed
            func: Function to execute
            default: Default value to return on error
            context: Additional context information
            
        Returns:
            Function result or default value on error
        """
        try:
            return func()
        except Exception as e:
            self.handle_error(e, operation, context)
            return default
    
    @classmethod
    async def safe_execute_async(
        self,
        operation: str,
        func: Callable[[], T],
        default: Optional[T] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> T:
        """
        Safely execute an async function with error handling.
        
        Args:
            operation: Name of operation being performed
            func: Async function to execute
            default: Default value to return on error
            context: Additional context information
            
        Returns:
            Function result or default value on error
        """
        try:
            return await func()
        except Exception as e:
            self.handle_error(e, operation, context)
            return default


# Convenience decorators
retry_on_failure = ErrorHandler.with_retry()
retry_database_ops = ErrorHandler.with_retry(
    max_retries=3,
    delay=2.0,
    retryable_errors=[DatabaseError, RetryableError]
)
retry_browser_ops = ErrorHandler.with_retry(
    max_retries=2,
    delay=3.0,
    retryable_errors=[BrowserError, RetryableError]
)