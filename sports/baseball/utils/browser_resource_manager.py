"""
Browser Resource Manager
Centralizes browser lifecycle management and eliminates cleanup code duplication
"""
from __future__ import annotations

import async<PERSON>
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional, Union
from enum import Enum

try:
    from playwright.async_api import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Playwright, async_playwright
except ImportError:
    Browser = BrowserContext = Page = Playwright = async_playwright = None

from utils.logger import Logger

logger = Logger(__name__)


class BrowserType(Enum):
    """Supported browser types for automation."""
    CHROMIUM = "chromium"
    FIREFOX = "firefox"
    WEBKIT = "webkit"


class BrowserResourceManager:
    """
    Centralized browser resource management for baseball data collection.
    
    Provides consistent browser creation, configuration, and cleanup patterns
    to eliminate duplicate code across collectors. Implements async context
    managers for automatic resource cleanup and error handling.
    
    Features:
    - Automatic browser lifecycle management
    - Consistent browser configuration
    - Resource leak prevention
    - Error handling and retry logic
    - Memory optimization
    """
    
    def __init__(
        self,
        browser_type: BrowserType = BrowserType.CHROMIUM,
        headless: bool = True,
        timeout: int = 30000,
        user_agent: Optional[str] = None
    ) -> None:
        """
        Initialize browser resource manager.
        
        Args:
            browser_type: Type of browser to launch
            headless: Whether to run browser in headless mode
            timeout: Default timeout for browser operations in ms
            user_agent: Custom user agent string
        """
        self.browser_type = browser_type
        self.headless = headless
        self.timeout = timeout
        self.user_agent = user_agent or self._get_default_user_agent()
        
        self._playwright: Optional[Playwright] = None
        self._browser: Optional[Browser] = None
        self._contexts: List[BrowserContext] = []
        self._pages: List[Page] = []
        self._cleanup_timeout = 10.0
    
    @staticmethod
    def _get_default_user_agent() -> str:
        """Get default user agent for web scraping."""
        return (
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/120.0.0.0 Safari/537.36"
        )
    
    def _get_browser_args(self) -> List[str]:
        """Get browser launch arguments for optimal performance."""
        return [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--no-first-run',
            '--no-default-browser-check',
            f'--user-agent={self.user_agent}'
        ]
    
    async def _launch_browser(self) -> Browser:
        """Launch browser with optimal configuration."""
        if not async_playwright:
            raise ImportError("Playwright is required but not installed")
        
        try:
            self._playwright = await async_playwright().start()
            
            browser_launcher = getattr(self._playwright, self.browser_type.value)
            if not browser_launcher:
                raise ValueError(f"Unsupported browser type: {self.browser_type.value}")
            
            browser = await browser_launcher.launch(
                headless=self.headless,
                args=self._get_browser_args()
            )
            
            logger.debug(f"✅ {self.browser_type.value.title()} browser launched successfully")
            return browser
            
        except Exception as e:
            logger.error(f"❌ Failed to launch {self.browser_type.value} browser: {e}")
            await self._cleanup_playwright()
            raise
    
    async def get_browser(self) -> Browser:
        """Get or create browser instance."""
        if self._browser is None:
            self._browser = await self._launch_browser()
        return self._browser
    
    @asynccontextmanager
    async def get_context(self, **context_options):
        """
        Async context manager for browser context.
        
        Usage:
            async with browser_manager.get_context() as context:
                page = await context.new_page()
                # Use page for scraping
        
        Args:
            **context_options: Additional context creation options
        """
        browser = await self.get_browser()
        context = None
        
        try:
            default_options = {
                'viewport': {'width': 1920, 'height': 1080},
                'user_agent': self.user_agent,
                'ignore_https_errors': True
            }
            default_options.update(context_options)
            
            context = await browser.new_context(**default_options)
            context.set_default_timeout(self.timeout)
            self._contexts.append(context)
            
            logger.debug("🔗 Browser context created")
            yield context
            
        except Exception as e:
            logger.error(f"❌ Browser context error: {e}")
            raise
        finally:
            if context:
                await self._close_context(context)
    
    @asynccontextmanager
    async def get_page(self, context: Optional[BrowserContext] = None, **page_options):
        """
        Async context manager for browser page.
        
        Usage:
            async with browser_manager.get_page() as page:
                await page.goto('https://example.com')
                # Perform scraping operations
        
        Args:
            context: Existing browser context (creates new if None)
            **page_options: Additional page creation options
        """
        page = None
        context_created = False
        
        try:
            if context is None:
                async with self.get_context() as new_context:
                    context = new_context
                    context_created = True
                    
                    page = await context.new_page()
                    self._pages.append(page)
                    
                    # Configure page
                    await self._configure_page(page, **page_options)
                    
                    logger.debug("📄 Browser page created")
                    yield page
            else:
                page = await context.new_page()
                self._pages.append(page)
                
                # Configure page
                await self._configure_page(page, **page_options)
                
                logger.debug("📄 Browser page created")
                yield page
                
        except Exception as e:
            logger.error(f"❌ Browser page error: {e}")
            raise
        finally:
            if page and not context_created:
                await self._close_page(page)
    
    async def _configure_page(self, page: Page, **options):
        """Configure page with optimal settings."""
        try:
            # Set timeout
            page.set_default_timeout(self.timeout)
            
            # Block unnecessary resources for better performance
            if options.get('block_resources', True):
                await page.route('**/*.{css,jpg,jpeg,png,gif,svg,woff,woff2}', 
                               lambda route: route.abort())
            
            # Set extra headers if provided
            extra_headers = options.get('extra_headers')
            if extra_headers:
                await page.set_extra_http_headers(extra_headers)
                
        except Exception as e:
            logger.warning(f"⚠️ Failed to configure page: {e}")
    
    async def _close_page(self, page: Page) -> None:
        """Safely close a browser page."""
        try:
            if page and not page.is_closed():
                await asyncio.wait_for(page.close(), timeout=self._cleanup_timeout)
                logger.debug("📄 Browser page closed")
        except Exception as e:
            logger.warning(f"⚠️ Failed to close page: {e}")
        finally:
            if page in self._pages:
                self._pages.remove(page)
    
    async def _close_context(self, context: BrowserContext) -> None:
        """Safely close a browser context."""
        try:
            if context:
                await asyncio.wait_for(context.close(), timeout=self._cleanup_timeout)
                logger.debug("🔗 Browser context closed")
        except Exception as e:
            logger.warning(f"⚠️ Failed to close context: {e}")
        finally:
            if context in self._contexts:
                self._contexts.remove(context)
    
    async def _close_browser(self) -> None:
        """Safely close the browser."""
        try:
            if self._browser:
                await asyncio.wait_for(self._browser.close(), timeout=self._cleanup_timeout)
                logger.debug("🌐 Browser closed")
        except Exception as e:
            logger.warning(f"⚠️ Failed to close browser: {e}")
        finally:
            self._browser = None
    
    async def _cleanup_playwright(self) -> None:
        """Safely cleanup playwright instance."""
        try:
            if self._playwright:
                await self._playwright.stop()
                logger.debug("🎭 Playwright stopped")
        except Exception as e:
            logger.warning(f"⚠️ Failed to stop playwright: {e}")
        finally:
            self._playwright = None
    
    async def cleanup(self) -> None:
        """
        Cleanup all browser resources.
        
        Ensures all pages, contexts, browser, and playwright instances
        are properly closed to prevent resource leaks.
        """
        logger.debug("🧹 Starting browser cleanup...")
        
        # Close all pages
        for page in self._pages[:]:  # Copy list to avoid modification during iteration
            await self._close_page(page)
        
        # Close all contexts
        for context in self._contexts[:]:
            await self._close_context(context)
        
        # Close browser
        await self._close_browser()
        
        # Cleanup playwright
        await self._cleanup_playwright()
        
        logger.debug("✅ Browser cleanup completed")
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with cleanup."""
        await self.cleanup()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current resource usage statistics."""
        return {
            'contexts': len(self._contexts),
            'pages': len(self._pages),
            'browser_active': self._browser is not None,
            'playwright_active': self._playwright is not None
        }


class BrowserPool:
    """
    Pool of browser instances for concurrent operations.
    
    Manages multiple browser instances to enable parallel data collection
    while maintaining resource limits and proper cleanup.
    """
    
    def __init__(self, max_browsers: int = 3):
        """
        Initialize browser pool.
        
        Args:
            max_browsers: Maximum number of concurrent browser instances
        """
        self.max_browsers = max_browsers
        self._managers: List[BrowserResourceManager] = []
        self._available: List[BrowserResourceManager] = []
        self._in_use: List[BrowserResourceManager] = []
        self._lock = asyncio.Lock()
    
    @asynccontextmanager
    async def get_browser_manager(self) -> BrowserResourceManager:
        """
        Get a browser manager from the pool.
        
        Usage:
            async with browser_pool.get_browser_manager() as manager:
                async with manager.get_page() as page:
                    await page.goto('https://example.com')
        """
        manager = None
        
        try:
            async with self._lock:
                # Try to get available manager
                if self._available:
                    manager = self._available.pop()
                # Create new manager if under limit
                elif len(self._managers) < self.max_browsers:
                    manager = BrowserResourceManager()
                    self._managers.append(manager)
                # Wait for available manager
                else:
                    # For now, create a new temporary manager
                    # In production, this could implement a queue
                    manager = BrowserResourceManager()
            
            if manager:
                async with self._lock:
                    self._in_use.append(manager)
                
            yield manager
            
        finally:
            if manager:
                async with self._lock:
                    if manager in self._in_use:
                        self._in_use.remove(manager)
                    if manager in self._managers:
                        self._available.append(manager)
                    else:
                        # Temporary manager, cleanup immediately
                        await manager.cleanup()
    
    async def cleanup_all(self) -> None:
        """Cleanup all browser managers in the pool."""
        logger.debug("🧹 Cleaning up browser pool...")
        
        for manager in self._managers[:]:
            await manager.cleanup()
        
        self._managers.clear()
        self._available.clear()
        self._in_use.clear()
        
        logger.debug("✅ Browser pool cleanup completed")


# Global instances for easy access
default_browser_manager = BrowserResourceManager()
browser_pool = BrowserPool(max_browsers=3)