"""
Database Service Singleton
Centralizes database connection management and eliminates code duplication
"""
from __future__ import annotations

import threading
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional

from supabase import Client

from database.database import connect_supabase
from sports.baseball.exceptions import (CriticalError, DatabaseError,
                                        ValidationError)
from sports.baseball.utils.error_handler import retry_database_ops
from utils.logger import Logger

logger = Logger(__name__)


class DatabaseService:
    """
    Singleton service for managing Supabase database connections.
    
    Provides centralized database operations and eliminates the need for
    repeated connection patterns throughout the baseball module.
    Implements thread-safe singleton pattern for consistent access.
    """
    
    _instance: Optional[DatabaseService] = None
    _lock = threading.Lock()
    _client: Optional[Client] = None
    
    def __new__(cls) -> DatabaseService:
        """Thread-safe singleton implementation."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self) -> None:
        """Initialize the database service."""
        if not hasattr(self, '_initialized'):
            self._initialized = True
            self._connection_attempts = 0
            self._max_connection_attempts = 3
    
    @property
    def client(self) -> Optional[Client]:
        """Get the current Supabase client."""
        if self._client is None:
            self._client = self._create_connection()
        return self._client
    
    @retry_database_ops
    def _create_connection(self) -> Optional[Client]:
        """Create a new Supabase connection with retry logic."""
        try:
            client = connect_supabase()
            logger.debug("✅ Database connection established")
            self._connection_attempts = 0
            return client
        except Exception as e:
            self._connection_attempts += 1
            
            # Classify and handle the error
            context = {
                'connection_attempts': self._connection_attempts,
                'max_attempts': self._max_connection_attempts
            }
            
            if self._connection_attempts >= self._max_connection_attempts:
                # This is a critical failure
                raise CriticalError(
                    f"Failed to establish database connection after "
                    f"{self._max_connection_attempts} attempts",
                    operation='database_connection',
                    cause=e,
                    **context
                )
            else:
                # This is retryable
                raise DatabaseError(
                    f"Database connection attempt "
                    f"{self._connection_attempts} failed",
                    operation='database_connection',
                    cause=e,
                    **context
                )
    
    def get_client(self) -> Optional[Client]:
        """
        Get Supabase client with automatic retry on failure.
        
        Returns:
            Optional[Client]: Connected Supabase client or None if failed
        """
        if self._client is None:
            self._client = self._create_connection()
        return self._client
    
    def refresh_connection(self) -> Optional[Client]:
        """
        Force refresh of the database connection.
        
        Returns:
            Optional[Client]: New Supabase client or None if failed
        """
        self._client = None
        return self.get_client()
    
    @asynccontextmanager
    async def get_async_client(self):
        """
        Async context manager for database operations.
        
        Usage:
            async with DatabaseService().get_async_client() as client:
                if client:
                    # Perform database operations
                    result = client.table('table').select().execute()
        """
        client = self.get_client()
        try:
            yield client
        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            # Attempt connection refresh on failure
            client = self.refresh_connection()
            yield client
        finally:
            # Context cleanup if needed
            pass
    
    @retry_database_ops
    def execute_query(
        self, 
        table: str, 
        operation: str, 
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        Execute a database query with automatic error handling.
        
        Args:
            table: Table name to query
            operation: Operation type ('select', 'insert', 'update', 'upsert')
            **kwargs: Additional parameters for the operation
            
        Returns:
            Optional[Dict]: Query result or None if failed
            
        Raises:
            DatabaseError: For database operation failures
            ValidationError: For invalid operation parameters
        """
        client = self.get_client()
        if not client:
            raise DatabaseError(
                "No database connection available",
                table=table,
                operation=operation
            )
        
        # Validate operation type
        valid_operations = {'select', 'insert', 'update', 'upsert'}
        if operation not in valid_operations:
            raise ValidationError(
                f"Unsupported database operation: {operation}",
                field='operation',
                value=operation,
                expected_type=f"One of {valid_operations}"
            )
        
        try:
            table_ref = client.table(table)
            
            if operation == 'select':
                columns = kwargs.get('columns', '*')
                query = table_ref.select(columns)
                
                # Add filters with validation
                filters = kwargs.get('filters', {})
                if not isinstance(filters, dict):
                    raise ValidationError(
                        "Filters must be a dictionary",
                        field='filters',
                        value=type(filters).__name__,
                        expected_type='dict'
                    )
                
                for key, value in filters.items():
                    query = query.eq(key, value)
                
                return query.execute()
                
            elif operation == 'insert':
                data = kwargs.get('data', {})
                if not data:
                    raise ValidationError(
                        "Insert operation requires data",
                        field='data',
                        operation=operation
                    )
                return table_ref.insert(data).execute()
                
            elif operation == 'update':
                data = kwargs.get('data', {})
                filters = kwargs.get('filters', {})
                
                if not data:
                    raise ValidationError(
                        "Update operation requires data",
                        field='data',
                        operation=operation
                    )
                if not filters:
                    raise ValidationError(
                        "Update operation requires filters",
                        field='filters',
                        operation=operation
                    )
                
                query = table_ref.update(data)
                for key, value in filters.items():
                    query = query.eq(key, value)
                
                return query.execute()
                
            elif operation == 'upsert':
                data = kwargs.get('data', {})
                if not data:
                    raise ValidationError(
                        "Upsert operation requires data",
                        field='data',
                        operation=operation
                    )
                return table_ref.upsert(data).execute()
                
        except (ValidationError, DatabaseError):
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            # Convert generic exceptions to DatabaseError
            raise DatabaseError(
                f"Database query execution failed: {str(e)}",
                table=table,
                operation=operation,
                query_params=kwargs,
                cause=e
            )
    
    def check_team_data_exists(
        self, 
        match_id: str, 
        team_id: str, 
        match_date: str,
        fields: List[str] = None
    ) -> bool:
        """
        Check if team data already exists for skip logic.
        
        Args:
            match_id: Match identifier
            team_id: Team identifier  
            match_date: Match date
            fields: Specific fields to check for existence
            
        Returns:
            bool: True if data exists, False otherwise
        """
        if not fields:
            fields = ['season_summary', 'recent_games_summary']
        
        # Handle None match_date
        if match_date is None:
            logger.warning(
                f"match_date is None for match_id: {match_id}, "
                f"team_id: {team_id}"
            )
            return False
        
        result = self.execute_query(
            table='baseball_stats',
            operation='select',
            columns=', '.join(fields),
            filters={
                'match_id': match_id,
                'team_id': team_id,
                'match_date': match_date
            }
        )
        
        if not result or not result.data:
            return False
        
        record = result.data[0]
        return any(record.get(field) for field in fields)
    
    def check_pitcher_data_exists(
        self, 
        match_id: str, 
        team_id: str, 
        match_date: str,
        fields: List[str] = None
    ) -> bool:
        """
        Check if pitcher data already exists for skip logic.
        
        Args:
            match_id: Match identifier
            team_id: Team identifier
            match_date: Match date
            fields: Specific fields to check for existence
            
        Returns:
            bool: True if data exists, False otherwise
        """
        if not fields:
            fields = ['pitcher_profile', 'pitcher_stats']
        
        # Handle None match_date
        if match_date is None:
            logger.warning(
                f"match_date is None for match_id: {match_id}, "
                f"team_id: {team_id}"
            )
            return False
        
        result = self.execute_query(
            table='baseball_stats',
            operation='select',
            columns=', '.join(fields),
            filters={
                'match_id': match_id,
                'team_id': team_id,
                'match_date': match_date
            }
        )
        
        if not result or not result.data:
            return False
        
        record = result.data[0]
        return any(record.get(field) for field in fields)
    
    @classmethod
    def get_instance(cls) -> DatabaseService:
        """Get the singleton instance of DatabaseService."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance


# Global instance for easy access
db_service = DatabaseService()