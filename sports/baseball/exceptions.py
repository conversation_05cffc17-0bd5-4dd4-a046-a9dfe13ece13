"""
Baseball Module Custom Exceptions
Provides specific exception types for better error handling and debugging
"""
from typing import Any, Dict, Optional


class BaseballError(Exception):
    """Base exception for all baseball module errors."""
    
    def __init__(
        self, 
        message: str, 
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ) -> None:
        """
        Initialize baseball error.
        
        Args:
            message: Human-readable error message
            context: Additional context information
            cause: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.context = context or {}
        self.cause = cause
    
    def __str__(self) -> str:
        """Return string representation with context."""
        if self.context:
            context_str = ", ".join(f"{k}={v}" for k, v in self.context.items())
            return f"{self.message} (Context: {context_str})"
        return self.message


class DatabaseError(BaseballError):
    """Exception raised for database-related errors."""
    
    def __init__(
        self, 
        message: str, 
        query: Optional[str] = None,
        table: Optional[str] = None,
        **kwargs
    ) -> None:
        """
        Initialize database error.
        
        Args:
            message: Error message
            query: SQL query that failed (if applicable)
            table: Table name involved (if applicable)
            **kwargs: Additional context
        """
        context = kwargs
        if query:
            context['query'] = query
        if table:
            context['table'] = table
        
        super().__init__(message, context, kwargs.get('cause'))


class BrowserError(BaseballError):
    """Exception raised for browser automation errors."""
    
    def __init__(
        self, 
        message: str, 
        url: Optional[str] = None,
        action: Optional[str] = None,
        **kwargs
    ) -> None:
        """
        Initialize browser error.
        
        Args:
            message: Error message
            url: URL being accessed when error occurred
            action: Browser action being performed
            **kwargs: Additional context
        """
        context = kwargs
        if url:
            context['url'] = url
        if action:
            context['action'] = action
        
        super().__init__(message, context, kwargs.get('cause'))


class DataCollectionError(BaseballError):
    """Exception raised for data collection failures."""
    
    def __init__(
        self, 
        message: str, 
        team_id: Optional[str] = None,
        pitcher_name: Optional[str] = None,
        data_type: Optional[str] = None,
        **kwargs
    ) -> None:
        """
        Initialize data collection error.
        
        Args:
            message: Error message
            team_id: Team identifier
            pitcher_name: Pitcher name
            data_type: Type of data being collected
            **kwargs: Additional context
        """
        context = kwargs
        if team_id:
            context['team_id'] = team_id
        if pitcher_name:
            context['pitcher_name'] = pitcher_name
        if data_type:
            context['data_type'] = data_type
        
        super().__init__(message, context, kwargs.get('cause'))


class ValidationError(BaseballError):
    """Exception raised for data validation failures."""
    
    def __init__(
        self, 
        message: str, 
        field: Optional[str] = None,
        value: Optional[Any] = None,
        expected_type: Optional[str] = None,
        **kwargs
    ) -> None:
        """
        Initialize validation error.
        
        Args:
            message: Error message
            field: Field name that failed validation
            value: Invalid value
            expected_type: Expected data type
            **kwargs: Additional context
        """
        context = kwargs
        if field:
            context['field'] = field
        if value is not None:
            context['value'] = str(value)
        if expected_type:
            context['expected_type'] = expected_type
        
        super().__init__(message, context, kwargs.get('cause'))


class ConfigurationError(BaseballError):
    """Exception raised for configuration-related errors."""
    
    def __init__(
        self, 
        message: str, 
        config_file: Optional[str] = None,
        missing_key: Optional[str] = None,
        **kwargs
    ) -> None:
        """
        Initialize configuration error.
        
        Args:
            message: Error message
            config_file: Configuration file path
            missing_key: Missing configuration key
            **kwargs: Additional context
        """
        context = kwargs
        if config_file:
            context['config_file'] = config_file
        if missing_key:
            context['missing_key'] = missing_key
        
        super().__init__(message, context, kwargs.get('cause'))


class TaskError(BaseballError):
    """Exception raised for task processing errors."""
    
    def __init__(
        self, 
        message: str, 
        task_id: Optional[str] = None,
        task_type: Optional[str] = None,
        **kwargs
    ) -> None:
        """
        Initialize task error.
        
        Args:
            message: Error message
            task_id: Task identifier
            task_type: Type of task
            **kwargs: Additional context
        """
        context = kwargs
        if task_id:
            context['task_id'] = task_id
        if task_type:
            context['task_type'] = task_type
        
        super().__init__(message, context, kwargs.get('cause'))


class ResourceError(BaseballError):
    """Exception raised for resource management errors."""
    
    def __init__(
        self, 
        message: str, 
        resource_type: Optional[str] = None,
        operation: Optional[str] = None,
        **kwargs
    ) -> None:
        """
        Initialize resource error.
        
        Args:
            message: Error message
            resource_type: Type of resource (browser, connection, etc.)
            operation: Operation being performed
            **kwargs: Additional context
        """
        context = kwargs
        if resource_type:
            context['resource_type'] = resource_type
        if operation:
            context['operation'] = operation
        
        super().__init__(message, context, kwargs.get('cause'))


class RetryableError(BaseballError):
    """Exception that indicates the operation should be retried."""
    
    def __init__(
        self, 
        message: str, 
        retry_count: int = 0,
        max_retries: int = 3,
        **kwargs
    ) -> None:
        """
        Initialize retryable error.
        
        Args:
            message: Error message
            retry_count: Current retry attempt
            max_retries: Maximum retry attempts
            **kwargs: Additional context
        """
        context = kwargs
        context['retry_count'] = retry_count
        context['max_retries'] = max_retries
        
        super().__init__(message, context, kwargs.get('cause'))
    
    @property
    def should_retry(self) -> bool:
        """Check if this error should trigger a retry."""
        return self.context.get('retry_count', 0) < self.context.get('max_retries', 3)


class CriticalError(BaseballError):
    """Exception that indicates a critical system failure requiring immediate attention."""
    
    def __init__(self, message: str, **kwargs) -> None:
        """
        Initialize critical error.
        
        Args:
            message: Error message
            **kwargs: Additional context
        """
        super().__init__(f"CRITICAL: {message}", kwargs, kwargs.get('cause'))