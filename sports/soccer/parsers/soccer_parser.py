"""
축구 데이터 파서
기존 야구 파서 패턴을 축구에 적용
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from bs4 import BeautifulSoup, Tag

from utils.logger import Logger

logger = Logger(__name__)


@dataclass
class SoccerData:
    """축구 데이터 구조"""
    team_name: str
    league: str
    league_id: str
    profile: Dict[str, Any]
    season_stats: Dict[str, Any]
    recent_games: List[Dict[str, Any]]
    career_stats: Dict[str, Any]
    players: List[Dict[str, Any]]


@dataclass
class SoccerPlayerData:
    """축구 선수 데이터 구조"""
    player_name: str
    team_name: str
    league: str
    league_id: str
    position: str
    profile: Dict[str, Any]
    season_stats: Dict[str, Any]
    career_stats: Dict[str, Any]


class BaseSoccerParser(ABC):
    """축구 파서 기본 클래스 - 야구 BaseStatsParser 패턴 적용"""
    
    def __init__(self, html: str, team_mappings: Optional[Dict[str, str]] = None):
        self.soup = BeautifulSoup(html, "html.parser")
        self.team_mappings = team_mappings or {}
        self.league_code = self.get_league_code()
    
    @abstractmethod
    def get_league_code(self) -> str:
        """리그 코드 반환"""
        pass
    
    @abstractmethod
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """시즌 요약 컬럼 매핑 반환"""
        pass
    
    @abstractmethod
    def get_recent_games_mapping(self) -> Dict[str, Dict[str, int]]:
        """최근 경기 컬럼 매핑 반환"""
        pass
    
    def parse_season_summary(self) -> Dict[str, Optional[Dict[str, str]]]:
        """시즌 전체 성적 파싱 - 실제 HTML 구조 기반"""
        season_stats = {
            "home": None,
            "away": None,
            "total": None
        }

        try:
            # 시즌 전체성적 테이블 찾기
            season_table = self.soup.find('table', string=lambda text: text and '시즌 전체성적' in text)
            if not season_table:
                season_table = self.soup.find('table')
                if not season_table:
                    return season_stats

            # 테이블 행들 가져오기
            rows = season_table.find_all('tr')
            if len(rows) < 3:
                return season_stats

            # 데이터 행들 (헤더 제외)
            data_rows = []
            for row in rows[2:]:  # 첫 두 행은 헤더
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 10:  # 최소 필요한 컬럼 수
                    data_rows.append(cells)

            # 홈, 원정, 전체 데이터 파싱
            for row in data_rows:
                row_type = row[1].get_text(strip=True) if len(row) > 1 else ""

                if "홈" in row_type:
                    season_stats["home"] = self._extract_season_data(row, "home")
                elif "원정" in row_type:
                    season_stats["away"] = self._extract_season_data(row, "away")
                elif "전체" in row_type:
                    season_stats["total"] = self._extract_season_data(row, "total")

        except Exception as e:
            logger.debug(f"시즌 통계 파싱 실패: {e}")

        return season_stats
    
    def parse_recent_games(self, limit: int = 5) -> List[Dict[str, str]]:
        """최근 경기 파싱 - 실제 HTML 구조 기반"""
        try:
            games = []

            # 최근 경기 테이블 찾기
            recent_table = self._find_recent_games_table()
            if not recent_table:
                return games

            rows = recent_table.find_all('tr')
            if len(rows) < 3:
                return games

            # 데이터 행들 (헤더 제외)
            data_rows = []
            for row in rows[2:]:  # 첫 두 행은 헤더
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 8:  # 최소 필요한 컬럼 수
                    data_rows.append(cells)

            # 최근 경기 데이터 파싱
            for row in data_rows[:limit]:
                try:
                    game_data = self._extract_recent_game_data(row)
                    if game_data:
                        game_data["league_code"] = self.league_code
                        games.append(game_data)
                except Exception as e:
                    logger.debug(f"개별 경기 파싱 실패: {e}")
                    continue

            return games

        except Exception as e:
            logger.debug(f"최근 경기 파싱 실패: {e}")
            return []
    
    def _parse_season_row(self, row_id: str, mapping: Dict[str, int]) -> Optional[Dict[str, str]]:
        """시즌 통계 행 파싱"""
        try:
            row = self.soup.find('tr', id=row_id)
            if not row:
                return None
            
            cells = row.find_all(['td', 'th'])
            if len(cells) < max(mapping.values()) + 1:
                return None
            
            result = {}
            for field, index in mapping.items():
                if index < len(cells):
                    result[field] = cells[index].get_text(strip=True)
            
            return result if result else None
            
        except Exception as e:
            logger.debug(f"시즌 행 파싱 실패 {row_id}: {e}")
            return None
    
    def _extract_season_data(self, row: List, row_type: str) -> Dict[str, str]:
        """시즌 데이터 추출"""
        try:
            data = {
                "type": row_type,
                "league_code": self.league_code
            }

            # 실제 HTML 구조에 맞는 인덱스 매핑
            if len(row) >= 20:  # 축구 테이블 컬럼 수
                data.update({
                    "rank": row[0].get_text(strip=True) if row[0] else "",
                    "home_away": row[1].get_text(strip=True) if row[1] else "",
                    "games": row[2].get_text(strip=True) if row[2] else "",
                    "wins": row[3].get_text(strip=True) if row[3] else "",
                    "draws": row[4].get_text(strip=True) if row[4] else "",
                    "losses": row[5].get_text(strip=True) if row[5] else "",
                    "points": row[6].get_text(strip=True) if row[6] else "",
                    "win_rate": row[7].get_text(strip=True) if row[7] else "",
                    "goals_for": row[8].get_text(strip=True) if row[8] else "",
                    "goals_avg": row[9].get_text(strip=True) if row[9] else "",
                    "shooting_rate": row[10].get_text(strip=True) if row[10] else "",
                    "goals_against": row[11].get_text(strip=True) if row[11] else "",
                    "goals_against_avg": row[12].get_text(strip=True) if row[12] else "",
                    "assists": row[13].get_text(strip=True) if row[13] else "",
                    "goal_kicks": row[14].get_text(strip=True) if row[14] else "",
                    "corner_kicks": row[15].get_text(strip=True) if row[15] else "",
                    "penalty_kicks": row[16].get_text(strip=True) if row[16] else "",
                    "offsides": row[17].get_text(strip=True) if row[17] else "",
                    "fouls": row[18].get_text(strip=True) if row[18] else "",
                    "yellow_cards": row[19].get_text(strip=True) if row[19] else "",
                    "red_cards": row[20].get_text(strip=True) if len(row) > 20 else ""
                })

            return data

        except Exception as e:
            logger.debug(f"시즌 데이터 추출 실패: {e}")
            return {"type": row_type, "league_code": self.league_code}

    def _extract_recent_game_data(self, row: List) -> Optional[Dict[str, str]]:
        """최근 경기 데이터 추출"""
        try:
            if len(row) < 8:
                return None

            data = {
                "date": row[0].get_text(strip=True) if row[0] else "",
                "home_away": row[1].get_text(strip=True) if row[1] else "",
                "opponent": row[2].get_text(strip=True) if row[2] else "",
                "result": row[3].get_text(strip=True) if row[3] else "",
                "goals_total": row[4].get_text(strip=True) if row[4] else "",
                "goals_first": row[5].get_text(strip=True) if row[5] else "",
                "goals_second": row[6].get_text(strip=True) if row[6] else "",
                "goals_extra": row[7].get_text(strip=True) if row[7] else "",
                "goals_penalty": row[8].get_text(strip=True) if len(row) > 8 else "",
                "goals_against": row[9].get_text(strip=True) if len(row) > 9 else "",
                "goal_diff": row[10].get_text(strip=True) if len(row) > 10 else "",
                "assists": row[11].get_text(strip=True) if len(row) > 11 else "",
                "corner_kicks": row[12].get_text(strip=True) if len(row) > 12 else "",
                "penalty_kicks": row[13].get_text(strip=True) if len(row) > 13 else "",
                "offsides": row[14].get_text(strip=True) if len(row) > 14 else "",
                "fouls": row[15].get_text(strip=True) if len(row) > 15 else "",
                "yellow_cards": row[16].get_text(strip=True) if len(row) > 16 else "",
                "red_cards": row[17].get_text(strip=True) if len(row) > 17 else ""
            }

            return data

        except Exception as e:
            logger.debug(f"최근 경기 데이터 추출 실패: {e}")
            return None

    def _find_recent_games_table(self) -> Optional[Tag]:
        """최근 경기 테이블 찾기"""
        # 실제 HTML 구조에 맞는 테이블 찾기
        try:
            # 시즌 최근성적 테이블 찾기
            tables = self.soup.find_all('table')
            for table in tables:
                caption = table.find('caption')
                if caption and '최근성적' in caption.get_text():
                    return table

            # 대안: 두 번째 테이블 (보통 최근 경기)
            if len(tables) >= 2:
                return tables[1]

        except Exception as e:
            logger.debug(f"최근 경기 테이블 찾기 실패: {e}")

        return None


class KLeagueParser(BaseSoccerParser):
    """K리그 파서"""
    
    def get_league_code(self) -> str:
        return "K_LEAGUE"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {
                "games": 0, "wins": 1, "draws": 2, "losses": 3,
                "goals_for": 4, "goals_against": 5, "goal_diff": 6, "points": 7
            },
            "away": {
                "games": 0, "wins": 1, "draws": 2, "losses": 3,
                "goals_for": 4, "goals_against": 5, "goal_diff": 6, "points": 7
            },
            "total": {
                "games": 0, "wins": 1, "draws": 2, "losses": 3,
                "goals_for": 4, "goals_against": 5, "goal_diff": 6, "points": 7
            }
        }
    
    def get_recent_games_mapping(self) -> Dict[str, int]:
        return {
            "date": 0,
            "opponent": 1,
            "home_away": 2,
            "result": 3,
            "score": 4,
            "goals_for": 5,
            "goals_against": 6
        }


class EPLParser(BaseSoccerParser):
    """EPL 파서"""
    
    def get_league_code(self) -> str:
        return "EPL"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {
                "played": 0, "won": 1, "drawn": 2, "lost": 3,
                "goals_for": 4, "goals_against": 5, "goal_difference": 6, "points": 7
            },
            "away": {
                "played": 0, "won": 1, "drawn": 2, "lost": 3,
                "goals_for": 4, "goals_against": 5, "goal_difference": 6, "points": 7
            },
            "total": {
                "played": 0, "won": 1, "drawn": 2, "lost": 3,
                "goals_for": 4, "goals_against": 5, "goal_difference": 6, "points": 7
            }
        }
    
    def get_recent_games_mapping(self) -> Dict[str, int]:
        return {
            "date": 0,
            "opponent": 1,
            "venue": 2,
            "result": 3,
            "score": 4,
            "competition": 5
        }


class SoccerParser:
    """축구 통합 파서 - 리그별 파서 선택"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        self.team_mappings = team_mappings or {}
        self.league_parsers = {
            'SC001': KLeagueParser,  # K리그1
            'SC003': KLeagueParser,  # K리그2
            '52': EPLParser,         # EPL
            '53': EPLParser,         # 프리메라리가 (EPL 패턴 재사용)
            '54': EPLParser,         # 세리에A
            '55': EPLParser,         # 분데스리가
            '56': EPLParser,         # 프랑스리그
            '57': EPLParser,         # 에레디비시
            '58': KLeagueParser,     # J리그 (K리그 패턴 재사용)
        }
    
    def parse_team_data(self, html: str, team_name: str, **kwargs) -> SoccerData:
        """팀 데이터 파싱"""
        try:
            league_id = kwargs.get('league_id', 'SC001')
            league = kwargs.get('league', 'K리그1')

            # 리그별 파서 선택
            parser_class = self.league_parsers.get(league_id, KLeagueParser)
            parser = parser_class(html, self.team_mappings)

            # 프로필 파싱
            profile = parser._parse_team_profile(html, team_name)

            # 시즌 통계 파싱
            season_stats = parser.parse_season_summary()

            # 최근 경기 파싱
            recent_games = parser.parse_recent_games()

            # 선수 명단 파싱
            players = parser.parse_team_players()

            # 커리어 통계 파싱 (축구는 시즌 통계와 동일)
            career_stats = season_stats.copy()

            return SoccerData(
                team_name=team_name,
                league=league,
                league_id=league_id,
                profile=profile,
                season_stats=season_stats,
                recent_games=recent_games,
                career_stats=career_stats,
                players=players
            )

        except Exception as e:
            logger.error(f"축구 팀 데이터 파싱 실패 {team_name}: {e}")
            return SoccerData(
                team_name=team_name,
                league=kwargs.get('league', ''),
                league_id=kwargs.get('league_id', ''),
                profile={},
                season_stats={},
                recent_games=[],
                career_stats={},
                players=[]
            )
    
    def parse_team_players(self) -> List[Dict[str, str]]:
        """팀 선수 명단 파싱 - 실제 HTML 구조 기반"""
        try:
            players = []

            # 선수명단 섹션 찾기
            player_sections = self.soup.find_all('div', class_=lambda x: x and any(pos in str(x) for pos in ['GK', 'DF', 'MF', 'FW']))

            if not player_sections:
                # 대안: 선수명단이 포함된 리스트 아이템 찾기
                player_list = self.soup.find('li', string=lambda text: text and '선수명단' in text)
                if player_list:
                    player_sections = player_list.find_all_next('div')[:4]  # GK, DF, MF, FW

            for section in player_sections:
                try:
                    # 포지션 추출
                    position_elem = section.find(string=lambda text: text and text.strip() in ['GK', 'DF', 'MF', 'FW'])
                    position = position_elem.strip() if position_elem else ""

                    # 선수 링크들 찾기
                    player_links = section.find_all('a', href=lambda x: x and 'scPlayerDetail.do' in x)

                    for link in player_links:
                        try:
                            player_name = link.get_text(strip=True)
                            player_url = link.get('href', '')

                            # URL에서 선수 ID 추출
                            player_id = self._extract_player_id_from_url(player_url)

                            if player_name and player_id:
                                players.append({
                                    'player_name': player_name,
                                    'position': position,
                                    'player_id': player_id,
                                    'player_url': player_url,
                                    'league_code': self.league_code
                                })

                        except Exception as e:
                            logger.debug(f"선수 정보 추출 실패: {e}")
                            continue

                except Exception as e:
                    logger.debug(f"포지션 섹션 파싱 실패: {e}")
                    continue

            return players

        except Exception as e:
            logger.debug(f"선수 명단 파싱 실패: {e}")
            return []

    def _extract_player_id_from_url(self, url: str) -> Optional[str]:
        """URL에서 선수 ID 추출"""
        try:
            import re
            match = re.search(r'playerId=([^&]+)', url)
            return match.group(1) if match else None
        except:
            return None

    def _parse_team_profile(self, html: str, team_name: str) -> Dict[str, Any]:
        """팀 프로필 파싱 - 실제 HTML 구조 기반"""
        try:
            soup = BeautifulSoup(html, 'html.parser')

            profile = {
                'team_name': team_name,
                'manager': '',
                'stadium': '',
                'website': '',
                'season_rank': '',
                'season_record': '',
                'last_updated': ''
            }

            # 팀 정보 리스트에서 추출
            info_list = soup.find('ul')
            if info_list:
                items = info_list.find_all('li')

                for item in items:
                    text = item.get_text(strip=True)

                    if '감독' in text:
                        profile['manager'] = text.replace('감독', '').strip()
                    elif '구장' in text:
                        profile['stadium'] = text.replace('구장', '').strip()
                    elif '공식홈페이지' in text:
                        link = item.find('a')
                        if link:
                            profile['website'] = link.get('href', '')
                    elif '시즌성적' in text:
                        profile['season_record'] = text.replace('시즌성적', '').strip()

            # 순위 정보 추출
            rank_elem = soup.find('strong', string=lambda text: text and '위' in text)
            if rank_elem:
                profile['season_rank'] = rank_elem.get_text(strip=True)

            return profile

        except Exception as e:
            logger.debug(f"팀 프로필 파싱 실패 {team_name}: {e}")
            return {'team_name': team_name}


class SoccerPlayerParser:
    """축구 선수 개별 파서"""

    def __init__(self, html: str):
        self.soup = BeautifulSoup(html, "html.parser")

    def parse_player_data(self, player_name: str, **kwargs) -> SoccerPlayerData:
        """선수 데이터 파싱"""
        try:
            league_id = kwargs.get('league_id', 'SC001')
            league = kwargs.get('league', 'K리그1')
            team_name = kwargs.get('team_name', '')
            position = kwargs.get('position', '')

            # 프로필 파싱
            profile = self._parse_player_profile(player_name)

            # 시즌 통계 파싱
            season_stats = self._parse_player_season_stats()

            # 커리어 통계 파싱
            career_stats = self._parse_player_career_stats()

            return SoccerPlayerData(
                player_name=player_name,
                team_name=team_name,
                league=league,
                league_id=league_id,
                position=position,
                profile=profile,
                season_stats=season_stats,
                career_stats=career_stats
            )

        except Exception as e:
            logger.error(f"축구 선수 데이터 파싱 실패 {player_name}: {e}")
            return SoccerPlayerData(
                player_name=player_name,
                team_name=kwargs.get('team_name', ''),
                league=kwargs.get('league', ''),
                league_id=kwargs.get('league_id', ''),
                position=kwargs.get('position', ''),
                profile={},
                season_stats={},
                career_stats={}
            )

    def _parse_player_profile(self, player_name: str) -> Dict[str, Any]:
        """선수 프로필 파싱"""
        try:
            profile = {
                'player_name': player_name,
                'birth_date': '',
                'height': '',
                'weight': '',
                'nationality': '',
                'position': '',
                'back_number': '',
                'career': []
            }

            # 선수 정보 리스트에서 추출
            info_list = self.soup.find('ul')
            if info_list:
                items = info_list.find_all('li')

                for item in items:
                    text = item.get_text(strip=True)

                    if '생년월일' in text:
                        profile['birth_date'] = text.replace('생년월일', '').strip()
                    elif '신장' in text:
                        profile['height'] = text.replace('신장', '').strip()
                    elif '체중' in text:
                        profile['weight'] = text.replace('체중', '').strip()
                    elif '국적' in text:
                        profile['nationality'] = text.replace('국적', '').strip()
                    elif '포지션' in text:
                        profile['position'] = text.replace('포지션', '').strip()
                    elif '등번호' in text:
                        profile['back_number'] = text.replace('등번호', '').strip()

            return profile

        except Exception as e:
            logger.debug(f"선수 프로필 파싱 실패 {player_name}: {e}")
            return {'player_name': player_name}

    def _parse_player_season_stats(self) -> Dict[str, Any]:
        """선수 시즌 통계 파싱"""
        try:
            stats = {}

            # 시즌 통계 테이블 찾기
            tables = self.soup.find_all('table')

            for table in tables:
                caption = table.find('caption')
                if caption and '시즌' in caption.get_text():
                    rows = table.find_all('tr')
                    if len(rows) >= 2:
                        # 헤더와 데이터 행
                        header_row = rows[0].find_all(['th', 'td'])
                        data_row = rows[1].find_all(['th', 'td'])

                        if len(header_row) == len(data_row):
                            for i, header in enumerate(header_row):
                                header_text = header.get_text(strip=True)
                                data_text = data_row[i].get_text(strip=True)
                                if header_text and data_text:
                                    stats[header_text] = data_text

            return stats

        except Exception as e:
            logger.debug(f"선수 시즌 통계 파싱 실패: {e}")
            return {}

    def _parse_player_career_stats(self) -> Dict[str, Any]:
        """선수 커리어 통계 파싱"""
        try:
            stats = {}

            # 커리어 통계 테이블 찾기
            tables = self.soup.find_all('table')

            for table in tables:
                caption = table.find('caption')
                if caption and ('통산' in caption.get_text() or '커리어' in caption.get_text()):
                    rows = table.find_all('tr')
                    if len(rows) >= 2:
                        # 헤더와 데이터 행
                        header_row = rows[0].find_all(['th', 'td'])

                        # 여러 시즌 데이터가 있을 수 있음
                        career_data = []
                        for row in rows[1:]:
                            data_row = row.find_all(['th', 'td'])
                            if len(data_row) == len(header_row):
                                season_data = {}
                                for i, header in enumerate(header_row):
                                    header_text = header.get_text(strip=True)
                                    data_text = data_row[i].get_text(strip=True)
                                    if header_text and data_text:
                                        season_data[header_text] = data_text
                                if season_data:
                                    career_data.append(season_data)

                        stats['career_seasons'] = career_data

            return stats

        except Exception as e:
            logger.debug(f"선수 커리어 통계 파싱 실패: {e}")
            return {}
