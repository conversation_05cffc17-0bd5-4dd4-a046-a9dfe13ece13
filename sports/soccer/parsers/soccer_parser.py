"""
축구 데이터 파서
기존 야구 파서 패턴을 축구에 적용
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from bs4 import BeautifulSoup, Tag

from utils.logger import Logger

logger = Logger(__name__)


@dataclass
class SoccerData:
    """축구 데이터 구조"""
    team_name: str
    league: str
    league_id: str
    profile: Dict[str, Any]
    season_stats: Dict[str, Any]
    recent_games: List[Dict[str, Any]]
    career_stats: Dict[str, Any]


class BaseSoccerParser(ABC):
    """축구 파서 기본 클래스 - 야구 BaseStatsParser 패턴 적용"""
    
    def __init__(self, html: str, team_mappings: Optional[Dict[str, str]] = None):
        self.soup = BeautifulSoup(html, "html.parser")
        self.team_mappings = team_mappings or {}
        self.league_code = self.get_league_code()
    
    @abstractmethod
    def get_league_code(self) -> str:
        """리그 코드 반환"""
        pass
    
    @abstractmethod
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """시즌 요약 컬럼 매핑 반환"""
        pass
    
    @abstractmethod
    def get_recent_games_mapping(self) -> Dict[str, Dict[str, int]]:
        """최근 경기 컬럼 매핑 반환"""
        pass
    
    def parse_season_summary(self) -> Dict[str, Optional[Dict[str, str]]]:
        """시즌 전체 성적 파싱"""
        season_stats = {
            "home": None, 
            "away": None, 
            "total": None
        }
        
        mapping = self.get_season_summary_mapping()
        
        # 홈 통계 파싱
        home_dict = self._parse_season_row("home_record", mapping["home"])
        if home_dict:
            home_dict["league_code"] = self.league_code
            season_stats["home"] = home_dict
        
        # 원정 통계 파싱
        away_dict = self._parse_season_row("away_record", mapping["away"])
        if away_dict:
            away_dict["league_code"] = self.league_code
            season_stats["away"] = away_dict
        
        # 전체 통계 파싱
        total_dict = self._parse_season_row("all_record", mapping["total"])
        if total_dict:
            total_dict["league_code"] = self.league_code
            season_stats["total"] = total_dict
        
        return season_stats
    
    def parse_recent_games(self, limit: int = 5) -> List[Dict[str, str]]:
        """최근 경기 파싱"""
        try:
            games = []
            mapping = self.get_recent_games_mapping()
            
            # 최근 경기 테이블 찾기
            recent_table = self._find_recent_games_table()
            if not recent_table:
                return games
            
            rows = recent_table.find_all('tr')[1:]  # 헤더 제외
            
            for row in rows[:limit]:
                cells = row.find_all(['td', 'th'])
                if len(cells) < len(mapping):
                    continue
                
                game_data = {}
                for field, index in mapping.items():
                    if index < len(cells):
                        game_data[field] = cells[index].get_text(strip=True)
                
                if game_data:
                    game_data["league_code"] = self.league_code
                    games.append(game_data)
            
            return games
            
        except Exception as e:
            logger.debug(f"최근 경기 파싱 실패: {e}")
            return []
    
    def _parse_season_row(self, row_id: str, mapping: Dict[str, int]) -> Optional[Dict[str, str]]:
        """시즌 통계 행 파싱"""
        try:
            row = self.soup.find('tr', id=row_id)
            if not row:
                return None
            
            cells = row.find_all(['td', 'th'])
            if len(cells) < max(mapping.values()) + 1:
                return None
            
            result = {}
            for field, index in mapping.items():
                if index < len(cells):
                    result[field] = cells[index].get_text(strip=True)
            
            return result if result else None
            
        except Exception as e:
            logger.debug(f"시즌 행 파싱 실패 {row_id}: {e}")
            return None
    
    def _find_recent_games_table(self) -> Optional[Tag]:
        """최근 경기 테이블 찾기"""
        # 여러 패턴으로 테이블 찾기
        selectors = [
            'table.recent_games',
            'table.game_list',
            'div.recent_games table',
            'div.game_history table'
        ]
        
        for selector in selectors:
            table = self.soup.select_one(selector)
            if table:
                return table
        
        return None


class KLeagueParser(BaseSoccerParser):
    """K리그 파서"""
    
    def get_league_code(self) -> str:
        return "K_LEAGUE"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {
                "games": 0, "wins": 1, "draws": 2, "losses": 3,
                "goals_for": 4, "goals_against": 5, "goal_diff": 6, "points": 7
            },
            "away": {
                "games": 0, "wins": 1, "draws": 2, "losses": 3,
                "goals_for": 4, "goals_against": 5, "goal_diff": 6, "points": 7
            },
            "total": {
                "games": 0, "wins": 1, "draws": 2, "losses": 3,
                "goals_for": 4, "goals_against": 5, "goal_diff": 6, "points": 7
            }
        }
    
    def get_recent_games_mapping(self) -> Dict[str, int]:
        return {
            "date": 0,
            "opponent": 1,
            "home_away": 2,
            "result": 3,
            "score": 4,
            "goals_for": 5,
            "goals_against": 6
        }


class EPLParser(BaseSoccerParser):
    """EPL 파서"""
    
    def get_league_code(self) -> str:
        return "EPL"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {
                "played": 0, "won": 1, "drawn": 2, "lost": 3,
                "goals_for": 4, "goals_against": 5, "goal_difference": 6, "points": 7
            },
            "away": {
                "played": 0, "won": 1, "drawn": 2, "lost": 3,
                "goals_for": 4, "goals_against": 5, "goal_difference": 6, "points": 7
            },
            "total": {
                "played": 0, "won": 1, "drawn": 2, "lost": 3,
                "goals_for": 4, "goals_against": 5, "goal_difference": 6, "points": 7
            }
        }
    
    def get_recent_games_mapping(self) -> Dict[str, int]:
        return {
            "date": 0,
            "opponent": 1,
            "venue": 2,
            "result": 3,
            "score": 4,
            "competition": 5
        }


class SoccerParser:
    """축구 통합 파서 - 리그별 파서 선택"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        self.team_mappings = team_mappings or {}
        self.league_parsers = {
            'SC001': KLeagueParser,  # K리그1
            'SC003': KLeagueParser,  # K리그2
            '52': EPLParser,         # EPL
            '53': EPLParser,         # 프리메라리가 (EPL 패턴 재사용)
            '54': EPLParser,         # 세리에A
            '55': EPLParser,         # 분데스리가
            '56': EPLParser,         # 프랑스리그
            '57': EPLParser,         # 에레디비시
            '58': KLeagueParser,     # J리그 (K리그 패턴 재사용)
        }
    
    def parse_team_data(self, html: str, team_name: str, **kwargs) -> SoccerData:
        """팀 데이터 파싱"""
        try:
            league_id = kwargs.get('league_id', 'SC001')
            league = kwargs.get('league', 'K리그1')
            
            # 리그별 파서 선택
            parser_class = self.league_parsers.get(league_id, KLeagueParser)
            parser = parser_class(html, self.team_mappings)
            
            # 프로필 파싱
            profile = self._parse_team_profile(html, team_name)
            
            # 시즌 통계 파싱
            season_stats = parser.parse_season_summary()
            
            # 최근 경기 파싱
            recent_games = parser.parse_recent_games()
            
            # 커리어 통계 파싱 (축구는 시즌 통계와 동일)
            career_stats = season_stats.copy()
            
            return SoccerData(
                team_name=team_name,
                league=league,
                league_id=league_id,
                profile=profile,
                season_stats=season_stats,
                recent_games=recent_games,
                career_stats=career_stats
            )
            
        except Exception as e:
            logger.error(f"축구 팀 데이터 파싱 실패 {team_name}: {e}")
            return SoccerData(
                team_name=team_name,
                league=kwargs.get('league', ''),
                league_id=kwargs.get('league_id', ''),
                profile={},
                season_stats={},
                recent_games=[],
                career_stats={}
            )
    
    def _parse_team_profile(self, html: str, team_name: str) -> Dict[str, Any]:
        """팀 프로필 파싱"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            profile = {
                'team_name': team_name,
                'founded': '',
                'stadium': '',
                'manager': '',
                'league_position': '',
                'last_updated': ''
            }
            
            # 프로필 정보 추출 (사이트별 구조에 따라 조정 필요)
            profile_section = soup.find('div', class_='team_profile')
            if profile_section:
                # 설립연도
                founded_elem = profile_section.find('span', string='설립')
                if founded_elem and founded_elem.next_sibling:
                    profile['founded'] = founded_elem.next_sibling.strip()
                
                # 홈구장
                stadium_elem = profile_section.find('span', string='홈구장')
                if stadium_elem and stadium_elem.next_sibling:
                    profile['stadium'] = stadium_elem.next_sibling.strip()
                
                # 감독
                manager_elem = profile_section.find('span', string='감독')
                if manager_elem and manager_elem.next_sibling:
                    profile['manager'] = manager_elem.next_sibling.strip()
            
            return profile
            
        except Exception as e:
            logger.debug(f"팀 프로필 파싱 실패 {team_name}: {e}")
            return {'team_name': team_name}
