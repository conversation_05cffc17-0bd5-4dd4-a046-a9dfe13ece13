"""
K리그 팀 통계 파서
베트맨 사이트의 축구 팀 상세 페이지에서 통계를 추출
"""
from typing import Any, Dict, List

from bs4 import BeautifulSoup, Tag

from sports.base.utils.text_parsing import safe_float, safe_int
from utils.helpers import map_result
from utils.logger import Logger

logger = Logger(__name__)


class KLeagueStatsParser:
    """K리그 팀 통계 파서"""
    
    def __init__(self, html: str, team_name: str):
        self.html = html
        self.team_name = team_name
        self.soup = BeautifulSoup(html, 'html.parser')
        
    def parse_season_summary(self) -> Dict[str, Any]:
        """시즌 요약 정보 파싱 (홈/원정/전체 시즌 통계)"""
        summary = {}
        
        try:
            # 홈/원정 통계 (homeAway_record)
            home_away_section = self.soup.find('tbody', id='homeAway_record')
            if home_away_section:
                summary.update(self._parse_home_away_stats(home_away_section))
                
            # 전체 통계 (total_record)
            total_section = self.soup.find('tfoot', id='total_record')
            if total_section:
                summary.update(self._parse_total_stats(total_section))
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 시즌 요약 파싱 실패: {e}")
             
        return summary
         
    def _parse_home_away_stats(self, section: Tag) -> Dict[str, Any]:
        """홈/원정 통계 파싱 (야구 방식 매핑 기반)"""
        stats = {}
        
        try:
            rows = section.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 15:
                    continue
                
                # 홈/원정 매핑 정의 (야구 방식)
                if cells[0].get('rowspan') == '2':
                    # 홈 행 (순위 rowspan + 21개 셀, 파울 없음)
                    location = cells[1].get_text(strip=True)  # "홈"
                    mapping = {
                        'matches_played': 2,
                        'wins': 3,
                        'draws': 4,
                        'losses': 5,
                        'points': 6,
                        'win_rate': 7,
                        'goals_scored': 8,
                        'goals_scored_avg': 9,
                        'shooting_efficiency': 10,
                        'goals_conceded': 11,
                        'goals_conceded_avg': 12,
                        'goal_difference': 13,
                        'assists': 14,
                        'goal_kicks': 15,
                        'corner_kicks': 16,
                        'penalty_kicks': 17,
                        'offsides': 18,
                        'fouls': None,  # 홈 행에는 파울 없음
                        'yellow_cards': 19,  # [19] = 15 (경고)
                        'red_cards': 20   # [20] = 0 (퇴장)
                    }
                else:
                    # 원정 행 (20개 셀: 파울 없이 경고와 퇴장)
                    location = cells[0].get_text(strip=True)  # "원정"
                    mapping = {
                        'matches_played': 1,
                        'wins': 2,
                        'draws': 3,
                        'losses': 4,
                        'points': 5,
                        'win_rate': 6,
                        'goals_scored': 7,
                        'goals_scored_avg': 8,
                        'shooting_efficiency': 9,
                        'goals_conceded': 10,
                        'goals_conceded_avg': 11,
                        'goal_difference': 12,
                        'assists': 13,
                        'goal_kicks': 14,
                        'corner_kicks': 15,
                        'penalty_kicks': 16,
                        'offsides': 17,
                        'fouls': None,  # 원정 행에도 파울 없음
                        'yellow_cards': 18,  # [18] = 19 (경고)
                        'red_cards': 19   # [19] = 1 (퇴장)
                    }
                
                # 매핑 기반 데이터 추출
                location_stats = {}
                for field, index in mapping.items():
                    if index is None or index >= len(cells):
                        location_stats[field] = 0
                    else:
                        cell_text = cells[index].get_text(strip=True)
                        if field == 'shooting_efficiency':
                            location_stats[field] = self._safe_percentage_as_percent(
                                cell_text
                            )
                        elif field in ['win_rate', 'goals_scored_avg', 
                                     'goals_conceded_avg']:
                            location_stats[field] = self._safe_float(cell_text)
                        else:
                            location_stats[field] = self._safe_int(cell_text)
                
                stats[location] = location_stats
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 홈/원정 통계 파싱 실패: {e}")
            
        return stats
    
    def _parse_total_stats(self, section: Tag) -> Dict[str, Any]:
        """전체 통계 파싱 (야구 방식 매핑 기반)"""
        stats = {}
        
        try:
            rows = section.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 15:
                    continue
                
                # 전체 행 매핑 (20개 셀: 파울 없이 경고와 퇴장)
                mapping = {
                    'matches_played': 1,
                    'wins': 2,
                    'draws': 3,
                    'losses': 4,
                    'points': 5,
                    'win_rate': 6,
                    'goals_scored': 7,
                    'goals_scored_avg': 8,
                    'shooting_efficiency': 9,
                    'goals_conceded': 10,
                    'goals_conceded_avg': 11,
                    'goal_difference': 12,
                    'assists': 13,
                    'goal_kicks': 14,
                    'corner_kicks': 15,
                    'penalty_kicks': 16,
                    'offsides': 17,
                    'fouls': None,  # 전체 행에는 파울 없음
                    'yellow_cards': 18,
                    'red_cards': 19
                }
                
                # 매핑 기반 데이터 추출
                total_stats = {}
                for field, index in mapping.items():
                    if index is None or index >= len(cells):
                        total_stats[field] = 0
                    else:
                        cell_text = cells[index].get_text(strip=True)
                        if field == 'shooting_efficiency':
                            total_stats[field] = self._safe_percentage_as_percent(
                                cell_text
                            )
                        elif field in ['win_rate', 'goals_scored_avg', 
                                     'goals_conceded_avg']:
                            total_stats[field] = self._safe_float(cell_text)
                        else:
                            total_stats[field] = self._safe_int(cell_text)
                
                stats['전체'] = total_stats
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 전체 통계 파싱 실패: {e}")
            
        return stats

    def parse_recent_games_summary(self) -> Dict[str, Any]:
        """최근 경기 요약 정보 파싱 - K-League 형식"""
        summary = {}
        
        try:
            # 1. 먼저 실제 recent_games 데이터가 있으면 그걸로 계산
            recent_games = self.parse_recent_games()
            if recent_games and (recent_games.get('recent_5_home_games') or recent_games.get('recent_5_away_games')):
                summary = self._calculate_summary_from_games(recent_games)
                logger.debug(f"최근 경기 데이터에서 요약 계산: {summary}")
            else:
                # 2. recent_games가 없으면 HTML에서 파싱 (fallback)
                total_section = self.soup.find(
                    'tfoot', id='seasonLatestTeamRecordTotal'
                )
                if total_section:
                    raw_data = self._parse_recent_games_summary_data(total_section)
                    summary = self._convert_to_kleague_format(raw_data)
                
        except Exception as e:
            logger.error(
                f"❌ [{self.team_name}] 최근 경기 요약 파싱 실패: {e}"
            )
             
        # 🎯 통일된 정규화 메서드 사용 (BaseSoccerParser 공통)
        return self.normalize_recent_games_structure(summary)

    def _calculate_summary_from_games(self, recent_games: Dict[str, Any]) -> Dict[str, Any]:
        """최근 경기 데이터로부터 요약 통계 계산 (야구 스타일과 동일한 패턴)"""
        if not recent_games:
            return {}
            
        try:
            summary = {}
            
            # 홈 경기 계산
            home_games = recent_games.get('recent_5_home_games', {})
            if home_games:
                home_wins = home_draws = home_losses = 0
                home_goals_for = home_goals_against = 0
                home_game_count = 0
                
                for date_key, game_data in home_games.items():
                    if isinstance(game_data, dict) and 'home' in game_data:
                        home_data = game_data['home']
                        result = home_data.get('result', '')
                        
                        # WDL 카운트
                        if result == 'W' or result == '승':
                            home_wins += 1
                        elif result == 'D' or result == '무':
                            home_draws += 1  
                        elif result == 'L' or result == '패':
                            home_losses += 1
                            
                        # 득실점 합산
                        score = home_data.get('score', '0-0')
                        if '-' in score:
                            goals_for, goals_against = score.split('-')
                            home_goals_for += int(goals_for.strip()) if goals_for.strip().isdigit() else 0
                            home_goals_against += int(goals_against.strip()) if goals_against.strip().isdigit() else 0
                        
                        home_game_count += 1
                
                # 홈 요약 생성
                summary['recent_5_home_games'] = {
                    'record': f"{home_wins}W {home_draws}D {home_losses}L",
                    'avg_goals_scored': round(home_goals_for / home_game_count, 2) if home_game_count > 0 else 0.0,
                    'avg_goals_conceded': round(home_goals_against / home_game_count, 2) if home_game_count > 0 else 0.0,
                    'avg_assists': 0.0,
                    'avg_corners': 0.0,
                    'avg_red_cards': 0.0,
                    'avg_yellow_cards': 0.0
                }
            
            # 원정 경기 계산  
            away_games = recent_games.get('recent_5_away_games', {})
            if away_games:
                away_wins = away_draws = away_losses = 0
                away_goals_for = away_goals_against = 0
                away_game_count = 0
                
                for date_key, game_data in away_games.items():
                    if isinstance(game_data, dict) and 'away' in game_data:
                        away_data = game_data['away']
                        result = away_data.get('result', '')
                        
                        # WDL 카운트
                        if result == 'W' or result == '승':
                            away_wins += 1
                        elif result == 'D' or result == '무':
                            away_draws += 1
                        elif result == 'L' or result == '패':
                            away_losses += 1
                            
                        # 득실점 합산
                        score = away_data.get('score', '0-0')
                        if '-' in score:
                            goals_for, goals_against = score.split('-')
                            away_goals_for += int(goals_for.strip()) if goals_for.strip().isdigit() else 0
                            away_goals_against += int(goals_against.strip()) if goals_against.strip().isdigit() else 0
                        
                        away_game_count += 1
                
                # 원정 요약 생성
                summary['recent_5_away_games'] = {
                    'record': f"{away_wins}W {away_draws}D {away_losses}L",
                    'avg_goals_scored': round(away_goals_for / away_game_count, 2) if away_game_count > 0 else 0.0,
                    'avg_goals_conceded': round(away_goals_against / away_game_count, 2) if away_game_count > 0 else 0.0,
                    'avg_assists': 0.0,
                    'avg_corners': 0.0,
                    'avg_red_cards': 0.0,
                    'avg_yellow_cards': 0.0
                }
                
        except Exception as e:
            logger.error(f"최근 경기 요약 계산 실패: {e}")
            return {}
            
        return summary
         
    def _parse_recent_games_summary_data(self, total_section: Tag) -> Dict[str, Any]:
        """최근 경기 평균 통계 파싱"""
        summary = {}
        
        try:
            rows = total_section.find_all('tr')
            
            home_section = {'home': None, 'away': None}
            away_section = {'home': None, 'away': None}
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 8:
                    continue
                    
                # 홈/원정 구분
                location = self._safe_get_cell_text(cells, 1) if len(cells) > 1 else ""
                
                # 첫 번째 행 (홈) - rowspan으로 인해 다른 구조
                if len(cells) == 12:  # 홈 행
                    record = self._safe_get_cell_text(cells, 2)  # colspan="2"인 셀
                    
                    # J-League처럼 홈 데이터가 모두 "-"인 경우 스킵
                    if record == "-" or record == "" or all(
                        self._safe_get_cell_text(cells, i) == "-" 
                        for i in [3, 8, 10]
                    ):
                        continue
                    
                    # 홈 행 데이터 매핑 (colspan="2" 고려)
                    home_data = {
                        'record': record,
                        'avg_goals_scored': self._safe_float(self._safe_get_cell_text(cells, 3)),
                        'avg_goals_conceded': self._safe_float(self._safe_get_cell_text(cells, 8)),
                        'avg_assists': self._safe_float(self._safe_get_cell_text(cells, 10)),
                        'avg_corners': 0.0,
                        'avg_red_cards': 0.0,
                        'avg_yellow_cards': 0.0
                    }
                    home_section['home'] = home_data
                    
                elif len(cells) == 11:  # 원정 행
                    record = self._safe_get_cell_text(cells, 1)  # colspan="2"인 셀
                    
                    # 원정 행 데이터 매핑 (colspan="2" 고려)
                    away_data = {
                        'record': record,
                        'avg_goals_scored': self._safe_float(self._safe_get_cell_text(cells, 2)),
                        'avg_goals_conceded': self._safe_float(self._safe_get_cell_text(cells, 7)),
                        'avg_assists': self._safe_float(self._safe_get_cell_text(cells, 9)),
                        'avg_corners': 0.0,
                        'avg_red_cards': 0.0,
                        'avg_yellow_cards': 0.0
                    }
                    away_section['away'] = away_data
            
            # 홈 섹션 추가 (valid data만) - flatten structure
            if home_section['home']:
                summary['recent_5_home_games'] = home_section['home']
            
            # 원정 섹션 추가 (valid data만) - flatten structure
            if away_section['away']:
                summary['recent_5_away_games'] = away_section['away']
                
        except Exception as e:
            logger.error(
                f"❌ [{self.team_name}] 최근 경기 요약 파싱 실패: {e}"
            )
             
        return summary
     
    def _convert_to_kleague_format(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """원시 데이터를 K-League 형식으로 변환"""
        kleague_format = {}
        
        # recent_5_games 구조 생성
        recent_5_games = {}
        
        # 홈 게임 데이터 처리 (플랫 구조 확인)
        if 'recent_5_home_games' in raw_data:
            home_data = raw_data['recent_5_home_games']
            # 플랫 구조인지 확인
            if isinstance(home_data, dict) and 'record' in home_data:
                if self._is_valid_game_data(home_data):
                    # 🎯 기록을 영어로 변환
                    from utils.parsers.game_result_parser import map_result
                    home_converted = home_data.copy()
                    if 'record' in home_converted:
                        home_converted['record'] = map_result(home_converted['record'])
                    recent_5_games['last_3_home_games'] = home_converted
        
        # 원정 게임 데이터 처리 (플랫 구조 확인)
        if 'recent_5_away_games' in raw_data:
            away_data = raw_data['recent_5_away_games']
            # 플랫 구조인지 확인
            if isinstance(away_data, dict) and 'record' in away_data:
                if self._is_valid_game_data(away_data):
                    # 🎯 기록을 영어로 변환
                    from utils.parsers.game_result_parser import map_result
                    away_converted = away_data.copy()
                    if 'record' in away_converted:
                        away_converted['record'] = map_result(away_converted['record'])
                    recent_5_games['last_2_away_games'] = away_converted
        
        # recent_5_games 추가
        if recent_5_games:
            kleague_format['recent_5_games'] = recent_5_games
        
        # 기존 구조도 유지
        if 'recent_5_home_games' in raw_data:
            kleague_format['recent_5_home_games'] = raw_data['recent_5_home_games']
        if 'recent_5_away_games' in raw_data:
            kleague_format['recent_5_away_games'] = raw_data['recent_5_away_games']
        
        return kleague_format
     
    def _filter_empty_data(self, summary: Dict[str, Any]) -> Dict[str, Any]:
        """빈 데이터나 무효한 데이터 항목 필터링"""
        filtered = {}
        
        for key, value in summary.items():
            if isinstance(value, dict):
                # 'home' 또는 'away' 키가 있는 경우 확인
                if 'home' in value or 'away' in value:
                    filtered_value = {}
                    
                    # 홈 데이터 확인
                    if 'home' in value:
                        home_data = value['home']
                        if self._is_valid_game_data(home_data):
                            filtered_value['home'] = home_data
                    
                    # 원정 데이터 확인
                    if 'away' in value:
                        away_data = value['away']
                        if self._is_valid_game_data(away_data):
                            filtered_value['away'] = away_data
                    
                    # 유효한 데이터가 있는 경우에만 포함
                    if filtered_value:
                        filtered[key] = filtered_value
                else:
                    # 기존 형식 지원
                    if self._is_valid_game_data(value):
                        filtered[key] = value
            else:
                filtered[key] = value
        
        return filtered
    
    def _is_valid_game_data(self, data: Dict[str, Any]) -> bool:
        """게임 데이터가 유효한지 확인"""
        if not isinstance(data, dict):
            return False
        
        # 기록이 '-' 또는 빈 문자열인 경우 무효
        record = data.get('record', '')
        if record in ['-', '', '0승 0무 0패']:
            return False
        
        # 모든 통계가 0인 경우 무효 (단, 정상적인 0-0 경기는 제외)
        goals_scored = data.get('avg_goals_scored', 0)
        goals_conceded = data.get('avg_goals_conceded', 0)
        assists = data.get('avg_assists', 0)
        
        # 기록이 있으면서 모든 통계가 0인 경우만 무효로 처리
        if record != '-' and goals_scored == 0 and goals_conceded == 0 and assists == 0:
            return False
        
        return True
     
    def parse_recent_games(self) -> Dict[str, Dict]:
        """최근 경기 상세 정보 파싱 (실제 베트맨 사이트 구조)"""
        recent_games = {
            'recent_5_home_games': {},
            'recent_5_away_games': {}
        }
        
        try:
            # 직접 두 번째 테이블 사용 (베트맨 실제 구조)
            tables = self.soup.find_all('table', class_=['tbl', 'tblAuto'])
            if len(tables) >= 2:
                recent_table = tables[1]  # 두 번째 테이블
                recent_games = self._parse_betman_recent_games(recent_table)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 최근 경기 파싱 실패: {e}")
            
        return recent_games
    
    def _parse_betman_recent_games(self, recent_table: Tag) -> Dict[str, Dict]:
        """베트맨 사이트 최근 경기 결과 파싱 - 작동하는 버전"""
        games = {'recent_5_home_games': {}, 'recent_5_away_games': {}}
        
        try:
            tbody = recent_table.find('tbody')
            if not tbody:
                return games
                
            rows = tbody.find_all('tr')
            
            # 데이터 행들 (헤더 행들 제외)
            data_rows = []
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 8:  # 최소 필요한 셀 수
                    data_rows.append(cells)
            
            home_count = 0
            away_count = 0
            
            # 각 경기 데이터 파싱
            for row_cells in data_rows:
                cell_texts = [cell.get_text(strip=True) for cell in row_cells]
                
                if len(cell_texts) < 8:
                    continue
                
                # 경기 데이터 추출
                game_data = self._extract_game_data_from_row(cell_texts)
                
                if not game_data:
                    continue
                
                # 홈/원정 구분
                home_away = game_data.get('home_away', '')
                
                if home_away == '홈' and home_count < 5:
                    # 홈 경기
                    date_key = f"2025-07-{6+home_count:02d}"  # 임시 날짜
                    games['recent_5_home_games'][date_key] = self._create_game_structure(
                        game_data, True, self.team_name
                    )
                    home_count += 1
                    
                elif home_away == '원정' and away_count < 5:
                    # 원정 경기
                    date_key = f"2025-07-{1+away_count:02d}"  # 임시 날짜  
                    games['recent_5_away_games'][date_key] = self._create_game_structure(
                        game_data, False, self.team_name
                    )
                    away_count += 1
                    
        except Exception as e:
            logger.error(f"최근 경기 테이블 파싱 실패: {e}")
            
        return games
    
    def _extract_game_data_from_row(self, cells: List[str]) -> Dict[str, Any]:
        """행에서 경기 데이터 추출"""
        try:
            if len(cells) < 8:
                return {}
                
            return {
                'date': cells[0],           # 일시
                'home_away': cells[1],      # 홈/원정
                'opponent': cells[2],       # 상대팀
                'result': cells[3],         # 결과
                'goals_scored': self._safe_int(cells[4]),     # 득점
                'goals_conceded': self._safe_int(cells[5]),   # 실점
                'goal_difference': self._safe_int(cells[6]),  # 득실차
                'assists': self._safe_int(cells[7]) if len(cells) > 7 else 0,  # 도움
                'corners': self._safe_int(cells[8]) if len(cells) > 8 else 0,  # 코너킥
                'penalty_kicks': self._safe_int(cells[9]) if len(cells) > 9 else 0,  # 패널티
                'yellow_cards': self._safe_int(cells[13]) if len(cells) > 13 else 0,  # 경고
                'red_cards': self._safe_int(cells[14]) if len(cells) > 14 else 0,  # 퇴장
            }
            
        except Exception as e:
            logger.error(f"경기 데이터 추출 실패: {e}")
            return {}
    
    def _create_game_structure(self, game_data: Dict, is_home: bool, team_name: str) -> Dict[str, Any]:
        """야구 스타일의 경기 구조 생성"""
        try:
            # 결과 변환 (승/무/패 -> W/D/L)
            result_map = {'승': 'W', '무': 'D', '패': 'L'}
            my_result = result_map.get(game_data.get('result', ''), game_data.get('result', ''))
            opponent_result = 'L' if my_result == 'W' else ('W' if my_result == 'L' else 'D')
            
            # 팀 이름 매핑 
            from database.database import connect_supabase, get_team_full_name
            client = connect_supabase()
            if client:
                full_team_name = get_team_full_name(client, team_name, 'SOCCER')
                opponent_full_name = get_team_full_name(client, game_data.get('opponent', ''), 'SOCCER')
            else:
                full_team_name = team_name
                opponent_full_name = game_data.get('opponent', '')
            
            if is_home:
                # 홈 경기
                return {
                    'home': {
                        'result': my_result,
                        'goals_scored': str(game_data.get('goals_scored', 0)),
                        'goals_conceded': str(game_data.get('goals_conceded', 0)),
                        'assists': str(game_data.get('assists', 0)),
                        'corners': str(game_data.get('corners', 0)),
                        'penalty_kicks': str(game_data.get('penalty_kicks', 0)),
                        'yellow_cards': str(game_data.get('yellow_cards', 0)),
                        'red_cards': str(game_data.get('red_cards', 0)),
                        'home_team_name': full_team_name
                    },
                    'away': {
                        'result': opponent_result,
                        'goals_scored': str(game_data.get('goals_conceded', 0)),
                        'goals_conceded': str(game_data.get('goals_scored', 0)),
                        'assists': '0',
                        'corners': '0',
                        'penalty_kicks': '0',
                        'yellow_cards': '0',
                        'red_cards': '0',
                        'away_team_name': opponent_full_name
                    }
                }
            else:
                # 원정 경기
                return {
                    'home': {
                        'result': opponent_result,
                        'goals_scored': str(game_data.get('goals_conceded', 0)),
                        'goals_conceded': str(game_data.get('goals_scored', 0)),
                        'assists': '0',
                        'corners': '0',
                        'penalty_kicks': '0',
                        'yellow_cards': '0',
                        'red_cards': '0',
                        'home_team_name': opponent_full_name
                    },
                    'away': {
                        'result': my_result,
                        'goals_scored': str(game_data.get('goals_scored', 0)),
                        'goals_conceded': str(game_data.get('goals_conceded', 0)),
                        'assists': str(game_data.get('assists', 0)),
                        'corners': str(game_data.get('corners', 0)),
                        'penalty_kicks': str(game_data.get('penalty_kicks', 0)),
                        'yellow_cards': str(game_data.get('yellow_cards', 0)),
                        'red_cards': str(game_data.get('red_cards', 0)),
                        'away_team_name': full_team_name
                    }
                }
                
        except Exception as e:
            logger.error(f"경기 구조 생성 실패: {e}")
            return {}
    
    def _safe_int(self, value: str) -> int:
        """안전한 정수 변환"""
        try:
            return int(value.strip()) if value.strip().isdigit() else 0
        except:
            return 0
