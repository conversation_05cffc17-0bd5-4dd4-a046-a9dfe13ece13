"""
Standard League Stats Parser
For international leagues (J-League, EPL, LaLiga, Serie A, Bundesliga, etc.)
with simpler data structures than K-League
"""

import re
from typing import Any, Dict, List, Optional

from bs4 import BeautifulSoup, Tag

from sports.soccer.parsers.soccer_parser import BaseSoccerParser
from utils.logger import Logger
from sports.base.utils.text_parsing import safe_int, safe_float

logger = Logger(__name__)


class StandardLeagueStatsParser(BaseSoccerParser):
    """
    Standard League 축구 통계 파서
    
    J-League, EPL, LaLiga, Serie A, Bundesliga, Ligue 1, MLS 등
    K-League보다 단순한 데이터 구조를 가진 리그들에 사용
    """
    
    def __init__(self, html_content: str, team_name: str = ""):
        """
        Args:
            html_content: HTML 문자열
            team_name: 팀 이름 (로깅용)
        """
        # 부모 클래스 초기화
        super().__init__(html_content)
        self.team_name = team_name
        
    def get_league_code(self) -> str:
        """리그 코드 반환"""
        return "Standard"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """시즌 요약 컬럼 매핑 반환"""
        return {}
    
    def get_recent_games_mapping(self) -> Dict[str, Dict[str, int]]:
        """최근 경기 컬럼 매핑 반환"""
        return {}
        
    def parse_team_profile(self, team_name: str) -> Dict[str, Any]:
        """팀 프로필 파싱"""
        try:
            # 🎯 팀 이름을 풀네임으로 매핑
            full_team_name = self.map_team_name_to_full_name(team_name)
            
            profile = {
                "team_name": full_team_name,
                "manager": "",
                "stadium": "",
                "season_record": "",
            }
            
            # HTML에서 팀 프로필 정보 추출
            info_box = self.soup.find('div', class_='infoBox')
            if info_box:
                list_items = info_box.find('ul', class_='list')
                if list_items:
                    for item in list_items.find_all('li'):
                        # 감독 정보
                        director_span = item.find('span', id='t_director')
                        if director_span:
                            profile['manager'] = director_span.get_text(strip=True)
                        
                        # 구장 정보  
                        place_span = item.find('span', id='t_place')
                        if place_span:
                            profile['stadium'] = place_span.get_text(strip=True)
                        
                        # 시즌 성적
                        season_span = item.find('span', class_='cgText')
                        if season_span:
                            profile['season_record'] = season_span.get_text(strip=True)
                        
                        # 대안: strong 태그로 라벨 찾기
                        strong_tag = item.find('strong', class_='lb')
                        if strong_tag:
                            label = strong_tag.get_text(strip=True)
                            if '감독' in label:
                                next_span = strong_tag.find_next_sibling()
                                if next_span:
                                    profile['manager'] = next_span.get_text(strip=True)
                            elif '구장' in label:
                                next_span = strong_tag.find_next_sibling()
                                if next_span:
                                    profile['stadium'] = next_span.get_text(strip=True)
                            elif '시즌성적' in label:
                                next_span = strong_tag.find_next_sibling()
                                if next_span:
                                    profile['season_record'] = next_span.get_text(strip=True)
            
            return profile
            
        except Exception as e:
            logger.error(f"❌ [{team_name}] 팀 프로필 파싱 실패: {e}")
            return {
                "team_name": team_name,
                "manager": "",
                "stadium": "",
                "season_record": "",
            }
        
    def parse_season_summary(self) -> Dict[str, Any]:
        """시즌 요약 통계 파싱"""
        summary = {}
        
        try:
            # 시즌 전체 성적 테이블 찾기
            season_table = self.soup.find('tbody', id='homeAway_record')
            if season_table:
                parsed_data = self._parse_season_table(season_table)
                # 2024 시즌 형식으로 변환
                if parsed_data and 'total' in parsed_data:
                    summary['2024'] = parsed_data['total']
                    
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 시즌 요약 파싱 실패: {e}")
            
        return summary
    
    def _parse_season_table(self, table: Tag) -> Dict[str, Any]:
        """시즌 테이블 파싱 - J-League 구조에 맞춤"""
        summary = {}
        
        try:
            rows = table.find_all('tr')
            
            home_data = {}
            away_data = {}
            
            # 첫 번째 행: 홈 데이터 (J-League는 홈 데이터가 대부분 "-")
            if len(rows) >= 1:
                home_cells = rows[0].find_all(['td', 'th'])
                if len(home_cells) >= 10:
                    # 홈 데이터에서 "-" 체크
                    record_text = home_cells[2].get_text(strip=True) if len(home_cells) > 2 else ""
                    if record_text != "-":
                        home_data = {
                            'matches_played': safe_int(home_cells[2].get_text(strip=True)),
                            'wins': safe_int(home_cells[3].get_text(strip=True)),
                            'draws': safe_int(home_cells[4].get_text(strip=True)),
                            'losses': safe_int(home_cells[5].get_text(strip=True)),
                            'points': safe_int(home_cells[6].get_text(strip=True)),
                            'win_rate': safe_float(home_cells[7].get_text(strip=True)),
                            'goals_scored': safe_int(home_cells[8].get_text(strip=True)),
                            'goals_conceded': safe_int(home_cells[11].get_text(strip=True)),
                            'assists': safe_int(home_cells[13].get_text(strip=True)) if len(home_cells) > 13 else 0
                        }
            
            # 두 번째 행: 원정 데이터 (J-League는 원정 데이터가 실제 데이터)
            if len(rows) >= 2:
                away_cells = rows[1].find_all(['td', 'th'])
                if len(away_cells) >= 10:
                    # 원정 데이터는 실제 값이 있음
                    away_data = {
                        'matches_played': safe_int(away_cells[1].get_text(strip=True)),
                        'wins': safe_int(away_cells[2].get_text(strip=True)),
                        'draws': safe_int(away_cells[3].get_text(strip=True)),
                        'losses': safe_int(away_cells[4].get_text(strip=True)),
                        'points': safe_int(away_cells[5].get_text(strip=True)),
                        'win_rate': safe_float(away_cells[6].get_text(strip=True)),
                        'goals_scored': safe_int(away_cells[7].get_text(strip=True)),
                        'goals_conceded': safe_int(away_cells[10].get_text(strip=True)),
                        'assists': safe_int(away_cells[12].get_text(strip=True)) if len(away_cells) > 12 else 0
                    }
            
            # 전체 데이터 계산
            total_data = self._calculate_total_stats(home_data, away_data)
            
            # 순위 정보 (원정 행에서 추출 - J-League는 첫 번째 행이 "-"일 수 있음)
            if len(rows) >= 2:
                away_cells = rows[1].find_all(['td', 'th'])
                if away_cells:
                    total_data['rank'] = safe_int(away_cells[0].get_text(strip=True))
            elif len(rows) >= 1:
                first_cells = rows[0].find_all(['td', 'th'])
                if first_cells:
                    total_data['rank'] = safe_int(first_cells[0].get_text(strip=True))
            
            summary = {
                'home': home_data,
                'away': away_data,
                'total': total_data
            }
            
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 시즌 테이블 파싱 실패: {e}")
            
        return summary
    
    def _calculate_total_stats(self, home_data: Dict, away_data: Dict) -> Dict[str, Any]:
        """홈/원정 데이터를 합쳐서 전체 통계 계산"""
        total = {}
        
        # 기본 통계 합산
        total['matches_played'] = home_data.get('matches_played', 0) + away_data.get('matches_played', 0)
        total['wins'] = home_data.get('wins', 0) + away_data.get('wins', 0)
        total['draws'] = home_data.get('draws', 0) + away_data.get('draws', 0)
        total['losses'] = home_data.get('losses', 0) + away_data.get('losses', 0)
        total['points'] = home_data.get('points', 0) + away_data.get('points', 0)
        total['goals_scored'] = home_data.get('goals_scored', 0) + away_data.get('goals_scored', 0)
        total['goals_conceded'] = home_data.get('goals_conceded', 0) + away_data.get('goals_conceded', 0)
        total['assists'] = home_data.get('assists', 0) + away_data.get('assists', 0)
        
        # 카드 및 파울 데이터 (J-League에서는 0으로 설정)
        total['fouls'] = 0
        total['yellow_cards'] = 0
        total['red_cards'] = 0
        
        # 계산 통계
        total['goal_difference'] = total['goals_scored'] - total['goals_conceded']
        if total['matches_played'] > 0:
            total['win_rate'] = round(total['wins'] / total['matches_played'], 3)
            total['goals_scored_avg'] = round(total['goals_scored'] / total['matches_played'], 2)
            total['goals_conceded_avg'] = round(total['goals_conceded'] / total['matches_played'], 2)
        else:
            total['win_rate'] = 0.0
            total['goals_scored_avg'] = 0.0
            total['goals_conceded_avg'] = 0.0
        
        return total
    
    def parse_recent_games_summary(self) -> Dict[str, Any]:
        """최근 경기 요약 정보 파싱 - Standard League 형식"""
        summary = {}
        
        try:
            # 최근 경기 평균 통계 (seasonLatestTeamRecordTotal)
            total_section = self.soup.find('tfoot', id='seasonLatestTeamRecordTotal')
            if total_section:
                raw_data = self._parse_recent_games_summary_data(total_section)
                
                # Standard League 형식으로 변환
                summary = self._convert_to_standard_format(raw_data)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 최근 경기 요약 파싱 실패: {e}")
             
        # 🎯 통일된 정규화 메서드 사용 (BaseSoccerParser 공통)
        return self.normalize_recent_games_structure(summary)
    
    def _parse_recent_games_summary_data(self, total_section: Tag) -> Dict[str, Any]:
        """최근 경기 평균 통계 파싱"""
        summary = {}
        
        try:
            rows = total_section.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 3:
                    continue
                    
                # 홈/원정 구분
                location = self._safe_get_cell_text(cells, 1) if len(cells) > 1 else ""
                
                if location == "홈":
                    # 홈 행 데이터
                    record = self._safe_get_cell_text(cells, 2)  # colspan="2"인 경우
                    
                    # J-League처럼 모든 데이터가 "-"인 경우 스킵
                    if record == "-" or record == "":
                        continue
                    
                    # 홈 데이터 매핑
                    home_data = {
                        'record': record,
                        'avg_goals_scored': safe_float(self._safe_get_cell_text(cells, 3)) if len(cells) > 3 else 0.0,
                        'avg_goals_conceded': safe_float(self._safe_get_cell_text(cells, 8)) if len(cells) > 8 else 0.0,
                        'avg_assists': safe_float(self._safe_get_cell_text(cells, 10)) if len(cells) > 10 else 0.0,
                        'avg_corners': 0.0,
                        'avg_red_cards': 0.0,
                        'avg_yellow_cards': 0.0
                    }
                    
                    # 유효한 데이터인지 확인
                    if self._is_valid_game_data(home_data):
                        summary['recent_5_home_games'] = home_data
                    
                elif location == "원정":
                    # 원정 행 데이터
                    record = self._safe_get_cell_text(cells, 2)  # colspan="2"인 경우
                    
                    if record == "-" or record == "":
                        continue
                    
                    # 원정 데이터 매핑 (J-League 구조에 맞춤)
                    away_data = {
                        'record': record,
                        'avg_goals_scored': safe_float(self._safe_get_cell_text(cells, 3)) if len(cells) > 3 else 0.0,
                        'avg_goals_conceded': safe_float(self._safe_get_cell_text(cells, 8)) if len(cells) > 8 else 0.0,
                        'avg_assists': safe_float(self._safe_get_cell_text(cells, 10)) if len(cells) > 10 else 0.0,
                        'avg_corners': 0.0,
                        'avg_red_cards': 0.0,
                        'avg_yellow_cards': 0.0
                    }
                    
                    # 유효한 데이터인지 확인
                    if self._is_valid_game_data(away_data):
                        summary['recent_5_away_games'] = away_data
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 최근 경기 요약 파싱 실패: {e}")
             
        return summary
    
    def _convert_to_standard_format(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """원시 데이터를 Standard League 형식으로 변환 (야구와 동일한 구조)"""
        standard_format = {}
        
        # recent_5_games 구조 생성 (야구와 동일)
        recent_5_games = {}
        
        # 홈 게임 데이터 처리
        if 'recent_5_home_games' in raw_data:
            home_section = raw_data['recent_5_home_games']
            # 이미 플랫 구조인 경우 (직접 record, avg_goals_scored 등을 가진 경우)
            if isinstance(home_section, dict) and 'record' in home_section:
                if self._is_valid_game_data(home_section):
                    # 🎯 기록을 영어로 변환
                    from utils.parsers.game_result_parser import map_result
                    home_converted = home_section.copy()
                    if 'record' in home_converted:
                        home_converted['record'] = map_result(home_converted['record'])
                    recent_5_games['last_5_home_games'] = home_converted
                    standard_format['recent_5_home_games'] = home_converted
            # 중첩 구조인 경우 (home 키를 가진 경우)
            elif isinstance(home_section, dict) and 'home' in home_section:
                if self._is_valid_game_data(home_section['home']):
                    from utils.parsers.game_result_parser import map_result
                    home_converted = home_section['home'].copy()
                    if 'record' in home_converted:
                        home_converted['record'] = map_result(home_converted['record'])
                    recent_5_games['last_5_home_games'] = home_converted
                    standard_format['recent_5_home_games'] = home_converted
        
        # 원정 게임 데이터 처리
        if 'recent_5_away_games' in raw_data:
            away_section = raw_data['recent_5_away_games']
            # 이미 플랫 구조인 경우 (직접 record, avg_goals_scored 등을 가진 경우)
            if isinstance(away_section, dict) and 'record' in away_section:
                if self._is_valid_game_data(away_section):
                    # 🎯 기록을 영어로 변환
                    from utils.parsers.game_result_parser import map_result
                    away_converted = away_section.copy()
                    if 'record' in away_converted:
                        away_converted['record'] = map_result(away_converted['record'])
                    recent_5_games['last_5_away_games'] = away_converted
                    standard_format['recent_5_away_games'] = away_converted
            # 중첩 구조인 경우 (away 키를 가진 경우)
            elif isinstance(away_section, dict) and 'away' in away_section:
                if self._is_valid_game_data(away_section['away']):
                    from utils.parsers.game_result_parser import map_result
                    away_converted = away_section['away'].copy()
                    if 'record' in away_converted:
                        away_converted['record'] = map_result(away_converted['record'])
                    recent_5_games['last_5_away_games'] = away_converted
                    standard_format['recent_5_away_games'] = away_converted
        
        # recent_5_games 추가 (야구와 동일한 중첩 구조)
        if recent_5_games:
            standard_format['recent_5_games'] = recent_5_games
        
        return standard_format
    
    def _is_valid_game_data(self, data: Dict[str, Any]) -> bool:
        """게임 데이터가 유효한지 확인"""
        if not isinstance(data, dict):
            return False
        
        # 기록이 '-' 또는 빈 문자열인 경우 무효
        record = data.get('record', '')
        if record in ['-', '', '0승 0무 0패']:
            return False
        
        # 모든 통계가 0인 경우 무효 (단, 정상적인 0-0 경기는 제외)
        goals_scored = data.get('avg_goals_scored', 0)
        goals_conceded = data.get('avg_goals_conceded', 0)
        assists = data.get('avg_assists', 0)
        
        # 기록이 있으면서 모든 통계가 0인 경우만 무효로 처리
        if record != '-' and goals_scored == 0 and goals_conceded == 0 and assists == 0:
            return False
        
        return True
    
    def parse_season_stats(self) -> Dict[str, Any]:
        """시즌 통계 파싱 - SoccerService 호환성"""
        try:
            # parse_season_summary를 활용해서 실제 데이터 반환
            season_data = self.parse_season_summary()
            
            # SoccerStatsParser 형식으로 변환
            if season_data and '2024' in season_data:
                stats = season_data['2024']
                return {
                    'total': stats,
                    'home': {},  # StandardLeague는 홈/원정 분리 없음
                    'away': {}   # StandardLeague는 홈/원정 분리 없음
                }
            else:
                return {'total': {}, 'home': {}, 'away': {}}
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 시즌 통계 파싱 실패: {e}")
            return {'total': {}, 'home': {}, 'away': {}}
    
    def parse_recent_games(self, limit: int = 5) -> Dict[str, Dict]:
        """최근 경기 상세 정보 파싱"""
        recent_games = {
            'recent_5_home_games': {},
            'recent_5_away_games': {}
        }
        
        try:
            # 최근 경기 테이블 찾기
            recent_table = self.soup.find('tbody', id='seasonLatestTeamRecord')
            if recent_table:
                recent_games = self._parse_recent_games_table(recent_table, limit)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 최근 경기 파싱 실패: {e}")
            
        return recent_games
    
    def _parse_recent_games_table(self, table: Tag, limit: int) -> Dict[str, Dict]:
        """최근 경기 테이블 파싱"""
        recent_games = {
            'recent_5_home_games': {},
            'recent_5_away_games': {}
        }
        
        try:
            rows = table.find_all('tr')
            
            # 2행씩 묶어서 처리 (각 매치 = 2행)
            matches_processed = 0
            i = 0
            
            while i < len(rows) - 1 and matches_processed < limit:
                try:
                    # 첫 번째 행 (홈팀 데이터)
                    home_row = rows[i]
                    # 두 번째 행 (원정팀 데이터)
                    away_row = rows[i + 1]
                    
                    home_cells = home_row.find_all(['td', 'th'])
                    away_cells = away_row.find_all(['td', 'th'])
                    
                    if len(home_cells) < 5 or len(away_cells) < 5:
                        i += 2
                        continue
                    
                    # 경기 날짜 (홈팀 행에서 추출)
                    date_text = home_cells[0].get_text(strip=True)
                    
                    # 홈/원정 구분
                    venue = home_cells[1].get_text(strip=True)
                    
                    # 현재 팀의 역할에 따라 데이터 저장
                    if venue == "홈":
                        # 현재 팀이 홈에서 경기
                        match_data = self._extract_match_data(home_cells, away_cells, date_text, True)
                        if match_data:
                            recent_games['recent_5_home_games'][date_text] = match_data
                    else:
                        # 현재 팀이 원정에서 경기
                        match_data = self._extract_match_data(away_cells, home_cells, date_text, False)
                        if match_data:
                            recent_games['recent_5_away_games'][date_text] = match_data
                    
                    matches_processed += 1
                    i += 2
                    
                except Exception as e:
                    logger.debug(f"매치 파싱 실패 (행 {i}): {e}")
                    i += 2
                    continue
                    
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 최근 경기 테이블 파싱 실패: {e}")
            
        return recent_games
    
    def _extract_match_data(self, current_cells: List, opponent_cells: List, date_text: str, is_home: bool) -> Optional[Dict]:
        """매치 데이터 추출"""
        try:
            # 현재 팀 데이터
            current_result = current_cells[3].get_text(strip=True) if len(current_cells) > 3 else ''
            current_goals = safe_int(current_cells[4].get_text(strip=True)) if len(current_cells) > 4 else 0
            current_assists = safe_int(current_cells[11].get_text(strip=True)) if len(current_cells) > 11 else 0
            
            # 상대 팀 데이터
            opponent_result = opponent_cells[2].get_text(strip=True) if len(opponent_cells) > 2 else ''
            opponent_goals = safe_int(opponent_cells[3].get_text(strip=True)) if len(opponent_cells) > 3 else 0
            
            # 팀 이름 추출
            current_team_name = self.map_team_name_to_full_name(self.team_name)
            opponent_team_cell = opponent_cells[2] if len(opponent_cells) > 2 else None
            if opponent_team_cell:
                opponent_link = opponent_team_cell.find('a')
                opponent_name = opponent_link.get_text(strip=True) if opponent_link else opponent_team_cell.get_text(strip=True)
                opponent_team_name = self.map_team_name_to_full_name(opponent_name)
            else:
                opponent_team_name = ""
            
            # 🎯 결과 변환 - 공통 유틸리티 함수 사용
            from utils.parsers.game_result_parser import map_result
            logger.debug(f"🔍 변환 전: current_result='{current_result}', opponent_result='{opponent_result}'")
            current_result = map_result(current_result)
            opponent_result = map_result(opponent_result)
            logger.debug(f"🔍 변환 후: current_result='{current_result}', opponent_result='{opponent_result}'")
            
            if is_home:
                return {
                    'home': {
                        'result': current_result,
                        'goals_scored': current_goals,
                        'goals_conceded': opponent_goals,
                        'assists': current_assists,
                        'home_team_name': current_team_name,
                        'match_date': date_text
                    },
                    'away': {
                        'result': opponent_result,
                        'goals_scored': opponent_goals,
                        'goals_conceded': current_goals,
                        'assists': 0,
                        'away_team_name': opponent_team_name,
                        'match_date': date_text
                    }
                }
            else:
                return {
                    'home': {
                        'result': opponent_result,
                        'goals_scored': opponent_goals,
                        'goals_conceded': current_goals,
                        'assists': 0,
                        'home_team_name': opponent_team_name,
                        'match_date': date_text
                    },
                    'away': {
                        'result': current_result,
                        'goals_scored': current_goals,
                        'goals_conceded': opponent_goals,
                        'assists': current_assists,
                        'away_team_name': current_team_name,
                        'match_date': date_text
                    }
                }
                
        except Exception as e:
            logger.debug(f"매치 데이터 추출 실패: {e}")
            return None
    
    def parse_season_history(self) -> Dict[str, Any]:
        """역대 시즌 성적 파싱"""
        history = {}
        
        try:
            # 역대 시즌 테이블 찾기
            history_table = self.soup.find('tbody', id='history_record')
            if history_table:
                history = self._parse_history_table(history_table)
                
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 역대 시즌 파싱 실패: {e}")
            
        return history
    
    def _parse_history_table(self, table: Tag) -> Dict[str, Any]:
        """역대 시즌 테이블 파싱"""
        history = {}
        
        try:
            rows = table.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 8:
                    continue
                
                season = cells[0].get_text(strip=True)
                if season:
                    history[season] = {
                        'rank': safe_int(cells[1].get_text(strip=True)),
                        'matches_played': safe_int(cells[2].get_text(strip=True)),
                        'wins': safe_int(cells[3].get_text(strip=True)),
                        'draws': safe_int(cells[4].get_text(strip=True)),
                        'losses': safe_int(cells[5].get_text(strip=True)),
                        'points': safe_int(cells[6].get_text(strip=True)),
                        'win_rate': safe_float(cells[7].get_text(strip=True)),
                        'goals_scored': safe_int(cells[8].get_text(strip=True)) if len(cells) > 8 else 0,
                        'goals_conceded': safe_int(cells[10].get_text(strip=True)) if len(cells) > 10 else 0,
                        'assists': safe_int(cells[13].get_text(strip=True)) if len(cells) > 13 else 0,
                        'fouls': 0,
                        'yellow_cards': 0,
                        'red_cards': 0,
                        'goal_difference': 0,
                        'goals_scored_avg': 0.0,
                        'goals_conceded_avg': 0.0
                    }
                    
                    # 계산 통계
                    season_data = history[season]
                    season_data['goal_difference'] = season_data['goals_scored'] - season_data['goals_conceded']
                    if season_data['matches_played'] > 0:
                        season_data['goals_scored_avg'] = round(season_data['goals_scored'] / season_data['matches_played'], 2)
                        season_data['goals_conceded_avg'] = round(season_data['goals_conceded'] / season_data['matches_played'], 2)
                    
        except Exception as e:
            logger.error(f"❌ [{self.team_name}] 역대 시즌 테이블 파싱 실패: {e}")
            
        return history
    
    def _safe_get_cell_text(self, cells: List[Tag], index: int) -> str:
        """셀 텍스트 안전 추출"""
        try:
            return cells[index].get_text(strip=True) if index < len(cells) else ""
        except (IndexError, AttributeError):
            return ""
    
