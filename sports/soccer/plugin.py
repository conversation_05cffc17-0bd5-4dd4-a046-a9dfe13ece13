"""
축구 플러그인 - 기존 시스템과의 통합 인터페이스
기존 야구 시스템과 동일한 인터페이스 제공
"""
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from core.config.sport_config import Sport, get_sport_config
from sports.soccer.collectors.soccer_collector import SoccerCollector
from sports.soccer.parsers.soccer_parser import SoccerParser
from sports.soccer.services.soccer_service import SoccerService
from utils.logger import Logger

logger = Logger(__name__)


@dataclass
class SoccerPlugin:
    """축구 데이터 수집 플러그인"""
    
    def __init__(self):
        self.sport_config = get_sport_config(Sport.SOCCER)
        self.cache = None  # 🚀 캐시 추가
    
    def set_cache(self, cache):
        """캐시 설정"""
        self.cache = cache
    
    async def collect_data(self) -> Dict[str, Any]:
        """축구 데이터 수집 실행"""
        try:
            # 서비스 초기화
            from database.database import (connect_supabase,
                                           get_target_games_by_sport)
            from sports.soccer.services.soccer_service import SoccerService
            
            service = SoccerService()
            
            # 🚀 캐시를 서비스에 주입
            if self.cache:
                service._cache = self.cache
            
            # Supabase 클라이언트 연결
            client = connect_supabase()
            if not client:
                return {
                    'success': False,
                    'message': '데이터베이스 연결 실패',
                    'count': 0,
                    'duration': 0,
                    'errors': ['DB 연결 실패']
                }
            
            # target_games에서 게임 데이터 조회
            games = get_target_games_by_sport('SC')
            
            # 🚀 지원하지 않는 리그 필터링 - K-League 파서 지원 리그들
            supported_league_ids = ['SC001', 'SC003', '52', '67', '53', '56', '54', '2', 'SC007', 'SC017', 'SC018']
            filtered_games = [
                game for game in games 
                if game.get('league_id') in supported_league_ids
            ]
            
            if not filtered_games:
                return {
                    'success': True,
                    'message': '처리할 축구 게임 없음 (지원 리그 필터링 후)',
                    'count': 0,
                    'duration': 0,
                    'errors': []
                }
            
            # logger.info(f"🎯 지원 리그 필터링: {len(games)}개 → {len(filtered_games)}개")
            games = filtered_games
            
            # 팀별 처리
            start_time = time.time()
            total_teams = await service.collect_and_save_team_stats(games)
            duration = time.time() - start_time
            
            return {
                'success': True,
                'message': f"축구 데이터 수집 완료: {total_teams}팀 처리",
                'count': total_teams,
                'duration': duration,
                'errors': [],
                'teams_processed': total_teams,
                'players_processed': 0  # 축구는 현재 선수 통계 비활성화
            }
            
        except Exception as e:
            logger.error(f"축구 플러그인 실행 실패: {e}")
            return {
                'success': False,
                'message': f'축구 데이터 수집 실패: {str(e)}',
                'count': 0,
                'duration': 0,
                'errors': [str(e)],
                'teams_processed': 0,
                'players_processed': 0
            }
    
    async def collect_games(self) -> List[Dict]:
        """경기 데이터만 수집 - UnifiedDataCollector 호환"""
        try:
            return await self.collector.collect_data()
            
        except Exception as e:
            logger.error(f"❌ 축구 경기 수집 실패: {e}")
            return []
    
    async def save_team_stats(self, games: List[Dict]) -> int:
        """팀 통계 저장 - UnifiedDataCollector 호환"""
        return await self.service.save_multiple_team_stats(games)
    
    async def save_player_stats(self, games: List[Dict]) -> List[Dict]:
        """선수 통계 저장 - UnifiedDataCollector 호환"""
        return await self.service.save_multiple_player_stats(games)
    
    async def _get_target_games(self) -> List[Dict]:
        """타겟 게임 조회 - 축구 리그만 필터링"""
        try:
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                return []
            
            # 축구 리그 ID 목록
            soccer_league_ids = [league.id for league in self.sport_config.leagues]
            
            # target_games 테이블에서 축구 경기만 조회
            response = client.table('target_games').select('*').in_(
                'league_id', soccer_league_ids
            ).execute()
            
            if not response.data:
                return []
            
            logger.info(f"📋 축구 타겟 경기: {len(response.data)}개")
            return response.data
            
        except Exception as e:
            logger.error(f"❌ 타겟 게임 조회 실패: {e}")
            return []
    
    async def _process_games_parallel(self, games: List[Dict]) -> List[Dict]:
        """게임 데이터 병렬 처리 - 팀/선수 통계 저장"""
        if not games:
            return []
        
        try:
            import asyncio

            # 팀 통계와 선수 통계를 병렬로 처리
            team_task = asyncio.create_task(
                self.service.collect_and_save_team_stats(games)
            )
            player_task = asyncio.create_task(
                self.service.collect_and_save_player_stats(games)
            )
            
            # 병렬 실행
            team_saved, player_results = await asyncio.gather(
                team_task, player_task, return_exceptions=True
            )
            
            # 결과 처리
            if isinstance(team_saved, Exception):
                logger.error(f"축구 팀 저장 실패: {team_saved}")
                team_count = 0
            else:
                team_count = team_saved
            
            if isinstance(player_results, Exception):
                logger.error(f"축구 선수 저장 실패: {player_results}")
                player_count = 0
            else:
                player_count = len(player_results) if player_results else 0
            
            logger.info(f"📊 축구 통계 저장: 팀 {team_count}개, 선수 {player_count}명")
            
            # 처리된 게임 정보에 통계 추가
            for game in games:
                game['team_stats_saved'] = team_count > 0
                game['player_stats_saved'] = player_count > 0
                game['sport'] = 'soccer'
            
            return games
            
        except Exception as e:
            logger.error(f"❌ 축구 게임 병렬 처리 실패: {e}")
            return []

    async def collect_all_data(self, page=None) -> Dict[str, Any]:
        """팀 통계(및 향후 선수 통계)까지 수집 후 요약 반환 - MultiSportOrchestrator 호환"""
        from database.database import connect_supabase

        summary: Dict[str, Any] = {
            "teams_processed": 0,
            "players_processed": 0,
            "games_processed": 0,
            "errors": [],
        }
        try:
            # 1) 경기 스케줄 수집
            games = await self.collect_games()
            if not games:
                # 수집 대상 경기가 없는 것은 정상 상황이므로 오류로 간주하지 않음
                logger.info("📭 축구 경기가 없어 종료")
                return summary

            summary["games_processed"] = len(games)

            # 2) 팀 통계 저장
            teams_processed = await self.save_team_stats(games)
            summary["teams_processed"] = teams_processed

            # 3) (선택) 선수 통계 저장 – 현재 비활성화지만 호출 구조 유지
            player_results = await self.save_player_stats(games)
            summary["players_processed"] = len(player_results) if player_results else 0

            return summary
        except Exception as exc:  # pylint: disable=broad-except
            logger.error(f"❌ 축구 데이터 수집 실패: {exc}")
            summary["errors"].append(str(exc))
            return summary

    async def collect_league_data(self, page, league: str) -> Dict[str, Any]:
        """🏟️ 리그별 데이터 수집 - 오케스트레이터용"""
        try:
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                raise RuntimeError("DB 연결 실패")

            # 리그별 타겟 게임 필터링  
            target_games = await self._get_target_games_by_league(league)
            
            if not target_games:
                logger.info(f"📭 {league} 리그 수집할 경기가 없습니다")
                return {
                    'teams_processed': 0,
                    'players_processed': 0,
                    'errors': []
                }

            # 축구 컬렉터로 해당 리그 데이터 수집
            collector = SoccerCollector(client, wait_time=3.0)
            league_games = await collector._crawl_league_target_games_by_league(league, target_games)
            
            if not league_games:
                logger.info(f"📭 {league} 리그 매칭된 경기가 없습니다")
                return {
                    'teams_processed': 0,
                    'players_processed': 0,
                    'errors': []
                }

            # 팀 통계 수집 및 저장
            teams_processed = await self.service.collect_and_save_team_stats(league_games)
            players_processed = 0  # 축구는 현재 선수 통계 비활성화
            
            return {
                'teams_processed': teams_processed,
                'players_processed': players_processed,
                'errors': []
            }

        except Exception as e:
            logger.error(f"❌ {league} 리그 데이터 수집 실패: {e}")
            return {
                'teams_processed': 0,
                'players_processed': 0,
                'errors': [str(e)]
            }

    async def _get_target_games_by_league(self, league: str) -> List[Dict]:
        """리그별 타겟 게임 조회"""
        try:
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                return []
            
            # 리그명을 league_id로 변환
            league_id_map = {
                "K리그2": "SC003",
                "J리그": "SC007",
                "MLS": "SC018"
            }
            
            league_id = league_id_map.get(league)
            if not league_id:
                return []
            
            # 해당 리그의 경기만 조회
            response = client.table('target_games').select('*').eq(
                'league_id', league_id
            ).execute()
            
            return response.data or []
            
        except Exception as e:
            logger.error(f"❌ {league} 리그 타겟 게임 조회 실패: {e}")
            return []


# 기존 시스템과의 호환성을 위한 별칭
class SoccerDataCollector(SoccerPlugin):
    """SoccerPlugin의 별칭 - 명명 일관성"""
    pass


# 편의 함수들
async def collect_soccer_data(**kwargs) -> List[Dict]:
    """축구 데이터 수집 편의 함수"""
    plugin = SoccerPlugin()
    return await plugin.collect_data(**kwargs)


async def collect_soccer_games() -> List[Dict]:
    """축구 경기 수집 편의 함수"""
    plugin = SoccerPlugin()
    return await plugin.collect_games()


async def save_soccer_team_stats(games: List[Dict]) -> int:
    """축구 팀 통계 저장 편의 함수"""
    plugin = SoccerPlugin()
    return await plugin.save_team_stats(games)


async def save_soccer_player_stats(games: List[Dict]) -> List[Dict]:
    """축구 선수 통계 저장 편의 함수"""
    plugin = SoccerPlugin()
    return await plugin.save_player_stats(games)


# 팩토리 함수
def create_soccer_collector(team_mappings: Optional[Dict[str, str]] = None) -> SoccerPlugin:
    """축구 수집기 생성 팩토리"""
    return SoccerPlugin(team_mappings)


def get_soccer_leagues() -> List[str]:
    """축구 리그 목록 반환"""
    config = get_sport_config(Sport.SOCCER)
    return [league.id for league in config.leagues]


def get_soccer_league_names() -> Dict[str, str]:
    """축구 리그 ID -> 이름 매핑"""
    config = get_sport_config(Sport.SOCCER)
    return {league.id: league.name for league in config.leagues}


def get_soccer_config() -> Dict[str, Any]:
    """축구 설정 조회 편의 함수"""
    config = get_sport_config(Sport.SOCCER)
    return {
        'name': config.name,
        'enabled': config.enabled,
        'leagues': [
            {
                'id': league.id,
                'name': league.name,
                'enabled': league.enabled
            }
            for league in config.leagues
        ]
    }
