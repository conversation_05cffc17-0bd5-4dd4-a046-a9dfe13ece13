"""
축구 서비스
팀 통계 수집, 저장 및 관리
"""
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional

from core.config.sport_config import Sport, get_sport_config
from sports.soccer.collectors.soccer_collector import SoccerCollector
from sports.soccer.parsers.soccer_parser import <PERSON>Parser
from utils.logger import Logger
from sports.base.utils.text_parsing import safe_int, safe_float

logger = Logger(__name__)


class SoccerService:
    """축구 서비스 - 기존 야구 서비스 패턴 적용"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        self.collector = SoccerCollector(team_mappings)
        self.parser = SoccerParser(team_mappings)
        self.sport_config = get_sport_config(Sport.SOCCER)
        self.team_mappings = team_mappings or {}
    
    async def collect_and_save_team_stats(self, games: List[Dict]) -> int:
        # logger.info(f"🚀 [축구] {len(games)}개 게임의 URL 정보 크롤링 중")
        saved_count = 0
        total_matches = len(games)
        try:
            team_games = self._group_games_by_team(games)
            if not team_games:
                # logger.info(f"✅ [축구] 팀 0개, 경기 {total_matches}개 완료")
                return 0
            team_items = list(team_games.items())
            batch_size = 3
            total_batches = (len(team_items) + batch_size - 1) // batch_size
            for batch_num in range(total_batches):
                start_idx = batch_num * batch_size
                end_idx = min(start_idx + batch_size, len(team_items))
                batch_teams = team_items[start_idx:end_idx]
                for idx, (team_id, team_data) in enumerate(batch_teams):
                    try:
                        team_stats_json = await self._collect_team_stats(team_data)
                        if team_stats_json:
                            if await self._save_team_stats(team_data, team_stats_json):
                                saved_count += 1
                        if idx < len(batch_teams) - 1:
                            await asyncio.sleep(2)
                    except Exception:
                        pass
                if batch_num < total_batches - 1:
                    await asyncio.sleep(5)
        except Exception as e:
            pass
        # logger.info(f"✅ [축구] 팀 {saved_count}개, 경기 {total_matches}개 완료")
        return saved_count
    
    async def collect_and_save_player_stats(self, games: List[Dict]) -> List[Dict]:
        """선수 통계 수집 및 저장 - 현재 비활성화됨"""
        # logger.info("📋 선수 통계 수집은 현재 비활성화되어 있습니다 (팀 통계에 집중)")
        return []
    
    def _group_games_by_team(self, games: List[Dict]) -> Dict[str, Dict]:
        """경기를 팀별로 그룹화"""
        team_games = {}
        
        for game in games:
            # 홈팀 처리
            home_team_id = game.get('home_team_id')
            if home_team_id:
                if home_team_id not in team_games:
                    team_games[home_team_id] = {
                        'team_id': home_team_id,
                        'team_name': game.get('home_team'),
                        # URL 자동 생성 (베트맨 형식)
                        'team_url': (
                            game.get('home_team_url')
                            or self._generate_betman_url(
                                game.get('league_id'), home_team_id
                            )
                        ),
                        'league': game.get('league'),
                        'league_id': game.get('league_id'),
                        'games': []
                    }
                team_games[home_team_id]['games'].append(game)
            
            # 어웨이팀 처리
            away_team_id = game.get('away_team_id')
            if away_team_id:
                if away_team_id not in team_games:
                    team_games[away_team_id] = {
                        'team_id': away_team_id,
                        'team_name': game.get('away_team'),
                        'team_url': (
                            game.get('away_team_url')
                            or self._generate_betman_url(
                                game.get('league_id'), away_team_id
                            )
                        ),
                        'league': game.get('league'),
                        'league_id': game.get('league_id'),
                        'games': []
                    }
                team_games[away_team_id]['games'].append(game)
        
        return team_games
    
    async def _collect_team_stats(self, team_data: Dict) -> Optional[Dict]:
        """개별 팀 통계 수집 - JSON 구조 반환"""
        try:
            team_url = team_data.get('team_url')
            if not team_url:
                from config.config import create_team_url

                # 리그 ID가 없으면 K리그2(SC003) 기본값 사용
                league_id = team_data.get('league_id') or 'SC003'
                team_url = create_team_url('SC', league_id, team_data['team_id'])
                logger.debug(
                    "🔗 %s URL 생성: %s", team_data.get('team_name'), team_url
                )
            
            # 팀 페이지에서 실제 통계 데이터 추출
            team_stats_json = await self._extract_team_stats_from_page(team_url, team_data)

            # 파싱 실패 시 저장하지 않음
            if not team_stats_json:
                logger.warning("❌ %s 파싱 실패 - 저장 스킵", team_data.get('team_name'))
                return None
            
            return team_stats_json
            
        except Exception as e:
            logger.error(f"팀 통계 수집 실패 {team_data.get('team_name')}: {e}")
            return None
    
    async def _extract_team_stats_from_page(self, team_url: str, team_data: Dict) -> Optional[Dict]:
        """팀 페이지에서 통계 데이터 추출 - 야구 방식 적용: 전체/홈/원정 각각 수집"""
        try:
            from playwright.async_api import async_playwright
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                await page.goto(team_url, wait_until="networkidle", timeout=10000)
                # 시즌·최근 경기 테이블이 로드될 때까지 대기 (최대 7초)
                try:
                    await page.wait_for_selector(
                        "tbody#seasonLatestTeamRecord", timeout=7000
                    )
                except Exception:
                    logger.debug("seasonLatestTeamRecord selector timeout – 계속 진행")
                
                # 야구 방식: 전체/홈/원정 각각의 HTML 데이터 수집
                homeaway_data = await self._collect_homeaway_data(page)
                
                await browser.close()
                
                # 수집된 데이터를 파싱하고 합치기
                team_stats_json = self._parse_and_combine_soccer_stats(
                    homeaway_data,
                    team_data,
                )

                # Parsing completed - individual team logging removed for cleaner output
                
                return team_stats_json
                
        except Exception as e:
            logger.error(f"팀 통계 추출 실패 {team_url}: {e}")
            return None
    
    async def _collect_homeaway_data(self, page) -> Dict[str, str]:
        """전체 / 홈 / 원정 HTML을 안정적으로 수집 (테이블 등장까지 대기)"""

        async def _grab(option: str) -> str:
            await self._click_homeaway_button(page, option)
            # 테이블 교체 완료 대기
            try:
                await page.wait_for_selector(
                    "tbody#seasonLatestTeamRecord", timeout=5000
                )
            except Exception:
                logger.debug("selector wait timeout – option %s", option)
            return await page.content()

        try:
            return {
                "total": await _grab("0"),
                "home": await _grab("1"),
                "away": await _grab("2"),
            }
        except Exception as exc:  # noqa: BLE001
            logger.warning("홈/원정 데이터 수집 실패: %s", exc)
            return {"total": await page.content(), "home": "", "away": ""}
    
    async def _click_homeaway_button(self, page, option: str) -> None:
        """
        홈/원정 선택 버튼 클릭 (야구 방식 적용)
        
        Args:
            page: 플레이라이트 페이지
            option: '0'=전체, '1'=홈, '2'=원정
        """
        try:
            # JavaScript 함수 직접 호출 (야구와 동일)
            await page.evaluate(f"searchLatestRecord('homeAway', '{option}')")
            await page.wait_for_timeout(1000)  # 축구는 야구보다 조금 더 대기
        except Exception as e:
            logger.debug(f"홈/원정 버튼 클릭 실패 (option={option}): {e}")
            pass
    
    def _parse_and_combine_soccer_stats(
        self, 
        homeaway_data: Dict[str, str], 
        team_data: Dict
    ) -> Dict[str, Any]:
        """
        야구 방식: 전체/홈/원정 각각의 HTML 데이터를 파싱하여 합치기
        """
        from bs4 import BeautifulSoup
        
        combined_stats = {
            'team_id': team_data.get('team_id'),
            'team_name': team_data.get('team_name'),
            'league': team_data.get('league_id', 'SC003'),
            'season_summary': {},
            'recent_games': {},
            'recent_games_summary': {},
            'season_stats': {},
            'team_profile': {},
            'sports': 'soccer'
        }
        
        # 각 카테고리별로 파싱
        for category, html in homeaway_data.items():
            if not html:
                continue
                
            try:
                soup = BeautifulSoup(html, 'html.parser')
                # 모든 리그가 동일한 HTML 구조를 가지므로 SoccerStatsParser 사용
                from sports.soccer.parsers.soccer_parser import \
                    SoccerStatsParser
                parser = SoccerStatsParser(soup, team_data.get('team_name', ''))
                
                # 시즌 요약은 전체에서만 수집 (야구 방식, 중복 방지)
                if category == 'total':
                    combined_stats['season_summary'] = parser.parse_season_summary()
                    combined_stats['season_stats'] = parser.parse_season_stats()

                    # 팀 프로필 파싱 (SoccerParser 활용)
                    try:
                        from sports.soccer.parsers.soccer_parser import \
                            SoccerParser

                        profile_parser = SoccerParser()
                        profile = profile_parser._parse_team_profile(
                            html,
                            team_data.get('team_name', ''),
                        )  # pylint: disable=protected-access
                        combined_stats['team_profile'] = profile or {}
                    except Exception as profile_err:  # noqa: E722  # 구문 단축용
                        logger.debug("팀 프로필 파싱 실패: %s", profile_err)
                
                # 최근 경기는 야구와 동일한 방식으로 처리 (개별 게임을 날짜 키로 저장)
                recent_games = parser.parse_recent_games()
                if recent_games and category == 'total':  # 전체 버튼에서만 처리
                    # 구조 변환: 홈/원정 split이면 unified로 변환, 이미 unified면 그대로 사용
                    unified_games = self._convert_to_unified_games(recent_games)
                    combined_stats['recent_games'] = unified_games
                
                # 최근 경기 요약은 각 버튼별로 별도 저장
                recent_summary = parser.parse_recent_games_summary()
                if recent_summary:
                    if category != 'total':
                        # 🎯 통일된 정규화 메서드 사용
                        normalized_summary = parser.normalize_recent_games_structure(recent_summary)
                        summary_dict = combined_stats['recent_games_summary']
                        
                        if category == 'home' and 'recent_5_home_games' in normalized_summary:
                            # Convert Korean record to English format (like baseball)
                            home_data = normalized_summary['recent_5_home_games'].copy()
                            if 'record' in home_data:
                                home_data['record'] = self._convert_korean_to_english_record(home_data['record'])
                            summary_dict['recent_5_home_games'] = home_data
                        elif category == 'away' and 'recent_5_away_games' in normalized_summary:
                            # Convert Korean record to English format (like baseball)
                            away_data = normalized_summary['recent_5_away_games'].copy()
                            if 'record' in away_data:
                                away_data['record'] = self._convert_korean_to_english_record(away_data['record'])
                            summary_dict['recent_5_away_games'] = away_data
                    else:
                        # 전체 버튼: 최근 경기 요약 → recent_{n}_games (야구 방식)
                        # 하위 키는 last_{n}_home_games, last_{n}_away_games
                        if 'home' in recent_summary and 'away' in recent_summary:
                            home_data = recent_summary['home']
                            away_data = recent_summary['away']
                            
                            home_n = self._extract_games_from_record(
                                home_data.get('record', '')
                            )
                            away_n = self._extract_games_from_record(
                                away_data.get('record', '')
                            )
                            
                            if home_n > 0 or away_n > 0:
                                total_summary = {}
                                if home_n > 0:
                                    total_summary[f'last_{home_n}_home_games'] = home_data
                                if away_n > 0:
                                    total_summary[f'last_{away_n}_away_games'] = away_data
                                
                                # 전체 경기 수 (home + away)
                                n_games = home_n + away_n
                                if n_games > 0:
                                    key_name = f"recent_{n_games}_games"
                                    combined_stats['recent_games_summary'][key_name] = total_summary
                
            except Exception as e:
                logger.warning(f"❌ {team_data.get('team_name')} {category} 파싱 실패: {e}")
                continue
        
        # 🚀 최종 JSON 구조로 변환하여 반환
        return self._create_final_soccer_stats_json(combined_stats, team_data)
    
    def _extract_games_from_record(self, record_str: str) -> int:
        """'0승 0무 2패' → 2경기"""
        import re
        nums = list(map(int, re.findall(r"(\d+)", record_str)))
        return sum(nums) if nums else 5  # 기본값 5
    
    def _convert_korean_to_english_record(self, korean_record: str) -> str:
        """Convert Korean record '2승0무3패' to English '2W 0D 3L'"""
        import re
        if not korean_record:
            return "0W 0D 0L"
        
        try:
            # Extract numbers and Korean characters
            pattern = r'(\d+)([승무패])'
            matches = re.findall(pattern, korean_record)
            
            wins = draws = losses = 0
            
            for num, char in matches:
                count = int(num)
                if char == '승':
                    wins = count
                elif char == '무':
                    draws = count
                elif char == '패':
                    losses = count
            
            return f"{wins}W {draws}D {losses}L"
            
        except Exception:
            return "0W 0D 0L"
    
    def _create_final_soccer_stats_json(
        self, 
        combined_stats: Dict, 
        team_data: Dict
    ) -> Dict:
        """축구 최종 JSON 구조 생성"""
        try:
            # 기본 구조 설정
            season_summary = combined_stats.get('season_summary', {})
            season_stats = combined_stats.get('season_stats', {})
            recent_games = combined_stats.get('recent_games', {})
            recent_games_summary = combined_stats.get('recent_games_summary', {})
            team_profile = combined_stats.get('team_profile', {})
            
            # 파싱된 데이터가 없는 경우 기본값 생성
            if not season_summary and not season_stats and not recent_games:
                logger.warning(f"❌ {team_data.get('team_name')} 파싱 데이터 없음, 기본값 생성")
                return self._create_default_soccer_stats_json(team_data)
            
            # 야구와 동일한 구조로 변환
            final_json = {
                "team_profile": team_profile,
                "season_stats": season_stats,
                "season_summary": season_summary,
                "recent_games": recent_games,
                "recent_games_summary": recent_games_summary,
                "soccer_specific_stats": {
                    "attacking_stats": {},
                    "defensive_stats": {},
                    "discipline_stats": {}
                }
            }
            
            # Final JSON generation completed - individual team logging removed
            return final_json
            
        except Exception as e:
            logger.error(f"축구 최종 JSON 생성 실패: {e}")
            # 기본 구조 반환
            return self._create_default_soccer_stats_json(team_data)
    
    def _create_soccer_team_stats_json(
        self, 
        team_data: Dict, 
        home_data: Dict, 
        away_data: Dict, 
        total_data: Dict, 
        recent_games_data: Dict
    ) -> Dict:
        """축구 팀 통계 JSON 구조 생성 (테스트된 구조)"""
        from datetime import datetime
        
        return {
            "season_stats": {
                "2025": {
                    "league_position": total_data.get('league_position', 0),
                    "matches_played": total_data.get('matches_played', 0),
                    "wins": total_data.get('wins', 0),
                    "draws": total_data.get('draws', 0),
                    "losses": total_data.get('losses', 0),
                    "points": total_data.get('points', 0),
                    "win_rate": total_data.get('win_rate', 0.0),
                    "goals_scored": total_data.get('goals_scored', 0),
                    "goals_conceded": total_data.get('goals_conceded', 0),
                    "goal_difference": total_data.get('goal_difference', 0),
                    "clean_sheets": total_data.get('clean_sheets', 0),
                    "assists": total_data.get('assists', 0),
                    "corners": total_data.get('corners', 0),
                    "yellow_cards": total_data.get('yellow_cards', 0),
                    "red_cards": total_data.get('red_cards', 0),
                    "fouls_committed": total_data.get('fouls_committed', 0),
                    "offsides": total_data.get('offsides', 0),
                    "league_code": team_data.get('league_id', 'SC003')
                }
            },
            
            "season_summary": {
                "home": home_data,
                "away": away_data,
                "total": total_data,
                "latest_season_secondary_stats": {
                    "year": "2025",
                    "goals_per_game": round(total_data.get('goals_scored', 0) / max(total_data.get('matches_played', 1), 1), 2),
                    "goals_conceded_per_game": round(total_data.get('goals_conceded', 0) / max(total_data.get('matches_played', 1), 1), 2),
                    "win_percentage": total_data.get('win_rate', 0.0),
                    "goal_difference": total_data.get('goal_difference', 0),
                    "points_per_game": round(total_data.get('points', 0) / max(total_data.get('matches_played', 1), 1), 2),
                    "clean_sheet_rate": round(total_data.get('clean_sheets', 0) / max(total_data.get('matches_played', 1), 1), 2),
                    "cards_per_game": round((total_data.get('yellow_cards', 0) + total_data.get('red_cards', 0)) / max(total_data.get('matches_played', 1), 1), 2)
                }
            },
            
            "recent_games": recent_games_data,
            
            "recent_games_summary": self._calculate_recent_games_summary(recent_games_data),
            
            "soccer_specific_stats": {
                "attacking_stats": {
                    "conversion_rate": round((total_data.get('goals_scored', 0) / max(total_data.get('corners', 1), 1)) * 100, 1),
                    "assists_per_goal": round(total_data.get('assists', 0) / max(total_data.get('goals_scored', 1), 1), 2)
                },
                "defensive_stats": {
                    "clean_sheet_percentage": round((total_data.get('clean_sheets', 0) / max(total_data.get('matches_played', 1), 1)) * 100, 1),
                    "goals_conceded_per_game": round(total_data.get('goals_conceded', 0) / max(total_data.get('matches_played', 1), 1), 2)
                },
                "discipline_stats": {
                    "total_cards": total_data.get('yellow_cards', 0) + total_data.get('red_cards', 0),
                    "cards_per_game": round((total_data.get('yellow_cards', 0) + total_data.get('red_cards', 0)) / max(total_data.get('matches_played', 1), 1), 2),
                    "fair_play_index": round(100 - (total_data.get('fouls_committed', 0) / max(total_data.get('matches_played', 1), 1)), 1)
                }
            }
        }
    
    async def _crawl_team_page(self, team_url: str) -> Optional[str]:
        """팀 페이지 크롤링"""
        try:
            from playwright.async_api import async_playwright
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                await page.goto(team_url, wait_until='networkidle')
                await asyncio.sleep(2)
                
                html = await page.content()
                await browser.close()
                
                return html
                
        except Exception as e:
            logger.error(f"팀 페이지 크롤링 실패 {team_url}: {e}")
            return None
    
    # 선수 수집 로직은 추후 구현 예정 (현재 팀 통계에 집중)
    
    
    async def _save_team_stats(self, team_data: Dict, team_stats_json: Dict) -> bool:
        """팀 통계 데이터베이스 저장 - 크롤링된 실제 데이터 저장"""
        try:
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                return False

            saved_count = 0
            current_team_id = team_data.get('team_id')
            
            # 현재 처리 중인 팀의 통계만 저장
            for game in team_data.get('games', []):
                # 현재 팀이 홈인지 어웨이인지 결정
                if game.get('home_team_id') == current_team_id:
                    team_role = 'home'
                elif game.get('away_team_id') == current_team_id:
                    team_role = 'away'
                else:
                    logger.warning(f"팀 {current_team_id}이 경기에 없음: {game}")
                    continue

                # 팀 레코드 생성 (크롤링된 실제 데이터 사용)
                record = self._create_team_record(game, team_stats_json, team_role, team_data)
                if not record:
                    logger.warning(f"레코드 생성 실패: {team_role} 팀")
                    continue

                match_id = record.get('match_id')
                team_id = record.get('team_id')
                match_date = record.get('match_date')

                # 중복 체크: 이미 저장된 데이터가 있으면 스킵
                existing_check = client.table('soccer_stats').select('id').eq(
                    'match_id', match_id
                ).eq('team_id', team_id).eq(
                    'match_date', match_date
                ).execute()
                
                if existing_check.data:
                    continue

                # 데이터베이스에 저장
                response = client.table('soccer_stats').upsert(record).execute()
                if response.data:
                    saved_count += 1

            if saved_count:
                pass  # Team stats saved successfully - individual save logging removed
            else:
                pass  # Team stats already exist - individual skip logging removed

            return True  # 저장했든, 이미 있었든 성공으로 간주
            
        except Exception as e:
            logger.error(f"팀 통계 저장 실패: {e}")
            return False
    
    def _create_team_record(
        self, 
        game: Dict, 
        team_stats_json: Dict, 
        team_role: str, 
        team_data: Dict
    ) -> Optional[Dict]:
        """팀 레코드 생성 (홈/어웨이) - 야구와 동일한 패턴 사용"""
        try:
            from database.database import connect_supabase
            client = connect_supabase()
            if not client:
                logger.error("Supabase 연결 실패")
                return None
            # 팀 역할에 따른 팀 정보 결정
            if team_role == 'home':
                team_id = game.get('home_team_id')
            else:
                team_id = game.get('away_team_id')
            # team_info에서 sportic_team_id, team_name 조회
            team_info = self._get_team_info(client, team_id)
            sportic_team_id = team_info.get('sportic_team_id')
            team_name = team_info.get('team_full_name')
            # team_info mapping completed - detailed logging removed for cleaner output
            if not sportic_team_id or not team_name:
                logger.error(
                    f"필수값 없음: sportic_team_id={sportic_team_id}, team_name={team_name}, team_id={team_id}"
                )
                return None
            match_date = game.get('match_date', '2025-07-06')
            match_time = game.get('match_time', '19:00:00')
            # target_games에서 축구 경기 조회 (축구용 쿼리)
            target_data = self._get_target_game(
                client, team_id, match_date, match_time
            )
            if not target_data['found_in_target_games']:
                logger.warning(f"target_games에서 경기 정보 없음: {team_id}, {match_date}, {match_time}")
                return None
            match_id = target_data['match_id']
            game_type = target_data.get('game_type', '')
            if game_type:
                pass  # game_type information available but not logged individually
            record = {
                "id": f"{match_id}_{team_id}",
                "match_id": match_id,
                "sports": "soccer",
                "sportic_team_id": sportic_team_id,
                "match_date": match_date,
                "match_time": match_time,
                "league": game.get('league', 'K리그2'),
                "game_type": game_type,
                "team_role": team_role,
                "team_name": team_name,
                "team_id": team_id,
                "team_profile": team_stats_json.get('team_profile', {}),
                "season_stats": team_stats_json.get('season_stats', {}),
                "season_summary": team_stats_json.get('season_summary', {}),
                "recent_games": team_stats_json.get('recent_games', {}),
                "recent_games_summary": team_stats_json.get('recent_games_summary', {}),
                "created_at": None,
                "updated_at": None
            }
            return record
        except Exception as e:
            logger.error(f"팀 레코드 생성 실패 ({team_role}): {e}")
            return None
    
    async def save_multiple_team_stats(
        self, 
        games: List[Dict], 
        client=None
    ) -> int:
        """다중 팀 통계 저장 (기존 야구 서비스 호환)"""
        return await self.collect_and_save_team_stats(games)
    
    async def save_multiple_player_stats(self, games: List[Dict]) -> List[Dict]:
        """다중 선수 통계 저장 (현재 비활성화됨)"""
        logger.info("📋 선수 통계 저장은 현재 비활성화되어 있습니다")
        return []

    # 선수 통계 저장 로직은 추후 구현 예정

    def _get_league_name(self, league_id: str) -> str:
        """리그 ID로 리그 이름 가져오기"""
        league_names = {
            'SC001': 'K리그1',
            'SC003': 'K리그2',
            '52': 'EPL',
            '53': '프리메라리가',
            '54': '세리에A',
            '55': '분데스리가',
            '56': '프랑스리그',
            '57': '에레디비시',
            'SC007': 'J리그'
        }
        return league_names.get(league_id, 'Unknown')

    def _get_team_name(self, team_id: str) -> str:
        """팀 ID로 팀 이름 가져오기 (매핑 테이블 활용)"""
        # 실제로는 데이터베이스나 매핑 테이블에서 가져와야 함
        return self.team_mappings.get(team_id, f'Team_{team_id}')

    async def _parse_season_stats_table(self, table):
        """시즌 통계 테이블 파싱 (테이블 1)"""
        try:
            rows = await table.query_selector_all('tbody tr')
            
            home_data = {}
            away_data = {}
            
            if len(rows) >= 2:
                # 홈 데이터 파싱 (Playwright MCP 문서 기반 정확한 매핑)
                # Documentation: docs/soccer_html_structure_guide.md
                # Column Mapping from Playwright MCP:
                # 0:league_position 1:home_away 2:matches_played 3:wins 4:draws 5:losses 6:points 7:win_rate 
                # 8:goals_scored 9:goals_scored_avg 10:shooting_rate 11:goals_conceded 12:goals_conceded_avg 
                # 13:assists 14:goal_kicks 15:corners 16:penalties 17:offsides 18:fouls_committed 19:yellow_cards 20:red_cards
                home_cells = await rows[0].query_selector_all('td')
                if len(home_cells) >= 20:
                    home_data = {
                        "matches_played": safe_int(await home_cells[2].text_content()),  # Column 2
                        "wins": safe_int(await home_cells[3].text_content()),           # Column 3
                        "draws": safe_int(await home_cells[4].text_content()),          # Column 4
                        "losses": safe_int(await home_cells[5].text_content()),         # Column 5
                        "points": safe_int(await home_cells[6].text_content()),         # Column 6
                        "win_rate": safe_float(await home_cells[7].text_content()),     # Column 7
                        "goals_scored": safe_int(await home_cells[8].text_content()),   # Column 8
                        "goals_conceded": safe_int(await home_cells[11].text_content()), # Column 11
                        "assists": safe_int(await home_cells[13].text_content()),       # Column 13
                        "corners": safe_int(await home_cells[15].text_content()),       # Column 15
                        "yellow_cards": safe_int(await home_cells[19].text_content()),  # Column 19
                        "red_cards": safe_int(await home_cells[20].text_content()),     # Column 20
                        "fouls_committed": safe_int(await home_cells[18].text_content()), # Column 18
                        "offsides": safe_int(await home_cells[17].text_content()),       # Column 17
                        "clean_sheets": 0  # 계산 필요
                    }
                
                # 어웨이 데이터 파싱 (Playwright MCP 문서 기반 정확한 매핑)
                # Away row has different structure - no league position column
                # Away mapping: 0:home_away 1:matches_played 2:wins 3:draws 4:losses 5:points 6:win_rate 
                # 7:goals_scored 8:goals_scored_avg 9:shooting_rate 10:goals_conceded 11:goals_conceded_avg 
                # 12:assists 13:goal_kicks 14:corners 15:penalties 16:offsides 17:fouls_committed 18:yellow_cards 19:red_cards
                away_cells = await rows[1].query_selector_all('td')
                if len(away_cells) >= 19:
                    away_data = {
                        "matches_played": safe_int(await away_cells[1].text_content()),  # Column 1
                        "wins": safe_int(await away_cells[2].text_content()),           # Column 2
                        "draws": safe_int(await away_cells[3].text_content()),          # Column 3
                        "losses": safe_int(await away_cells[4].text_content()),         # Column 4
                        "points": safe_int(await away_cells[5].text_content()),         # Column 5
                        "win_rate": safe_float(await away_cells[6].text_content()),     # Column 6
                        "goals_scored": safe_int(await away_cells[7].text_content()),   # Column 7
                        "goals_conceded": safe_int(await away_cells[10].text_content()), # Column 10
                        "assists": safe_int(await away_cells[12].text_content()),       # Column 12
                        "corners": safe_int(await away_cells[14].text_content()),       # Column 14
                        "yellow_cards": safe_int(await away_cells[18].text_content()),  # Column 18
                        "red_cards": safe_int(await away_cells[19].text_content()),     # Column 19
                        "fouls_committed": safe_int(await away_cells[17].text_content()), # Column 17
                        "offsides": safe_int(await away_cells[16].text_content()),       # Column 16
                        "clean_sheets": 0
                    }
            
            # 전체 통계 계산
            league_position = 0
            if rows:
                first_row_cells = await rows[0].query_selector_all('td')
                if first_row_cells:
                    league_position = safe_int(await first_row_cells[0].text_content())
            
            total_data = {
                "league_position": league_position,
                "matches_played": home_data.get('matches_played', 0) + away_data.get('matches_played', 0),
                "wins": home_data.get('wins', 0) + away_data.get('wins', 0),
                "draws": home_data.get('draws', 0) + away_data.get('draws', 0),
                "losses": home_data.get('losses', 0) + away_data.get('losses', 0),
                "points": home_data.get('points', 0) + away_data.get('points', 0),
                "goals_scored": home_data.get('goals_scored', 0) + away_data.get('goals_scored', 0),
                "goals_conceded": home_data.get('goals_conceded', 0) + away_data.get('goals_conceded', 0),
                "assists": home_data.get('assists', 0) + away_data.get('assists', 0),
                "corners": home_data.get('corners', 0) + away_data.get('corners', 0),
                "yellow_cards": home_data.get('yellow_cards', 0) + away_data.get('yellow_cards', 0),
                "red_cards": home_data.get('red_cards', 0) + away_data.get('red_cards', 0),
                "fouls_committed": home_data.get('fouls_committed', 0) + away_data.get('fouls_committed', 0),
                "clean_sheets": 0,  # 계산 필요
                "offsides": 0  # 기본값
            }
            
            # 골 차이와 승률 계산
            total_data['goal_difference'] = total_data['goals_scored'] - total_data['goals_conceded']
            if total_data['matches_played'] > 0:
                total_data['win_rate'] = round(total_data['wins'] / total_data['matches_played'], 3)
            else:
                total_data['win_rate'] = 0.0
            
            return home_data, away_data, total_data
            
        except Exception as e:
            logger.error(f"시즌 통계 테이블 파싱 실패: {e}")
            return {}, {}, {}
    
    async def _parse_recent_matches_table(self, table, team_data=None):
        """최근 경기 테이블 파싱 - 야구와 동일한 방식으로 2행 구조 파싱"""
        try:
            rows = await table.query_selector_all('tbody tr')
            
            recent_games_data = {
                "recent_5_home_games": {},
                "recent_5_away_games": {}
            }
            
            from datetime import datetime, timedelta
            base_date = datetime.now()  # 현재 날짜로 수정
            
            home_games = []
            away_games = []
            
            # 2행씩 묶어서 처리 (각 매치 = 2행, 야구와 동일)
            i = 0
            while i < len(rows) - 1:
                try:
                    # 첫 번째 행 (홈팀 데이터, 날짜 포함)
                    home_row_cells = await rows[i].query_selector_all('td')
                    # 두 번째 행 (어웨이팀 데이터, 날짜 없음)  
                    away_row_cells = await rows[i + 1].query_selector_all('td')
                    
                    if len(home_row_cells) < 6 or len(away_row_cells) < 6:
                        i += 2
                        continue
                    
                    # 홈팀 행 데이터 추출 (18개 셀)
                    home_data = []
                    for cell in home_row_cells:
                        text = await cell.text_content()
                        home_data.append(text.strip() if text else '')
                    
                    # 어웨이팀 행 데이터 추출 (17개 셀)
                    away_data = []
                    for cell in away_row_cells:
                        text = await cell.text_content()
                        away_data.append(text.strip() if text else '')
                    
                    # 매치 날짜 (홈팀 행 첫 번째 셀)
                    match_date = home_data[0] if home_data else ''
                    
                    # 홈팀 데이터 파싱 (18 cells: 날짜, 홈/원정, 팀명, 결과, 득점, 실점, 득실차, 도움, 코너킥...)
                    from utils.parsers.game_result_parser import map_result
                    from database.database import get_team_full_name, connect_supabase
                    
                    home_team_name = home_data[2] if len(home_data) > 2 else ''
                    client = connect_supabase()
                    home_team_full_name = get_team_full_name(client, home_team_name, 'SOCCER') if client else home_team_name
                    home_team_dict = {
                        "team_name": home_team_full_name,  # 풀네임 사용
                        "result": map_result(home_data[3]) if len(home_data) > 3 else '',  # WDL 결과
                        "goals_scored": home_data[4] if len(home_data) > 4 else '0',  # 득점
                        "goals_conceded": home_data[5] if len(home_data) > 5 else '0',  # 실점
                        "assists": home_data[7] if len(home_data) > 7 else '0',  # 도움
                        "corners": home_data[8] if len(home_data) > 8 else '0',  # 코너킥
                        "yellow_cards": home_data[12] if len(home_data) > 12 else '0',  # 경고
                        "red_cards": home_data[13] if len(home_data) > 13 else '0',  # 퇴장
                        "clean_sheet": "1" if (len(home_data) > 5 and safe_int(home_data[5]) == 0) else "0",
                        "match_date": match_date
                    }
                    
                    # 어웨이팀 데이터 파싱 (17 cells: 홈/원정, 팀명, 결과, 득점, 실점, 득실차, 도움, 코너킥...)
                    away_team_name = away_data[1] if len(away_data) > 1 else ''
                    away_team_full_name = get_team_full_name(client, away_team_name, 'SOCCER') if client else away_team_name
                    away_team_dict = {
                        "team_name": away_team_full_name,  # 풀네임 사용
                        "result": map_result(away_data[2]) if len(away_data) > 2 else '',  # WDL 결과
                        "goals_scored": away_data[3] if len(away_data) > 3 else '0',  # 득점
                        "goals_conceded": away_data[4] if len(away_data) > 4 else '0',  # 실점
                        "assists": away_data[6] if len(away_data) > 6 else '0',  # 도움
                        "corners": away_data[7] if len(away_data) > 7 else '0',  # 코너킥
                        "yellow_cards": away_data[11] if len(away_data) > 11 else '0',  # 경고
                        "red_cards": away_data[12] if len(away_data) > 12 else '0',  # 퇴장
                        "clean_sheet": "1" if (len(away_data) > 4 and safe_int(away_data[4]) == 0) else "0",
                        "match_date": match_date
                    }
                    
                    # 현재 팀 정보 가져오기 (풀네임 사용)
                    current_team_name = team_data.get('team_name', 'Current Team') if team_data else 'Current Team'
                    current_team_full_name = get_team_full_name(client, current_team_name, 'SOCCER') if client else current_team_name
                    
                    # 홈팀 행의 홈/원정 표시로 현재 팀의 역할 판단
                    venue_indicator = home_data[1] if len(home_data) > 1 else ''
                    
                    if venue_indicator == '홈':
                        # 현재 팀이 홈에서 경기 - 야구 방식과 동일하게 구성
                        current_team_dict = {
                            "home_team_name": current_team_full_name,  # 현재 팀 (홈) - 풀네임
                            "result": home_team_dict["result"],
                            "goals_scored": home_team_dict["goals_scored"],
                            "goals_conceded": home_team_dict["goals_conceded"],
                            "assists": home_team_dict["assists"],
                            "corners": home_team_dict["corners"],
                            "yellow_cards": home_team_dict["yellow_cards"],
                            "red_cards": home_team_dict["red_cards"],
                            "clean_sheet": home_team_dict["clean_sheet"],
                            "match_date": match_date
                        }
                        
                        opponent_team_dict = {
                            "away_team_name": away_team_dict["team_name"],  # 상대팀 (어웨이)
                            "result": away_team_dict["result"],
                            "goals_scored": away_team_dict["goals_scored"],
                            "goals_conceded": away_team_dict["goals_conceded"],
                            "assists": away_team_dict["assists"],
                            "corners": away_team_dict["corners"],
                            "yellow_cards": away_team_dict["yellow_cards"],
                            "red_cards": away_team_dict["red_cards"],
                            "clean_sheet": away_team_dict["clean_sheet"],
                            "match_date": match_date
                        }
                        
                        match_dict = {
                            "home": current_team_dict,
                            "away": opponent_team_dict,
                            "date": match_date
                        }
                        home_games.append(match_dict)
                        
                    elif venue_indicator == '원정':
                        # 현재 팀이 어웨이에서 경기 - 야구 방식과 동일하게 구성
                        opponent_team_dict = {
                            "home_team_name": home_team_dict["team_name"],  # 상대팀 (홈)
                            "result": home_team_dict["result"],
                            "goals_scored": home_team_dict["goals_scored"],
                            "goals_conceded": home_team_dict["goals_conceded"],
                            "assists": home_team_dict["assists"],
                            "corners": home_team_dict["corners"],
                            "yellow_cards": home_team_dict["yellow_cards"],
                            "red_cards": home_team_dict["red_cards"],
                            "clean_sheet": home_team_dict["clean_sheet"],
                            "match_date": match_date
                        }
                        
                        current_team_dict = {
                            "away_team_name": current_team_full_name,  # 현재 팀 (어웨이) - 풀네임
                            "result": away_team_dict["result"],
                            "goals_scored": away_team_dict["goals_scored"],
                            "goals_conceded": away_team_dict["goals_conceded"],
                            "assists": away_team_dict["assists"],
                            "corners": away_team_dict["corners"],
                            "yellow_cards": away_team_dict["yellow_cards"],
                            "red_cards": away_team_dict["red_cards"],
                            "clean_sheet": away_team_dict["clean_sheet"],
                            "match_date": match_date
                        }
                        
                        match_dict = {
                            "home": opponent_team_dict,
                            "away": current_team_dict,
                            "date": match_date
                        }
                        away_games.append(match_dict)
                    
                except Exception as e:
                    logger.warning(f"매치 파싱 실패 (행 {i}): {e}")
                
                i += 2
            
            # 최근 5경기로 제한하고 날짜 키로 구조화
            for i, game in enumerate(home_games[:5]):
                date_key = (base_date - timedelta(days=i)).strftime('%Y-%m-%d')
                recent_games_data["recent_5_home_games"][date_key] = {
                    "home": game["home"],
                    "away": game["away"]
                }
            
            for i, game in enumerate(away_games[:5]):
                date_key = (base_date - timedelta(days=i+5)).strftime('%Y-%m-%d')
                recent_games_data["recent_5_away_games"][date_key] = {
                    "home": game["home"], 
                    "away": game["away"]
                }
            
            return recent_games_data
            
        except Exception as e:
            logger.error(f"최근 경기 테이블 파싱 실패: {e}")
            return {"recent_5_home_games": {}, "recent_5_away_games": {}}
    
    def _calculate_recent_games_summary(self, recent_games_data):
        """최근 경기 요약 계산 - 야구와 동일한 구조로 생성 (home/away 분리 + total)"""
        try:
            # 야구와 동일: recent_games_data에서 날짜별 개별 게임들을 직접 저장
            summary = {}
            
            if isinstance(recent_games_data, dict):
                # 개별 게임들이 날짜 키로 저장된 경우 (야구와 동일한 구조)
                game_count = len(recent_games_data)
                if game_count > 0:
                    # 야구와 동일: recent_N_games 키로 저장
                    summary[f"recent_{game_count}_games"] = recent_games_data
            
            return summary
            
        except Exception as e:
            logger.error(f"최근 경기 요약 계산 실패: {e}")
            return {}
    
    def _convert_to_unified_games(self, recent_games_data):
        """홈/원정 split 구조를 unified 구조로 변환하거나 이미 unified면 그대로 반환"""
        # 이미 unified 구조인지 확인 (날짜 키들이 있고 recent_5_home_games, recent_5_away_games가 없으면)
        if (isinstance(recent_games_data, dict) and 
            'recent_5_home_games' not in recent_games_data and 
            'recent_5_away_games' not in recent_games_data):
            # 이미 unified 구조
            return recent_games_data
        
        # 홈/원정 split 구조를 unified로 변환
        unified_games = {}
        
        try:
            home_games = recent_games_data.get('recent_5_home_games', {})
            away_games = recent_games_data.get('recent_5_away_games', {})
            
            # 홈 게임들 추가
            for date, game_data in home_games.items():
                if isinstance(game_data, dict):
                    unified_games[date] = game_data
            
            # 원정 게임들 추가 (중복 방지)
            for date, game_data in away_games.items():
                if isinstance(game_data, dict) and date not in unified_games:
                    unified_games[date] = game_data
                    
        except Exception as e:
            logger.debug(f"게임 구조 변환 실패: {e}")
            return {}
        
        return unified_games
    
    def _calculate_game_stats(self, games):
        """게임 리스트에서 통계 계산"""
        if not games:
            return {
                'wins': 0, 'draws': 0, 'losses': 0,
                'avg_goals_scored': 0, 'avg_goals_conceded': 0,
                'avg_assists': 0, 'avg_corners': 0, 'clean_sheet_rate': 0
            }
        
        wins = sum(1 for game in games if game.get('result') == 'W')
        draws = sum(1 for game in games if game.get('result') == 'D')
        losses = sum(1 for game in games if game.get('result') == 'L')
        
        total_goals_scored = sum(int(game.get('goals_scored', 0)) for game in games)
        total_goals_conceded = sum(int(game.get('goals_conceded', 0)) for game in games)
        total_assists = sum(int(game.get('assists', 0)) for game in games)
        total_corners = sum(int(game.get('corners', 0)) for game in games)
        clean_sheets = sum(1 for game in games if game.get('clean_sheet') == '1')
        
        game_count = len(games)
        
        return {
            'wins': wins,
            'draws': draws,
            'losses': losses,
            'avg_goals_scored': total_goals_scored / game_count,
            'avg_goals_conceded': total_goals_conceded / game_count,
            'avg_assists': total_assists / game_count,
            'avg_corners': total_corners / game_count,
            'clean_sheet_rate': clean_sheets / game_count
        }
    
    
    def _get_target_game(self, client, team_id: str, match_date: str, match_time: str) -> dict:
        """축구 target_games에서 데이터 조회 (야구 함수의 축구 버전)"""
        try:
            # 축구 경기 조회 - 야구와 동일한 패턴이지만 sports='soccer'
            response = client.table('target_games').select(
                'match_id, sports, league, league_id, game_type, '
                'home_team_id, away_team_id, home_team, away_team'
            ).eq('match_date', match_date).eq(
                'match_time', match_time  
            ).eq('game_type', 'W').eq(
                'sports', 'soccer'  # 축구로 변경
            ).or_(
                f'home_team_id.eq.{team_id},away_team_id.eq.{team_id}'
            ).limit(1).execute()
            
            if response.data:
                game = response.data[0]
                
                # 팀 역할 결정 (홈/어웨이)
                team_role = 'home' if game['home_team_id'] == team_id else 'away'
                
                return {
                    'found_in_target_games': True,
                    'match_id': game['match_id'],
                    'sports': game['sports'],
                    'league': game['league'],
                    'league_id': game['league_id'],
                    'game_type': game['game_type'],  # 🚀 게임 타입 추가
                    'team_role': team_role,
                    'home_team_id': game['home_team_id'],
                    'away_team_id': game['away_team_id'],
                    'home_team': game['home_team'],
                    'away_team': game['away_team']
                }
            else:
                return {'found_in_target_games': False}
                
        except Exception as e:
            logger.error(f"축구 target_games 조회 실패: {e}")
            return {'found_in_target_games': False}

    def _get_team_info(self, client, team_id: str) -> dict:
        """team_info에서 team_id, sports='soccer'로 row 조회 (야구와 동일)"""
        try:
            response = (
                client.table('team_info')
                .select('sportic_team_id, team_full_name')
                .eq('team_id', team_id)
                .eq('sports', 'soccer')
                .execute()
            )
            if response.data and len(response.data) > 0:
                return response.data[0]
            else:
                logger.error(
                    f"team_info에 team_id={team_id}, sports=soccer 없음"
                )
                return {}
        except Exception as e:
            logger.error(
                f"team_info 조회 실패: {e}"
            )
            return {}

    def _generate_betman_url(self, league_id: str, team_id: str) -> str:
        """리그 ID와 팀 ID로 베트맨 팀 페이지 URL 생성"""
        from config.config import create_team_url

        league_id = league_id or 'SC003'  # 기본 K리그2
        return create_team_url('SC', league_id, team_id)

    def _create_default_team_stats(self, team_data: Dict) -> Dict:
        """URL이 없는 팀에 대한 기본 팀 통계 구조 생성"""
        team_name = team_data.get('team_name', '')
        return {
            'team_name': team_name,
            'league': team_data.get('league', ''),
            'sports': 'soccer',
            'seasons_summary': {
                'total': {'record': '데이터 없음', 'goals_for': '0', 'goals_against': '0'},
                'home': {'record': '데이터 없음', 'goals_for': '0', 'goals_against': '0'},
                'away': {'record': '데이터 없음', 'goals_for': '0', 'goals_against': '0'}
            },
            'recent_games': {},
            'recent_games_summary': {},
            'season_stats': {},
            'collection_time': datetime.now().isoformat()
        }
    
    def _create_default_soccer_stats_json(self, team_data: Dict) -> Dict:
        """파싱 실패 시 기본 축구 통계 JSON 구조 생성"""
        team_name = team_data.get('team_name', '')
        return {
            "season_stats": {
                "2025": {
                    "matches_played": 0,
                    "wins": 0,
                    "draws": 0,
                    "losses": 0,
                    "points": 0,
                    "goals_scored": 0,
                    "goals_conceded": 0,
                    "goal_difference": 0,
                    "data_source": "default"
                }
            },
            "season_summary": {
                "total": {
                    "matches_played": 0,
                    "wins": 0,
                    "draws": 0,
                    "losses": 0,
                    "points": 0,
                    "data_source": "default"
                }
            },
            "recent_games": {
                "recent_5_games": []
            },
            "recent_games_summary": {
                "recent_5_games": {
                    "home": {"record": "데이터 없음"},
                    "away": {"record": "데이터 없음"}
                }
            },
            "soccer_specific_stats": {
                "attacking_stats": {},
                "defensive_stats": {},
                "discipline_stats": {}
            }
        }
