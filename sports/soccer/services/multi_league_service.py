"""
Multi-League Soccer Service
8개 리그 지원: K리그2, EPL, 프리메라리가, 세리에A, 분데스리가, 프랑스리그, 에레디비시, J리그
simple_soccer_extractor.py에서 검증된 로직을 실제 서비스에 적용
"""
import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from sports.soccer.parsers.k_league_stats_parser import KLeagueStatsParser
from sports.soccer.parsers.standard_league_stats_parser import StandardLeagueStatsParser
from sports.soccer.parsers.soccer_parser import SoccerParser
from sports.soccer.services.soccer_service import SoccerService
from utils.logger import Logger

try:
    from playwright.async_api import async_playwright, Page
except ImportError:
    print("❌ Playwright required: pip install playwright")
    raise

logger = Logger(__name__)


@dataclass
class LeagueConfig:
    """리그 설정 정보"""
    name: str
    code: str
    country: str
    league_id: str
    team_id: str
    sample_url: str
    parser_type: str = "standard"  # "k_league" for Korean leagues, "standard_league" for international leagues


class MultiLeagueSoccerService(SoccerService):
    """다중 리그 축구 서비스 - 기존 SoccerService 확장"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        super().__init__(team_mappings)
        self.supported_leagues = self._load_supported_leagues()
        
    def _load_supported_leagues(self) -> List[LeagueConfig]:
        """지원 리그 설정 로드"""
        return [
            LeagueConfig(
                name="K리그2", code="KR2", country="한국",
                league_id="SC003", team_id="K20",
                sample_url="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=SC003&teamId=K20",
                parser_type="k_league"
            ),
            LeagueConfig(
                name="EPL", code="EPL", country="잉글랜드",
                league_id="52", team_id="4088",
                sample_url="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=52&teamId=4088",
                parser_type="standard_league"
            ),
            LeagueConfig(
                name="프리메라리가", code="ESP", country="스페인",
                league_id="67", team_id="100009",
                sample_url="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=67&teamId=100009",
                parser_type="standard_league"
            ),
            LeagueConfig(
                name="세리에A", code="ITA", country="이탈리아",
                league_id="53", team_id="100133",
                sample_url="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=53&teamId=100133",
                parser_type="standard_league"
            ),
            LeagueConfig(
                name="분데스리가", code="GER", country="독일",
                league_id="56", team_id="100133",  # 올바른 league_id 사용
                sample_url="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=56&teamId=100133",
                parser_type="standard_league"
            ),
            LeagueConfig(
                name="프랑스리그", code="FRA", country="프랑스",
                league_id="54", team_id="4094",
                sample_url="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=54&teamId=4094",
                parser_type="standard_league"
            ),
            LeagueConfig(
                name="에레디비시", code="NED", country="네덜란드",
                league_id="2", team_id="7",
                sample_url="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=2&teamId=7",
                parser_type="standard_league"
            ),
            LeagueConfig(
                name="J리그", code="JPN", country="일본",
                league_id="SC007", team_id="296",
                sample_url="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=SC007&teamId=296",
                parser_type="standard_league"
            ),
            LeagueConfig(
                name="J2리그", code="J2", country="일본",
                league_id="SC017", team_id="296",
                sample_url="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=SC017&teamId=296",
                parser_type="standard_league"
            ),
            LeagueConfig(
                name="MLS", code="MLS", country="미국",
                league_id="SC018", team_id="4088",
                sample_url="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=SC018&teamId=4088",
                parser_type="standard_league"
            )
        ]
    
    async def extract_all_leagues_data(self) -> Dict:
        """모든 지원 리그에서 데이터 추출"""
        logger.info(f"🌍 Multi-League Soccer Data Extraction 시작")
        logger.info(f"🎯 대상 리그: {len(self.supported_leagues)}개")
        
        results = {}
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            
            try:
                for i, league in enumerate(self.supported_leagues, 1):
                    logger.info(f"📊 [{i}/{len(self.supported_leagues)}] {league.name} 처리 중...")
                    
                    league_data = await self._extract_league_data(browser, league)
                    results[league.code] = league_data
                    
                    logger.info(f"✅ {league.name} 완료")
                    
                    # 리그 간 딜레이
                    if i < len(self.supported_leagues):
                        await asyncio.sleep(2)
                        
            finally:
                await browser.close()
        
        return self._compile_extraction_results(results)
    
    async def _extract_league_data(self, browser, league: LeagueConfig) -> Dict:
        """개별 리그 데이터 추출 - 검증된 로직 적용"""
        page = await browser.new_page()
        
        try:
            # 팀 데이터 추출
            team_data = await self._extract_enhanced_team_data(page, league)
            
            # 선수 데이터 추출
            players_data = await self._extract_enhanced_players_data(page, league)
            
            return {
                'league_info': {
                    'name': league.name,
                    'code': league.code,
                    'country': league.country,
                    'league_id': league.league_id,
                    'sample_team_id': league.team_id,
                    'sample_url': league.sample_url,
                    'parser_type': league.parser_type
                },
                'team_data': team_data,
                'players_data': players_data,
                'extraction_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"{league.name} 데이터 추출 실패: {e}")
            return {
                'league_info': {
                    'name': league.name,
                    'code': league.code,
                    'error': str(e)
                },
                'extraction_timestamp': datetime.now().isoformat()
            }
        finally:
            await page.close()
    
    async def _extract_enhanced_team_data(self, page: Page, league: LeagueConfig) -> Dict:
        """향상된 팀 데이터 추출 - simple_soccer_extractor 로직 적용"""
        try:
            # 페이지 로드
            await page.goto(league.sample_url, timeout=30000)
            await page.wait_for_load_state('networkidle')
            await page.wait_for_timeout(3000)
            
            # HTML 가져오기
            html_content = await page.content()
            
            # 리그 타입에 따른 파서 선택
            if league.parser_type == "k_league":
                parser = KLeagueStatsParser(html_content)
                season_stats = parser.parse_season_summary()
                recent_games = parser.parse_recent_games(limit=5)
                season_history = parser.parse_season_history()
            elif league.parser_type == "standard_league":
                parser = StandardLeagueStatsParser(html_content, league.name)
                season_stats = parser.parse_season_summary()
                recent_games = parser.parse_recent_games(limit=5)
                season_history = parser.parse_season_history()
            else:
                parser = SoccerParser()
                team_data = parser.parse_team_data(
                    html_content,
                    league.name,
                    league=league.name,
                    league_id=league.league_id
                )
                season_stats = team_data.season_stats
                recent_games = team_data.recent_games
                season_history = {}
            
            # 팀 기본 정보 추출
            team_info = await page.evaluate("""
                () => {
                    const info = {};
                    
                    // 팀명
                    const teamSelectors = ['h1', 'h2', '.team-name', '.teamName'];
                    for (const selector of teamSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            info.team_name = element.textContent.trim();
                            break;
                        }
                    }
                    
                    // 순위 정보
                    const rankSelectors = ['.rank', '.ranking', '.position'];
                    for (const selector of rankSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            info.current_rank = element.textContent.trim();
                            break;
                        }
                    }
                    
                    // 추가 정보
                    const infoElements = document.querySelectorAll('li, .info-item, dt, dd');
                    infoElements.forEach(element => {
                        const text = element.textContent.trim();
                        if (text.includes('감독')) {
                            info.manager = text.replace(/감독|매니저|코치/g, '').trim();
                        }
                        if (text.includes('구장') || text.includes('스타디움')) {
                            info.stadium = text.replace(/구장|스타디움/g, '').trim();
                        }
                    });
                    
                    return info;
                }
            """)
            
            # 통계 요약
            stats_count = 0
            if isinstance(season_stats, dict):
                stats_count = len([s for s in season_stats.values() if s])
            
            games_count = len(recent_games) if recent_games else 0
            history_count = len(season_history) if season_history else 0
            
            # 팀 프로필 정보 포맷팅
            team_profile = self._format_team_profile(team_info, season_stats)
            
            # 최근 경기 요약 정보 추가
            recent_games_summary = {}
            if league.parser_type == "standard_league":
                recent_games_summary = parser.parse_recent_games_summary()
            
            return {
                'team_profile': team_profile,
                'season_stats': season_stats,
                'recent_games': recent_games,
                'recent_games_summary': recent_games_summary,
                'season_history': season_history,
                'stats_summary': {
                    'season_stats_count': stats_count,
                    'recent_games_count': games_count,
                    'history_count': history_count
                }
            }
            
        except Exception as e:
            logger.error(f"{league.name} 팀 데이터 추출 실패: {e}")
            return {'error': str(e)}
    
    def _format_team_profile(self, team_info: Dict, season_stats: Dict) -> Dict:
        """팀 프로필 정보 포맷팅"""
        profile = {
            'team_name': team_info.get('team_name', ''),
            'manager': team_info.get('manager', ''),
            'stadium': team_info.get('stadium', ''),
            'season_record': ''
        }
        
        # season_record 포맷팅: "9승5무8패" (순위 정보 제거)
        if season_stats and '2024' in season_stats:
            stats = season_stats['2024']
            wins = stats.get('wins', 0)
            draws = stats.get('draws', 0)
            losses = stats.get('losses', 0)
            
            if wins > 0 or draws > 0 or losses > 0:
                profile['season_record'] = f"{wins}승{draws}무{losses}패"
        
        return profile
    
    async def _extract_enhanced_players_data(self, page: Page, league: LeagueConfig) -> List[Dict]:
        """향상된 선수 데이터 추출"""
        try:
            # 선수 목록 추출
            players_data = await page.evaluate("""
                () => {
                    const players = [];
                    const seen = new Set();
                    
                    // 선수 링크에서 추출
                    document.querySelectorAll('a[href*="Player"], a[href*="player"]').forEach(link => {
                        const name = link.textContent.trim();
                        if (name && name.length > 1 && name.length < 30 && !seen.has(name)) {
                            seen.add(name);
                            players.push({
                                name: name,
                                url: link.href,
                                source: 'link'
                            });
                        }
                    });
                    
                    // 테이블에서 선수명 추출
                    document.querySelectorAll('table tr').forEach(row => {
                        const cells = Array.from(row.querySelectorAll('td, th'));
                        if (cells.length >= 2) {
                            const possibleName = cells[0].textContent.trim() || cells[1].textContent.trim();
                            
                            if (possibleName && 
                                possibleName.length > 1 && 
                                possibleName.length < 30 &&
                                /^[가-힣a-zA-Z\\s\\-\\.\']+$/.test(possibleName) &&
                                !possibleName.includes('순위') &&
                                !possibleName.includes('팀명') &&
                                !possibleName.includes('경기') &&
                                !seen.has(possibleName)) {
                                
                                seen.add(possibleName);
                                players.push({
                                    name: possibleName,
                                    position: cells[2] ? cells[2].textContent.trim() : 'Unknown',
                                    number: cells.length > 3 ? cells[0].textContent.trim() : 'N/A',
                                    source: 'table'
                                });
                            }
                        }
                    });
                    
                    return players.slice(0, 30); // 최대 30명까지만
                }
            """)
            
            # 중복 제거
            unique_players = []
            seen_names = set()
            
            for player in players_data:
                name = player.get('name', '').strip()
                if name and name not in seen_names:
                    seen_names.add(name)
                    unique_players.append({
                        'name': name,
                        'position': player.get('position', 'Unknown'),
                        'number': player.get('number', 'N/A'),
                        'url': player.get('url', ''),
                        'source': player.get('source', 'unknown'),
                        'league': league.name,
                        'league_code': league.code
                    })
            
            return unique_players
            
        except Exception as e:
            logger.error(f"{league.name} 선수 데이터 추출 실패: {e}")
            return []
    
    def _compile_extraction_results(self, results: Dict) -> Dict:
        """추출 결과 컴파일"""
        total_teams = 0
        total_players = 0
        total_stats = 0
        total_games = 0
        successful_leagues = 0
        
        for league_code, data in results.items():
            if 'error' not in data.get('league_info', {}):
                successful_leagues += 1
                
                if 'team_data' in data:
                    total_teams += 1
                    team_stats = data['team_data'].get('stats_summary', {})
                    total_stats += team_stats.get('season_stats_count', 0)
                    total_games += team_stats.get('recent_games_count', 0)
                
                if 'players_data' in data:
                    total_players += len(data['players_data'])
        
        return {
            'extraction_info': {
                'timestamp': datetime.now().isoformat(),
                'total_leagues': len(self.supported_leagues),
                'successful_leagues': successful_leagues,
                'total_teams': total_teams,
                'total_players': total_players,
                'total_stats': total_stats,
                'total_games': total_games
            },
            'league_results': results,
            'summary': {
                'success_rate': f"{successful_leagues}/{len(self.supported_leagues)} ({successful_leagues/len(self.supported_leagues)*100:.1f}%)",
                'data_points': {
                    'teams': total_teams,
                    'players': total_players,
                    'stats': total_stats,
                    'games': total_games
                }
            }
        }
    
    async def save_multi_league_results(self, results: Dict, filename: str = None) -> str:
        """다중 리그 결과 저장"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'multi_league_results_{timestamp}.json'
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 Multi-league results saved: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"결과 저장 실패: {e}")
            return ""
    
    def get_supported_leagues(self) -> List[Dict]:
        """지원 리그 목록 반환"""
        return [
            {
                'name': league.name,
                'code': league.code,
                'country': league.country,
                'league_id': league.league_id,
                'parser_type': league.parser_type
            }
            for league in self.supported_leagues
        ]