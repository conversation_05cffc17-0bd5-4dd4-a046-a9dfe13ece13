"""
농구 데이터 파서
축구 파서 패턴을 농구에 적용
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from bs4 import BeautifulSoup, Tag

from utils.logger import Logger

logger = Logger(__name__)


@dataclass
class BasketballData:
    """농구 데이터 구조"""
    team_name: str
    league: str
    league_id: str
    profile: Dict[str, Any]
    season_stats: Dict[str, Any]
    recent_games: List[Dict[str, Any]]
    career_stats: Dict[str, Any]


class BaseBasketballParser(ABC):
    """농구 파서 기본 클래스 - 축구 패턴 적용"""
    
    def __init__(self, html: str, team_mappings: Optional[Dict[str, str]] = None):
        self.soup = BeautifulSoup(html, "html.parser")
        self.team_mappings = team_mappings or {}
        self.league_code = self.get_league_code()
    
    @abstractmethod
    def get_league_code(self) -> str:
        """리그 코드 반환"""
        pass
    
    @abstractmethod
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        """시즌 요약 컬럼 매핑 반환"""
        pass
    
    @abstractmethod
    def get_recent_games_mapping(self) -> Dict[str, int]:
        """최근 경기 컬럼 매핑 반환"""
        pass
    
    def parse_season_summary(self) -> Dict[str, Optional[Dict[str, str]]]:
        """시즌 전체 성적 파싱"""
        season_stats = {
            "home": None, 
            "away": None, 
            "total": None
        }
        
        mapping = self.get_season_summary_mapping()
        
        # 홈 통계 파싱
        home_dict = self._parse_season_row("home_record", mapping["home"])
        if home_dict:
            home_dict["league_code"] = self.league_code
            season_stats["home"] = home_dict
        
        # 원정 통계 파싱
        away_dict = self._parse_season_row("away_record", mapping["away"])
        if away_dict:
            away_dict["league_code"] = self.league_code
            season_stats["away"] = away_dict
        
        # 전체 통계 파싱
        total_dict = self._parse_season_row("all_record", mapping["total"])
        if total_dict:
            total_dict["league_code"] = self.league_code
            season_stats["total"] = total_dict
        
        return season_stats
    
    def parse_recent_games(self, limit: int = 5) -> List[Dict[str, str]]:
        """최근 경기 파싱"""
        try:
            games = []
            mapping = self.get_recent_games_mapping()
            
            # 최근 경기 테이블 찾기
            recent_table = self._find_recent_games_table()
            if not recent_table:
                return games
            
            rows = recent_table.find_all('tr')[1:]  # 헤더 제외
            
            for row in rows[:limit]:
                cells = row.find_all(['td', 'th'])
                if len(cells) < len(mapping):
                    continue
                
                game_data = {}
                for field, index in mapping.items():
                    if index < len(cells):
                        game_data[field] = cells[index].get_text(strip=True)
                
                if game_data:
                    game_data["league_code"] = self.league_code
                    games.append(game_data)
            
            return games
            
        except Exception as e:
            logger.debug(f"최근 경기 파싱 실패: {e}")
            return []
    
    def _parse_season_row(self, row_id: str, mapping: Dict[str, int]) -> Optional[Dict[str, str]]:
        """시즌 통계 행 파싱"""
        try:
            row = self.soup.find('tr', id=row_id)
            if not row:
                return None
            
            cells = row.find_all(['td', 'th'])
            if len(cells) < max(mapping.values()) + 1:
                return None
            
            result = {}
            for field, index in mapping.items():
                if index < len(cells):
                    result[field] = cells[index].get_text(strip=True)
            
            return result if result else None
            
        except Exception as e:
            logger.debug(f"시즌 행 파싱 실패 {row_id}: {e}")
            return None
    
    def _find_recent_games_table(self) -> Optional[Tag]:
        """최근 경기 테이블 찾기"""
        # 여러 패턴으로 테이블 찾기
        selectors = [
            'table.recent_games',
            'table.game_list',
            'div.recent_games table',
            'div.game_history table'
        ]
        
        for selector in selectors:
            table = self.soup.select_one(selector)
            if table:
                return table
        
        return None


class KBLParser(BaseBasketballParser):
    """KBL/WKBL 파서"""
    
    def get_league_code(self) -> str:
        return "KBL"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {
                "games": 0, "wins": 1, "losses": 2, "win_rate": 3,
                "points_for": 4, "points_against": 5, "point_diff": 6
            },
            "away": {
                "games": 0, "wins": 1, "losses": 2, "win_rate": 3,
                "points_for": 4, "points_against": 5, "point_diff": 6
            },
            "total": {
                "games": 0, "wins": 1, "losses": 2, "win_rate": 3,
                "points_for": 4, "points_against": 5, "point_diff": 6
            }
        }
    
    def get_recent_games_mapping(self) -> Dict[str, int]:
        return {
            "date": 0,
            "opponent": 1,
            "home_away": 2,
            "result": 3,
            "score": 4,
            "points_for": 5,
            "points_against": 6
        }


class NBAParser(BaseBasketballParser):
    """NBA 파서"""
    
    def get_league_code(self) -> str:
        return "NBA"
    
    def get_season_summary_mapping(self) -> Dict[str, Dict[str, int]]:
        return {
            "home": {
                "games": 0, "wins": 1, "losses": 2, "win_percentage": 3,
                "points_per_game": 4, "opponent_points": 5, "point_differential": 6
            },
            "away": {
                "games": 0, "wins": 1, "losses": 2, "win_percentage": 3,
                "points_per_game": 4, "opponent_points": 5, "point_differential": 6
            },
            "total": {
                "games": 0, "wins": 1, "losses": 2, "win_percentage": 3,
                "points_per_game": 4, "opponent_points": 5, "point_differential": 6
            }
        }
    
    def get_recent_games_mapping(self) -> Dict[str, int]:
        return {
            "date": 0,
            "opponent": 1,
            "venue": 2,
            "result": 3,
            "score": 4,
            "field_goal_pct": 5,
            "three_point_pct": 6,
            "free_throw_pct": 7
        }


class BasketballParser:
    """농구 통합 파서 - 리그별 파서 선택"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        self.team_mappings = team_mappings or {}
        self.league_parsers = {
            'BK001': KBLParser,    # KBL
            'BK003': KBLParser,    # WKBL (KBL 패턴 재사용)
            'BK002': NBAParser,    # NBA
        }
    
    def parse_team_data(self, html: str, team_name: str, **kwargs) -> BasketballData:
        """팀 데이터 파싱"""
        try:
            league_id = kwargs.get('league_id', 'BK001')
            league = kwargs.get('league', 'KBL')
            
            # 리그별 파서 선택
            parser_class = self.league_parsers.get(league_id, KBLParser)
            parser = parser_class(html, self.team_mappings)
            
            # 프로필 파싱
            profile = self._parse_team_profile(html, team_name)
            
            # 시즌 통계 파싱
            season_stats = parser.parse_season_summary()
            
            # 최근 경기 파싱
            recent_games = parser.parse_recent_games()
            
            # 커리어 통계 파싱 (농구는 시즌 통계와 동일)
            career_stats = season_stats.copy()
            
            return BasketballData(
                team_name=team_name,
                league=league,
                league_id=league_id,
                profile=profile,
                season_stats=season_stats,
                recent_games=recent_games,
                career_stats=career_stats
            )
            
        except Exception as e:
            logger.error(f"농구 팀 데이터 파싱 실패 {team_name}: {e}")
            return BasketballData(
                team_name=team_name,
                league=kwargs.get('league', ''),
                league_id=kwargs.get('league_id', ''),
                profile={},
                season_stats={},
                recent_games=[],
                career_stats={}
            )
    
    def _parse_team_profile(self, html: str, team_name: str) -> Dict[str, Any]:
        """팀 프로필 파싱"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            profile = {
                'team_name': team_name,
                'founded': '',
                'arena': '',
                'coach': '',
                'conference': '',
                'division': '',
                'last_updated': ''
            }
            
            # 프로필 정보 추출 (사이트별 구조에 따라 조정 필요)
            profile_section = soup.find('div', class_='team_profile')
            if profile_section:
                # 설립연도
                founded_elem = profile_section.find('span', string='설립')
                if founded_elem and founded_elem.next_sibling:
                    profile['founded'] = founded_elem.next_sibling.strip()
                
                # 홈구장/아레나
                arena_elem = profile_section.find('span', string='홈구장')
                if arena_elem and arena_elem.next_sibling:
                    profile['arena'] = arena_elem.next_sibling.strip()
                
                # 감독/코치
                coach_elem = profile_section.find('span', string='감독')
                if coach_elem and coach_elem.next_sibling:
                    profile['coach'] = coach_elem.next_sibling.strip()
                
                # 컨퍼런스 (NBA용)
                conference_elem = profile_section.find('span', string='컨퍼런스')
                if conference_elem and conference_elem.next_sibling:
                    profile['conference'] = conference_elem.next_sibling.strip()
            
            return profile
            
        except Exception as e:
            logger.debug(f"팀 프로필 파싱 실패 {team_name}: {e}")
            return {'team_name': team_name}
