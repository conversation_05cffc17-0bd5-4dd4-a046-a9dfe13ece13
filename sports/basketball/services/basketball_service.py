"""
농구 서비스
팀 통계 수집, 저장 및 관리 - 축구 서비스 패턴 적용
"""
import asyncio
from dataclasses import asdict
from typing import Any, Dict, List, Optional

from core.config.sport_config import Sport, get_sport_config
from sports.basketball.collectors.basketball_collector import \
    BasketballCollector
from sports.basketball.parsers.basketball_parser import (BasketballData,
                                                         BasketballParser)
from utils.logger import Logger

logger = Logger(__name__)


class BasketballService:
    """농구 서비스 - 축구 서비스 패턴 적용"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        self.collector = BasketballCollector(team_mappings)
        self.parser = BasketballParser(team_mappings)
        self.sport_config = get_sport_config(Sport.BASKETBALL)
        self.team_mappings = team_mappings or {}
    
    async def collect_and_save_team_stats(self, games: List[Dict]) -> int:
        """팀 통계 수집 및 저장"""
        logger.info(f"🚀 농구 팀 통계 수집 시작 - {len(games)}개 경기")
        
        saved_count = 0
        
        try:
            # 팀별로 그룹화
            team_games = self._group_games_by_team(games)
            
            # 각 팀별로 통계 수집
            for team_id, team_data in team_games.items():
                try:
                    stats = await self._collect_team_stats(team_data)
                    if stats:
                        # 데이터베이스에 저장
                        if await self._save_team_stats(stats):
                            saved_count += 1
                            # Team stats saved successfully - individual logging removed
                        else:
                            logger.warning(f"⚠️ {stats.team_name} 통계 저장 실패")
                    
                except Exception as e:
                    logger.error(f"❌ {team_id} 팀 통계 처리 실패: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"❌ 농구 팀 통계 수집 실패: {e}")
        
        logger.info(f"🎯 농구 팀 통계 수집 완료: {saved_count}개 저장")
        return saved_count
    
    async def collect_and_save_player_stats(self, games: List[Dict]) -> List[Dict]:
        """선수 통계 수집 및 저장"""
        logger.info(f"🚀 농구 선수 통계 수집 시작 - {len(games)}개 경기")
        
        saved_players = []
        
        try:
            # 팀별로 선수 통계 수집
            for game in games:
                try:
                    # 홈팀 선수 통계
                    if game.get('home_team_id') and game.get('home_team_url'):
                        home_players = await self._collect_team_players(
                            game['home_team_id'], 
                            game['home_team_url'],
                            game['league_id']
                        )
                        saved_players.extend(home_players)
                    
                    # 어웨이팀 선수 통계
                    if game.get('away_team_id') and game.get('away_team_url'):
                        away_players = await self._collect_team_players(
                            game['away_team_id'], 
                            game['away_team_url'],
                            game['league_id']
                        )
                        saved_players.extend(away_players)
                    
                except Exception as e:
                    logger.error(f"❌ 경기 선수 통계 처리 실패: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"❌ 농구 선수 통계 수집 실패: {e}")
        
        logger.info(f"🎯 농구 선수 통계 수집 완료: {len(saved_players)}명")
        return saved_players
    
    def _group_games_by_team(self, games: List[Dict]) -> Dict[str, Dict]:
        """경기를 팀별로 그룹화"""
        team_games = {}
        
        for game in games:
            # 홈팀 처리
            home_team_id = game.get('home_team_id')
            if home_team_id:
                if home_team_id not in team_games:
                    team_games[home_team_id] = {
                        'team_id': home_team_id,
                        'team_name': game.get('home_team'),
                        'team_url': game.get('home_team_url'),
                        'league': game.get('league'),
                        'league_id': game.get('league_id'),
                        'games': []
                    }
                team_games[home_team_id]['games'].append(game)
            
            # 어웨이팀 처리
            away_team_id = game.get('away_team_id')
            if away_team_id:
                if away_team_id not in team_games:
                    team_games[away_team_id] = {
                        'team_id': away_team_id,
                        'team_name': game.get('away_team'),
                        'team_url': game.get('away_team_url'),
                        'league': game.get('league'),
                        'league_id': game.get('league_id'),
                        'games': []
                    }
                team_games[away_team_id]['games'].append(game)
        
        return team_games
    
    async def _collect_team_stats(self, team_data: Dict) -> Optional[BasketballData]:
        """개별 팀 통계 수집"""
        try:
            team_url = team_data.get('team_url')
            if not team_url:
                return None
            
            # 팀 페이지 크롤링
            html = await self._crawl_team_page(team_url)
            if not html:
                return None
            
            # 데이터 파싱
            stats = self.parser.parse_team_data(
                html,
                team_data['team_name'],
                league=team_data['league'],
                league_id=team_data['league_id']
            )
            
            return stats
            
        except Exception as e:
            logger.error(f"팀 통계 수집 실패 {team_data.get('team_name')}: {e}")
            return None
    
    async def _crawl_team_page(self, team_url: str) -> Optional[str]:
        """팀 페이지 크롤링"""
        try:
            from playwright.async_api import async_playwright
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                await page.goto(team_url, wait_until='networkidle')
                await asyncio.sleep(2)
                
                html = await page.content()
                await browser.close()
                
                return html
                
        except Exception as e:
            logger.error(f"팀 페이지 크롤링 실패 {team_url}: {e}")
            return None
    
    async def _collect_team_players(self, team_id: str, team_url: str, 
                                   league_id: str) -> List[Dict]:
        """팀 선수 목록 수집"""
        try:
            # 팀 페이지에서 선수 목록 추출
            html = await self._crawl_team_page(team_url)
            if not html:
                return []
            
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')
            
            players = []
            
            # 선수 목록 테이블 찾기
            player_table = soup.find('table', class_='player_list')
            if not player_table:
                return []
            
            rows = player_table.find_all('tr')[1:]  # 헤더 제외
            
            for row in rows:
                try:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) < 3:
                        continue
                    
                    # 선수 정보 추출
                    player_name = cells[1].get_text(strip=True)
                    position = cells[2].get_text(strip=True)
                    
                    # 선수 상세 URL 생성
                    player_link = cells[1].find('a')
                    if player_link and player_link.get('href'):
                        player_id = self._extract_player_id(player_link.get('href'))
                        if player_id:
                            player_url = self.sport_config.player_url_pattern.format(
                                item=self.sport_config.sport_code,
                                leagueId=league_id,
                                teamId=team_id,
                                playerId=player_id
                            )
                            
                            players.append({
                                'player_id': player_id,
                                'player_name': player_name,
                                'position': position,
                                'team_id': team_id,
                                'league_id': league_id,
                                'player_url': player_url
                            })
                
                except Exception as e:
                    logger.debug(f"선수 정보 추출 실패: {e}")
                    continue
            
            return players
            
        except Exception as e:
            logger.error(f"팀 선수 수집 실패 {team_id}: {e}")
            return []
    
    def _extract_player_id(self, href: str) -> Optional[str]:
        """선수 링크에서 선수 ID 추출"""
        try:
            import re
            match = re.search(r'playerId=([^&]+)', href)
            return match.group(1) if match else None
        except:
            return None
    
    async def _save_team_stats(self, stats: BasketballData) -> bool:
        """팀 통계 데이터베이스 저장"""
        try:
            from database.database import connect_supabase
            
            client = connect_supabase()
            if not client:
                return False
            
            # BasketballData를 딕셔너리로 변환
            stats_dict = asdict(stats)
            
            # 데이터베이스 저장
            response = client.table('basketball_team_stats').upsert(stats_dict).execute()
            
            return len(response.data) > 0
            
        except Exception as e:
            logger.error(f"팀 통계 저장 실패: {e}")
            return False
    
    async def save_multiple_team_stats(self, games: List[Dict], 
                                      client=None) -> int:
        """다중 팀 통계 저장 (기존 야구 서비스 호환)"""
        return await self.collect_and_save_team_stats(games)
    
    async def save_multiple_player_stats(self, games: List[Dict]) -> List[Dict]:
        """다중 선수 통계 저장 (기존 야구 서비스 호환)"""
        return await self.collect_and_save_player_stats(games)
