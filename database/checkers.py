"""
중복 확인 및 검증 모듈
"""
from typing import List, Set

from supabase import Client

from utils.logger import Logger

logger = Logger(__name__)


def connect_supabase() -> Client:
    """Supabase 클라이언트 연결 (임시 - database.py에서 이동 예정)"""
    from config.config import SUPABASE_KEY, SUPABASE_URL
    from supabase import create_client
    
    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError("Supabase URL과 API 키를 설정해야 합니다.")
    return create_client(SUPABASE_URL, SUPABASE_KEY)


class BatchDuplicateChecker:
    """배치 중복 확인 전담 클래스"""
    
    @staticmethod
    def check_batch_duplicate_stats(combinations: List[tuple], sport_type: str) -> Set[str]:
        """🚀 배치 통계 중복 확인 - 30배 빠름"""
        try:
            client = connect_supabase()
            if not client or not combinations:
                return set()
            
            # 테이블 이름 결정
            table_name = f"{sport_type}_stats"
            
            # match_id 리스트 추출
            match_ids = list(set(combo[0] for combo in combinations))
            
            # 한 번의 쿼리로 모든 기존 데이터 조회
            response = client.table(table_name).select(
                'match_id,team_id,match_date'
            ).in_('match_id', match_ids).execute()
            
            # 기존 데이터를 set으로 변환
            existing_combinations = set()
            for row in response.data:
                key = f"{row['match_id']}_{row['team_id']}_{row['match_date']}"
                existing_combinations.add(key)
            
            return existing_combinations
            
        except Exception as e:
            logger.error(f"배치 중복 확인 실패 ({sport_type}): {e}")
            return set()
    
    @staticmethod
    def check_batch_duplicate_baseball_stats(combinations: List[tuple]) -> Set[str]:
        """야구 통계 중복 확인"""
        return BatchDuplicateChecker.check_batch_duplicate_stats(combinations, "baseball")
    
    @staticmethod
    def check_batch_duplicate_soccer_stats(combinations: List[tuple]) -> Set[str]:
        """축구 통계 중복 확인"""
        return BatchDuplicateChecker.check_batch_duplicate_stats(combinations, "soccer")