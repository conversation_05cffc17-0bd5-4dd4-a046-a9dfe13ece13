"""
Pitcher Profile and Stats Updater Service
Updates null pitcher_profile and pitcher_stats columns in baseball_stats table
"""
import asyncio
from typing import Any, Dict, List, Optional

from supabase import Client

from database.config import DatabaseConfig
from database.services import PitcherStatsProcessor
from sports.baseball.collectors.pitcher_crawler import PitcherCrawler
from utils.logger import Logger

logger = Logger(__name__)


class PitcherDataUpdater:
    """Service to update null pitcher_profile and pitcher_stats in baseball_stats table"""
    
    def __init__(self, client: Client):
        self.client = client
        self.pitcher_processor = PitcherStatsProcessor()
        
    def get_records_with_null_pitcher_data(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get baseball_stats records where pitcher_profile OR pitcher_stats is null/empty.
        
        This is for Betman data updates only - we only update pitcher fields when they're empty.
        Team data (team_batting, team_pitching, boxscores) is handled separately.
        
        Args:
            limit: Maximum number of records to return
            
        Returns:
            List of records that need pitcher data updates from Betman
        """
        try:
            # Query for records with null pitcher data (Betman data only)
            response = (
                self.client.table(DatabaseConfig.BASEBALL_TEAM_STATS_TABLE)
                .select("*")
                .or_("pitcher_profile.is.null,pitcher_stats.is.null")
                .limit(limit)
                .execute()
            )
            
            if response.data:
                return response.data
            else:
                logger.info("No records found with null pitcher data")
                return []
                
        except Exception as e:
            logger.error(f"Failed to query null pitcher data: {e}")
            return []
    
    def get_records_needing_pitcher_update(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get records that need pitcher data updates (have null pitcher_profile or pitcher_stats)
        """
        try:
            records = self.get_records_with_null_pitcher_data(limit)
            
            if records:
                logger.info(f"Found {len(records)} records needing pitcher data updates")
            
            return records
            
        except Exception as e:
            logger.error(f"Failed to get records needing pitcher updates: {e}")
            return []
    
    def _get_pitcher_name_from_target_games(self, match_id: str, team_role: str) -> Optional[str]:
        """Get pitcher name from target_games table"""
        try:
            response = (
                self.client.table("target_games")
                .select(f"{team_role}_pitcher")
                .eq("match_id", match_id)
                .execute()
            )
            
            if response.data and len(response.data) > 0:
                pitcher_field = f"{team_role}_pitcher"
                return response.data[0].get(pitcher_field, "").strip()
            
            return None
            
        except Exception as e:
            logger.debug(f"Failed to get pitcher name from target_games: {e}")
            return None
    
    async def update_pitcher_data_for_record(self, record: Dict[str, Any]) -> bool:
        """
        Update pitcher data for a single baseball_stats record
        
        Args:
            record: Baseball stats record with null pitcher data
            
        Returns:
            True if update successful, False otherwise
        """
        try:
            match_id = record.get('match_id')
            team_id = record.get('team_id')
            team_role = record.get('team_role', 'home')
            league = record.get('league', '')
            team_name = record.get('team_name', '')
            
            # Get pitcher name from target_games table
            pitcher_name = self._get_pitcher_name_from_target_games(match_id, team_role)
            
            if not pitcher_name or not pitcher_name.strip():
                logger.debug(f"No pitcher name available for {match_id}/{team_id}")
                return False
            
            # Get team URL for pitcher crawling
            team_url = self._get_team_url_for_crawling(record)
            if not team_url:
                logger.debug(f"No team URL available for pitcher crawling: {match_id}/{team_id}")
                return False
            
            # Crawl pitcher data
            pitcher_data = await self._crawl_pitcher_data(pitcher_name, team_url, league)
            if not pitcher_data:
                logger.debug(f"Failed to crawl pitcher data for {pitcher_name}")
                return False
            
            # Process pitcher data
            processed_pitchers = self.pitcher_processor.process_pitcher_data_list([pitcher_data], league)
            
            # Update database record
            update_data = {
                'pitcher_profile': processed_pitchers.get('pitcher_profile'),
                'pitcher_stats': processed_pitchers.get('pitcher_stats')
            }
            
            response = (
                self.client.table(DatabaseConfig.BASEBALL_TEAM_STATS_TABLE)
                .update(update_data)
                .eq('match_id', match_id)
                .eq('team_id', team_id)
                .execute()
            )
            
            if response.data:
                logger.info(f"✅ Updated pitcher data for {pitcher_name} ({match_id}/{team_id})")
                return True
            else:
                logger.warning(f"❌ Failed to update database for {pitcher_name}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to update pitcher data for record {record.get('match_id', 'unknown')}: {e}")
            return False
    
    def _get_team_url_for_crawling(self, record: Dict[str, Any]) -> Optional[str]:
        """Generate or retrieve team URL for pitcher crawling"""
        try:
            team_id = record.get('team_id')
            league = record.get('league', '')
            
            # Try to construct URL based on league and team_id
            from config.config import League, create_team_url, get_league_id

            # Convert league string to League enum
            league_mapping = {
                'KBO': League.KBO,
                'MLB': League.MLB,
                'NPB': League.NPB
            }
            
            league_enum = league_mapping.get(league.upper())
            if not league_enum:
                return None
            
            league_id = get_league_id(league_enum)
            team_url = create_team_url("BS", league_id, team_id)
            
            return team_url
            
        except Exception as e:
            logger.debug(f"Failed to generate team URL: {e}")
            return None
    
    async def _crawl_pitcher_data(self, pitcher_name: str, team_url: str, league: str) -> Optional[Dict[str, Any]]:
        """Crawl pitcher data using PitcherCrawler"""
        pitcher_crawler = None
        try:
            pitcher_crawler = PitcherCrawler()
            await pitcher_crawler.initialize_browser()
            
            # Wait for browser initialization
            await asyncio.sleep(2)
            
            # Crawl pitcher data
            pitcher_stats = await pitcher_crawler.crawl_pitcher_stats(
                pitcher_name=pitcher_name,
                team_url=team_url
            )
            
            # Wait after crawling
            await asyncio.sleep(1)
            
            # Validate data
            if pitcher_stats and (
                pitcher_stats.get('name') or 
                pitcher_stats.get('season_stats') or 
                pitcher_stats.get('recent_5_games')
            ):
                return pitcher_stats
            
            return None
            
        except Exception as e:
            logger.warning(f"Failed to crawl pitcher data for {pitcher_name}: {e}")
            return None
        finally:
            if pitcher_crawler:
                try:
                    await pitcher_crawler.cleanup()
                except Exception:
                    pass
    
    async def update_batch_pitcher_data(self, batch_size: int = 10) -> Dict[str, int]:
        """
        Update pitcher data in batches
        
        Args:
            batch_size: Number of records to process in one batch
            
        Returns:
            Dictionary with update statistics
        """
        stats = {
            'total_found': 0,
            'total_updated': 0,
            'total_failed': 0,
            'total_skipped': 0
        }
        
        try:
            # Get records that need updates
            records_to_update = self.get_records_needing_pitcher_update(batch_size)
            stats['total_found'] = len(records_to_update)
            
            if not records_to_update:
                logger.info("No records found that need pitcher data updates")
                return stats
            
            logger.info(f"Updating pitcher data for {len(records_to_update)} records...")
            
            # Process records one by one to avoid overwhelming the system
            for i, record in enumerate(records_to_update):
                try:
                    logger.info(f"Processing record {i+1}/{len(records_to_update)}: {record.get('match_id', 'unknown')}")
                    
                    success = await self.update_pitcher_data_for_record(record)
                    if success:
                        stats['total_updated'] += 1
                    else:
                        stats['total_failed'] += 1
                    
                    # Add delay between records to be respectful to the target server
                    if i < len(records_to_update) - 1:
                        await asyncio.sleep(3)
                        
                except Exception as e:
                    logger.error(f"Failed to process record {record.get('match_id', 'unknown')}: {e}")
                    stats['total_failed'] += 1
            
            logger.info(f"Pitcher data update completed: {stats['total_updated']} updated, {stats['total_failed']} failed")
            return stats
            
        except Exception as e:
            logger.error(f"Batch pitcher data update failed: {e}")
            return stats


async def update_null_pitcher_data(client: Client, batch_size: int = 10) -> Dict[str, int]:
    """
    Convenience function to update null pitcher data
    
    Args:
        client: Supabase client
        batch_size: Number of records to process in one batch
        
    Returns:
        Update statistics
    """
    updater = PitcherDataUpdater(client)
    return await updater.update_batch_pitcher_data(batch_size)


# CLI interface for manual execution
if __name__ == "__main__":
    import os
    import sys

    # Add project root to path
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    from database.database import connect_supabase
    
    async def main():
        client = connect_supabase()
        if not client:
            print("Failed to connect to database")
            return
        
        print("Starting pitcher data update...")
        stats = await update_null_pitcher_data(client, batch_size=5)
        
        print("\nUpdate Statistics:")
        print(f"  Total found: {stats['total_found']}")
        print(f"  Total updated: {stats['total_updated']}")
        print(f"  Total failed: {stats['total_failed']}")
        print(f"  Total skipped: {stats['total_skipped']}")
    
    asyncio.run(main())