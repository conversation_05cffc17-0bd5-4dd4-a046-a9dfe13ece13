"""
통합 Supabase 데이터베이스 연결 및 쿼리 유틸리티
"""
from datetime import datetime
from typing import Any, Dict, Optional

from supabase import Client, create_client

from config.config import SUPABASE_KEY, SUPABASE_URL
from utils.helpers import normalize_innings_in_dict
from utils.logger import Logger

logger = Logger(__name__)


def connect_supabase() -> Client:
    """
    Supabase 클라이언트 연결 초기화
    """
    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError("Supabase URL과 API 키를 설정해야 합니다.")

    return create_client(SUPABASE_URL, SUPABASE_KEY)


def get_sports_name(sport_code: str) -> str:
    """
    스포츠 코드를 스포츠 이름으로 변환
    """
    sports_mapping = {
        'BS': 'baseball',
        'SC': 'soccer',
        'BK': 'basketball',
        'VL': 'volleyball',
    }
    return sports_mapping.get(sport_code, sport_code.lower())


def get_team_mappings(client: Client) -> Dict[str, str]:
    """
    team_info 테이블에서 team_name -> team_full_name 매핑 가져오기
    
    Args:
        client: Supabase 클라이언트
    
    Returns:
        Dict[str, str]: {team_name: team_full_name} 매핑
    """
    try:
        response = client.table('team_info').select('team_name, team_full_name').execute()
        
        mappings = {}
        for row in response.data:
            team_name = row.get('team_name')
            team_full_name = row.get('team_full_name')
            if team_name and team_full_name:
                mappings[team_name] = team_full_name
        
        return mappings
    except Exception as e:
        logger.error(f"팀 매핑 로드 실패: {e}")
        return {}


def normalize_pitcher_stats(pitcher_stats: Dict[str, Any]) -> Dict[str, Any]:
    """
    투수 통계 데이터를 정규화합니다.
    - "없음", "No data" 값들을 빈 문자열로 변환
    - 이닝 필드 정규화 ("25 2" -> "25 2/3" 형태로 변환)
    
    Args:
        pitcher_stats: 정규화할 투수 통계 딕셔너리
        
    Returns:
        Dict[str, Any]: 정규화된 투수 통계 딕셔너리
    """
    if not isinstance(pitcher_stats, dict):
        return pitcher_stats
    
    normalized_stats = {}
    
    for key, value in pitcher_stats.items():
        if isinstance(value, dict):
            # 중첩된 딕셔너리 처리
            normalized_value = {}
            for nested_key, nested_value in value.items():
                if nested_value in ["없음", "No data", None]:
                    normalized_value[nested_key] = ""
                else:
                    normalized_value[nested_key] = nested_value
            
            # 이닝 필드 정규화
            normalized_value = normalize_innings_in_dict(normalized_value)
            normalized_stats[key] = normalized_value
            
        elif isinstance(value, list):
            # 리스트 처리 (각 항목이 딕셔너리인 경우)
            normalized_list = []
            for item in value:
                if isinstance(item, dict):
                    normalized_item = {}
                    for item_key, item_value in item.items():
                        if item_value in ["없음", "No data", None]:
                            normalized_item[item_key] = ""
                        else:
                            normalized_item[item_key] = item_value
                    
                    # 이닝 필드 정규화
                    normalized_item = normalize_innings_in_dict(normalized_item)
                    normalized_list.append(normalized_item)
                else:
                    normalized_list.append(item)
            normalized_stats[key] = normalized_list
            
        else:
            # 단순 값 처리
            if value in ["없음", "No data", None]:
                normalized_stats[key] = ""
            else:
                normalized_stats[key] = value
    
    return normalized_stats


def save_pitching_stats(
    client: Client,
    pitcher_data: Dict[str, Any],
    league: Optional[str] = None,
    team_name: Optional[str] = None
) -> bool:
    """
    투수 통계를 team_stats 테이블의 pitching_stats 컬럼에 저장/업데이트
    
    Args:
        client: Supabase 클라이언트
        pitcher_data: 투수 데이터
        league: 리그 정보
        team_name: 팀명
    
    Returns:
        bool: 저장 성공 여부
    """
    try:
        # 필수 정보 확인
        team_id = pitcher_data.get('team_id')
        match_date = pitcher_data.get('game_date')
        pitcher_name = pitcher_data.get('pitcher_name', '')
        
        if not team_id or not match_date or not pitcher_name:
            return False
        
        # match_time 가져오기 (투수 데이터에서)
        match_time = pitcher_data.get('match_time', '')
        
        # target_games에서 match_id 가져오기
        target_data = get_team_stats_data_from_target_games(
            client, team_id, match_date, match_time
        )
        
        # match_id 기반 ID 생성 (타겟 경기에서 찾은 경우만 처리)
        if not target_data['found_in_target_games']:
            return False
        
        pk = target_data['match_id']
        
        # 기존 pitching_stats 조회 (upsert 전에 병합을 위해)
        existing = client.table('team_stats').select('pitching_stats').eq(
            'id', pk
        ).execute()
        
        # 저장할 투수 데이터 구성 (pitcher_stats만 + 메타 정보)
        pitcher_record = {
            'name': pitcher_name,
            'team_role': pitcher_data.get('team_role'),
            'position': pitcher_data.get('position', 'starting_pitcher'),
            'league': league or pitcher_data.get('league'),
            'pitcher_stats': pitcher_data.get('pitcher_stats', {})
        }
        
        # 투수 통계 정규화 (이닝 필드 포함)
        if 'pitcher_stats' in pitcher_record:
            pitcher_record['pitcher_stats'] = normalize_pitcher_stats(
                pitcher_record['pitcher_stats']
            )
        
        # 기존 pitching_stats와 병합
        current_pitching_stats = {}
        if existing.data and existing.data[0].get('pitching_stats'):
            current_pitching_stats = existing.data[0]['pitching_stats']
            if not isinstance(current_pitching_stats, dict):
                current_pitching_stats = {}
        
        # 투수별로 저장 (깔끔한 구조)
        current_pitching_stats[pitcher_name] = pitcher_record
        
        # upsert로 한 번에 처리
        upsert_data = {
            'id': pk,
            'team_id': team_id,
            'team_role': target_data.get('team_role'),  # home/away 구분
            'match_date': match_date,
            'pitching_stats': current_pitching_stats
        }
        result = client.table('team_stats').upsert(upsert_data).execute()
        
        return len(result.data) > 0
        
    except Exception as e:
        logger.error(f"투수 통계 저장 실패 - {pitcher_name}: {e}")
        return False


def get_team_stats_data_from_target_games(
    client: Client, 
    team_id: str, 
    match_date: str, 
    match_time: str
) -> Dict:
    """
    target_games에서 team_stats에 필요한 모든 데이터를 한 번에 조회
    
    Args:
        client: Supabase 클라이언트
        team_id: 팀 ID
        match_date: 경기 날짜 
        match_time: 경기 시간
    
    Returns:
        Dict: team_stats에 필요한 모든 정보
    """
    try:
        # 한 번의 쿼리로 필요한 모든 정보 가져오기
        response = client.table('target_games').select(
            'match_id, sports, league, league_id, '
            'home_team_id, away_team_id, home_team_name, away_team_name'
        ).eq('match_date', match_date).eq(
            'match_time', match_time  
        ).eq('game_type', 'W').eq(
            'sports', 'baseball'
        ).or_(
            f'home_team_id.eq.{team_id},away_team_id.eq.{team_id}'
        ).limit(1).execute()
        
        if response.data and len(response.data) > 0:
            game_data = response.data[0]
            match_id = game_data['match_id']
            
            # 팀명 결정 (홈/원정 구분)
            if team_id == game_data['home_team_id']:
                team_name = game_data['home_team_name']
            else:
                team_name = game_data['away_team_name']
            
            # 홈/원정 구분
            team_role = 'home' if team_id == game_data['home_team_id'] else 'away'
            
            return {
                'match_id': match_id,
                'sports': game_data['sports'],
                'league': game_data['league'], 
                'league_id': game_data['league_id'],
                'team_name': team_name,
                'team_role': team_role,
                'found_in_target_games': True
            }
        else:
            # 게임타입 W가 없거나 매치 없음
            return {
                'found_in_target_games': False
            }
            
    except Exception:
        # 오류 시
        return {
            'found_in_target_games': False
        }


def save_team_stats(
    client: Client,
    team_id: str,
    stat: Dict[str, Any],
    match_date: Optional[str] = None
) -> bool:
    """
    팀 통계를 Supabase 데이터베이스에 저장 (target_games 기반 ID 생성)
    
    Args:
        client: Supabase 클라이언트
        team_id: 팀 ID
        stat: 팀 통계 데이터
        match_date: 저장 날짜 (기본값: 오늘)
    
    Returns:
        bool: 저장 성공 여부
    """
    try:
        if match_date is None:
            match_date = datetime.now().strftime("%Y-%m-%d")

        # match_time 가져오기
        match_time = stat.get("match_time", "")
        
        # target_games에서 데이터 조회 (게임타입 W만)
        target_data = get_team_stats_data_from_target_games(
            client, team_id, match_date, match_time
        )
        
        # 타겟 경기에서 찾은 경우만 처리
        if not target_data['found_in_target_games']:
            return False
            
        match_id = target_data['match_id']
        pk = f"{match_id}_{team_id}"
        
        # sportic_team_id는 항상 team_info에서 조회 (필수 정보)
        team_info_response = (
            client.table('team_info')
            .select('sportic_team_id, sports')
            .eq('team_id', team_id)
            .eq('sports', 'baseball')
            .execute()
        )
        team_info = (
            team_info_response.data[0]
            if team_info_response.data else {}
        )
        
        # target_games에서 찾은 경우 sports 정보 우선 사용
        if target_data['found_in_target_games']:
            team_info['sports'] = target_data['sports']

        # match_time 처리 (TIME 타입으로 시간만 저장)
        match_time = stat.get("match_time")
        if match_time and isinstance(match_time, str):
            # "18:30" 형식 그대로 저장
            pass
        else:
            match_time = None
        
        # 팀 통계 데이터 구성 (target_games 우선, fallback은 기존 데이터)
        team_stat_data = {
            "id": pk,
            "match_id": match_id,
            "team_id": team_id,
            "team_role": target_data.get("team_role"),  # home/away 구분
            "match_date": match_date,
            "match_time": match_time,
            "season_summary": stat.get("seasons_summary", {}),
            "recent_games": stat.get("recent_games", {}),
            "recent_games_summary": stat.get("recent_games_summary", {}),
            "season_stats": stat.get("season_stats", {}),
            "sports": team_info.get("sports", stat.get("sports", "")),
            "team_name": (target_data.get("team_name") or 
                         stat.get("team_name")),
            "league": (target_data.get("league") or 
                      stat.get("league")),
            "sportic_team_id": team_info.get("sportic_team_id", "")
        }

        # team_stats 테이블에 저장
        response = client.table("team_stats").upsert(team_stat_data).execute()
        
        return bool(response.data)
        
    except Exception as e:
        logger.error(f"❌ 팀 통계 저장 실패 ({team_id}): {str(e)}")
        return False


def save_game_pitcher_stats(
    client: Client,
    game_data: Dict[str, Any]
) -> bool:
    """
    경기 데이터에서 홈/원정 팀의 투수 기록을 각각 저장
    
    Args:
        client: Supabase 클라이언트
        game_data: 경기 데이터
    
    Returns:
        bool: 저장 성공 여부
    """
    success_count = 0
    total_count = 0
    
    try:
        date = game_data.get('date', '')
        league = game_data.get('league', '').value if hasattr(game_data.get('league'), 'value') else str(game_data.get('league', ''))
        home_team = game_data.get('home_team', '')
        away_team = game_data.get('away_team', '')
        
        # 홈팀 투수 데이터 저장
        home_team_id = game_data.get('home_team_id', '')
        home_pitcher_stats = game_data.get('home_pitcher_stats', {})
        home_pitcher = game_data.get('home_pitcher', '')
        
        if home_team_id and home_pitcher_stats and 'error' not in home_pitcher_stats:
            total_count += 1
            
            pitcher_data = {
                'game_date': date,
                'team_role': 'home',
                'team_id': home_team_id,
                'team_name': home_team,
                'league': league,
                'pitcher_name': home_pitcher,
                'pitcher_stats': home_pitcher_stats,
                'position': 'starting_pitcher',
                'pitcher_url': home_pitcher_stats.get('pitcher_url', '')
            }
            
            if save_pitching_stats(client, pitcher_data, league, home_team):
                success_count += 1
        
        # 원정팀 투수 데이터 저장
        away_team_id = game_data.get('away_team_id', '')
        away_pitcher_stats = game_data.get('away_pitcher_stats', {})
        away_pitcher = game_data.get('away_pitcher', '')
        
        if away_team_id and away_pitcher_stats and 'error' not in away_pitcher_stats:
            total_count += 1
            
            pitcher_data = {
                'game_date': date,
                'team_role': 'away',
                'team_id': away_team_id,
                'team_name': away_team,
                'league': league,
                'pitcher_name': away_pitcher,
                'pitcher_stats': away_pitcher_stats,
                'position': 'starting_pitcher',
                'pitcher_url': away_pitcher_stats.get('pitcher_url', '')
            }
            
            if save_pitching_stats(client, pitcher_data, league, away_team):
                success_count += 1
        
        logger.info(f"투수 통계 저장: {success_count}/{total_count} 성공")
        return success_count == total_count
        
    except Exception as e:
        logger.error(f"경기 투수 통계 저장 실패: {e}")
        return False


def save_structured_pitcher_data(
    client: Client,
    pitcher_name: str,
    starter_info: Dict[str, Any],
    pitching_stats: Dict[str, Any],
    team_id: str,
    match_date: str,
    match_time: str = ""
) -> bool:
    """
    구조화된 투수 데이터를 별도 컬럼에 저장
    
    Args:
        client: Supabase 클라이언트
        pitcher_name: 투수명
        starter_info: 선수 기본 정보
        pitching_stats: 투구 통계 정보
        team_id: 팀 ID
        match_date: 경기 날짜
    
    Returns:
        bool: 저장 성공 여부
    """
    try:
        # target_games에서 match_id 가져오기 (팀 통계와 동일한 로직)
        target_data = get_team_stats_data_from_target_games(
            client, team_id, match_date, match_time
        )
        
        # match_id 생성 (타겟 경기 있으면 사용, 없으면 기본값 생성)
        if target_data['found_in_target_games']:
            match_id = target_data['match_id']
        else:
            # 타겟 경기 없으면 기본 match_id 생성
            match_id = f"auto_{match_date}_{match_time}_{team_id}"
        
        pk = f"{match_id}_{team_id}"
        
        # upsert로 한 번에 처리
        upsert_data = {
            'id': pk,
            'match_id': match_id,
            'team_id': team_id,
            'team_role': target_data.get('team_role'),
            'match_date': match_date,
            'pitcher_profile': starter_info,
            'pitcher_stats': pitching_stats
        }
        
        result = client.table('team_stats').upsert(upsert_data).execute()
        
        return len(result.data) > 0
        
    except Exception as e:
        logger.error(f"구조화된 투수 데이터 저장 실패 - {pitcher_name}: {e}")
        return False


def save_pitcher_stats(
    client: Client,
    match_id: str,
    team_id: str,
    pitcher_name: str,
    pitcher_profile: Dict[str, Any],
    pitcher_stats: Dict[str, Any],
    log_errors: bool = False
) -> bool:
    """
    투수 통계를 team_stats 테이블에 저장/업데이트합니다.
    
    Args:
        client: Supabase 클라이언트
        match_id: 경기 ID
        team_id: 팀 ID
        pitcher_name: 투수 이름
        pitcher_profile: 선수 기본 정보
        pitcher_stats: 투구 통계 정보
        log_errors: 오류 로깅 여부
        
    Returns:
        bool: 저장 성공 여부
    """
    try:
        if not match_id or not team_id:
            if log_errors:
                logger.error("match_id 또는 team_id가 누락되었습니다.")
            return False
            
        # pitcher_name이 없으면 저장 불가
        if not pitcher_name:
            if log_errors:
                logger.error("pitcher_name이 누락되었습니다.")
            return False
            
        # 팀 통계 조회
        response = client.table('team_stats').select('id').eq(
            'match_id', match_id
        ).eq('team_id', team_id).execute()
        
        team_stats_row = None
        if response.data:
            team_stats_row = response.data[0]
            
        # 만약 조회되는 team_stats 행이 없으면 업데이트 불가
        if not team_stats_row:
            if log_errors:
                logger.error(f"team_stats에서 match_id={match_id}, team_id={team_id}인 행을 찾을 수 없습니다.")
            return False
            
        # 이 함수에서는 pitcher_stats만 업데이트
        # 기존 pitcher_stats 조회 (upsert 전에 병합을 위해)
        existing = client.table('team_stats').select('pitcher_stats').eq(
            'match_id', match_id
        ).eq('team_id', team_id).execute()
        
        if existing.error:
            if log_errors:
                logger.error(f"team_stats 조회 오류: {existing.error}")
            return False
            
        # 기존 pitcher_stats와 병합
        current_pitcher_stats = {}
        if existing.data and existing.data[0].get('pitcher_stats'):
            current_pitcher_stats = existing.data[0]['pitcher_stats']
            if not isinstance(current_pitcher_stats, dict):
                current_pitcher_stats = {}
        
        # 현재 투수 정보 추가/갱신
        current_pitcher_stats[pitcher_name] = pitcher_stats
            
        # 투수 통계 업데이트
        update_data = {
            'pitcher_profile': pitcher_profile,
            'pitcher_stats': current_pitcher_stats
        }
        
        response = client.table('team_stats').update(
            update_data
        ).eq('match_id', match_id).eq('team_id', team_id).execute()
        
        if response.error:
            if log_errors:
                logger.error(f"team_stats 업데이트 오류: {response.error}")
            return False
            
        return True
    except Exception as e:
        if log_errors:
            logger.exception(f"투수 통계 저장 중 오류: {str(e)}")
        return False 