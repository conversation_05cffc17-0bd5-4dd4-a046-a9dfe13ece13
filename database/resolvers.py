"""
팀명 해결 및 매핑 모듈
"""
from typing import Dict, Any

from supabase import Client

from database.config import DatabaseConfig
from utils.logger import Logger

logger = Logger(__name__)


class TeamNameResolver:
    """팀명 해결 전담 클래스"""
    
    def __init__(self, client: Client):
        self.client = client
    
    def _query_team_info(self, team_name: str, league: str = None, select_fields: str = 'team_name, team_full_name, sportic_league_id'):
        """팀 정보 조회 공통 로직"""
        # 별칭을 실제 DB 이름으로 변환
        actual_team_name = DatabaseConfig.resolve_team_alias(team_name)
        
        query = self.client.table('team_info').select(select_fields)
        
        # 리그가 제공된 경우 sportic_league_id로 필터링
        if league and league in DatabaseConfig.LEAGUE_MAPPING:
            league_id = DatabaseConfig.LEAGUE_MAPPING[league]
            response = query.eq('team_name', actual_team_name).eq('sportic_league_id', league_id).execute()
        else:
            response = query.eq('team_name', actual_team_name).execute()
        
        return response, actual_team_name
    
    def get_team_display_name(self, team_name: str, league: str = None) -> str:
        """
        리그에 따라 적절한 팀명을 반환
        - KBO, NPB: 짧은 팀명 (team_name)
        - MLB: 풀네임 (team_full_name)
        """
        try:
            response, actual_team_name = self._query_team_info(team_name, league)
            
            if response.data and len(response.data) > 0:
                row = response.data[0]
                
                # 리그별 반환 정책
                if league in ['KBO', 'NPB', 'SOCCER']:  # 축구도 짧은 이름 사용
                    result = row.get('team_name', team_name)
                else:
                    result = row.get('team_full_name', team_name)
                
                if actual_team_name != team_name:
                    logger.debug(f"팀명 별칭 매핑 ({league}): {team_name} → {actual_team_name} → {result}")
                return result
            else:
                # 폴백: 리그 없이 조회
                if league:
                    response_fallback, _ = self._query_team_info(team_name, None, 'team_name, team_full_name')
                    if response_fallback.data and len(response_fallback.data) > 0:
                        row = response_fallback.data[0]
                        result = row.get('team_name' if league in ['KBO', 'NPB'] else 'team_full_name', team_name)
                        logger.debug(f"리그 없이 매핑 성공: {team_name} → {result}")
                        return result
                
                return team_name
        except Exception as e:
            logger.error(f"팀 표시명 조회 실패 ({team_name}, {league}): {e}")
            return team_name
    
    def get_team_full_name(self, team_name: str, league: str = None) -> str:
        """팀 풀네임 조회"""
        try:
            response, actual_team_name = self._query_team_info(team_name, league, 'team_full_name, sportic_league_id')
            
            if response.data and len(response.data) > 0:
                result = response.data[0].get('team_full_name', team_name)
                if actual_team_name != team_name:
                    logger.debug(f"팀명 별칭 매핑: {team_name} → {actual_team_name} → {result}")
                return result
            else:
                # 폴백: 리그 없이 조회
                if league:
                    response_fallback, _ = self._query_team_info(team_name, None, 'team_full_name')
                    if response_fallback.data and len(response_fallback.data) > 0:
                        logger.debug(f"리그 없이 매핑 성공: {team_name}")
                        return response_fallback.data[0].get('team_full_name', team_name)
                
                return team_name
        except Exception as e:
            logger.error(f"팀 풀네임 조회 실패 ({team_name}, {league}): {e}")
            return team_name
    
    def get_team_mappings(self) -> Dict[str, str]:
        """team_info 테이블에서 team_name -> team_full_name 매핑 가져오기"""
        try:
            response = self.client.table('team_info').select('team_name, team_full_name').execute()
            
            mappings = {}
            for row in response.data:
                team_name = row.get('team_name')
                team_full_name = row.get('team_full_name')
                if team_name and team_full_name:
                    mappings[team_name] = team_full_name
            
            return mappings
        except Exception as e:
            logger.error(f"팀 매핑 로드 실패: {e}")
            return {}