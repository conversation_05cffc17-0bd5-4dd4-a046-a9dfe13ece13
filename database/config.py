"""
데이터베이스 설정 및 상수 모듈
"""


class DatabaseConfig:
    """데이터베이스 관련 설정과 매핑을 중앙화하는 클래스"""
    
    # 소수점 표기 필드
    DECIMAL_FIELDS = {
        'era', 'whip', 'win_rate', 'ba', 'avg', 'batting_avg',
    }
    
    # 리그 매핑
    LEAGUE_MAPPING = {
        'NPB': 'bs004',
        'KBO': 'bs001', 
        'MLB': 'bs002'
    }
    
    # 팀명 별칭 매핑
    TEAM_ALIAS_MAPPING = {
        '치바 롯데': '지바롯데',  # NPB
        '치바롯데': '지바롯데',   # NPB 변형
        '소프트뱅크': '소프트뱅',   # NPB 단축형
        '니폰햄': '닛폰햠',        # NPB 변형
    }
    
    # 스포츠 매핑
    SPORTS_MAPPING = {
        'BS': 'baseball',
        'SC': 'soccer',
        'BK': 'basketball',
        'VL': 'volleyball',
    }
    
    # NPB 제거 키
    UNWANTED_NPB_KEYS = {"at_bats", "pitch_count", "batters_faced"}
    
    # 테이블 상수
    BASEBALL_TEAM_STATS_TABLE = "baseball_stats"
    SOCCER_TEAM_STATS_TABLE = "soccer_stats"
    
    @staticmethod
    def get_sports_name(sport_code: str) -> str:
        """스포츠 코드를 스포츠 이름으로 변환"""
        return DatabaseConfig.SPORTS_MAPPING.get(
            sport_code, sport_code.lower()
        )
    
    @staticmethod
    def resolve_team_alias(team_name: str) -> str:
        """팀명 별칭을 실제 DB 이름으로 변환"""
        return DatabaseConfig.TEAM_ALIAS_MAPPING.get(
            team_name, team_name
        )