#!/usr/bin/env python3
"""
종합 시스템 테스트 - 전체 멀티스포츠 시스템 검증
"""
import asyncio
import sys
import os
from datetime import datetime

# 프로젝트 루트를 Python path에 추가
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def test_core_infrastructure():
    """핵심 인프라 테스트"""
    print("🔧 핵심 인프라 테스트")
    
    try:
        # SportConfig 테스트
        from core.config.sport_config import get_sport_config, get_all_sport_configs, get_active_sports
        from datetime import time
        
        all_configs = get_all_sport_configs()
        assert len(all_configs) == 4  # 4개 스포츠
        print(f"   ✅ 스포츠 설정: {len(all_configs)}개 스포츠 지원")
        
        # 시간대 스케줄러 테스트
        from core.scheduler.time_slot_scheduler import TimeSlotScheduler
        scheduler = TimeSlotScheduler()
        
        # 업무 시간 확인
        is_business_hours = scheduler.is_business_hours()
        print(f"   ✅ 시간대 스케줄러: 업무시간 {is_business_hours}")
        
        # 스케줄 요약
        summary = scheduler.get_schedule_summary()
        assert 'current_time' in summary
        assert 'all_sports' in summary
        print(f"   ✅ 스케줄 요약: {len(summary['all_sports'])}개 스포츠")
        
        # 팀 매처 테스트
        from core.matcher.universal_team_matcher import UniversalTeamMatcher
        matcher = UniversalTeamMatcher()
        
        stats = matcher.get_statistics()
        assert stats['total_sports'] == 4
        print(f"   ✅ 팀 매처: {stats['total_teams']}개 팀 매핑")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 핵심 인프라 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_all_sport_parsers():
    """모든 스포츠 파서 테스트"""
    print("🔧 모든 스포츠 파서 테스트")
    
    try:
        sample_html = """
        <html>
            <body>
                <h2>테스트 팀</h2>
                <ul>
                    <li>감독: 김감독</li>
                    <li>구장: 테스트 스타디움</li>
                </ul>
                <table>
                    <caption>시즌 전체성적</caption>
                    <tr><th>순위</th><th>경기수</th><th>승</th><th>무</th><th>패</th></tr>
                    <tr><td>1</td><td>30</td><td>20</td><td>5</td><td>5</td></tr>
                </table>
                <a href="scPlayerDetail.do?playerId=123">김선수</a>
            </body>
        </html>
        """
        
        # 축구 파서 테스트
        from sports.soccer.parsers.soccer_parser import SoccerParser
        soccer_parser = SoccerParser()
        soccer_data = soccer_parser.parse_team_data(sample_html, "울산 HD FC", league_id="SC001", league="K리그1")
        assert soccer_data.team_name == "울산 HD FC"
        assert len(soccer_data.players) >= 0
        print("   ✅ 축구 파서 테스트 통과")
        
        # 농구 파서 테스트
        from sports.basketball.parsers.basketball_parser import BasketballParser
        basketball_parser = BasketballParser()
        basketball_data = basketball_parser.parse_team_data(sample_html, "서울 SK 나이츠", league_id="BK001", league="KBL")
        assert basketball_data.team_name == "서울 SK 나이츠"
        assert len(basketball_data.players) >= 0
        print("   ✅ 농구 파서 테스트 통과")
        
        # 배구 파서 테스트
        from sports.volleyball.parsers.volleyball_parser import VolleyballParser
        volleyball_parser = VolleyballParser()
        volleyball_data = volleyball_parser.parse_team_data(sample_html, "현대캐피탈 스카이워커스", league_id="VL001", league="KOVO남")
        assert volleyball_data.team_name == "현대캐피탈 스카이워커스"
        assert len(volleyball_data.players) >= 0
        print("   ✅ 배구 파서 테스트 통과")
        
        # 야구 선수 파서 테스트
        from sports.baseball.parsers.baseball_parser import BaseballPlayerParser
        player_parser = BaseballPlayerParser(sample_html)
        player_data = player_parser.parse_player_data("김선수", league_id="BB001", league="KBO", team_name="LG 트윈스", position="투수")
        assert player_data.player_name == "김선수"
        print("   ✅ 야구 선수 파서 테스트 통과")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 스포츠 파서 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_all_sport_plugins():
    """모든 스포츠 플러그인 테스트"""
    print("🔧 모든 스포츠 플러그인 테스트")
    
    try:
        plugins = {}
        
        # 축구 플러그인
        from sports.soccer.plugin import SoccerPlugin
        plugins['soccer'] = SoccerPlugin()
        config = plugins['soccer'].get_sport_config()
        assert config.sport_name == 'soccer'
        assert len(config.leagues) == 9
        print("   ✅ 축구 플러그인 테스트 통과")
        
        # 농구 플러그인
        from sports.basketball.plugin import BasketballPlugin
        plugins['basketball'] = BasketballPlugin()
        config = plugins['basketball'].get_sport_config()
        assert config.sport_name == 'basketball'
        assert len(config.leagues) == 3
        print("   ✅ 농구 플러그인 테스트 통과")
        
        # 배구 플러그인
        from sports.volleyball.plugin import VolleyballPlugin
        plugins['volleyball'] = VolleyballPlugin()
        config = plugins['volleyball'].get_sport_config()
        assert config.sport_name == 'volleyball'
        assert len(config.leagues) == 2
        print("   ✅ 배구 플러그인 테스트 통과")
        
        # 야구 플러그인
        from sports.baseball.plugin import BaseballPlugin
        plugins['baseball'] = BaseballPlugin()
        config = plugins['baseball'].get_sport_config()
        assert config.sport_name == 'baseball'
        assert len(config.leagues) == 3
        print("   ✅ 야구 플러그인 테스트 통과")
        
        print(f"   📊 총 {len(plugins)}개 플러그인 로드 완료")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 스포츠 플러그인 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_data_collection_interfaces():
    """데이터 수집 인터페이스 테스트"""
    print("🔧 데이터 수집 인터페이스 테스트")
    
    try:
        # 야구 서비스 선수 데이터 수집 인터페이스 테스트
        from sports.baseball.services.baseball_service import BaseballService
        
        baseball_service = BaseballService()
        
        # 선수 통계 수집 메서드 존재 확인
        assert hasattr(baseball_service, 'collect_and_save_player_stats')
        print("   ✅ 야구 선수 데이터 수집 인터페이스 확인")
        
        # 테스트 게임 데이터
        test_games = [
            {'team_id': 'LG', 'league_id': 'BB001'},
            {'team_id': 'KT', 'league_id': 'BB001'}
        ]
        
        # 실제 호출은 하지 않고 메서드 존재만 확인
        print("   ✅ 야구 선수 데이터 수집 메서드 확인")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 데이터 수집 인터페이스 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_system_integration():
    """시스템 통합 테스트"""
    print("🔧 시스템 통합 테스트")
    
    try:
        # 모든 컴포넌트가 함께 작동하는지 확인
        from core.config.sport_config import get_all_sport_configs
        from core.scheduler.time_slot_scheduler import TimeSlotScheduler
        from core.matcher.universal_team_matcher import UniversalTeamMatcher
        
        # 설정 로드
        configs = get_all_sport_configs()
        scheduler = TimeSlotScheduler()
        matcher = UniversalTeamMatcher()
        
        # 각 스포츠별로 통합 테스트
        for sport_name, config in configs.items():
            # 스케줄 확인
            next_schedule = scheduler.get_next_schedule(sport_name)
            
            # 팀 매핑 확인
            teams = matcher.get_all_teams(sport_name)
            
            print(f"   ✅ {sport_name}: {len(teams)}개 팀, 다음 실행: {next_schedule.strftime('%H:%M') if next_schedule else 'N/A'}")
        
        # 전체 시스템 상태 확인
        summary = scheduler.get_schedule_summary()
        stats = matcher.get_statistics()
        
        print(f"   📊 시스템 요약:")
        print(f"      - 지원 스포츠: {len(configs)}개")
        print(f"      - 총 팀: {stats['total_teams']}개")
        print(f"      - 활성 스포츠: {len(summary['active_sports'])}개")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 시스템 통합 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """메인 테스트 함수"""
    print("🚀 종합 시스템 테스트 시작")
    print("=" * 80)
    
    tests = [
        ("핵심 인프라", test_core_infrastructure),
        ("모든 스포츠 파서", test_all_sport_parsers),
        ("모든 스포츠 플러그인", test_all_sport_plugins),
        ("데이터 수집 인터페이스", test_data_collection_interfaces),
        ("시스템 통합", test_system_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 {test_name} 테스트 실행 중...")
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 테스트 통과")
            else:
                print(f"❌ {test_name} 테스트 실패")
        except Exception as e:
            print(f"❌ {test_name} 테스트 실행 실패: {e}")
    
    print("\n" + "=" * 80)
    print(f"🎉 종합 시스템 테스트 완료: {passed}/{total} 통과")
    print("=" * 80)
    
    if passed == total:
        print("✅ 모든 테스트 통과! 멀티스포츠 시스템 준비 완료")
        print("🚀 실제 운영 환경에서 데이터 수집을 시작할 수 있습니다!")
        return True
    else:
        print(f"⚠️ {total - passed}개 테스트 실패")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
