"""
프로젝트 구조 생성기들
"""

from pathlib import Path
from typing import Dict, List


class SmallProjectGenerator:
    """소규모 프로젝트 구조 생성기"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        
    def generate(self) -> Dict[str, str]:
        """소규모 프로젝트 구조 생성"""
        return {
            "main.py": self._generate_main_py(),
            "utils.py": self._generate_utils_py(),
            "requirements.txt": self._generate_requirements(),
            ".env.example": self._generate_env_example(),
            "README.md": self._generate_readme(),
        }
        
    def _generate_main_py(self) -> str:
        return '''#!/usr/bin/env python3
"""
메인 애플리케이션 진입점
"""

from utils import setup_logging

def main():
    """메인 함수"""
    setup_logging()
    print("애플리케이션 시작")

if __name__ == "__main__":
    main()
'''

class MediumProjectGenerator:
    """중규모 프로젝트 구조 생성기"""
    
    def __init__(self, project_root: Path, domain: str):
        self.project_root = project_root
        self.domain = domain
        
    def generate_structure(self) -> List[str]:
        """중규모 프로젝트 디렉토리 구조"""
        return [
            "src/core",
            "src/services", 
            "src/utils",
            "src/config",
            "tests"
        ]

class LargeProjectGenerator:
    """대규모 프로젝트 구조 생성기"""
    
    def __init__(self, project_root: Path, domain: str):
        self.project_root = project_root
        self.domain = domain
        
    def generate_structure(self) -> List[str]:
        """대규모 프로젝트 디렉토리 구조"""
        return [
            f"src/domain/{self.domain}",
            "src/services/api",
            "src/services/db", 
            "src/services/calc",
            "src/infrastructure",
            "src/shared/utils",
            "src/shared/config",
            "tests/unit",
            "tests/integration",
            "tests/fixtures"
        ] 