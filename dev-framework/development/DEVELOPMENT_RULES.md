# 현대적 개발 프레임워크 규칙 (Modern Development Framework Rules)

## 🎯 프레임워크 철학

이 프레임워크는 **적응형(Adaptive)** 개발을 지향합니다:
- 프로젝트 규모에 따라 구조가 자동 조정됩니다
- 팀 크기에 맞춰 명명 규칙이 변화합니다  
- 도메인 복잡도에 따라 아키텍처가 진화합니다

### 핵심 원칙
1. **점진적 진화**: 작게 시작해서 필요에 따라 확장
2. **목적 기반**: 기술적 분류보다 비즈니스 도메인 우선
3. **도구 통합**: MCP를 통한 AI 도구 활용
4. **자동화 우선**: 반복 작업의 철저한 자동화

## 📊 프로젝트 분류 기준

### 소형 프로젝트 (Small) - 파일 10개 미만
```
project/
├── main.py              # 단일 진입점
├── models.py            # 데이터 모델
├── utils.py             # 유틸리티
├── config.py            # 설정
└── requirements.txt     # 의존성
```

**특징**:
- 플랫 구조, 최소한의 폴더
- 기능별 파일 분리
- 단순한 의존성 관리

### 중형 프로젝트 (Medium) - 파일 10-50개
```
project/
├── src/                 # 소스 코드
│   ├── models/         # 도메인별 모델
│   ├── services/       # 비즈니스 로직
│   ├── api/           # API 엔드포인트
│   └── utils/         # 공통 유틸리티
├── tests/              # 테스트
├── docs/              # 문서
└── scripts/           # 스크립트
```

**특징**:
- 계층적 구조 도입
- 도메인별 분리 시작
- 테스트 및 문서 체계화

### 대형 프로젝트 (Large) - 파일 50개 이상
```
project/
├── src/
│   ├── domains/           # 도메인별 모듈
│   │   ├── user/         # 사용자 도메인
│   │   ├── product/      # 상품 도메인
│   │   └── order/        # 주문 도메인
│   ├── shared/           # 공유 컴포넌트
│   │   ├── database/     # DB 관련
│   │   ├── auth/         # 인증
│   │   └── utils/        # 유틸리티
│   └── infrastructure/   # 인프라 코드
├── tests/
├── docs/
├── tools/               # 개발 도구
└── deployment/          # 배포 설정
```

**특징**:
- 도메인 중심 아키텍처
- 명확한 경계와 책임 분리
- 확장 가능한 구조

## 🔧 MCP 도구 통합 전략

### 필수 MCP 도구
1. **Tavily** - 웹 연구 및 트렌드 분석 (월 1,000 크레딧 무료)
2. **Supabase** - 데이터베이스 스키마 기반 개발 (프로젝트별)
3. **Web Search** - 기본 웹 검색 (완전 무료)

### 선택적 MCP 도구
- **OpenRouter** - 고급 코드 리뷰 및 생성
- **GitHub** - 코드 저장소 분석
- **Web Search** - 기본 웹 검색 (완전 무료)

### MCP 활용 워크플로우
```python
# 1. 프로젝트 조사
프롬프트: "최신 Python 웹 프레임워크 트렌드 use context7"

# 2. 상세 분석
프롬프트: "FastAPI vs Django 성능 비교 및 생산성 분석"
도구: Tavily (상세 연구)

# 3. 스키마 설계
프롬프트: "사용자/제품/주문 엔티티 관계 설계"
도구: Supabase MCP

# 4. 코드 생성
프롬프트: "RESTful API 엔드포인트 자동 생성"
도구: OpenRouter (코드 특화 모델)
```

## 📱 언어별 구조 패턴

### Python 프로젝트
```python
# 소형: main.py, models.py, utils.py
# 중형: src/ 구조 + 도메인 분리
# 대형: DDD 패턴 + 클린 아키텍처

# 공통 파일
pyproject.toml          # 프로젝트 메타데이터 (권장)
requirements.txt        # 의존성 (호환성용)
.env.example           # 환경변수 템플릿
pytest.ini             # 테스트 설정
```

### JavaScript/TypeScript 프로젝트
```javascript
// 소형: index.js, models.js, utils.js
// 중형: src/ + components/, services/
// 대형: domains/, shared/, features/

// 공통 파일
package.json           # 의존성 및 스크립트
tsconfig.json         # TypeScript 설정
.eslintrc.js          # 코드 스타일
jest.config.js        # 테스트 설정
```

## 🎨 명명 규칙 전략

### 적응형 명명 규칙

#### 소형 프로젝트 (개발자 1-2명)
- **파일명**: 짧고 직관적 (`user.py`, `api.py`)
- **함수명**: 동사_명사 (`get_user`, `create_order`)
- **클래스명**: PascalCase (`UserService`, `OrderModel`)

#### 중형 프로젝트 (개발자 3-5명)
- **파일명**: 도메인 접두사 (`user_service.py`, `order_api.py`)
- **모듈명**: 도메인별 그룹화 (`user/`, `order/`)
- **함수명**: 도메인_동작 (`user_get_profile`, `order_create`)

#### 대형 프로젝트 (개발자 6명 이상)
- **네임스페이스**: 완전한 경로 (`domains.user.services.profile`)
- **인터페이스**: 명시적 계약 (`IUserRepository`, `IOrderService`)
- **이벤트**: 도메인_이벤트 (`UserRegistered`, `OrderPlaced`)

### 데이터베이스 명명 규칙
```sql
-- 테이블: snake_case (복수형)
users, orders, order_items

-- 컬럼: snake_case (단수형)  
user_id, created_at, order_status

-- 인덱스: {table}_{column}_idx
idx_users_email, idx_orders_created_at

-- 외래키: fk_{from_table}_{to_table}
fk_orders_users, fk_order_items_orders
```

## 🔄 개발 생명주기

### 1. 계획 단계 (Planning)
```bash
# MCP 도구 활용한 조사
Context7: "프로젝트에 적합한 기술 스택"
Tavily: "유사 프로젝트 사례 및 베스트 프랙티스"
GitHub: "관련 오픈소스 프로젝트 분석"
```

### 2. 설계 단계 (Design)
```bash
# 아키텍처 설계
Supabase: "데이터베이스 스키마 설계"
OpenRouter: "아키텍처 패턴 검증"
```

### 3. 개발 단계 (Development)
```bash
# 코드 생성 및 리뷰
Context7: "라이브러리별 최신 사용법"
OpenRouter: "코드 리뷰 및 최적화"
Supabase: "타입 및 API 자동 생성"
```

### 4. 테스트 단계 (Testing)
```python
# 테스트 전략
- 단위 테스트: 각 함수/클래스
- 통합 테스트: API 엔드포인트
- E2E 테스트: 전체 워크플로우
```

### 5. 배포 단계 (Deployment)
```bash
# 배포 자동화
Tavily: "배포 베스트 프랙티스 조사"
GitHub: "CI/CD 파이프라인 설정"
```

## 📋 품질 기준

### 코드 품질
- **테스트 커버리지**: 80% 이상
- **순환 복잡도**: 10 이하
- **함수 길이**: 20라인 이하 (권장)

### 성능 기준
- **API 응답 시간**: 200ms 이하
- **메모리 사용량**: 모니터링 및 알림
- **데이터베이스 쿼리**: N+1 문제 방지

### 보안 기준
- **환경변수**: 민감 정보 분리
- **API 키**: 암호화 저장
- **데이터베이스**: 최소 권한 원칙

## 🚀 자동화 도구

### 개발 환경 자동화
```python
# 프로젝트 초기화 스크립트
def setup_project():
    create_directory_structure()
    install_dependencies()
    setup_git_hooks()
    configure_mcp_tools()
    generate_initial_files()
```

### CI/CD 파이프라인
```yaml
# .github/workflows/main.yml
name: CI/CD Pipeline
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
      - name: Run tests
        run: pytest --cov=src/
```

## 📈 성과 측정

### 개발 생산성 지표
- **코드 작성 속도**: 일일 코드 라인 수
- **버그 수정 시간**: 이슈 해결 평균 시간
- **배포 빈도**: 주간 배포 횟수

### 품질 지표
- **버그 발생률**: 배포당 버그 수
- **기술 부채**: 코드 냄새 및 복잡도
- **사용자 만족도**: 성능 및 안정성

## 🎯 팀별 적용 가이드

### 스타트업 (1-3명)
- 소형 프로젝트 구조 사용
- MCP 무료 도구 집중 활용
- 빠른 프로토타이핑 우선

### 성장 기업 (4-10명)
- 중형 프로젝트 구조로 전환
- 코드 리뷰 프로세스 도입
- 자동화 도구 적극 활용

### 대기업 (11명 이상)
- 대형 프로젝트 구조 적용
- 엄격한 품질 기준 적용
- 완전한 자동화 파이프라인

이 프레임워크는 프로젝트와 팀의 성장에 따라 자연스럽게 진화할 수 있도록 설계되었습니다. 각 단계에서 필요한 복잡성만을 도입하여 과도한 설계를 방지하면서도, 확장 가능한 기반을 제공합니다. 