#!/usr/bin/env python3
"""
간단한 프레임워크 검증기
"""

import ast
from pathlib import Path
from typing import Dict, List


class ValidationHelper:
    """검증 도우미 클래스"""
    
    @staticmethod
    def check_file_size(file_path: Path, max_lines: int = 200) -> bool:
        """파일 크기 검사"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return len(f.readlines()) <= max_lines
        except (OSError, UnicodeDecodeError):
            return True
    
    @staticmethod
    def check_python_structures(file_path: Path) -> Dict:
        """Python 파일의 클래스/함수 크기 검사"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            tree = ast.parse(content)
        except (OSError, UnicodeDecodeError, SyntaxError):
            return {}
        
        issues = []
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                lines = node.end_lineno - node.lineno + 1
                if lines > 50:
                    issues.append(f"클래스 {node.name}: {lines}라인")
            elif isinstance(node, ast.FunctionDef):
                lines = node.end_lineno - node.lineno + 1
                if lines > 15:
                    issues.append(f"함수 {node.name}: {lines}라인")
        return {"file": str(file_path), "issues": issues}


class SimpleValidator:
    """간단한 프레임워크 검증기"""
    
    def __init__(self, framework_path: Path):
        self.framework_path = framework_path
        self.issues = []
        self.helper = ValidationHelper()
    
    def validate_all(self) -> List[Dict]:
        """모든 검증 실행"""
        print("🔍 프레임워크 검증 시작...")
        self._check_file_sizes()
        self._check_naming()
        self._check_docs()
        return self.issues
    
    def _check_file_sizes(self):
        """파일 크기 검증"""
        print("📏 파일 크기 검증 중...")
        
        for py_file in self.framework_path.rglob("*.py"):
            # 파일 크기 검사
            if not self.helper.check_file_size(py_file):
                self.issues.append(f"파일 크기 초과: {py_file}")
            
            # 클래스/함수 크기 검사
            result = self.helper.check_python_structures(py_file)
            for issue in result.get("issues", []):
                rel_path = py_file.relative_to(self.framework_path)
                self.issues.append(f"크기 초과: {rel_path} - {issue}")
    
    def _check_naming(self):
        """명명 규칙 검증"""
        print("📝 명명 규칙 검증 중...")
        
        for py_file in self.framework_path.rglob("*.py"):
            stem = py_file.stem
            if len(stem) > 12:
                rel_path = py_file.relative_to(self.framework_path)
                msg = f"파일명 길이 초과: {rel_path} - {stem}: {len(stem)}자"
                self.issues.append(msg)
    
    def _check_docs(self):
        """문서 검증"""
        print("📚 문서 검증 중...")
        
        prohibited_terms = []
        for md_file in self.framework_path.rglob("*.md"):
            try:
                content = md_file.read_text(encoding='utf-8').lower()
                for term in prohibited_terms:
                    if term in content:
                        rel_path = md_file.relative_to(self.framework_path)
                        msg = f"프로젝트별 용어 발견: {rel_path} - '{term}'"
                        self.issues.append(msg)
            except (OSError, UnicodeDecodeError):
                continue


def main():
    """메인 실행 함수"""
    framework_path = Path(__file__).parent.parent
    validator = SimpleValidator(framework_path)
    issues = validator.validate_all()
    
    if issues:
        print(f"\n⚠️ {len(issues)}개 이슈 발견:")
        for issue in issues:
            print(f"- {issue}")
    else:
        print("\n✅ 모든 검증 통과!")


if __name__ == "__main__":
    main() 