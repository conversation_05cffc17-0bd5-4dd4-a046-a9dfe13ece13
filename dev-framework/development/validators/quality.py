"""
코드 품질 검증기
"""

import ast

from .base import BaseValidator, SeverityLevel


class QualityValidator(BaseValidator):
    """코드 품질 검증"""
    
    def validate(self):
        """코드 품질 검증 실행"""
        print("🏗️ 코드 품질 검증 중...")
        
        for py_file in self.framework_path.rglob("*.py"):
            self._check_file_size(py_file)
            self._check_python_quality(py_file)
            
        return self.results
        
    def _check_file_size(self, py_file):
        """파일 크기 검증"""
        try:
            lines = py_file.read_text(encoding='utf-8').splitlines()
            line_count = len([l for l in lines if l.strip() and not l.strip().startswith('#')])
            
            if line_count > 200:
                self.add_result(
                    "QUALITY_FILE_SIZE", SeverityLevel.ERROR,
                    f"파일이 너무 큽니다: {py_file.name} ({line_count}라인)",
                    self.get_relative_path(py_file), "파일을 200라인 이하로 분할하세요"
                )
        except Exception:
            pass
            
    def _check_python_quality(self, py_file):
        """Python 코드 품질 검증"""
        try:
            content = py_file.read_text(encoding='utf-8')
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    self._check_class_size(node, py_file)
                elif isinstance(node, ast.FunctionDef):
                    self._check_function_size(node, py_file)
        except Exception:
            pass
            
    def _check_class_size(self, node, py_file):
        """클래스 크기 검증"""
        class_lines = node.end_lineno - node.lineno + 1
        if class_lines > 50:
            self.add_result(
                "QUALITY_CLASS_SIZE", SeverityLevel.ERROR,
                f"클래스가 너무 큽니다: {node.name} ({class_lines}라인)",
                self.get_relative_path(py_file), "클래스를 50라인 이하로 분할하세요",
                node.lineno
            )
            
    def _check_function_size(self, node, py_file):
        """함수 크기 검증"""
        func_lines = node.end_lineno - node.lineno + 1
        if func_lines > 15:
            self.add_result(
                "QUALITY_FUNCTION_SIZE", SeverityLevel.ERROR,
                f"함수가 너무 큽니다: {node.name} ({func_lines}라인)",
                self.get_relative_path(py_file), "함수를 15라인 이하로 분할하세요",
                node.lineno
            ) 