"""
중복 코드 검증기
"""

from pathlib import Path
from typing import Dict, List

from .base import BaseValidator


class CodeAnalyzer:
    """코드 분석기"""
    
    @staticmethod
    def get_function_names(file_path: Path) -> List[str]:
        """함수명 추출"""
        import ast
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                tree = ast.parse(f.read())
            return [node.name for node in ast.walk(tree) 
                   if isinstance(node, ast.FunctionDef)]
        except (OSError, UnicodeDecodeError, SyntaxError):
            return []
    
    @staticmethod
    def check_similar_functions(functions: List[str]) -> List[str]:
        """유사 함수명 검사"""
        duplicates = []
        for i, func1 in enumerate(functions):
            for func2 in functions[i+1:]:
                if func1.lower() == func2.lower() and func1 != func2:
                    duplicates.append(f"{func1} ≈ {func2}")
        return duplicates


class DuplicationValidator(BaseValidator):
    """중복 검증기"""
    
    def __init__(self, framework_path: Path):
        super().__init__(framework_path)
        self.analyzer = CodeAnalyzer()
    
    def validate(self) -> List[Dict]:
        """중복 검증"""
        all_functions = []
        for py_file in self.framework_path.rglob("*.py"):
            functions = self.analyzer.get_function_names(py_file)
            all_functions.extend(functions)
        
        duplicates = self.analyzer.check_similar_functions(all_functions)
        for dup in duplicates:
            self.add_issue("DUP_FUNCTION", None, f"유사 함수: {dup}")
        
        return self.issues 