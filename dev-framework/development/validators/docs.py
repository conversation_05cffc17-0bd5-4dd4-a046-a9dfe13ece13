"""
문서 검증기
"""

from pathlib import Path
from typing import Dict, List

from .base import BaseValidator


class TermChecker:
    """용어 검사 클래스"""
    
    @staticmethod
    def check_prohibited_terms(content: str) -> List[str]:
        """금지 용어 검사"""
        prohibited = []
        found_terms = []
        content_lower = content.lower()
        for term in prohibited:
            if term in content_lower:
                found_terms.append(term)
        return found_terms
    
    @staticmethod
    def check_readme_sections(content: str) -> List[str]:
        """README 필수 섹션 검사"""
        required = ['사용법', '설치', '기능']
        missing = []
        content_lower = content.lower()
        for section in required:
            if section not in content_lower:
                missing.append(section)
        return missing


class DocumentationValidator(BaseValidator):
    """문서 검증기"""
    
    def __init__(self, framework_path: Path):
        super().__init__(framework_path)
        self.checker = TermChecker()
    
    def validate(self) -> List[Dict]:
        """문서 검증"""
        self._check_prohibited_terms()
        self._check_readme()
        return self.issues
        
    def _check_prohibited_terms(self):
        """금지 용어 검사"""
        for md_file in self.framework_path.rglob("*.md"):
            try:
                content = md_file.read_text(encoding='utf-8')
                terms = self.checker.check_prohibited_terms(content)
                for term in terms:
                    msg = f"금지 용어 '{term}' 발견"
                    self.add_issue("DOCS_PROHIBITED_TERM", md_file, msg)
            except (OSError, UnicodeDecodeError):
                continue
    
    def _check_readme(self):
        """README 검사"""
        readme_path = self.framework_path / "README.md"
        if readme_path.exists():
            try:
        content = readme_path.read_text(encoding='utf-8')
                missing = self.checker.check_readme_sections(content)
                for section in missing:
                    msg = f"필수 섹션 '{section}' 누락"
                    self.add_issue("DOCS_MISSING_SECTION", readme_path, msg)
            except (OSError, UnicodeDecodeError):
                pass 