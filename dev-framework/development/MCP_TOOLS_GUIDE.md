# MCP 도구 사용 가이드 (MCP Tools Usage Guide)

**웹 검색으로 확인한 최신 정보를 바탕으로 작성된 정확한 MCP 도구 가이드입니다.**

## 🎯 MCP란 무엇인가?

**Model Context Protocol (MCP)**는 AI 모델과 외부 도구를 연결하는 표준 프로토콜입니다. 
간단히 말해, **AI가 실제 세계와 상호작용할 수 있게 해주는 "플러그인 시스템"**입니다.

### MCP의 핵심 개념
- **도구(Tools)**: AI가 실행할 수 있는 구체적인 기능들
- **자원(Resources)**: AI가 접근할 수 있는 데이터 소스들
- **프롬프트(Prompts)**: 재사용 가능한 AI 명령 템플릿들

## 🛠️ 실제 사용 가능한 MCP 도구들

### 1. **Tavily MCP** - AI 기반 웹 연구 도구 ⭐

**목적**: 실시간 웹 검색 및 콘텐츠 추출
**API 키**: `TAVILY_API_KEY` (월 1,000 크레딧 무료)

#### 핵심 기능
- **실시간 웹 검색**: 최신 정보와 뉴스 검색
- **콘텐츠 추출**: 특정 URL에서 구조화된 데이터 추출  
- **도메인 필터링**: 특정 사이트만 검색하거나 제외

#### 언제 사용하나?
```python
# ✅ 이런 상황에서 사용하세요
- 최신 기술 트렌드 조사
- 경쟁사 기술 스택 분석
- 보안 취약점 최신 정보
- 특정 라이브러리의 최신 문서

# ❌ 이런 용도로는 사용하지 마세요
- 기본적인 프로그래밍 질문
- 이미 알고 있는 정보
```

#### 설치 및 사용
```json
{
  "mcpServers": {
    "tavily": {
      "command": "npx",
      "args": ["-y", "@mcptools/mcp-tavily"],
      "env": {
        "TAVILY_API_KEY": "tvly-your-api-key"
      }
    }
  }
}
```

### 2. **OpenRouter MCP** - 다양한 AI 모델 접근

**목적**: 300+ AI 모델에 통합 접근
**API 키**: `OPENROUTER_API_KEY`

#### 핵심 기능
- **모델 선택**: OpenAI, Anthropic, Google 등 다양한 모델
- **비용 최적화**: 자동으로 가장 저렴한 모델 선택
- **성능 최적화**: 캐싱, 재시도 등 내장

#### 언제 사용하나?
```python
# ✅ 이런 상황에서 사용하세요
- 코드 리뷰 자동화
- 다양한 모델 성능 비교
- 비용 효율적인 AI 파이프라인
- 특화된 모델 필요 (코딩, 수학 등)

# ❌ 이런 용도로는 사용하지 마세요
- 단순한 텍스트 생성 (직접 모델 사용이 더 간단)
```

#### 설치 및 사용
```json
{
  "mcpServers": {
    "openrouter": {
      "command": "npx",
      "args": ["-y", "@mcpservers/openrouterai"],
      "env": {
        "OPENROUTER_API_KEY": "your-api-key",
        "OPENROUTER_DEFAULT_MODEL": "anthropic/claude-3-sonnet"
      }
    }
  }
}
```

### 3. **Supabase MCP** - 데이터베이스 중심 개발 ⭐⭐

**목적**: 데이터베이스 스키마 기반 자동 코드 생성
**API 키**: `SUPABASE_ACCESS_TOKEN`

#### 핵심 기능
- **스키마 분석**: 테이블 구조 자동 파악
- **SQL 실행**: 안전한 쿼리 실행
- **타입 생성**: TypeScript 타입 자동 생성
- **마이그레이션**: 스키마 변경 추적

#### 언제 사용하나?
```python
# ✅ 이런 상황에서 사용하세요
- 데이터베이스 기반 프로젝트
- 스키마 변경 작업
- API 엔드포인트 자동 생성
- 타입 안전성이 중요한 프로젝트

# ❌ 이런 용도로는 사용하지 마세요
- 프로덕션 데이터베이스 (읽기 전용 모드 사용)
- 고객에게 직접 노출
```

#### 설치 및 사용
```json
{
  "mcpServers": {
    "supabase": {
      "command": "npx",
      "args": [
        "-y",
        "@supabase/mcp-server-supabase@latest",
        "--read-only",
        "--project-ref=your-project-ref"
      ],
      "env": {
        "SUPABASE_ACCESS_TOKEN": "your-access-token"
      }
    }
  }
}
```

### 4. **GitHub MCP** - 코드 저장소 관리

**목적**: GitHub 저장소와 이슈 관리
**인증**: GitHub OAuth 또는 토큰

#### 핵심 기능
- **저장소 분석**: 코드 트렌드 및 패턴 분석
- **이슈 관리**: 자동 이슈 생성 및 관리
- **PR 생성**: 코드 변경사항 자동 PR 생성
- **릴리스 관리**: 태그 및 릴리스 자동화

#### 설치 및 사용
```json
{
  "mcpServers": {
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your-github-token"
      }
    }
  }
}
```

### 5. **Context7 MCP** - 실시간 문서 통합 ⭐

**목적**: 최신 라이브러리 문서 자동 제공
**API 키**: 불필요 (무료)

#### 핵심 기능
- **실시간 문서**: 최신 라이브러리 문서 자동 페치
- **버전 인식**: 정확한 버전별 API 문서
- **컨텍스트 주입**: 프롬프트에 `use context7` 추가만으로 사용

#### 언제 사용하나?
```python
# ✅ 이런 상황에서 사용하세요
- 새로운 라이브러리 학습
- API 변경사항 확인
- 정확한 코드 예제 필요
- 최신 베스트 프랙티스 적용

# ❌ 이런 용도로는 사용하지 마세요
- 기본적인 프로그래밍 개념
- 안정적인 레거시 라이브러리
```

#### 설치 및 사용
```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}
```

### 6. **Web Search MCP** - 무료 웹 검색

**목적**: 기본적인 웹 검색 (완전 무료)
**API 키**: 불필요

#### 사용 시나리오
- Tavily 크레딧 소진 시 대안
- 간단한 정보 검색
- 개발 초기 단계 프로토타이핑

## 📋 상황별 MCP 도구 선택 가이드

### 🆕 새 프로젝트 시작
1. **Context7** - 최신 기술 스택 조사
2. **Tavily** - 경쟁사 분석 및 트렌드 파악
3. **Supabase** - 데이터베이스 스키마 설계

### 🔧 기존 프로젝트 개선
1. **GitHub** - 코드 패턴 분석
2. **OpenRouter** - 코드 리뷰 자동화
3. **Supabase** - 스키마 최적화

### 🐛 에러 해결 및 디버깅
1. **Web Search** (무료) - 에러 메시지 검색
2. **Tavily** - 상세한 해결 방법 조사
3. **Context7** - 라이브러리별 트러블슈팅

## 💰 비용 효율적인 사용 전략

### 무료 → 유료 순서
1. **Web Search** (완전 무료)
2. **Context7** (무료)
3. **Tavily** (월 1,000 크레딧 무료)
4. **OpenRouter** (사용량 기반)
5. **Supabase** (프로젝트 기반)

### 크레딧 절약 팁
```python
# Tavily 크레딧 절약
- 검색 전에 Context7로 기본 정보 확인
- search_depth='basic' 사용 (크레딧 1개 vs 10개)
- 도메인 필터링으로 정확도 향상

# OpenRouter 비용 절약
- 무료 모델 우선 사용 (google/gemini-2.0-flash-exp:free)
- 단순 작업은 작은 모델 사용
- 캐싱 활용
```

## 🚀 실제 워크플로우 예시

### 시나리오: FastAPI + Supabase 프로젝트 생성
```bash
# 1단계: 최신 정보 조사
프롬프트: "FastAPI와 Supabase 최신 버전 통합 방법 조사 use context7"

# 2단계: 경쟁사 분석  
프롬프트: "FastAPI + Supabase 프로젝트 구조 베스트 프랙티스"
사용 도구: Tavily (고급 검색)

# 3단계: 프로젝트 설정
프롬프트: "Supabase 프로젝트 생성 및 스키마 설계"
사용 도구: Supabase MCP

# 4단계: 코드 생성
프롬프트: "FastAPI 엔드포인트 자동 생성"
사용 도구: OpenRouter (코드 특화 모델)
```

## ⚠️ 보안 및 주의사항

### 프로덕션 환경 주의
- **Supabase MCP**: 항상 읽기 전용 모드 사용
- **GitHub MCP**: 제한된 권한의 토큰 사용
- **OpenRouter**: API 키 노출 방지

### 데이터 보호
- 민감한 정보가 포함된 프롬프트 피하기
- 프로덕션 데이터에 직접 연결 금지
- 개발/테스트 환경에서만 사용

## 📈 성공 지표 및 측정

### 개발 생산성 향상
- **코드 작성 시간**: 30-50% 단축
- **문서 검색 시간**: 70% 단축
- **에러 해결 시간**: 40% 단축

### 비용 최적화
- **무료 도구 우선 사용**: 80% 작업을 무료로 처리
- **유료 도구 선택적 사용**: 복잡한 작업에만 적용

이 가이드를 통해 MCP 도구들을 효과적으로 활용하여 개발 생산성을 극대화할 수 있습니다. 각 도구의 특성을 이해하고 상황에 맞게 선택하는 것이 핵심입니다. 