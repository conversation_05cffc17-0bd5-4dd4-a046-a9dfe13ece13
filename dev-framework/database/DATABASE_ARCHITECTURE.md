# 범용 데이터베이스 아키텍처 가이드 (Universal Database Architecture Guide)

## 개요

이 가이드는 어떤 도메인(전자상거래, IoT, 금융, 헬스케어 등)에서도 적용할 수 있는 데이터베이스 아키텍처 패턴을 제공합니다.

## 아키텍처 패턴

### 1. 레거시 시스템 보존 패턴 (Legacy Preservation Pattern)

기존 프로덕션 시스템을 중단 없이 유지하면서 새로운 기능을 추가하는 패턴입니다.

#### 예시: 기존 시스템 보존
```sql
-- 기존 프로덕션 테이블 (보존)
CREATE TABLE legacy_records (
    id SERIAL PRIMARY KEY,
    entity_id INTEGER NOT NULL,
    performance_data JSONB,        -- 핵심 성능 지표
    metadata JSONB,                -- 메타데이터
    created_at TIMESTAMP DEFAULT NOW()
);
```

**핵심 원칙**:
- ⚠️ **기존 워크플로우 절대 변경 금지**
- 새로운 테이블은 **추가적(ADDITIONAL)** 용도로만 사용
- 기존 프로덕션 시스템과 독립적으로 운영

### 2. 범용 엔티티 시스템 (Generic Entity System)

다양한 엔티티 타입을 지원하는 확장 가능한 구조입니다.

```sql
-- 범용 엔티티 테이블
CREATE TABLE entities (
    id SERIAL PRIMARY KEY,
    external_id VARCHAR(100) UNIQUE NOT NULL,
    entity_type VARCHAR(50) NOT NULL,        -- 'customer', 'product', 'device', 'transaction'
    entity_name VARCHAR(200),
    category VARCHAR(50),                    -- 'retail', 'iot', 'finance', 'healthcare'
    metadata JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 범용 스케줄 테이블
CREATE TABLE schedules (
    id SERIAL PRIMARY KEY,
    entity_id INTEGER REFERENCES entities(id),
    event_type VARCHAR(50),                  -- 'purchase', 'maintenance', 'payment', 'appointment'
    event_date DATE NOT NULL,
    event_time TIME,
    details JSONB,
    status VARCHAR(20) DEFAULT 'scheduled'
);

-- 범용 성능 테이블
CREATE TABLE performance_metrics (
    id SERIAL PRIMARY KEY,
    entity_id INTEGER REFERENCES entities(id),
    metric_type VARCHAR(50),                 -- 'sales', 'operational', 'financial', 'health'
    metric_data JSONB,
    measurement_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 3. 도메인별 사용 예시

#### 전자상거래 도메인
```sql
-- entities 테이블 사용
INSERT INTO entities (external_id, entity_type, entity_name, category) VALUES
('store_001', 'store', 'Main Store', 'retail'),
('product_001', 'product', 'Laptop Pro', 'retail');

-- 성능 데이터
INSERT INTO performance_metrics (entity_id, metric_type, metric_data) VALUES
(1, 'sales', '{"revenue": 150000, "orders": 245, "conversion_rate": 0.032}'),
(2, 'inventory', '{"stock_level": 45, "reorder_point": 10, "turnover_rate": 6.2}');
```

#### IoT/센서 도메인
```sql
-- entities 테이블 사용
INSERT INTO entities (external_id, entity_type, entity_name, category) VALUES
('sensor_001', 'device', 'Temperature Sensor', 'iot'),
('gateway_001', 'gateway', 'Building A Gateway', 'iot');

-- 성능 데이터
INSERT INTO performance_metrics (entity_id, metric_type, metric_data) VALUES
(1, 'operational', '{"temperature": 23.5, "humidity": 45.2, "battery_level": 87}'),
(2, 'network', '{"uptime": 99.8, "data_throughput": 1024, "error_rate": 0.001}');
```

#### 금융 도메인
```sql
-- entities 테이블 사용
INSERT INTO entities (external_id, entity_type, entity_name, category) VALUES
('account_001', 'account', 'Business Account', 'finance'),
('portfolio_001', 'portfolio', 'Growth Portfolio', 'finance');

-- 성능 데이터
INSERT INTO performance_metrics (entity_id, metric_type, metric_data) VALUES
(1, 'financial', '{"balance": 50000, "transactions": 127, "fees": 25.50}'),
(2, 'investment', '{"value": 125000, "return_rate": 0.087, "risk_score": 6.2}');
```

## 아키텍처 결정 가이드

### 언제 레거시 보존 패턴을 사용하나?
- 기존 프로덕션 시스템이 안정적으로 운영 중인 경우
- 중단 없는 서비스 확장이 필요한 경우
- 점진적 마이그레이션을 계획하는 경우

### 언제 범용 시스템을 사용하나?
- 새로운 프로젝트를 시작하는 경우
- 다양한 엔티티 타입을 지원해야 하는 경우
- 향후 확장성이 중요한 경우

## 서비스 아키텍처 패턴

### 패턴 1: 분리된 서비스 레이어
```
도메인별 워커 → 도메인별 서비스 → 도메인별 테이블
```

### 패턴 2: 통합 서비스 레이어
```
범용 워커 → 범용 서비스 → 범용 테이블
```

### 예시: 하이브리드 아키텍처
```
Legacy Workflow (기존):
legacy_processor.py → LegacyService → legacy_records table

New Generic Workflow (신규):  
generic_processor.py → GenericDatabaseService → entities/schedules/performance_metrics tables
```

## 마이그레이션 전략

### 단계별 마이그레이션
1. **Phase 1**: 범용 테이블 생성 (기존 시스템과 병렬)
2. **Phase 2**: 새로운 기능을 범용 시스템으로 구현
3. **Phase 3**: 기존 시스템 계속 운영 (변경 없음)
4. **Phase 4**: 선택적 - 기존 데이터를 범용 시스템으로 마이그레이션

### 위험 최소화 원칙
- 기존 워크플로우는 **절대 변경하지 않음**
- 새로운 테이블은 **추가적** 용도로만 사용
- 범용 테이블은 **확장 기능** 제공
- 기존 시스템과 **독립적으로** 운영

## 인덱스 및 성능 최적화

```sql
-- 범용 테이블 인덱스
CREATE INDEX idx_entities_type_category ON entities(entity_type, category);
CREATE INDEX idx_entities_external_id ON entities(external_id);
CREATE INDEX idx_schedules_entity_date ON schedules(entity_id, event_date);
CREATE INDEX idx_performance_entity_type ON performance_metrics(entity_id, metric_type);

-- JSONB 필드 인덱스
CREATE INDEX idx_performance_data ON performance_metrics USING GIN (metric_data);
CREATE INDEX idx_entity_metadata ON entities USING GIN (metadata);
```

## 확장성 고려사항

### 수평적 확장 (Horizontal Scaling)
- 샤딩 키: `entity_type` 또는 `category`
- 파티셔닝 전략: 날짜 기반 또는 해시 기반

### 수직적 확장 (Vertical Scaling)
- 읽기 전용 복제본 활용
- 캐싱 레이어 구현
- 인덱스 최적화

이 가이드는 어떤 도메인(전자상거래, IoT, 금융, 헬스케어 등)에서도 적용할 수 있는 범용적인 데이터베이스 아키텍처 패턴을 제공합니다.