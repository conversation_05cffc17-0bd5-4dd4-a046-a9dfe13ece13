# JSONB 데이터 구조 일관성 가이드

## 목적

데이터베이스의 JSONB 컬럼에 데이터를 저장할 때, 일관된 구조를 유지하여 데이터 무결성과 쿼리 성능을 보장하는 방법을 제시합니다.

---

## 1. 핵심 원칙: 키 래핑 (Key Wrapping)

### ✅ 올바른 방법: 키로 감싸서 저장
```python
# 권장: 구조화된 키 래핑
update_data = {
    "performance_data": {"metrics": performance_metrics},
    "user_profile": {"profile": user_information},
    "activity_logs": {"activities": activity_list}
}
```

### ❌ 잘못된 방법: 평평한 구조
```python
# 비권장: 평평한 딕셔너리 직접 저장
update_data = {
    "performance_data": performance_metrics,      # 구조 일관성 깨짐
    "user_profile": user_information,            # 쿼리 복잡성 증가
    "activity_logs": activity_list               # 데이터 검증 어려움
}
```

---

## 2. 데이터베이스 서비스 계층에서 구현

### (1) 기본 서비스 클래스
```python
class DatabaseService:
    """데이터베이스 서비스 - JSONB 일관성 보장"""
    
    def __init__(self, db_client):
        self.db = db_client
    
    def save_structured_data(self, table: str, entity_id: int, updates: dict) -> bool:
        """구조화된 데이터 저장"""
        
        # 키 래핑 적용
        wrapped_data = self._wrap_data_keys(updates)
        
        # 데이터베이스에 저장
        return self.db.update(table, entity_id, wrapped_data)
    
    def _wrap_data_keys(self, data: dict) -> dict:
        """데이터를 적절한 키로 래핑"""
        wrapped = {}
        
        for column, raw_data in data.items():
            if column == "performance_data":
                wrapped[column] = {"metrics": raw_data}
            elif column == "activity_data":
                wrapped[column] = {"activities": raw_data}
            elif column == "user_profile":
                wrapped[column] = {"profile": raw_data}
            else:
                # 기본 래핑 전략
                wrapped[column] = {"data": raw_data}
        
        return wrapped
```

### (2) 도메인별 서비스 구현

#### 전자상거래 도메인
```python
class EcommerceDataService(DatabaseService):
    """전자상거래 데이터 서비스"""
    
    def save_order_data(self, order_id: int, order_data: dict) -> bool:
        """주문 데이터 저장"""
        
        structured_data = {
            "order_details": {
                "items": order_data.get("items", []),
                "total": order_data.get("total", 0),
                "currency": order_data.get("currency", "USD")
            },
            "customer_info": {
                "customer_id": order_data.get("customer_id"),
                "shipping_address": order_data.get("shipping_address")
            },
            "payment_data": {
                "method": order_data.get("payment_method"),
                "status": order_data.get("payment_status", "pending")
            }
        }
        
        return self.db.update("orders", order_id, structured_data)
```

#### IoT/센서 도메인
```python
class IoTDataService(DatabaseService):
    """IoT 센서 데이터 서비스"""
    
    def save_sensor_reading(self, sensor_id: int, sensor_data: dict) -> bool:
        """센서 데이터 저장"""
        
        structured_data = {
            "sensor_readings": {
                "temperature": sensor_data.get("temperature"),
                "humidity": sensor_data.get("humidity"),
                "timestamp": sensor_data.get("timestamp")
            },
            "device_status": {
                "battery_level": sensor_data.get("battery", 100),
                "signal_strength": sensor_data.get("signal", 0),
                "error_count": sensor_data.get("errors", 0)
            },
            "location_data": {
                "latitude": sensor_data.get("lat"),
                "longitude": sensor_data.get("lng"),
                "altitude": sensor_data.get("alt", 0)
            }
        }
        
        return self.db.update("sensor_records", sensor_id, structured_data)
```

---

## 3. 데이터 검색 및 쿼리 패턴

### (1) JSONB 쿼리 최적화
```sql
-- ✅ 올바른 JSONB 쿼리 (키 래핑 활용)
SELECT * FROM orders 
WHERE order_details->>'total' > '1000';

SELECT * FROM sensor_records 
WHERE sensor_readings->>'temperature' > '25.0';

-- GIN 인덱스 활용
CREATE INDEX idx_orders_details ON orders USING GIN (order_details);
CREATE INDEX idx_sensor_readings ON sensor_records USING GIN (sensor_readings);
```

### (2) Python에서의 데이터 접근
```python
class DataRetriever:
    """구조화된 JSONB 데이터 조회"""
    
    def get_order_total(self, order_record: dict) -> float:
        """주문 총액 조회"""
        order_details = order_record.get("order_details", {})
        return float(order_details.get("total", 0))
    
    def get_sensor_temperature(self, sensor_record: dict) -> float:
        """센서 온도 조회"""
        readings = sensor_record.get("sensor_readings", {})
        return float(readings.get("temperature", 0))
    
    def get_customer_shipping(self, order_record: dict) -> dict:
        """배송 정보 조회"""
        customer_info = order_record.get("customer_info", {})
        return customer_info.get("shipping_address", {})
```

---

## 4. 마이그레이션 및 데이터 일관성

### (1) 기존 데이터 구조 변경
```python
def migrate_flat_to_wrapped(db_client, table_name: str):
    """평평한 구조를 래핑된 구조로 마이그레이션"""
    
    records = db_client.query(f"SELECT * FROM {table_name}")
    
    for record in records:
        record_id = record['id']
        
        # 기존 평평한 데이터를 래핑된 구조로 변환
        wrapped_data = {}
        
        if 'performance_data' in record:
            wrapped_data['performance_data'] = {
                "metrics": record['performance_data']
            }
        
        if 'activity_data' in record:
            wrapped_data['activity_data'] = {
                "activities": record['activity_data']
            }
        
        # 업데이트 실행
        db_client.update(table_name, record_id, wrapped_data)
```

### (2) 데이터 검증 함수
```python
def validate_jsonb_structure(data: dict, expected_keys: list) -> bool:
    """JSONB 구조 검증"""
    
    for column, content in data.items():
        if not isinstance(content, dict):
            return False
        
        # 각 컬럼이 적절한 키로 래핑되어 있는지 확인
        if column in expected_keys:
            expected_wrapper = expected_keys[column]
            if expected_wrapper not in content:
                return False
    
    return True

# 사용 예시
order_validation_keys = {
    "order_details": "items",
    "customer_info": "customer_id", 
    "payment_data": "method"
}

is_valid = validate_jsonb_structure(order_data, order_validation_keys)
```

---

## 5. 성능 최적화 전략

### (1) 인덱스 전략
```sql
-- 전자상거래 최적화
CREATE INDEX idx_orders_total ON orders 
USING BTREE ((order_details->>'total'));

CREATE INDEX idx_orders_status ON orders 
USING BTREE ((payment_data->>'status'));

-- IoT 데이터 최적화
CREATE INDEX idx_sensor_temp ON sensor_records 
USING BTREE ((sensor_readings->>'temperature'));

CREATE INDEX idx_sensor_timestamp ON sensor_records 
USING BTREE ((sensor_readings->>'timestamp'));
```

### (2) 쿼리 최적화
```python
class OptimizedQuery:
    """최적화된 JSONB 쿼리"""
    
    def find_high_value_orders(self, db_client, min_total: float) -> list:
        """고액 주문 조회 (인덱스 활용)"""
        query = """
        SELECT * FROM orders 
        WHERE (order_details->>'total')::numeric > %s
        ORDER BY (order_details->>'total')::numeric DESC
        """
        return db_client.query(query, [min_total])
    
    def find_hot_sensors(self, db_client, max_temp: float) -> list:
        """고온 센서 조회 (인덱스 활용)"""
        query = """
        SELECT * FROM sensor_records 
        WHERE (sensor_readings->>'temperature')::numeric > %s
        AND sensor_readings->>'timestamp' > NOW() - INTERVAL '1 hour'
        """
        return db_client.query(query, [max_temp])
```

---

## 6. 모니터링 및 품질 관리

### (1) 데이터 품질 검사
```python
def audit_jsonb_consistency(db_client, table_name: str) -> dict:
    """JSONB 데이터 일관성 감사"""
    
    issues = {
        "unwrapped_data": [],
        "missing_keys": [],
        "type_mismatches": []
    }
    
    records = db_client.query(f"SELECT * FROM {table_name}")
    
    for record in records:
        record_id = record['id']
        
        # 래핑되지 않은 데이터 검사
        for column, data in record.items():
            if column.endswith('_data') and isinstance(data, dict):
                if not any(isinstance(v, dict) for v in data.values()):
                    issues["unwrapped_data"].append({
                        "id": record_id,
                        "column": column
                    })
    
    return issues
```

### (2) 자동 복구 도구
```python
def auto_fix_jsonb_structure(db_client, table_name: str, issues: dict):
    """JSONB 구조 자동 복구"""
    
    for issue in issues["unwrapped_data"]:
        record_id = issue["id"]
        column = issue["column"]
        
        # 현재 데이터 조회
        current_data = db_client.get_column(table_name, record_id, column)
        
        # 적절한 키로 래핑
        wrapper_key = "data"  # 기본 래핑 키
        if column == "performance_data":
            wrapper_key = "metrics"
        elif column == "activity_data":
            wrapper_key = "activities"
        
        wrapped_data = {wrapper_key: current_data}
        
        # 업데이트 실행
        db_client.update_column(table_name, record_id, column, wrapped_data)
```

이 가이드를 통해 모든 도메인에서 일관되고 효율적인 JSONB 데이터 구조를 구축할 수 있습니다. 