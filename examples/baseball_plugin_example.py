"""
야구 플러그인 사용 예제
새로운 아키텍처를 사용한 야구 데이터 수집 및 처리
"""
import asyncio
import logging
from typing import List, Dict

from core.factories.sport_factory import get_sport_factory, get_plugin_manager, SportType
from sports.baseball import BaseballPlugin
from utils.logger import Logger

# 로거 설정
logger = Logger(__name__, level=logging.INFO)


async def main():
    """야구 플러그인 사용 예제"""
    
    try:
        # 1. 팩토리 및 플러그인 매니저 초기화
        factory = get_sport_factory()
        plugin_manager = get_plugin_manager()
        
        # 2. 야구 플러그인 등록
        baseball_plugin = BaseballPlugin()
        plugin_manager.load_plugin(baseball_plugin)
        
        logger.info("야구 플러그인이 성공적으로 등록되었습니다.")
        
        # 3. 지원하는 스포츠 확인
        supported_sports = factory.get_supported_sports()
        logger.info(f"지원하는 스포츠: {[sport.value for sport in supported_sports]}")
        
        # 4. 야구 플러그인 정보 확인
        baseball_info = factory.get_sport_info(SportType.BASEBALL)
        logger.info(f"야구 플러그인 정보: {baseball_info}")
        
        # 5. 야구 컴포넌트들 생성
        collector = factory.create_collector(SportType.BASEBALL)
        parser = factory.create_parser(SportType.BASEBALL)
        service = factory.create_service(SportType.BASEBALL)
        
        logger.info("야구 컴포넌트들이 성공적으로 생성되었습니다.")
        
        # 6. 전체 스택 생성
        complete_stack = factory.create_complete_stack(SportType.BASEBALL)
        logger.info("야구 전체 스택이 생성되었습니다.")
        
        # 7. 선수 데이터 수집 예제
        await demonstrate_player_collection(collector, parser, service)
        
        # 8. 팀 데이터 처리 예제
        await demonstrate_team_processing(parser, service)
        
    except Exception as e:
        logger.error(f"예제 실행 중 오류 발생: {e}")
    
    finally:
        # 9. 정리
        plugin_manager.shutdown_all()
        logger.info("플러그인 매니저가 종료되었습니다.")


async def demonstrate_player_collection(collector, parser, service):
    """선수 데이터 수집 및 처리 시연"""
    logger.info("\n=== 선수 데이터 수집 및 처리 시연 ===")
    
    # 가상의 선수 데이터
    test_players = ["김광현", "류현진", "최지만"]
    
    # 선수별 추가 정보
    player_data_map = {
        "김광현": {"position": "pitcher", "team": "SSG", "league": "KBO"},
        "류현진": {"position": "pitcher", "team": "TOR", "league": "MLB"}, 
        "최지만": {"position": "first_base", "team": "TB", "league": "MLB"}
    }
    
    try:
        # 1. 데이터 수집
        logger.info("선수 데이터 수집 시작...")
        
        collection_kwargs = {}
        for player in test_players:
            collection_kwargs[f'{player}_data'] = player_data_map[player]
        
        collection_result = await collector.collect_player_stats(
            test_players, 
            **collection_kwargs
        )
        
        logger.info(f"수집 결과: {collection_result.status.value}")
        logger.info(f"총 {collection_result.total_count}명 중 {collection_result.success_count}명 성공")
        
        if collection_result.errors:
            logger.warning(f"수집 중 오류: {collection_result.errors}")
        
        # 2. 데이터 파싱 (가상의 HTML 데이터 사용)
        logger.info("선수 데이터 파싱 시작...")
        
        sample_html = create_sample_player_html("김광현")
        parse_result = parser.parse_player_data(sample_html, "김광현", league="KBO")
        
        logger.info(f"파싱 결과: {parse_result.status.value}")
        if parse_result.data:
            logger.info(f"파싱된 선수 정보: {parse_result.data.player_name}")
        
        # 3. 비즈니스 로직 처리
        logger.info("선수 데이터 비즈니스 처리 시작...")
        
        sample_player_data = {
            "name": "김광현",
            "position": "pitcher",
            "team": "SSG",
            "league": "KBO",
            "stats": {"era": 3.45, "wins": 12, "losses": 8}
        }
        
        service_result = await service.process_player_data(sample_player_data)
        
        logger.info(f"서비스 처리 결과: {service_result.status.value}")
        if service_result.data:
            logger.info(f"처리된 엔티티: {service_result.data.entity_type} - {service_result.data.entity_id}")
        
    except Exception as e:
        logger.error(f"선수 데이터 처리 중 오류: {e}")


async def demonstrate_team_processing(parser, service):
    """팀 데이터 처리 시연"""
    logger.info("\n=== 팀 데이터 처리 시연 ===")
    
    try:
        # 가상의 팀 데이터
        sample_team_data = {
            "name": "SSG 랜더스",
            "league": "KBO",
            "stats": {
                "wins": 80,
                "losses": 62,
                "draws": 2,
                "win_rate": 0.563
            }
        }
        
        # 1. 팀 데이터 파싱
        sample_html = create_sample_team_html("SSG 랜더스")
        parse_result = parser.parse_team_data(sample_html, "SSG 랜더스", league="KBO")
        
        logger.info(f"팀 파싱 결과: {parse_result.status.value}")
        
        # 2. 팀 데이터 비즈니스 처리
        service_result = await service.process_team_data(sample_team_data)
        
        logger.info(f"팀 서비스 처리 결과: {service_result.status.value}")
        if service_result.data:
            logger.info(f"처리된 팀: {service_result.data.entity_id}")
        
    except Exception as e:
        logger.error(f"팀 데이터 처리 중 오류: {e}")


def create_sample_player_html(player_name: str) -> str:
    """샘플 선수 HTML 데이터 생성"""
    return f"""
    <html>
        <body>
            <div class="infoBox">
                <table>
                    <tr><th>이름</th><td>{player_name}</td></tr>
                    <tr><th>포지션</th><td>투수</td></tr>
                    <tr><th>팀</th><td>SSG 랜더스</td></tr>
                    <tr><th>나이</th><td>32</td></tr>
                </table>
            </div>
            <table class="tbl">
                <tr id="home_record">
                    <td>홈</td><td>8</td><td>4</td><td>0</td><td>3.20</td><td>1.25</td>
                </tr>
                <tr id="away_record">
                    <td>원정</td><td>4</td><td>4</td><td>0</td><td>3.70</td><td>1.30</td>
                </tr>
                <tr id="all_record">
                    <td>전체</td><td>12</td><td>8</td><td>0</td><td>3.45</td><td>1.28</td>
                </tr>
            </table>
        </body>
    </html>
    """


def create_sample_team_html(team_name: str) -> str:
    """샘플 팀 HTML 데이터 생성"""
    return f"""
    <html>
        <body>
            <div class="teamInfo">
                <h1>{team_name}</h1>
                <table>
                    <tr><td>승</td><td>80</td></tr>
                    <tr><td>패</td><td>62</td></tr>
                    <tr><td>무</td><td>2</td></tr>
                </table>
            </div>
        </body>
    </html>
    """


if __name__ == "__main__":
    # 비동기 메인 함수 실행
    asyncio.run(main()) 