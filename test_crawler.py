#!/usr/bin/env python3
"""
크롤링 로직 테스트 스크립트
"""
import asyncio
import os
import sys

# 현재 디렉토리를 Python 경로에 추가
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.database import connect_supabase
from sports.baseball.collectors.unified_crawler import UnifiedDataCollector


async def test_crawler():
    """크롤링 로직 테스트"""
    print("🔍 크롤링 테스트 시작")
    
    # Supabase 클라이언트 연결
    client = connect_supabase()
    if not client:
        print("❌ Supabase 연결 실패")
        return
    
    # 크롤러 초기화
    collector = UnifiedDataCollector(client)
    
    # target_games 조회
    target_games = await collector._get_target_games()
    print(f"📊 DB에서 {len(target_games)}개 경기 조회")
    
    if not target_games:
        print("❌ 수집할 경기가 없습니다")
        return
    
    # 첫 번째 경기만 테스트
    test_game = target_games[0]
    print(f"🧪 테스트 경기: {test_game['home_team']} vs {test_game['away_team']}")
    print(f"   날짜: {test_game['match_date']}, 시간: {test_game['match_time']}")
    print(f"   홈팀 ID: {test_game['home_team_id']}, 원정팀 ID: {test_game['away_team_id']}")
    
    # 크롤링 실행
    try:
        crawled_games = await collector._crawl_target_games([test_game])
        print(f"🎯 크롤링 결과: {len(crawled_games)}개")
        
        if crawled_games:
            print("✅ 크롤링 성공!")
            for game in crawled_games:
                print(f"   - {game.get('home_team', 'N/A')} vs {game.get('away_team', 'N/A')}")
        else:
            print("❌ 크롤링 실패 - 결과 없음")
            
    except Exception as e:
        print(f"❌ 크롤링 오류: {e}")
        import traceback
        traceback.print_exc()
    
    # 리소스 정리
    await collector.close()

if __name__ == "__main__":
    asyncio.run(test_crawler()) 