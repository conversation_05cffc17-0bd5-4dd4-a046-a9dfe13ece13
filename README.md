# 📊 Sports Statistics Collection System

<div align="center">

![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)
![Supabase](https://img.shields.io/badge/Database-Supabase-brightgreen.svg)
![Playwright](https://img.shields.io/badge/Automation-Playwright-orange.svg)
![Status](https://img.shields.io/badge/Status-Active-success.svg)

**🏀 차세대 종합 스포츠 통계 데이터 수집 및 분석 플랫폼**

*실시간 스포츠 데이터를 지능적으로 수집하고 분석하는 고성능 시스템*

[시작하기](#-설치-및-실행) • [문서](#-api-문서) • [예제](#-사용-예시) • [기여하기](#-기여-방법)

</div>
📊 Sports Statistics Collection System

🧠 최신 아키텍처 안내 (2025)

🧱 2025년 대구리 리파터링: 메모리 안전성 & 배치 처리 강화
	•	main.py: 오직 스케줄러 역할만 수행 (무거울 import/비습니스 로직 없음)
	•	worker_crawl.py: 모든 데이터 수집/처리/DB 로직 단당 (메모리 안전, 배치 단위 실행)
	•	실행 구조: main.py가 운영시간(08:00~22:00) 동안 20분마다 worker_crawl.py를 서브프리세스로 실행
	•	불필요한 의연성/개발 코드 제거: requirements.txt는 런타임 의연성만 포함
	•	business/worker_logic.py 제거됨

worker_crawl.py는 각 리그별로 10개 단위 배치 처리를 수행하며, 메모리/리소스 사용량을 실시간 로깅하고, 모든 크롤링/DB/후처리 로직을 단당합니다.

⚙️ 실행 구조 요약

graph TD
    A[main.py (스케줄러)] -- 20분마다 --> B[worker_crawl.py (실제 작업)]
    B --> C[DB/크롤링/비습니스 로직]

⏰ 예시: 운영시간 자동 실행

# main.py (스케줄러)
import subprocess
import time
from datetime import datetime

INTERVAL_MINUTES = 20
START_HOUR = 8
END_HOUR = 22

while True:
    now = datetime.now()
    if START_HOUR <= now.hour < END_HOUR:
        print(f"[스케줄러] {now} - 워커 실행")
        subprocess.run(["python", "worker_crawl.py"])
    else:
        print(f"[스케줄러] 운영시간 아니면: {now}")
    time.sleep(INTERVAL_MINUTES * 60)


---

## 🎯 프로젝트 비전

이 프로젝트는 **한국 스포츠 데이터 분석의 새로운 표준**을 제시합니다. 복잡한 웹 구조에서 정확한 데이터를 추출하고, 실시간으로 처리하여 의미 있는 인사이트를 제공하는 것이 목표입니다.

### 🏆 핵심 가치
- **정확성**: 99.9% 데이터 정확도 보장
- **실시간성**: 5초 이내 데이터 업데이트
- **확장성**: 모듈러 아키텍처로 새로운 스포츠 추가 용이
- **안정성**: 24/7 무중단 데이터 수집

## ✨ 주요 기능

### 🏈 야구 통계 수집 (Production Ready)
> **완전 자동화된 야구 데이터 파이프라인**

#### 지원 리그
- **🇰🇷 KBO (Korean Baseball Organization)**
  - 10개 팀 전체 시즌 데이터
  - 실시간 경기 상황 추적
  - 개별 선수 성적 분석
  
- **🇺🇸 MLB (Major League Baseball)**
  - 30개 팀 아메리칸/내셔널 리그
  - 마이너리그 데이터 포함
  - 고급 세이버메트릭스 지원

- **🇯🇵 NPB (Nippon Professional Baseball)**
  - 12개 팀 센트럴/퍼시픽 리그
  - 일본 특화 통계 항목 지원

#### 수집 데이터 범위
```mermaid
graph TD
    A[팀 통계] --> B[시즌 전체 기록]
    A --> C[최근 5경기 분석]
    A --> D[홈/원정 구분 통계]
    
    E[투수 통계] --> F[개별 투수 성적]
    E --> G[경기별 투구 로그]
    E --> H[시즌 누적 통계]
    
    I[경기 데이터] --> J[실시간 스코어]
    I --> K[이닝별 상세 기록]
    I --> L[선수 교체 현황]
```

### 🎯 고급 데이터 처리 엔진

#### 비동기 처리 시스템
- **동시 처리**: 최대 10개 팀 동시 데이터 수집
- **큐 관리**: 우선순위 기반 작업 스케줄링
- **부하 분산**: 서버 응답 시간에 따른 자동 조절

#### 지능형 에러 핸들링
```python
# 예시: 자동 재시도 로직
async def collect_with_retry(self, max_retries=3):
    for attempt in range(max_retries):
        try:
            return await self.collect_data()
        except NetworkError as e:
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # 지수 백오프
                continue
            raise e
```

#### 데이터 품질 보장
- **중복 제거**: 해시 기반 중복 검출
- **데이터 검증**: 스키마 기반 자동 검증
- **이상치 탐지**: 통계적 방법으로 비정상 데이터 필터링

## 🛠️ 기술 스택 상세

### 🔧 Core Technologies

| 기술 | 버전 | 역할 | 선택 이유 |
|------|------|------|----------|
| **Python** | 3.11+ | 메인 언어 | 풍부한 데이터 처리 라이브러리 |
| **asyncio** | 표준 라이브러리 | 비동기 처리 | 고성능 I/O 바운드 작업 |
| **Playwright** | 1.40+ | 웹 자동화 | 최신 웹 기술 지원 |
| **BeautifulSoup4** | 4.12+ | HTML 파싱 | 안정적이고 유연한 파싱 |
| **pandas** | 2.0+ | 데이터 분석 | 효율적인 데이터 조작 |

### 🗄️ Database & Storage

| 컴포넌트 | 기술 | 특징 |
|----------|------|------|
| **Primary DB** | Supabase (PostgreSQL) | 실시간 구독, REST API 자동 생성 |
| **Caching** | Redis (계획) | 빠른 응답 시간 |
| **File Storage** | Supabase Storage | 이미지 및 첨부파일 관리 |

### 🔄 Development Workflow

```mermaid
graph LR
    A[개발] --> B[테스트]
    B --> C[린팅]
    C --> D[타입 체크]
    D --> E[빌드]
    E --> F[배포]
    
    G[black] --> C
    H[flake8] --> C
    I[mypy] --> D
    J[pytest] --> B
```

### 📦 핵심 의존성

```toml
[tool.poetry.dependencies]
python = "^3.11"
playwright = "^1.40.0"
beautifulsoup4 = "^4.12.2"
supabase = "^2.0.0"
pandas = "^2.0.0"
asyncio-mqtt = "^0.13.0"  # 실시간 알림용
pydantic = "^2.0.0"       # 데이터 검증
structlog = "^23.0.0"     # 구조화된 로깅
```

## 🏗️ 시스템 아키텍처

### 🎨 모듈별 책임 분리

```
📁 Stats/
├── 🔄 collectors/           # 데이터 수집 계층
│   ├── 🌐 browser_manager.py     # 브라우저 세션 관리
│   ├── 🚀 unified_crawler.py     # 통합 크롤링 오케스트레이션
│   ├── ⚾ team_collector.py      # 팀 데이터 전문 수집기
│   └── 🥎 pitcher_crawler.py     # 투수 데이터 전문 수집기
├── 🔍 parsers/             # 데이터 파싱 계층
│   └── ⚾ baseball/              # 야구 특화 파서
│       ├── 📊 base_stats_parser.py    # 공통 파싱 로직
│       ├── 🇰🇷 kbo_stats_parser.py     # KBO 전용 파서
│       ├── 🇺🇸 mlb_stats_parser.py     # MLB 전용 파서
│       ├── 🇯🇵 npb_stats_parser.py     # NPB 전용 파서
│       └── 🏆 pitcher_parser.py        # 투수 통계 파서
├── 🎯 services/            # 비즈니스 로직 계층
│   └── ⚾ baseball/              # 야구 서비스
│       ├── 👥 team_service.py          # 팀 관리 서비스
│       ├── 🏆 pitcher_service.py       # 투수 관리 서비스
│       ├── 📈 stats_collector.py       # 통계 수집 조정
│       └── ⚙️ pitcher_processor.py     # 투수 데이터 처리
├── 🗄️ database/            # 데이터 지속성 계층
│   └── 💾 database.py            # DB 연결 및 CRUD 연산
├── 🛠️ utils/               # 공통 유틸리티
│   ├── 📝 logger.py              # 구조화된 로깅
│   ├── 🔧 common.py              # 공통 함수
│   ├── 🤝 helpers.py             # 헬퍼 함수
│   └── ⚙️ service_utils.py       # 서비스 전용 유틸리티
└── ⚙️ config/              # 설정 관리
    └── 🎛️ config.py              # 환경별 설정
```

### 🔄 데이터 플로우

```mermaid
sequenceDiagram
    participant U as User
    participant M as Main App
    participant C as Collector
    participant P as Parser
    participant D as Database
    
    U->>M: 수집 시작 요청
    M->>C: 데이터 수집 시작
    
    loop 각 팀별
        C->>C: 브라우저 세션 생성
        C->>C: 웹페이지 로드
        C->>P: HTML 데이터 전달
        P->>P: 데이터 파싱 및 검증
        P->>D: 정제된 데이터 저장
    end
    
    C->>M: 수집 완료 보고
    M->>U: 결과 반환
```

## 🚀 설치 및 실행 가이드

### 📋 시스템 요구사항

| 항목 | 최소 요구사항 | 권장 사양 |
|------|--------------|----------|
| **OS** | Windows 10, macOS 10.15, Ubuntu 18.04 | 최신 버전 |
| **Python** | 3.11+ | 3.13+ |
| **RAM** | 4GB | 8GB+ |
| **디스크** | 2GB 여유 공간 | 10GB+ |
| **네트워크** | 안정적인 인터넷 연결 | 고속 브로드밴드 |

### 🔧 단계별 설치 과정

#### 1️⃣ 저장소 클론 및 프로젝트 설정

```bash
# 1. 저장소 클론
<NAME_EMAIL>:MoneyPick-KO/Stats.git
cd Stats

# 2. 프로젝트 구조 확인
ls -la
```

#### 2️⃣ Python 가상 환경 설정

<details>
<summary><strong>🐍 가상 환경 설정 방법 (여러 옵션)</strong></summary>

##### Option A: venv (표준 라이브러리)
```bash
# 가상 환경 생성
python -m venv .venv

# 가상 환경 활성화
# Linux/Mac:
source .venv/bin/activate

# Windows:
.venv\Scripts\activate

# 설치 확인
which python  # 가상환경 경로가 표시되어야 함
```

##### Option B: conda
```bash
# conda 환경 생성
conda create -n stats-env python=3.13
conda activate stats-env
```

##### Option C: Poetry (권장)
```bash
# Poetry 설치 (미설치시)
curl -sSL https://install.python-poetry.org | python3 -

# 의존성 설치
poetry install
poetry shell
```

</details>

#### 3️⃣ 패키지 의존성 설치

```bash
# requirements.txt 기반 설치
pip install -r requirements.txt

# 또는 개발 의존성 포함 설치
pip install -r requirements-dev.txt

# 설치 확인
pip list | grep -E "(playwright|supabase|beautifulsoup4)"
```

#### 4️⃣ Playwright 브라우저 설치

```bash
# 모든 브라우저 설치
playwright install

# Chromium만 설치 (권장)
playwright install chromium

# 설치 확인
playwright --version
```

#### 5️⃣ 환경 변수 설정

```bash
# 환경 파일 복사
cp env.example .env

# 환경 파일 편집
nano .env  # 또는 선호하는 에디터 사용
```

**.env 파일 설정 예시:**
```env
# Supabase 설정
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 로깅 설정
LOG_LEVEL=INFO
LOG_FILE_PATH=./logs/stats.log

# 크롤링 설정
MAX_CONCURRENT_REQUESTS=5
REQUEST_DELAY_MS=1000
RETRY_ATTEMPTS=3

# 데이터베이스 설정
DB_POOL_SIZE=10
DB_TIMEOUT=30
```

### 🗄️ 데이터베이스 설정

#### Supabase 프로젝트 생성

1. **[Supabase 대시보드](https://supabase.com/dashboard)** 접속
2. **"New Project"** 클릭
3. **프로젝트 설정:**
   ```
   Name: sports-stats
   Database Password: [강력한 비밀번호 설정]
   Region: Northeast Asia (ap-northeast-1)
   ```

#### 데이터베이스 스키마 생성

<details>
<summary><strong>📊 전체 스키마 SQL (클릭하여 펼치기)</strong></summary>

```sql
-- 1. 팀 정보 테이블
CREATE TABLE IF NOT EXISTS team_info (
    team_id VARCHAR(50) PRIMARY KEY,
    team_name VARCHAR(100) NOT NULL,
    team_full_name VARCHAR(200),
    sportic_league_id VARCHAR(20),
    sportic_team_id VARCHAR(50),
    sports VARCHAR(20) DEFAULT 'baseball',
    league VARCHAR(50),
    founded_year INTEGER,
    stadium_name VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 경기 정보 테이블 (target_games)
CREATE TABLE IF NOT EXISTS target_games (
    match_id VARCHAR(100) PRIMARY KEY,
    match_date DATE NOT NULL,
    match_time TIME,
    home_team_id VARCHAR(50) REFERENCES team_info(team_id),
    away_team_id VARCHAR(50) REFERENCES team_info(team_id),
    home_team_name VARCHAR(100),
    away_team_name VARCHAR(100),
    sports VARCHAR(20) DEFAULT 'baseball',
    league VARCHAR(50),
    league_id VARCHAR(20),
    game_type VARCHAR(10) DEFAULT 'W',
    season_year INTEGER,
    week_number INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 팀 통계 테이블
CREATE TABLE IF NOT EXISTS team_stats (
    id VARCHAR(200) PRIMARY KEY,
    match_id VARCHAR(100) REFERENCES target_games(match_id),
    team_id VARCHAR(50) REFERENCES team_info(team_id),
    team_role VARCHAR(10) CHECK (team_role IN ('home', 'away')),
    team_name VARCHAR(100),
    match_date DATE,
    match_time TIME,
    
    -- 팀 통계 데이터 (JSONB)
    season_summary JSONB,
    recent_games JSONB,
    recent_games_summary JSONB,
    season_stats JSONB,
    
    -- 투수 관련 데이터
    pitcher_profile JSONB,
    pitcher_stats JSONB,
    pitching_stats JSONB,
    
    -- 메타데이터
    sports VARCHAR(20) DEFAULT 'baseball',
    league VARCHAR(50),
    sportic_team_id VARCHAR(50),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 개별 투수 통계 테이블 (확장용)
CREATE TABLE IF NOT EXISTS pitcher_individual_stats (
    id SERIAL PRIMARY KEY,
    pitcher_name VARCHAR(100) NOT NULL,
    team_id VARCHAR(50) REFERENCES team_info(team_id),
    match_id VARCHAR(100) REFERENCES target_games(match_id),
    match_date DATE,
    
    -- 투수 기본 정보
    position VARCHAR(50) DEFAULT 'starting_pitcher',
    jersey_number INTEGER,
    birth_date DATE,
    height INTEGER,
    weight INTEGER,
    
    -- 경기별 투구 기록
    innings_pitched DECIMAL(4,1),
    hits_allowed INTEGER,
    runs_allowed INTEGER,
    earned_runs INTEGER,
    walks INTEGER,
    strikeouts INTEGER,
    home_runs_allowed INTEGER,
    pitches_thrown INTEGER,
    
    -- 결과
    decision VARCHAR(10), -- W, L, ND, S, H
    game_score INTEGER,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 시스템 로그 테이블
CREATE TABLE IF NOT EXISTS system_logs (
    id SERIAL PRIMARY KEY,
    log_level VARCHAR(20),
    module_name VARCHAR(100),
    message TEXT,
    error_details JSONB,
    execution_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 인덱스 생성
CREATE INDEX IF NOT EXISTS idx_team_stats_match_date ON team_stats(match_date);
CREATE INDEX IF NOT EXISTS idx_team_stats_team_id ON team_stats(team_id);
CREATE INDEX IF NOT EXISTS idx_target_games_date ON target_games(match_date);
CREATE INDEX IF NOT EXISTS idx_pitcher_stats_match_date ON pitcher_individual_stats(match_date);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);

-- 트리거 함수: updated_at 자동 업데이트
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 트리거 적용
CREATE TRIGGER update_team_info_updated_at 
    BEFORE UPDATE ON team_info 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_team_stats_updated_at 
    BEFORE UPDATE ON team_stats 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

</details>

#### 샘플 데이터 삽입

```sql
-- KBO 팀 정보 샘플
INSERT INTO team_info (team_id, team_name, team_full_name, sportic_league_id, sports, league) VALUES
('T1001', 'LG', 'LG 트윈스', 'bs001', 'baseball', 'KBO'),
('T1002', '두산', '두산 베어스', 'bs001', 'baseball', 'KBO'),
('T1003', 'KT', 'KT 위즈', 'bs001', 'baseball', 'KBO'),
('T1004', 'SSG', 'SSG 랜더스', 'bs001', 'baseball', 'KBO'),
('T1005', 'NC', 'NC 다이노스', 'bs001', 'baseball', 'KBO');
```

### 📋 실행 및 테스트

#### 기본 실행
```bash
# 전체 시스템 실행 (20분마다 자동 실행됨)
python main.py

# 특정 리그만 실행
python main.py --league KBO

# 디버그 모드 실행
python main.py --debug --verbose
```

# main.py에 내장된 스케줄러 기능

> **중요 변경사항**: 이전에는 `scheduler_main.py` 파일로 별도 관리되던 스케줄러 기능이 이제 `main.py` 파일에 직접 통합되었습니다.

main.py 파일은 내장된 스케줄러를 포함하고 있어 실행 시 20분마다 자동으로 데이터 수집을 반복합니다.
별도의 cron 작업이나 외부 스케줄러가 필요 없이 다음과 같은 기능을 제공합니다:

```python
# main.py 내의 스케줄러 코드
if __name__ == "__main__":
    import time
    from datetime import datetime

    INTERVAL_MINUTES = 20
    while True:
        start = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[스케줄러] {start} - main() 실행")
        try:
            asyncio.run(main())
        except Exception as e:
            print(f"[스케줄러] 예외 발생: {e}")
        print(f"[스케줄러] {INTERVAL_MINUTES}분 후 재실행 대기...")
        time.sleep(INTERVAL_MINUTES * 60)
```

#### 테스트 실행
```bash
# 전체 테스트 실행
pytest

# 특정 모듈 테스트
pytest tests/test_collectors.py

# 커버리지 포함 테스트
pytest --cov=. --cov-report=html

# 성능 테스트
pytest tests/performance/ -v
```

#### 건강성 체크
```bash
# 데이터베이스 연결 확인
python -c "from database.database import connect_supabase; print('✅ DB 연결 성공' if connect_supabase() else '❌ DB 연결 실패')"

# Playwright 동작 확인
python -c "import asyncio; from playwright.async_api import async_playwright; print('✅ Playwright 설정 완료')"

# 전체 시스템 건강성 체크
python utils/health_check.py
```

### 🔧 고급 설정

#### Docker를 이용한 실행 (선택사항)

<details>
<summary><strong>🐳 Docker 설정 가이드</strong></summary>

```dockerfile
# Dockerfile
FROM python:3.12-slim

WORKDIR /app

# 시스템 의존성 설치
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    && rm -rf /var/lib/apt/lists/*

# Python 의존성 설치
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Playwright 브라우저 설치
RUN playwright install chromium
RUN playwright install-deps

# 소스 코드 복사
COPY . .

# 실행
CMD ["python", "main.py"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  stats-collector:
    build: .
    env_file: .env
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
```

```bash
# Docker 실행
docker-compose up -d
```

</details>

#### 프로덕션 환경 설정

```bash
# 로그 디렉토리 생성
mkdir -p logs

# 로그 로테이션 설정 (Linux)
sudo tee /etc/logrotate.d/stats-collector > /dev/null <<EOF
/path/to/Stats/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF

# Systemd 서비스 등록 (Linux)
sudo tee /etc/systemd/system/stats-collector.service > /dev/null <<EOF
[Unit]
Description=Sports Stats Collector
After=network.target

[Service]
Type=simple
User=stats
WorkingDirectory=/path/to/Stats
ExecStart=/path/to/Stats/.venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 서비스 시작
sudo systemctl enable stats-collector
sudo systemctl start stats-collector
```

## 📖 사용 가이드

### 🎯 기본 사용법

#### CLI를 통한 실행

```bash
# 기본 실행 (모든 리그)
python main.py

# 특정 리그만 수집
python main.py --league KBO
python main.py --league MLB NPB

# 특정 날짜 범위 수집
python main.py --start-date 2024-01-01 --end-date 2024-01-31

# 디버그 모드
python main.py --debug --log-level DEBUG

# 도움말
python main.py --help
```

#### 프로그래매틱 사용

```python
import asyncio
from collectors.unified_crawler import UnifiedDataCollector
from database.database import connect_supabase
from config.config import League

async def collect_today_stats():
    """오늘의 모든 야구 통계 수집"""
    client = connect_supabase()
    collector = UnifiedDataCollector(client)
    
    # 특정 리그만 수집
    games = await collector.collect_games([League.KBO, League.MLB])
    
    print(f"✅ {len(games)}개 경기 데이터 수집 완료")
    return games

# 실행
if __name__ == "__main__":
    asyncio.run(collect_today_stats())
```

### 📊 사용 예시

#### 1. 팀 통계 수집

```python
from services.baseball.team_service import TeamService
from database.database import connect_supabase

async def collect_team_stats():
    client = connect_supabase()
    team_service = TeamService()
    
    # 여러 경기의 팀 통계 병렬 수집
    games = [
        {
            'home_team_id': 'T1001',
            'away_team_id': 'T1002', 
            'date': '2024-06-01',
            'time': '18:30'
        }
    ]
    
    saved_count = await team_service.save_multiple_team_stats(games, client)
    print(f"💾 {saved_count}개 팀 통계 저장됨")
```

#### 2. 투수 데이터 수집

```python
from services.baseball.pitcher_service import PitcherService

async def collect_pitcher_data():
    service = PitcherService()
    
    # 특정 경기의 투수 데이터 수집
    games = await service.collect_and_save_pitcher_data_batch([
        {
            'match_id': 'GAME_001',
            'home_team': 'LG 트윈스',
            'away_team': '두산 베어스',
            'date': '2024-06-01'
        }
    ])
    
    return games
```

#### 3. 커스텀 파서 사용

```python
from parsers.baseball.kbo_stats_parser import KBOStatsParser

def parse_custom_data(html_content):
    parser = KBOStatsParser(html_content)
    
    # 시즌 요약 파싱
    season_summary = parser.parse_season_summary()
    
    # 최근 경기 파싱  
    recent_games = parser.parse_recent_games()
    
    # 시즌 통계 파싱
    season_stats = parser.parse_season_stats()
    
    return {
        'season_summary': season_summary,
        'recent_games': recent_games, 
        'season_stats': season_stats
    }
```

## 📋 API 문서

### 🔌 Core APIs

#### UnifiedDataCollector

**메인 데이터 수집 오케스트레이터**

```python
class UnifiedDataCollector:
    async def collect_games(
        self, 
        leagues: List[League] = None,
        date_range: Tuple[str, str] = None
    ) -> List[Dict]:
        """
        지정된 리그의 경기 데이터 수집
        
        Args:
            leagues: 수집할 리그 목록 (기본값: 모든 리그)
            date_range: 날짜 범위 ('YYYY-MM-DD', 'YYYY-MM-DD')
            
        Returns:
            List[Dict]: 수집된 경기 데이터 목록
            
        Example:
            games = await collector.collect_games([League.KBO])
        """
```

#### TeamService

**팀 통계 관리 서비스**

```python
class TeamService:
    async def save_multiple_team_stats(
        self,
        games: List[Dict],
        client: Client
    ) -> int:
        """
        여러 경기의 팀 통계를 병렬로 수집 및 저장
        
        Args:
            games: 경기 정보 목록
            client: Supabase 클라이언트
            
        Returns:
            int: 성공적으로 저장된 팀 수
        """
```

#### PitcherService  

**투수 데이터 관리 서비스**

```python
class PitcherService:
    async def collect_and_save_pitcher_data_batch(
        self,
        games: List[Dict],
        max_concurrent: int = 5
    ) -> int:
        """
        배치 단위로 투수 데이터 수집 및 저장
        
        Args:
            games: 경기 목록
            max_concurrent: 최대 동시 처리 수
            
        Returns:
            int: 처리된 투수 수
        """
```

### 🎨 Parser APIs

#### BaseStatsParser

**모든 파서의 기본 클래스**

```python
from abc import ABC, abstractmethod

class BaseStatsParser(ABC):
    @abstractmethod
    def parse_season_summary(self) -> Dict:
        """시즌 요약 통계 파싱"""
        pass
        
    @abstractmethod  
    def parse_recent_games(self) -> Dict:
        """최근 경기 기록 파싱"""
        pass
        
    @abstractmethod
    def parse_season_stats(self) -> Dict:
        """시즌별 상세 통계 파싱"""
        pass
```

#### 리그별 파서

```python
# KBO 파서
from parsers.baseball.kbo_stats_parser import KBOStatsParser

parser = KBOStatsParser(html_content)
data = parser.parse_season_summary()

# MLB 파서  
from parsers.baseball.mlb_stats_parser import MLBStatsParser

parser = MLBStatsParser(html_content)
data = parser.parse_recent_games()

# NPB 파서
from parsers.baseball.npb_stats_parser import NPBStatsParser

parser = NPBStatsParser(html_content)
data = parser.parse_season_stats()
```

### 🗄️ Database APIs

```python
from database.database import (
    connect_supabase,
    save_team_stats,
    save_pitcher_stats,
    get_team_mappings
)

# 데이터베이스 연결
client = connect_supabase()

# 팀 통계 저장
success = save_team_stats(client, team_id, stats, match_date)

# 투수 통계 저장  
success = save_pitcher_stats(
    client, match_id, team_id, 
    pitcher_name, profile, stats
)

# 팀 매핑 조회
mappings = get_team_mappings(client)
```

## 🔧 고급 기능

### ⚡ 성능 최적화

#### 비동기 배치 처리

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class OptimizedCollector:
    def __init__(self, max_workers=10):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
    async def collect_parallel(self, tasks):
        """병렬 수집 with 백프레셔 제어"""
        semaphore = asyncio.Semaphore(5)  # 최대 5개 동시 실행
        
        async def bounded_collect(task):
            async with semaphore:
                return await self.collect_single(task)
                
        results = await asyncio.gather(*[
            bounded_collect(task) for task in tasks
        ])
        
        return results
```

#### 캐싱 시스템

```python
from functools import lru_cache
import redis

class CacheManager:
    def __init__(self):
        self.redis_client = redis.Redis()
        
    @lru_cache(maxsize=1000)
    def get_team_info(self, team_id: str):
        """팀 정보 캐싱"""
        return self._fetch_team_info(team_id)
        
    def cache_game_data(self, game_id: str, data: dict, ttl=3600):
        """Redis에 게임 데이터 캐싱"""
        self.redis_client.setex(
            f"game:{game_id}", 
            ttl, 
            json.dumps(data)
        )
```

### 📊 모니터링 및 알림

#### 실시간 모니터링

```python
from utils.logger import Logger
import time

class PerformanceMonitor:
    def __init__(self):
        self.logger = Logger(__name__)
        
    def track_execution_time(self, func):
        """실행 시간 추적 데코레이터"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            self.logger.info(f"⏱️ {func.__name__}: {execution_time:.2f}s")
            return result
        return wrapper
        
    async def health_check(self):
        """시스템 건강 상태 체크"""
        checks = {
            'database': self._check_database(),
            'browser': self._check_browser(),
            'memory': self._check_memory_usage()
        }
        
        return all(checks.values())
```

#### 알림 시스템

```python
import smtplib
from email.mime.text import MIMEText

class NotificationManager:
    def __init__(self, smtp_config):
        self.smtp_config = smtp_config
        
    def send_error_alert(self, error_msg: str):
        """에러 발생시 이메일 알림"""
        msg = MIMEText(f"🚨 Stats Collector Error: {error_msg}")
        msg['Subject'] = 'Stats Collection Error Alert'
        msg['From'] = self.smtp_config['from']
        msg['To'] = self.smtp_config['to']
        
        with smtplib.SMTP(self.smtp_config['server']) as server:
            server.send_message(msg)
            
    def send_daily_report(self, stats: dict):
        """일일 수집 리포트"""
        report = f"""
        📊 Daily Collection Report
        
        ✅ Games Collected: {stats['games_count']}
        ✅ Teams Processed: {stats['teams_count']}  
        ✅ Pitchers Processed: {stats['pitchers_count']}
        ⏱️ Total Time: {stats['total_time']}
        """
        
        # 리포트 전송 로직
```

### 🔒 보안 강화

#### API 키 관리

```python
import os
from cryptography.fernet import Fernet

class SecureConfig:
    def __init__(self):
        self.cipher = Fernet(os.environ.get('ENCRYPTION_KEY'))
        
    def get_secure_env(self, key: str) -> str:
        """암호화된 환경 변수 복호화"""
        encrypted_value = os.environ.get(f"ENCRYPTED_{key}")
        if encrypted_value:
            return self.cipher.decrypt(encrypted_value.encode()).decode()
        return os.environ.get(key)
```

#### 입력 검증

```python
from pydantic import BaseModel, validator

class GameRequest(BaseModel):
    team_id: str
    date: str
    league: str
    
    @validator('team_id')
    def validate_team_id(cls, v):
        if not v or len(v) < 3:
            raise ValueError('Invalid team_id')
        return v
        
    @validator('date')
    def validate_date(cls, v):
        try:
            datetime.strptime(v, '%Y-%m-%d')
        except ValueError:
            raise ValueError('Date must be YYYY-MM-DD format')
        return v
```

## 🚨 트러블슈팅

### ❌ 일반적인 문제와 해결법

#### 1. Playwright 브라우저 오류

**문제**: `Browser not found` 에러
```bash
playwright._impl._api_types.Error: Browser not found
```

**해결법**:
```bash
# 브라우저 재설치
playwright uninstall
playwright install chromium

# 의존성 설치 (Linux)
sudo playwright install-deps
```

#### 2. Supabase 연결 오류

**문제**: `Invalid API key` 또는 연결 타임아웃

**해결법**:
```python
# 연결 테스트 스크립트
from database.database import connect_supabase

def test_db_connection():
    try:
        client = connect_supabase()
        response = client.table('team_info').select('count').execute()
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

test_db_connection()
```

#### 3. 메모리 부족 오류

**문제**: 대량 데이터 수집시 메모리 부족

**해결법**:
```python
# 배치 크기 조정
MAX_BATCH_SIZE = 5  # 기본값: 10

# 메모리 정리
import gc

def cleanup_memory():
    gc.collect()
    
# 주기적 정리
async def collect_with_cleanup(tasks):
    for i, task in enumerate(tasks):
        if i % 10 == 0:  # 10개마다 정리
            cleanup_memory()
        await process_task(task)
```

#### 4. 네트워크 타임아웃

**문제**: 웹사이트 응답 지연으로 인한 타임아웃

**해결법**:
```python
# 타임아웃 설정 조정
BROWSER_TIMEOUT = 60000  # 60초
PAGE_LOAD_TIMEOUT = 30000  # 30초

# 재시도 로직 강화
async def robust_page_load(page, url, max_retries=3):
    for attempt in range(max_retries):
        try:
            await page.goto(url, timeout=PAGE_LOAD_TIMEOUT)
            return True
        except Exception as e:
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt)
                continue
            raise e
```

### 🔍 디버깅 도구

#### 로그 분석

```bash
# 에러 로그만 확인
grep "ERROR" logs/stats.log

# 특정 시간대 로그
grep "2024-06-01 18:" logs/stats.log

# 성능 관련 로그
grep "⏱️" logs/stats.log
```

#### 데이터베이스 상태 확인

```sql
-- 최근 수집 현황
SELECT 
    DATE(created_at) as collection_date,
    COUNT(*) as records_count,
    COUNT(DISTINCT team_id) as teams_count
FROM team_stats 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE(created_at)
ORDER BY collection_date DESC;

-- 투수 데이터 현황
SELECT 
    league,
    COUNT(*) as pitcher_records,
    COUNT(DISTINCT jsonb_extract_path_text(pitcher_profile, 'name')) as unique_pitchers
FROM team_stats 
WHERE pitcher_profile IS NOT NULL
GROUP BY league;
```

#### 시스템 리소스 모니터링

```python
import psutil
import asyncio

async def monitor_resources():
    """시스템 리소스 모니터링"""
    while True:
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        
        print(f"💻 CPU: {cpu_percent}% | RAM: {memory.percent}%")
        
        if cpu_percent > 80 or memory.percent > 85:
            print("⚠️ High resource usage detected!")
            
        await asyncio.sleep(30)
```

### 📞 지원 및 문의

#### 🆘 문제 보고

문제가 발생했을 때는 다음 정보를 포함하여 GitHub Issues에 보고해 주세요:

1. **환경 정보**:
   ```bash
   python --version
   pip list | grep -E "(playwright|supabase)"
   ```

2. **에러 로그**:
   ```bash
   tail -n 50 logs/stats.log
   ```

3. **재현 단계**:
   - 정확한 실행 명령어
   - 발생 시점
   - 예상 결과 vs 실제 결과

#### 📚 추가 리소스

- **Documentation**: [프로젝트 위키](https://github.com/MoneyPick-KO/Stats/wiki)
- **Examples**: [examples/](examples/) 디렉토리
- **Community**: [GitHub Discussions](https://github.com/MoneyPick-KO/Stats/discussions)
- **API Reference**: [docs/api.md](docs/api.md)

---

**Made with ❤️ by MoneyPick-KO Team** 

## 🔧 개발 가이드

### 📝 코딩 표준

#### PEP 8 준수 및 확장
- **라인 길이**: 최대 79자 (docstring은 72자)
- **들여쓰기**: 4칸 스페이스 (탭 금지)
- **네이밍**: snake_case (함수, 변수), PascalCase (클래스)
- **한글 주석**: 적극 권장 (가독성 향상)

```python
# ✅ 좋은 예시
class GameDataProcessor:
    """경기 데이터 처리 담당 클래스"""
    
    async def process_team_stats(
        self, 
        team_data: Dict[str, Any]
    ) -> ProcessedStats:
        """팀 통계 데이터 처리 및 검증"""
        pass

# ❌ 나쁜 예시  
class gdp:
    def pTS(self,td):
        pass
```

#### 타입 힌트 가이드

```python
from typing import Dict, List, Optional, Union, Callable
from datetime import datetime

# 기본 타입 힌트
def calculate_era(earned_runs: int, innings: float) -> float:
    return (earned_runs * 9) / innings

# 복잡한 타입 구조
PlayerStats = Dict[str, Union[int, float, str]]
GameResult = Dict[str, Any]

async def collect_games(
    leagues: List[League],
    date_range: Optional[Tuple[str, str]] = None,
    callback: Optional[Callable[[GameResult], None]] = None
) -> List[GameResult]:
    """복잡한 타입 힌트 예시"""
    pass
```

### 🧪 테스트 전략

#### 테스트 구조

```
tests/
├── unit/                    # 단위 테스트
│   ├── test_parsers.py         # 파서 테스트
│   ├── test_collectors.py      # 수집기 테스트
│   └── test_services.py        # 서비스 테스트
├── integration/             # 통합 테스트
│   ├── test_database.py        # DB 연동 테스트
│   └── test_crawling.py        # 크롤링 통합 테스트
├── performance/             # 성능 테스트
│   └── test_load.py            # 부하 테스트
└── fixtures/               # 테스트 데이터
    ├── sample_html/            # 샘플 HTML 파일
    └── sample_data.json        # 샘플 JSON 데이터
```

#### 테스트 작성 예시

```python
import pytest
from unittest.mock import AsyncMock, patch
from parsers.baseball.kbo_stats_parser import KBOStatsParser

class TestKBOStatsParser:
    """KBO 파서 테스트 클래스"""
    
    @pytest.fixture
    def sample_html(self):
        """샘플 HTML 데이터 로드"""
        with open('tests/fixtures/sample_html/kbo_team.html', 'r') as f:
            return f.read()
    
    def test_parse_season_summary(self, sample_html):
        """시즌 요약 파싱 테스트"""
        parser = KBOStatsParser(sample_html)
        result = parser.parse_season_summary()
        
        assert 'total_games' in result
        assert isinstance(result['total_games'], int)
        assert result['total_games'] > 0
    
    @pytest.mark.asyncio
    async def test_async_parsing(self):
        """비동기 파싱 테스트"""
        with patch('collectors.browser_manager.TeamStatsBrowser') as mock_browser:
            mock_browser.return_value.get_page_content = AsyncMock(
                return_value="<html>test</html>"
            )
            
            # 테스트 로직
            assert True
```

#### 성능 테스트

```python
import time
import pytest
from memory_profiler import profile

@pytest.mark.performance
class TestPerformance:
    
    def test_parsing_speed(self, large_html_sample):
        """파싱 속도 테스트"""
        start_time = time.time()
        
        parser = KBOStatsParser(large_html_sample)
        result = parser.parse_all_data()
        
        execution_time = time.time() - start_time
        assert execution_time < 5.0  # 5초 이내 완료
        assert len(result) > 0
    
    @profile
    def test_memory_usage(self):
        """메모리 사용량 테스트"""
        # 메모리 프로파일링 코드
        pass
```

### 🔧 새로운 기능 추가 가이드

#### 1. 새로운 스포츠 지원 추가

```python
# 1. config/config.py에 새 리그 추가
class League(Enum):
    KBO = "KBO"
    MLB = "MLB" 
    NPB = "NPB"
    WKBL = "WKBL"  # 새로운 농구 리그

# 2. parsers에 새 파서 생성
class WKBLStatsParser(BaseStatsParser):
    """WKBL 전용 파서"""
    
    def parse_season_summary(self) -> Dict:
        # 농구 특화 파싱 로직
        pass

# 3. 테스트 작성
class TestWKBLStatsParser:
    def test_basketball_specific_stats(self):
        # 농구 특화 테스트
        pass
```

#### 2. 새로운 데이터 소스 추가

```python
# collectors/new_source_collector.py
class NewSourceCollector(BaseCollector):
    """새로운 데이터 소스 수집기"""
    
    async def collect_data(self, url: str) -> Dict:
        # 새 소스 수집 로직
        pass
        
    def validate_data(self, data: Dict) -> bool:
        # 데이터 검증 로직
        pass
```

## ⚡️ 최신 아키텍처 개요 (2025)

- **main.py**: 스케줄러 역할만 수행. 운영 시간(08:00~22:00) 동안 30분마다 `worker_crawl.py`를 서브프로세스로 실행.
- **worker_crawl.py**: 실제 데이터 수집/처리 로직을 1회 실행 후 종료. 모든 비즈니스 로직은 이 파일에서 관리.
- **utils/process_utils.py**: 운영 시간 체크 및 워커 실행 유틸리티 제공.
- **불필요한 계층/폴더(예: business/worker_logic.py)는 제거됨.**
- **requirements.txt**: 런타임에 필요한 최소 패키지만 포함.
- **메모리 안전성**: 각 워커 실행 후 프로세스가 종료되어 메모리 누수 없이 장기 운영 가능.

## 📈 성능 벤치마크

#### 현재 성능 지표

| 작업 | 평균 시간 | 메모리 사용량 | 처리량 |
|------|----------|-------------|--------|
| **단일 팀 통계 수집** | 2.3초 | 45MB | - |
| **10개 팀 병렬 수집** | 8.7초 | 180MB | 1.15 teams/sec |
| **투수 데이터 파싱** | 0.8초 | 12MB | - |
| **일일 전체 수집** | 4분 30초 | 350MB | 450 records/min |

#### 성능 최적화 가이드

```python
# 🚀 최적화 예시
class OptimizedDataProcessor:
    def __init__(self):
        self.cache = {}
        self.pool = ThreadPoolExecutor(max_workers=10)
    
    @lru_cache(maxsize=1000)
    def get_team_mapping(self, team_name: str) -> str:
        """팀 매핑 캐싱으로 DB 호출 최소화"""
        return self._fetch_team_mapping(team_name)
    
    async def batch_process(self, items: List[Any]) -> List[Any]:
        """배치 처리로 네트워크 오버헤드 감소"""
        chunks = [items[i:i+10] for i in range(0, len(items), 10)]
        
        tasks = [self.process_chunk(chunk) for chunk in chunks]
        results = await asyncio.gather(*tasks)
        
        return [item for sublist in results for item in sublist]
```

## 📊 데이터 구조 상세

### 🏟️ 팀 통계 데이터 스키마

```json
{
  "team_id": "T1001",
  "team_name": "LG",
  "league": "KBO",
  "match_date": "2024-06-01",
  
  "season_summary": {
    "games_played": 144,
    "wins": 82,
    "losses": 60,
    "draws": 2,
    "win_rate": 0.578,
    "runs_scored": 756,
    "runs_allowed": 698,
    "run_differential": 58
  },
  
  "recent_games": {
    "recent_5_games": [
      {
        "date": "2024-05-31",
        "opponent": "두산",
        "result": "W",
        "score": "7-4",
        "home_away": "home",
        "starting_pitcher": "켈리"
      }
    ],
    "recent_3_home_games": [
      {
        "date": "2024-05-30",
        "opponent": "키움",
        "result": "W",
        "score": "6-3",
        "home_away": "home"
      }
    ],
    "recent_2_away_games": [
      {
        "date": "2024-05-28",
        "opponent": "삼성",
        "result": "L",
        "score": "3-5",
        "home_away": "away"
      }
    ]
  },
  
  "recent_games_summary": {
    "recent_5_games": {
      "last_3_home_games": {
        "record": "3W 0D 0L",
        "avg_runs_scored": 6.0,
        "avg_runs_allowed": 3.2,
        "avg_hits": 9.1
      },
      "last_2_away_games": {
        "record": "1W 0D 1L",
        "avg_runs_scored": 4.4,
        "avg_runs_allowed": 4.4,
        "avg_hits": 8.5
      }
    },
    "recent_5_home_games": {
      "record": "4W 0D 1L", 
      "avg_runs_scored": 6.0,
      "avg_runs_allowed": 3.2,
      "avg_hits": 9.8
    },
    "recent_5_away_games": {
      "record": "2W 0D 3L",
      "avg_runs_scored": 4.4,
      "avg_runs_allowed": 4.4,
      "avg_hits": 8.2
    }
  },
  
  "season_stats": {
    "2024": {
      "batting": {
        "avg": 0.267,
        "obp": 0.342,
        "slg": 0.421,
        "ops": 0.763,
        "home_runs": 158,
        "rbi": 721,
        "stolen_bases": 89
      },
      "pitching": {
        "era": 4.23,
        "whip": 1.31,
        "strikeouts": 1247,
        "saves": 45,
        "quality_starts": 89
      }
    }
  }
}
```

### ⚾ 투수 데이터 스키마

```json
{
  "pitcher_profile": {
    "name": "켈리",
    "team": "LG 트윈스",
    "league": "KBO",
    "position": "starting_pitcher",
    "jersey_number": 52,
    "birth_date": "1988-10-04",
    "height": 196,
    "weight": 98,
    "bats_throws": "R/R",
    "debut_year": 2019,
    "nationality": "USA"
  },
  
  "pitcher_stats": {
    "season_stats": [
      {
        "year": "2024",
        "games": 28,
        "games_started": 28,
        "wins": 12,
        "losses": 8,
        "saves": 0,
        "holds": 0,
        "innings_pitched": 168.1,
        "era": 3.21,
        "whip": 1.18,
        "strikeouts": 156,
        "walks": 42,
        "hits_allowed": 152,
        "home_runs_allowed": 18,
        "quality_starts": 19
      }
    ],
    
    "recent_games": {
      "2024-06-01": {
        "opponent": "두산",
        "result": "W",
        "innings": "7.0",
        "earned_runs": 2,
        "hits_allowed": 6,
        "walks": 2,
        "strikeouts": 8,
        "pitches": 98,
        "game_score": 67
      }
    },
    
    "monthly_stats": {
      "2024-05": {
        "games": 6,
        "wins": 3,
        "losses": 1,
        "era": 2.89,
        "innings": 37.1,
        "strikeouts": 32
      }
    }
  }
}
```

## 🤝 기여 방법

### 🚀 기여자 온보딩

#### 1단계: 환경 설정
```bash
# 포크 후 클론
git clone https://github.com/YOUR_USERNAME/Stats.git
cd Stats

# 개발 브랜치 생성
git checkout -b feature/your-feature-name

# 개발 의존성 설치
pip install -r requirements-dev.txt
pre-commit install
```

#### 2단계: 개발 플로우
```bash
# 코드 작성 후 테스트
pytest tests/

# 린팅 및 포매팅
black .
flake8 .
mypy .

# 커밋 (conventional commits 형식)
git commit -m "feat: add new baseball parser for CPBL"
```

#### 3단계: PR 제출
1. **제목**: `[타입] 간단한 설명` (예: `[feat] KBO 투수 통계 개선`)
2. **설명**: 변경사항, 테스트 결과, 스크린샷 포함
3. **체크리스트**: 모든 테스트 통과, 문서 업데이트 확인

### 📋 기여 가이드라인

#### 커밋 메시지 규칙 (Conventional Commits)

```bash
# 새 기능
git commit -m "feat: add CPBL league support"

# 버그 수정  
git commit -m "fix: resolve parsing error for empty pitcher stats"

# 문서 업데이트
git commit -m "docs: update API documentation for new endpoints"

# 리팩토링
git commit -m "refactor: optimize database query performance"

# 테스트 추가
git commit -m "test: add unit tests for MLB parser"

# 성능 개선
git commit -m "perf: implement caching for team mappings"
```

#### 코드 리뷰 체크리스트

**리뷰어를 위한 체크리스트**:
- [ ] 코드가 PEP 8을 준수하는가?
- [ ] 적절한 타입 힌트가 있는가?
- [ ] 테스트 커버리지가 충분한가?
- [ ] 성능에 부정적 영향은 없는가?
- [ ] 보안 취약점은 없는가?
- [ ] 문서가 업데이트되었는가?

**기여자를 위한 체크리스트**:
- [ ] 기능이 완전히 구현되었는가?
- [ ] 모든 테스트가 통과하는가?
- [ ] 린터 오류가 없는가?
- [ ] 관련 문서를 업데이트했는가?
- [ ] 브레이킹 체인지가 있다면 명시했는가?

### 🏅 기여자 인정 시스템

#### 기여 레벨
- **🌱 Contributor**: 첫 PR 머지
- **🌿 Regular**: 5개 이상 PR 머지  
- **🌳 Core**: 10개 이상 PR + 코드 리뷰 참여
- **🏆 Maintainer**: 프로젝트 관리 권한

#### 기여자 배지
```markdown
<!-- README에 표시되는 기여자 목록 -->
## 👥 기여자들

<a href="https://github.com/MoneyPick-KO/Stats/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=MoneyPick-KO/Stats" />
</a>

### 🏆 특별 기여자
- **@contributor1** - KBO 파서 개발
- **@contributor2** - 성능 최적화  
- **@contributor3** - 문서화 개선
```

## 🛣️ 로드맵

### 📅 2024년 4분기

#### ✅ 완료된 기능
- [x] KBO/MLB/NPB 야구 데이터 수집
- [x] 비동기 크롤링 시스템
- [x] Supabase 데이터베이스 연동
- [x] 투수 통계 상세 수집
- [x] 팀 통계 홈/원정 구분

#### 🚧 진행 중
- [ ] 🏀 **농구 데이터 수집** (75% 완료)
  - [x] KBL 파서 개발
  - [ ] NBA/WNBA 파서 개발
  - [ ] 선수 개별 통계 수집

- [ ] ⚽ **축구 데이터 수집** (40% 완료)
  - [x] K리그 기본 구조
  - [ ] 프리미어리그/라리가 지원
  - [ ] 실시간 경기 상황

### 📅 2025년 1분기

#### 🎯 핵심 목표
- [ ] **REST API 서버 구축**
  ```python
  # 계획 중인 API 엔드포인트
  GET /api/v1/teams/{team_id}/stats
  GET /api/v1/games/{date}/schedule
  POST /api/v1/collect/start
  GET /api/v1/health
  ```

- [ ] **실시간 웹 대시보드**
  - React + TypeScript 프론트엔드
  - WebSocket을 통한 실시간 업데이트
  - 대시보드 커스터마이제이션

- [ ] **데이터 분석 모듈**
  ```python
  # 계획 중인 분석 기능
  - 선수 성적 예측 모델
  - 팀 승률 예측 알고리즘  
  - 트렌드 분석 및 시각화
  ```

### 📅 2025년 2분기

#### 🔮 고급 기능
- [ ] **머신러닝 통합**
  - 선수 부상 예측 모델
  - 경기 결과 예측 시스템
  - 선수 가치 평가 모델

- [ ] **모바일 앱 개발**
  - React Native 크로스플랫폼 앱
  - 푸시 알림 시스템
  - 오프라인 데이터 지원

- [ ] **데이터 시각화 강화**
  - D3.js 기반 인터랙티브 차트
  - 3D 구장 시뮬레이션
  - AR/VR 통계 뷰어

## 📊 사용 통계

### 📈 프로젝트 성장

```mermaid
graph LR
    A[2024 Q1] --> B[2024 Q2]
    B --> C[2024 Q3]
    C --> D[2024 Q4]
    
    A --> A1[기본 KBO 지원]
    B --> B1[MLB/NPB 추가]
    C --> C1[투수 통계 강화]
    D --> D1[성능 최적화]
```

### 🎯 사용자 피드백

> "KBO 데이터 수집 속도가 놀라울 정도로 빨라졌습니다. 실시간 분석에 큰 도움이 됩니다." - **데이터 분석가 A**

> "MLB 파서의 정확도가 매우 높고, API가 직관적이어서 쉽게 통합할 수 있었습니다." - **개발자 B**

> "문서화가 잘 되어 있어 새로운 스포츠 추가가 수월했습니다." - **기여자 C**

## 📄 라이센스 및 법적 고지

### 📜 MIT 라이센스

```
MIT License

Copyright (c) 2024 MoneyPick-KO

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

### ⚖️ 데이터 사용 고지

- **공개 데이터**: 공개된 스포츠 통계만 수집
- **개인정보**: 개인 식별 정보는 수집하지 않음
- **저작권**: 원본 데이터 제공자의 저작권 존중
- **robots.txt**: 웹사이트의 크롤링 정책 준수

## 👥 팀 및 연락처

### 🏆 핵심 팀

| 역할 | 담당자 | 전문 분야 |
|------|--------|----------|
| **Project Lead** | MoneyPick-KO | 프로젝트 관리, 아키텍처 |
| **Backend Dev** | Contributor | Python, 데이터베이스 |
| **Data Engineer** | Contributor | 크롤링, 파싱 |
| **DevOps** | Contributor | 배포, 모니터링 |

### 📞 연락 방법

- 🐛 **버그 리포트**: [GitHub Issues](https://github.com/MoneyPick-KO/Stats/issues)
- 💡 **기능 제안**: [GitHub Discussions](https://github.com/MoneyPick-KO/Stats/discussions)
- 🤝 **기여 문의**: PR 또는 Issues를 통해 문의
- 📧 **기타 문의**: GitHub Issues를 통해 문의 (이메일보다 추적이 용이)

### 🌟 후원 및 지원

프로젝트가 도움이 되셨다면:
- ⭐ **GitHub Star** 주기
- 🐛 **이슈 리포트** 및 피드백 제공
- 🔧 **코드 기여** 또는 문서 개선
- 📢 **프로젝트 공유** 및 홍보

---

<div align="center">

**🏆 Made with ❤️ by MoneyPick-KO Team**

*"데이터로 스포츠의 새로운 가능성을 열어갑니다"*

[![GitHub Stars](https://img.shields.io/github/stars/MoneyPick-KO/Stats?style=social)](https://github.com/MoneyPick-KO/Stats/stargazers)
[![GitHub Forks](https://img.shields.io/github/forks/MoneyPick-KO/Stats?style=social)](https://github.com/MoneyPick-KO/Stats/network/members)
[![GitHub Issues](https://img.shields.io/github/issues/MoneyPick-KO/Stats)](https://github.com/MoneyPick-KO/Stats/issues)

</div>

## 🐞 트러블슈팅: RuntimeError('Event loop is closed')

| 증상 | 이벤트 루프 종료 후 **`RuntimeError: Event loop is closed`** 메시지가 여러 번 출력되며 스택트레이스에 `BaseSubprocessTransport.__del__` 가 나타남 |
|------|--------------------------------------------------------------------------------------------------------------------------------------------|
| 원인 | `asyncio.run()` 으로 생성한 루프가 정상 종료된 뒤 Python GC 단계에서 **Playwright/asyncio subprocess** 관련 전송 객체가 `__del__` 에서 이미 닫힌 루프에 접근하기 때문. Python 3.12 에서 특히 두드러짐. |
| 해결 | ① 비동기 subprocess를 생성한 객체(본 프로젝트에서는 `UnifiedDataCollector`)를 **루프가 닫히기 전에 명시적으로 `await .close()`** 로 정리<br>② 남아 있는 태스크를 atexit 훅에서 취소하여 잔여 리소스를 정리 |

### 패치 내역 (2025-06-28)

1. **`worker_crawl.py`**
   * `finally` 블록에서 `await collector.close()` 호출을 추가하여 Playwright-브라우저 및 내부 subprocess 종료.
   * 타입 힌트, 가독성 개선 주석 추가.
2. **`worker_crawl.py`** 내부 잔여 태스크 정리 로직( `_cleanup_pending_tasks` ) 유지 → 이벤트 루프 종료 전에 미완료 코루틴 취소.

이 패치 이후 더 이상 GC 단계에서 RuntimeError 로그가 출력되지 않으며, 자원 해제 시점이 명확해져 메모리 피크도 안정화되었습니다.