#!/usr/bin/env python3
"""
모듈 경로 문제 해결 테스트
"""
import sys
import os

# 프로젝트 루트를 Python path에 추가
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_core_modules():
    """핵심 모듈 import 테스트"""
    print("🔧 핵심 모듈 import 테스트")
    
    try:
        # SportConfig 테스트
        from core.config.sport_config import SportConfig, get_sport_config, get_all_sport_configs
        print("   ✅ SportConfig import 성공")
        
        # 축구 설정 테스트
        soccer_config = get_sport_config('soccer')
        assert soccer_config is not None
        assert soccer_config.sport_name == 'soccer'
        assert len(soccer_config.leagues) == 9
        print("   ✅ 축구 설정 테스트 통과")
        
        # TimeSlotScheduler 테스트
        from core.scheduler.time_slot_scheduler import TimeSlotScheduler
        scheduler = TimeSlotScheduler()
        active_sports = scheduler.get_active_sports_now()
        print(f"   ✅ TimeSlotScheduler 테스트 통과 (활성 스포츠: {active_sports})")
        
        # UniversalTeamMatcher 테스트
        from core.matcher.universal_team_matcher import UniversalTeamMatcher
        matcher = UniversalTeamMatcher()
        soccer_team = matcher.get_team_name('soccer', 'K01')
        assert soccer_team == '울산 HD FC'
        print("   ✅ UniversalTeamMatcher 테스트 통과")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 핵심 모듈 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_sport_parsers():
    """스포츠 파서 import 테스트"""
    print("🔧 스포츠 파서 import 테스트")
    
    try:
        # 축구 파서 테스트
        from sports.soccer.parsers.soccer_parser import SoccerParser
        parser = SoccerParser()
        print("   ✅ 축구 파서 import 성공")
        
        # 농구 파서 테스트
        from sports.basketball.parsers.basketball_parser import BasketballParser
        parser = BasketballParser()
        print("   ✅ 농구 파서 import 성공")
        
        # 배구 파서 테스트
        from sports.volleyball.parsers.volleyball_parser import VolleyballParser
        parser = VolleyballParser()
        print("   ✅ 배구 파서 import 성공")
        
        # 야구 파서 테스트
        from sports.baseball.parsers.baseball_parser import BaseballPlayerParser
        parser = BaseballPlayerParser("<html></html>")
        print("   ✅ 야구 선수 파서 import 성공")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 스포츠 파서 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_sport_plugins():
    """스포츠 플러그인 import 테스트"""
    print("🔧 스포츠 플러그인 import 테스트")
    
    try:
        # 축구 플러그인 테스트
        from sports.soccer.plugin import SoccerPlugin
        plugin = SoccerPlugin()
        config = plugin.get_sport_config()
        assert config.sport_name == 'soccer'
        print("   ✅ 축구 플러그인 import 성공")
        
        # 농구 플러그인 테스트
        from sports.basketball.plugin import BasketballPlugin
        plugin = BasketballPlugin()
        config = plugin.get_sport_config()
        assert config.sport_name == 'basketball'
        print("   ✅ 농구 플러그인 import 성공")
        
        # 배구 플러그인 테스트
        from sports.volleyball.plugin import VolleyballPlugin
        plugin = VolleyballPlugin()
        config = plugin.get_sport_config()
        assert config.sport_name == 'volleyball'
        print("   ✅ 배구 플러그인 import 성공")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 스포츠 플러그인 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_parsing():
    """데이터 파싱 테스트"""
    print("🔧 데이터 파싱 테스트")
    
    try:
        # 축구 데이터 파싱 테스트
        from sports.soccer.parsers.soccer_parser import SoccerParser
        
        sample_html = """
        <html>
            <body>
                <h2>울산 HD FC</h2>
                <table>
                    <caption>시즌 전체성적</caption>
                    <tr><th>순위</th><th>경기수</th><th>승</th></tr>
                    <tr><td>1</td><td>30</td><td>20</td></tr>
                </table>
            </body>
        </html>
        """
        
        parser = SoccerParser()
        data = parser.parse_team_data(sample_html, "울산 HD FC", league_id="SC001", league="K리그1")
        
        assert data.team_name == "울산 HD FC"
        assert data.league == "K리그1"
        print("   ✅ 축구 데이터 파싱 테스트 통과")
        
        # 농구 데이터 파싱 테스트
        from sports.basketball.parsers.basketball_parser import BasketballParser
        
        parser = BasketballParser()
        data = parser.parse_team_data(sample_html, "서울 SK 나이츠", league_id="BK001", league="KBL")
        
        assert data.team_name == "서울 SK 나이츠"
        assert data.league == "KBL"
        print("   ✅ 농구 데이터 파싱 테스트 통과")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 데이터 파싱 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """메인 테스트 함수"""
    print("🚀 모듈 경로 문제 해결 테스트 시작")
    print("=" * 60)
    
    tests = [
        ("핵심 모듈", test_core_modules),
        ("스포츠 파서", test_sport_parsers),
        ("스포츠 플러그인", test_sport_plugins),
        ("데이터 파싱", test_data_parsing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 {test_name} 테스트 실행 중...")
            if test_func():
                passed += 1
                print(f"✅ {test_name} 테스트 통과")
            else:
                print(f"❌ {test_name} 테스트 실패")
        except Exception as e:
            print(f"❌ {test_name} 테스트 실행 실패: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎉 모듈 경로 문제 해결 테스트 완료: {passed}/{total} 통과")
    print("=" * 60)
    
    if passed == total:
        print("✅ 모든 테스트 통과! 모듈 경로 문제 해결 완료")
        return True
    else:
        print(f"⚠️ {total - passed}개 테스트 실패")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
