#!/usr/bin/env python3
"""
Test script to verify KBO baseball data processing works correctly
"""
import asyncio
from sports.baseball.services.baseball_orchestrator import BaseballOrchestrator
from database.database import connect_supabase

async def test_kbo_processing():
    """Test KBO data processing specifically"""
    print("🔄 Testing KBO baseball data processing...")
    
    try:
        # Connect to database
        client = connect_supabase()
        if not client:
            print("❌ Database connection failed")
            return
        
        # Get sample games for testing (without sport_type filter since that column doesn't exist)
        response = client.table('target_games').select('*').eq('league', 'KBO').limit(2).execute()
        
        if not response.data:
            print("ℹ️ No KBO games found in target_games table")
            return
        
        print(f"📋 Found {len(response.data)} KBO games to test")
        
        # Initialize orchestrator 
        orchestrator = BaseballOrchestrator()
        
        # Process games
        results = await orchestrator.process_games(response.data, client)
        
        print(f"✅ Processing complete: {results['teams_saved']} teams saved, {results['pitchers_saved']} pitchers saved")
        
        # Get workflow stats
        stats = orchestrator.get_workflow_stats()
        print(f"📊 Workflow stats: {stats}")
        
    except Exception as e:
        print(f"❌ Error during KBO test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_kbo_processing())