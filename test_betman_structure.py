#!/usr/bin/env python3
"""
betman.co.kr 야구 스케줄 페이지 HTML 구조 확인
"""
import asyncio
from datetime import datetime
from zoneinfo import ZoneInfo

from playwright.async_api import async_playwright


async def check_betman_structure():
    """betman.co.kr 페이지의 HTML 구조를 확인"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # 브라우저 표시
        page = await browser.new_page()
        
        try:
            # 페이지 로드
            await page.goto("https://www.betman.co.kr/main/mainPage/gameinfo/dataOfBaseballSchedule.do", timeout=30000)
            await page.wait_for_load_state('networkidle')
            
            # MLB 탭 클릭
            mlb_tab = await page.query_selector('a[href="#MLB"]')
            if mlb_tab:
                await mlb_tab.click()
                await page.wait_for_timeout(2000)
            
            # 테이블 구조 확인
            print("=== 테이블 구조 확인 ===")
            
            # vsDiv 클래스 찾기
            vs_divs = await page.query_selector_all('.vsDiv')
            print(f"vsDiv 요소 개수: {len(vs_divs)}")
            
            if vs_divs:
                # 첫 번째 vsDiv의 HTML 구조 확인
                first_vs_div = vs_divs[0]
                html = await first_vs_div.inner_html()
                print(f"첫 번째 vsDiv HTML:\n{html[:500]}...")
                
                # 팀 링크 확인
                team_links = await first_vs_div.query_selector_all('a.team')
                print(f"팀 링크 개수: {len(team_links)}")
                
                for i, link in enumerate(team_links):
                    text = await link.text_content()
                    href = await link.get_attribute('href')
                    print(f"팀 링크 {i+1}: {text} -> {href}")
                
                # 투수 정보 확인
                players = await first_vs_div.query_selector_all('.player')
                print(f"player 클래스 요소 개수: {len(players)}")
                
                for i, player in enumerate(players):
                    text = await player.text_content()
                    print(f"Player {i+1}: {text}")
                
                # 다른 가능한 선택자들 확인
                selectors_to_check = [
                    '.pitcher', '.starter', '.pitcher-name', '.starter-name',
                    'div:first-child', 'div:last-child'
                ]
                
                for selector in selectors_to_check:
                    elements = await first_vs_div.query_selector_all(selector)
                    if elements:
                        print(f"{selector} 요소 개수: {len(elements)}")
                        for i, elem in enumerate(elements[:3]):  # 처음 3개만
                            text = await elem.text_content()
                            print(f"  {selector} {i+1}: {text}")
            
            # 전체 페이지 HTML 일부 확인
            print("\n=== 전체 페이지 구조 ===")
            body_html = await page.inner_html('body')
            print(f"Body HTML 길이: {len(body_html)}")
            print(f"Body HTML 일부:\n{body_html[:1000]}...")
            
        except Exception as e:
            print(f"오류 발생: {e}")
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(check_betman_structure()) 