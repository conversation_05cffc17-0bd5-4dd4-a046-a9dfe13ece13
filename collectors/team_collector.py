"""
팀 통계 수집 및 저장 모듈
"""
from typing import Any, Dict, Optional

from supabase import Client

from database.database import save_team_stats
from parsers.baseball.kbo_stats_parser import KBOStatsParser
from parsers.baseball.mlb_stats_parser import MLBStatsParser
from parsers.baseball.npb_stats_parser import NPBStatsParser
from utils.logger import Logger

# 로거 설정
logger = Logger(__name__)


def collect_team_stats(team_id: str, url: str, html: str) -> Dict[str, Any]:
    """
    팀 ID와 URL을 기반으로 적절한 리그의 팀 통계를 수집 (core 파서 사용)
    
    Args:
        team_id: 팀 ID
        url: 팀 통계 URL
        html: HTML 콘텐츠
    
    Returns:
        Dict: 수집된 팀 통계 데이터
    """
    stats = {}
    
    try:
        # URL 분석하여 리그 타입 결정하고 core 파서 사용
        if 'BsKbo' in url or 'BS001' in url:
            logger.info(f"[KBO] {team_id} 팀 통계 수집 시작 (core)")
            parser = KBOStatsParser(html)
            stats = {
                "seasons_summary": parser.parse_season_summary(),
                "recent_games": parser.parse_recent_games(),
                "recent_games_summary": (
                    parser.parse_recent_games_summary()
                ),
                "season_stats": parser.parse_season_stats()
            }
        elif 'BsMlb' in url or 'BS002' in url:
            logger.info(f"[MLB] {team_id} 팀 통계 수집 시작 (core)")
            parser = MLBStatsParser(html)
            stats = {
                "seasons_summary": parser.parse_season_summary(),
                "recent_games": parser.parse_recent_games(),
                "recent_games_summary": (
                    parser.parse_recent_games_summary()
                ),
                "season_stats": parser.parse_season_stats()
            }
        elif 'BsNpb' in url or 'BS004' in url:
            logger.info(f"[NPB] {team_id} 팀 통계 수집 시작 (core)")
            parser = NPBStatsParser(html)
            stats = {
                "seasons_summary": parser.parse_season_summary(),
                "recent_games": parser.parse_recent_games(),
                "recent_games_summary": (
                    parser.parse_recent_games_summary()
                ),
                "season_stats": parser.parse_season_stats()
            }
        else:
            logger.warning(f"지원하지 않는 리그 URL: {url}")
            return {}
        
        # 로깅
        season_stats = stats.get("season_stats", {})
        season_count = len(season_stats)
        logger.info(f"=== [LOG] season_stats ({team_id}) ===")
        logger.info(f"역대 시즌 기록: {season_count}개 시즌")
        
    except Exception as e:
        logger.error(f"팀 통계 수집 중 오류 발생: {str(e)}")
    
    return stats


def collect_and_save_team_stats(
    client: Client,
    team_id: str,
    url: str,
    save: bool = True,
    date: Optional[str] = None
) -> Dict[str, Any]:
    """
    팀 통계를 수집하고 데이터베이스에 저장
    
    Args:
        client: Supabase 클라이언트
        team_id: 팀 ID
        url: 팀 통계 URL
        save: 데이터베이스 저장 여부
        date: 데이터 저장 날짜 (기본값: 오늘)
    
    Returns:
        Dict: 수집된 팀 통계 데이터
    """
    try:
        # 팀 통계 수집 (HTML 콘텐츠 필요 - 별도 구현 필요)
        # stats = collect_team_stats(team_id, url, html)
        stats = {}
        
        if not stats:
            logger.warning(f"{team_id} 팀 통계 수집 실패")
            return {}
        
        # 데이터베이스에 저장
        if save:
            save_success = save_team_stats(client, team_id, stats, date)
            if save_success:
                logger.info(f"{team_id} 팀 통계 저장 성공")
            else:
                logger.warning(f"{team_id} 팀 통계 저장 실패")
        
        return stats
    except Exception as e:
        logger.error(f"팀 통계 수집 및 저장 중 오류 발생: {str(e)}")
        return {} 