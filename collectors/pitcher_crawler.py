"""
투수 크롤러 - 브라우저 관리 및 페이지 크롤링 전담 (기존 검증된 로직 적용)
"""
import asyncio
import logging
import time
from typing import Dict, Optional

from playwright.async_api import Page, async_playwright

from utils.logger import Logger

# 로거 설정
logger = Logger(__name__, level=logging.ERROR)


class PitcherCrawler:
    """투수 페이지 크롤링 전담 클래스 (검증된 로직)"""
    
    def __init__(self):
        self.browser = None
        self.playwright = None
        self.cache = {}
        self.team_mappings = {}
    
    async def initialize_browser(self):
        """브라우저 초기화"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=True)
    
    async def cleanup(self):
        """브라우저 종료 - 메모리 누수 방지"""
        errors = []
        
        try:
            if self.browser:
                try:
                    await self.browser.close()
                except Exception as e:
                    errors.append(f"브라우저 종료 실패: {e}")
                finally:
                    self.browser = None
            
            if self.playwright:
                try:
                    await self.playwright.stop()
                except Exception as e:
                    errors.append(f"Playwright 종료 실패: {e}")
                finally:
                    self.playwright = None
            
            if errors:
                logger.warning(f"브라우저 정리 중 일부 오류 발생: {errors}")
                
        except Exception as e:
            logger.error(f"브라우저 정리 중 예상치 못한 오류: {e}")
            # 강제 정리
            self.browser = None
            self.playwright = None
    
    def set_team_mappings(self, team_mappings: Dict[str, str]):
        """팀 매핑 설정"""
        self.team_mappings = team_mappings
    
    async def crawl_pitcher_stats(
        self,
        pitcher_name: str,
        team_url: str
    ) -> Optional[Dict]:
        """투수 통계 크롤링 (기존 검증된 로직)"""
        if not pitcher_name or not team_url:
            return None
        
        # 캐시 확인
        cache_key = f"{pitcher_name}_{team_url}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        # 실제 크롤링
        try:
            stats = await self._crawl_pitcher_stats(pitcher_name, team_url)
            if stats:
                self.cache[cache_key] = stats
                return stats
        except Exception as e:
            logger.debug(f"투수 통계 수집 실패 - {pitcher_name}: {e}")
        
        return None
    
    async def _crawl_pitcher_stats(
        self,
        pitcher_name: str,
        team_url: str
    ) -> Optional[Dict]:
        if not self.browser:
            return PitcherCrawler._create_no_data_stats(pitcher_name)
        page = None
        t0 = time.time()
        try:
            page = await self.browser.new_page()
            # 페이지 로드 시도
            load_success = await PitcherCrawler._load_team_page(
                page, team_url, pitcher_name
            )
            if not load_success:
                logger.info(f"{pitcher_name} 팀페이지 로드 실패 - {time.time() - t0:.2f}s")
                return PitcherCrawler._create_no_data_stats(pitcher_name)
            # 투수 링크 찾기 및 클릭
            if not await PitcherCrawler._find_and_click_pitcher_link(
                page, pitcher_name
            ):
                logger.info(f"{pitcher_name} 투수링크 없음 - {time.time() - t0:.2f}s")
                return PitcherCrawler._create_no_data_stats(pitcher_name)
            # 투수 페이지 로드 대기
            await PitcherCrawler._wait_for_pitcher_page_load(
                page, pitcher_name
            )
            # 통계 파싱 (파싱 실패도 3회 재시도)
            for retry in range(3):
                try:
                    stats = await self._parse_pitcher_page(page, pitcher_name)
                    if stats and isinstance(stats, dict) and stats.get('name'):
                        logger.info(f"{pitcher_name} 전체 소요: {time.time() - t0:.2f}s")
                        return stats
                    else:
                        raise ValueError('파싱 결과 이상 또는 name 없음')
                except Exception as e:
                    if retry == 2:
                        logger.warning(
                            f"❌ {pitcher_name} 파싱 최종 실패 (3회 시도): {e}"
                        )
                        return PitcherCrawler._create_no_data_stats(
                            pitcher_name
                        )
                    else:
                        logger.debug(
                            f"⚠️ {pitcher_name} 파싱 재시도 {retry + 1}/3: {e}"
                        )
                        await asyncio.sleep(1)
        except Exception as e:
            logger.debug(f"크롤링 오류 - {pitcher_name}: {e}")
            return PitcherCrawler._create_no_data_stats(pitcher_name)
        finally:
            if page:
                try:
                    await page.close()
                except Exception:
                    pass

    @staticmethod
    async def _load_team_page(
        page: Page, team_url: str, pitcher_name: str
    ) -> bool:
        t0 = time.time()
        for retry in range(2):
            try:
                await page.goto(team_url, timeout=10000)
                await page.wait_for_selector("table.tbl", timeout=5000)
                logger.info(f"{pitcher_name} 팀페이지 로드 소요: {time.time() - t0:.2f}s")
                return True
            except Exception as e:
                if retry == 1:
                    logger.warning(f"❌ {pitcher_name} 최종 실패 (2회 시도): {e}")
                    return False
                else:
                    logger.debug(f"⚠️ {pitcher_name} 재시도 {retry + 1}/2: {e}")
                    await asyncio.sleep(1)
        return False

    async def _parse_pitcher_page(self, page: Page, pitcher_name: str) -> Dict:
        t0 = time.time()
        try:
            html = await page.content()
            from parsers.baseball.pitcher_parser import PitcherDataParser
            parser = PitcherDataParser(team_mappings=self.team_mappings)
            result = parser.parse_pitcher_data(html, pitcher_name)
            logger.info(f"{pitcher_name} 파싱 완료 - {time.time() - t0:.2f}s")
            return result
        except Exception as e:
            logger.debug(f"통계 파싱 오류 - {pitcher_name}: {e}")
            return PitcherCrawler._create_no_data_stats(pitcher_name)

    @staticmethod
    async def _wait_for_pitcher_page_load(
        page: Page, pitcher_name: str
    ) -> None:
        t0 = time.time()
        try:
            await page.wait_for_load_state('networkidle', timeout=9000)
            await page.wait_for_selector('.infoBox', timeout=7000)
            logger.info(
                f"{pitcher_name} 상세페이지 로드 소요: {time.time() - t0:.2f}s"
            )
        except Exception as e:
            logger.debug(f"페이지 로드 대기 중 오류: {pitcher_name} - {e}")
            await asyncio.sleep(4)
    
    @staticmethod
    def _create_no_data_stats(pitcher_name: str) -> Dict:
        """No data 케이스를 위한 기본 통계 구조"""
        return {
            'name': pitcher_name,
            'team': '',
            'birth': '',
            'height': 0,
            'weight': '',
            'season_stats': [{"games": "No data"}],
            'recent_games': {}
        }

    @staticmethod
    async def _find_and_click_pitcher_link(
        page: 'Page', pitcher_name: str
    ) -> bool:
        """투수 링크 찾기 및 클릭"""
        pitcher_link = await PitcherCrawler._find_pitcher_link(
            page, pitcher_name
        )
        if not pitcher_link:
            logger.debug(f"투수 링크 찾기 실패: {pitcher_name}")
            return False
        try:
            await pitcher_link.click()
            return True
        except Exception as e:
            logger.debug(f"투수 링크 클릭 실패: {pitcher_name} - {e}")
            return False

    @staticmethod
    async def _find_pitcher_link(page: 'Page', pitcher_name: str):
        """더 정확한 투수 링크 찾기"""
        selectors = [
            f'a.player:has-text("{pitcher_name}")',
            f'a[href*="bsPlayerDetail"]:has-text("{pitcher_name}")',
            f'span.player:has-text("{pitcher_name}") + a',
            f'*:has-text("{pitcher_name}") a'
        ]
        for selector in selectors:
            try:
                pitcher_link = await page.query_selector(selector)
                if pitcher_link:
                    return pitcher_link
            except Exception:
                continue
        return None

    # 컨텍스트 매니저 지원 추가
    async def __aenter__(self):
        """비동기 컨텍스트 매니저 진입"""
        await self.initialize_browser()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """비동기 컨텍스트 매니저 종료"""
        await self.cleanup()