"""
크롤링 관련 모듈 패키지
브라우저 관리, 페이지 크롤링, 데이터 수집 등을 담당
"""

# 내부 모듈 임포트
from collectors.base_crawler import BaseCrawler, ConsoleObserver
from collectors.pitcher_crawler import PitcherCrawler
from collectors.unified_crawler import UnifiedDataCollector
from utils.logger import Logger

# 로거 초기화
logger = Logger(__name__)

__all__ = [
    'BaseCrawler',
    'ConsoleObserver', 
    'UnifiedDataCollector',
    'PitcherCrawler',
    'logger'
] 