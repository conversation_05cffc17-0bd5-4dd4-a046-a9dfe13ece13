"""
팀 통계 브라우저 관리 서비스
Single Responsibility: 브라우저 생성, 관리, 종료만 담당
"""
import asyncio
from typing import Dict, Optional

from playwright.async_api import (<PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontext, <PERSON>,
                                  async_playwright)

from utils.logger import Logger

# 로거 설정
logger = Logger(__name__)


class TeamStatsBrowser:
    """팀 통계 수집용 브라우저 관리"""
    
    def __init__(self):
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
    
    async def initialize(self) -> bool:
        """브라우저 초기화"""
        try:
            self.playwright = await async_playwright().start()
            # 브라우저 최적화 설정 (속도 향상)
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--disable-images',
                    '--disable-web-security', 
                    '--disable-features=VizDisplayCompositor',
                    '--disable-extensions',
                    '--no-sandbox',
                    '--disable-dev-shm-usage',  # 메모리 최적화
                    '--disable-background-timers',  # 백그라운드 타이머 비활성화
                    '--disable-renderer-backgrounding',  # 렌더러 백그라운딩 비활성화
                    '--disable-backgrounding-occluded-windows',
                    '--memory-pressure-off',  # 메모리 압박 모드 비활성화
                    '--aggressive-cache-discard',
                    '--single-process',
                    '--js-flags=--max-old-space-size=1024'  # 1GB 제한
                ]
            )
            # 컨텍스트 최적화 설정
            self.context = await self.browser.new_context(
                java_script_enabled=True,  # 필요한 JS만 활성화
                bypass_csp=True,
                ignore_https_errors=True
            )
            return True
        except Exception as e:
            logger.error(f"브라우저 초기화 실패: {e}")
            return False
    
    async def create_page(self) -> Optional[Page]:
        """새 페이지 생성"""
        if not self.context:
            return None
        
        try:
            return await self.context.new_page()
        except Exception as e:
            logger.error(f"페이지 생성 실패: {e}")
            return None
    
    @staticmethod
    async def navigate_to_team_page(
        page: Page, 
        stats_url: str, 
        team_name: str,
        max_retries: int = 3
    ) -> bool:
        """팀 통계 페이지로 이동 (재시도 로직 포함)"""
        for retry in range(max_retries):
            try:
                await page.goto(stats_url, timeout=30000)
                await page.wait_for_selector("table.tbl", timeout=15000)
                return True
            except Exception as e:
                if retry == max_retries - 1:
                    logger.warning(
                        f"❌ {team_name} 최종 실패 ({max_retries}회 시도): {e}"
                    )
                    return False
                else:
                    logger.debug(
                        f"⚠️ {team_name} 재시도 {retry + 1}/{max_retries}"
                    )
                    await asyncio.sleep(3)
        
        return False
    
    @staticmethod
    async def click_recent_games_button(
        page: Page, games_count: str = "10"
    ) -> None:
        """최근 경기 수 버튼 클릭"""
        try:
            await page.click(
                'button:has-text("경기수 선택 펼치기")', 
                timeout=3000
            )
            await page.click(
                f'a:has-text("최근 {games_count}경기")', timeout=3000
            )
            await page.wait_for_selector('#selLatestCount', timeout=3000)
            await page.wait_for_timeout(500)  # 최적화
        except Exception:
            # 버튼 클릭 실패는 무시 (선택사항)
            pass

    @staticmethod
    async def set_games_count(page: Page, count: str) -> None:
        """경기수 설정 (5 또는 10)"""
        try:
            # JavaScript 함수로 직접 설정
            await page.evaluate(
                f"searchLatestRecord('latestCount', '{count}')"
            )
            await page.wait_for_timeout(500)  # 최적화
        except Exception:
            # 실패시 버튼 클릭 방식으로 대체
            await TeamStatsBrowser.click_recent_games_button(page, count)
    
    @staticmethod
    async def click_homeaway_button(page: Page, option: str) -> None:
        """
        홈/원정 선택 버튼 클릭
        
        Args:
            page: 플레이라이트 페이지
            option: '0'=전체, '1'=홈, '2'=원정
        """
        try:
            # JavaScript 함수 직접 호출
            await page.evaluate(f"searchLatestRecord('homeAway', '{option}')")
            await page.wait_for_timeout(500)  # 데이터 로딩 대기 (더 최적화)
        except Exception as e:
            logger.debug(f"홈/원정 버튼 클릭 실패 (option={option}): {e}")
            pass
    
    @staticmethod
    async def get_page_content(page: Page) -> str:
        """페이지 HTML 컨텐츠 가져오기"""
        try:
            return await page.content()
        except Exception as e:
            logger.error(f"페이지 컨텐츠 조회 실패: {e}")
            return ""
    
    @staticmethod
    async def close_page(page: Page) -> None:
        """페이지 종료"""
        try:
            await page.close()
        except Exception:
            pass
    
    @staticmethod
    async def collect_homeaway_data(
        page: Page, team_name: str
    ) -> Dict[str, str]:
        """
        전체/홈/원정 각각의 데이터를 수집
        - 모든 카테고리: 5경기로 통일
        
        Returns:
            Dict: {'total': html, 'home': html, 'away': html}
        """
        data = {}
        
        # 카테고리별 설정: (홈/원정 옵션, 경기수)
        category_configs = {
            'total': ('0', '5'),    # 전체, 5경기
            'home': ('1', '5'),     # 홈, 5경기  
            'away': ('2', '5')      # 원정, 5경기
        }
        
        for category, (option, games_count) in category_configs.items():
            try:
                # 경기수 설정
                await TeamStatsBrowser.set_games_count(page, games_count)
                
                # 해당 옵션 클릭
                await TeamStatsBrowser.click_homeaway_button(page, option)
                
                # HTML 컨텐츠 가져오기
                html = await TeamStatsBrowser.get_page_content(page)
                data[category] = html
                
            except Exception as e:
                logger.warning(
                    f"❌ {team_name} {category} 데이터 수집 실패: {e}"
                )
                data[category] = ""
        
        return data
    
    async def cleanup(self) -> None:
        """브라우저와 playwright 종료 - 메모리 누수 방지"""
        errors = []
        
        try:
            # 브라우저 컨텍스트 종료
            if self.context:
                try:
                    await self.context.close()
                except Exception as e:
                    errors.append(f"브라우저 컨텍스트 종료 실패: {e}")
                finally:
                    self.context = None
            
            # Playwright 브라우저 종료
            if self.browser:
                try:
                    await self.browser.close()
                except Exception as e:
                    errors.append(f"브라우저 종료 실패: {e}")
                finally:
                    self.browser = None
            
            # Playwright 종료
            if self.playwright:
                try:
                    await self.playwright.stop()
                except Exception as e:
                    errors.append(f"Playwright 종료 실패: {e}")
                finally:
                    self.playwright = None
            
            if errors:
                logger.warning(f"브라우저 정리 중 일부 오류 발생: {errors}")
                
        except Exception as e:
            logger.error(f"브라우저 정리 중 예상치 못한 오류: {e}")
            # 강제 정리
            self.context = None
            self.browser = None
            self.playwright = None
    
    def is_initialized(self) -> bool:
        """브라우저 초기화 상태 확인"""
        return (self.playwright is not None and 
                self.browser is not None and 
                self.context is not None)