#!/usr/bin/env python3
"""
농구와 배구 선수 데이터 파싱 테스트
"""
import asyncio
import sys
import os
from bs4 import BeautifulSoup
from typing import Dict, List, Optional

# 프로젝트 루트를 Python path에 추가
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class ImprovedBasketballPlayerParser:
    """개선된 농구 선수 파서"""
    
    def __init__(self, html: str):
        self.soup = BeautifulSoup(html, 'html.parser')
    
    def parse_player_profile(self) -> Dict[str, str]:
        """선수 프로필 파싱"""
        profile = {}
        
        # 선수명 추출 (h4 태그에서)
        player_name_elem = self.soup.find('h4')
        if player_name_elem:
            profile['player_name'] = player_name_elem.get_text(strip=True)
        
        # 선수 정보 리스트에서 추출
        info_list = self.soup.find('ul')
        if info_list:
            items = info_list.find_all('li')
            
            for item in items:
                text = item.get_text(strip=True)
                
                if '생년월일' in text:
                    profile['birth_date'] = text.replace('생년월일', '').strip()
                elif '신장' in text:
                    profile['height'] = text.replace('신장', '').strip()
                elif '체중' in text:
                    profile['weight'] = text.replace('체중', '').strip()
                elif '포지션' in text:
                    profile['position'] = text.replace('포지션', '').strip()
                elif '시즌성적' in text:
                    # 시즌 성적 요약 파싱
                    profile['season_summary'] = text.replace('시즌성적', '').strip()
        
        # 등번호와 팀명 추출
        strong_tags = self.soup.find_all('strong')
        for strong in strong_tags:
            text = strong.get_text(strip=True)
            if 'No.' in text:
                profile['back_number'] = text.replace('No.', '').strip()
            elif text and len(text) > 1 and text not in ['생년월일', '포지션', '신장', '체중', '시즌성적']:
                profile['team'] = text
        
        return profile
    
    def parse_player_stats(self) -> Dict[str, List[Dict]]:
        """선수 통계 파싱"""
        stats = {
            'season_stats': [],
            'season_records': [],
            'career_stats': []
        }
        
        tables = self.soup.find_all('table')
        
        for table in tables:
            caption = table.find('caption')
            caption_text = caption.get_text(strip=True) if caption else ""
            
            rows = table.find_all('tr')
            if len(rows) < 3:
                continue
            
            # 농구는 복잡한 헤더 구조 (2행)
            header_row1 = rows[0].find_all(['th', 'td'])
            header_row2 = rows[1].find_all(['th', 'td'])
            
            # 헤더 조합
            headers = []
            for i, cell in enumerate(header_row1):
                header1 = cell.get_text(strip=True)
                if i < len(header_row2):
                    header2 = header_row2[i].get_text(strip=True)
                    if header2 and header2 != header1:
                        headers.append(f"{header1}_{header2}")
                    else:
                        headers.append(header1)
                else:
                    headers.append(header1)
            
            # 데이터 행들 추출
            data_rows = []
            for row in rows[2:]:
                cells = row.find_all(['th', 'td'])
                if len(cells) >= len(headers):
                    row_data = {}
                    for j, cell in enumerate(cells):
                        if j < len(headers):
                            row_data[headers[j]] = cell.get_text(strip=True)
                    if row_data:
                        data_rows.append(row_data)
            
            # 테이블 분류
            if '시즌 성적' in caption_text and '출전기록' not in caption_text:
                stats['season_stats'].extend(data_rows)
            elif '출전기록' in caption_text:
                stats['season_records'].extend(data_rows)
            elif '역대시즌' in caption_text:
                stats['career_stats'].extend(data_rows)
        
        return stats


class ImprovedVolleyballPlayerParser:
    """개선된 배구 선수 파서"""
    
    def __init__(self, html: str):
        self.soup = BeautifulSoup(html, 'html.parser')
    
    def parse_player_profile(self) -> Dict[str, str]:
        """선수 프로필 파싱"""
        profile = {}
        
        # 선수명 추출 (h4 태그에서)
        player_name_elem = self.soup.find('h4')
        if player_name_elem:
            profile['player_name'] = player_name_elem.get_text(strip=True)
        
        # 선수 정보 리스트에서 추출
        info_list = self.soup.find('ul')
        if info_list:
            items = info_list.find_all('li')
            
            for item in items:
                text = item.get_text(strip=True)
                
                if '생년월일' in text:
                    profile['birth_date'] = text.replace('생년월일', '').strip()
                elif '신장' in text:
                    profile['height'] = text.replace('신장', '').strip()
                elif '체중' in text:
                    profile['weight'] = text.replace('체중', '').strip()
                elif '포지션' in text:
                    profile['position'] = text.replace('포지션', '').strip()
                elif '시즌성적' in text:
                    # 시즌 성적 요약 파싱
                    profile['season_summary'] = text.replace('시즌성적', '').strip()
        
        # 등번호와 팀명 추출
        strong_tags = self.soup.find_all('strong')
        for strong in strong_tags:
            text = strong.get_text(strip=True)
            if 'No.' in text:
                profile['back_number'] = text.replace('No.', '').strip()
            elif text and len(text) > 1 and text not in ['생년월일', '포지션', '신장', '체중', '시즌성적']:
                profile['team'] = text
        
        return profile
    
    def parse_player_stats(self) -> Dict[str, List[Dict]]:
        """선수 통계 파싱"""
        stats = {
            'season_stats': [],
            'season_records': [],
            'career_stats': []
        }
        
        tables = self.soup.find_all('table')
        
        for table in tables:
            caption = table.find('caption')
            caption_text = caption.get_text(strip=True) if caption else ""
            
            rows = table.find_all('tr')
            if len(rows) < 3:
                continue
            
            # 배구는 복잡한 헤더 구조 (2행)
            header_row1 = rows[0].find_all(['th', 'td'])
            header_row2 = rows[1].find_all(['th', 'td'])
            
            # 헤더 조합
            headers = []
            for i, cell in enumerate(header_row1):
                header1 = cell.get_text(strip=True)
                if i < len(header_row2):
                    header2 = header_row2[i].get_text(strip=True)
                    if header2 and header2 != header1:
                        headers.append(f"{header1}_{header2}")
                    else:
                        headers.append(header1)
                else:
                    headers.append(header1)
            
            # 데이터 행들 추출
            data_rows = []
            for row in rows[2:]:
                cells = row.find_all(['th', 'td'])
                if len(cells) >= len(headers):
                    row_data = {}
                    for j, cell in enumerate(cells):
                        if j < len(headers):
                            row_data[headers[j]] = cell.get_text(strip=True)
                    if row_data:
                        data_rows.append(row_data)
            
            # 테이블 분류
            if '시즌 성적' in caption_text and '출전기록' not in caption_text:
                stats['season_stats'].extend(data_rows)
            elif '출전기록' in caption_text:
                stats['season_records'].extend(data_rows)
            elif '역대시즌' in caption_text:
                stats['career_stats'].extend(data_rows)
        
        return stats


async def test_basketball_player_parsing():
    """농구 선수 파싱 테스트"""
    print("=" * 60)
    print("🏀 농구 선수 데이터 파싱 테스트")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        
        # 농구 선수 페이지 크롤링
        player_url = "https://www.betman.co.kr/main/mainPage/gameinfo/bkPlayerDetail.do?item=BK&leagueId=BK002&teamId=HOU&playerId=6959"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print(f"📄 농구 선수 페이지 로딩: {player_url}")
            await page.goto(player_url, wait_until='networkidle')
            await asyncio.sleep(3)
            
            html = await page.content()
            await browser.close()
            
            # 개선된 파서로 데이터 추출
            parser = ImprovedBasketballPlayerParser(html)
            
            # 선수 프로필
            profile = parser.parse_player_profile()
            print(f"\n👤 농구 선수 프로필:")
            for key, value in profile.items():
                if value:
                    print(f"     {key}: {value}")
            
            # 선수 통계
            player_stats = parser.parse_player_stats()
            print(f"\n📊 농구 선수 통계:")
            for stat_type, data in player_stats.items():
                print(f"   {stat_type}: {len(data)}개 항목")
                if data:
                    print(f"     첫 번째 항목 키: {list(data[0].keys())[:5]}...")
                    print(f"     샘플 데이터: {data[0]}")
            
            return True
            
    except Exception as e:
        print(f"❌ 농구 선수 파싱 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_volleyball_player_parsing():
    """배구 선수 파싱 테스트"""
    print("\n" + "=" * 60)
    print("🏐 배구 선수 데이터 파싱 테스트")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        
        # 배구 선수 페이지 크롤링
        player_url = "https://www.betman.co.kr/main/mainPage/gameinfo/vlPlayerDetail.do?item=VL&leagueId=VL001&teamId=1005&playerId=0000853&id=1"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print(f"📄 배구 선수 페이지 로딩: {player_url}")
            await page.goto(player_url, wait_until='networkidle')
            await asyncio.sleep(3)
            
            html = await page.content()
            await browser.close()
            
            # 개선된 파서로 데이터 추출
            parser = ImprovedVolleyballPlayerParser(html)
            
            # 선수 프로필
            profile = parser.parse_player_profile()
            print(f"\n👤 배구 선수 프로필:")
            for key, value in profile.items():
                if value:
                    print(f"     {key}: {value}")
            
            # 선수 통계
            player_stats = parser.parse_player_stats()
            print(f"\n📊 배구 선수 통계:")
            for stat_type, data in player_stats.items():
                print(f"   {stat_type}: {len(data)}개 항목")
                if data:
                    print(f"     첫 번째 항목 키: {list(data[0].keys())[:5]}...")
                    print(f"     샘플 데이터: {data[0]}")
            
            return True
            
    except Exception as e:
        print(f"❌ 배구 선수 파싱 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """메인 테스트 함수"""
    print("🚀 농구/배구 선수 데이터 파싱 테스트 시작")
    print("=" * 80)
    
    tests = [
        ("농구 선수", test_basketball_player_parsing),
        ("배구 선수", test_volleyball_player_parsing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 {test_name} 테스트 실행 중...")
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 테스트 통과")
            else:
                print(f"❌ {test_name} 테스트 실패")
        except Exception as e:
            print(f"❌ {test_name} 테스트 실행 실패: {e}")
    
    print("\n" + "=" * 80)
    print(f"🎉 농구/배구 선수 파싱 테스트 완료: {passed}/{total} 통과")
    print("=" * 80)
    
    if passed == total:
        print("✅ 모든 테스트 통과! 농구/배구 선수 데이터 파싱 성공")
    else:
        print(f"⚠️ {total - passed}개 테스트 실패")


if __name__ == "__main__":
    asyncio.run(main())
