"""
정적 리소스 관리
"""
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


@dataclass
class URLPatterns:
    """URL 패턴 정의"""
    base_url: str
    team_detail: str
    player_detail: str
    schedule: str
    
    def get_team_url(self, **kwargs) -> str:
        """팀 상세 URL 생성"""
        return self.team_detail.format(**kwargs)
    
    def get_player_url(self, **kwargs) -> str:
        """선수 상세 URL 생성"""
        return self.player_detail.format(**kwargs)


@dataclass
class LeagueMapping:
    """리그 매핑 정보"""
    id: str
    name: str
    short_name: str
    country: str
    sport_code: str
    tab_id: Optional[str] = None
    tab_order: Optional[int] = None


@dataclass
class TeamMapping:
    """팀 매핑 정보"""
    id: str
    name: str
    full_name: str
    league_id: str
    sport_code: str
    city: Optional[str] = None
    founded: Optional[str] = None


class StaticResourceManager:
    """정적 리소스 관리자"""
    
    def __init__(self, resource_dir: Optional[Path] = None):
        """
        초기화
        
        Args:
            resource_dir: 리소스 디렉토리 경로
        """
        self.resource_dir = resource_dir or Path(__file__).parent / "resources"
        self.resource_dir.mkdir(exist_ok=True)
        
        # 캐시
        self._url_patterns_cache: Dict[str, URLPatterns] = {}
        self._league_mappings_cache: Dict[str, LeagueMapping] = {}
        self._team_mappings_cache: Dict[str, TeamMapping] = {}
        
        # 리소스 로드
        self._load_all_resources()
    
    def _load_all_resources(self):
        """모든 리소스 로드"""
        try:
            self._load_url_patterns()
            self._load_league_mappings()
            self._load_team_mappings()
            logger.info("정적 리소스 로드 완료")
        except Exception as e:
            logger.error(f"정적 리소스 로드 실패: {e}")
    
    def _load_url_patterns(self):
        """URL 패턴 로드"""
        patterns_file = self.resource_dir / "url_patterns.json"
        
        if not patterns_file.exists():
            self._create_default_url_patterns(patterns_file)
        
        try:
            with open(patterns_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for sport, patterns in data.items():
                self._url_patterns_cache[sport] = URLPatterns(**patterns)
                
        except Exception as e:
            logger.error(f"URL 패턴 로드 실패: {e}")
            self._create_default_url_patterns(patterns_file)
    
    def _create_default_url_patterns(self, file_path: Path):
        """기본 URL 패턴 생성"""
        default_patterns = {
            "soccer": {
                "base_url": "https://www.betman.co.kr",
                "team_detail": "/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId={league_id}&teamId={team_id}",
                "player_detail": "/main/mainPage/gameinfo/scPlayerDetail.do?item=SC&leagueId={league_id}&teamId={team_id}&playerId={player_id}",
                "schedule": "/main/mainPage/gameinfo/dataOfSoccerSchedule.do"
            },
            "basketball": {
                "base_url": "https://www.betman.co.kr",
                "team_detail": "/main/mainPage/gameinfo/bkTeamDetail.do?item=BK&leagueId={league_id}&teamId={team_id}",
                "player_detail": "/main/mainPage/gameinfo/bkPlayerDetail.do?item=BK&leagueId={league_id}&teamId={team_id}&playerId={player_id}",
                "schedule": "/main/mainPage/gameinfo/dataOfBasketballSchedule.do"
            },
            "volleyball": {
                "base_url": "https://www.betman.co.kr",
                "team_detail": "/main/mainPage/gameinfo/vlTeamDetail.do?item=VL&leagueId={league_id}&teamId={team_id}",
                "player_detail": "/main/mainPage/gameinfo/vlPlayerDetail.do?item=VL&leagueId={league_id}&teamId={team_id}&playerId={player_id}",
                "schedule": "/main/mainPage/gameinfo/dataOfVolleyballSchedule.do"
            },
            "baseball": {
                "base_url": "https://www.betman.co.kr",
                "team_detail": "/main/mainPage/gameinfo/bsTeamDetail.do?item=BS&leagueId={league_id}&teamId={team_id}",
                "player_detail": "/main/mainPage/gameinfo/bsPlayerDetail.do?item=BS&leagueId={league_id}&teamId={team_id}&playerId={player_id}",
                "schedule": "/main/mainPage/gameinfo/dataOfBaseballSchedule.do"
            }
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(default_patterns, f, ensure_ascii=False, indent=2)
        
        # 캐시에 로드
        for sport, patterns in default_patterns.items():
            self._url_patterns_cache[sport] = URLPatterns(**patterns)
    
    def _load_league_mappings(self):
        """리그 매핑 로드"""
        mappings_file = self.resource_dir / "league_mappings.json"
        
        if not mappings_file.exists():
            self._create_default_league_mappings(mappings_file)
        
        try:
            with open(mappings_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for league_data in data:
                mapping = LeagueMapping(**league_data)
                self._league_mappings_cache[mapping.id] = mapping
                
        except Exception as e:
            logger.error(f"리그 매핑 로드 실패: {e}")
    
    def _create_default_league_mappings(self, file_path: Path):
        """기본 리그 매핑 생성"""
        default_leagues = [
            # 축구
            {"id": "SC001", "name": "K리그1", "short_name": "K1", "country": "KOR", "sport_code": "SC"},
            {"id": "SC003", "name": "K리그2", "short_name": "K2", "country": "KOR", "sport_code": "SC"},
            {"id": "52", "name": "EPL", "short_name": "EPL", "country": "ENG", "sport_code": "SC"},
            {"id": "53", "name": "프리메라리가", "short_name": "La Liga", "country": "ESP", "sport_code": "SC"},
            {"id": "54", "name": "세리에A", "short_name": "Serie A", "country": "ITA", "sport_code": "SC"},
            {"id": "55", "name": "분데스리가", "short_name": "Bundesliga", "country": "GER", "sport_code": "SC"},
            {"id": "56", "name": "프랑스리그", "short_name": "Ligue 1", "country": "FRA", "sport_code": "SC"},
            {"id": "57", "name": "에레디비시", "short_name": "Eredivisie", "country": "NED", "sport_code": "SC"},
            {"id": "SC007", "name": "J리그", "short_name": "J-League", "country": "JPN", "sport_code": "SC"},
            
            # 농구
            {"id": "BK001", "name": "KBL", "short_name": "KBL", "country": "KOR", "sport_code": "BK"},
            {"id": "BK002", "name": "NBA", "short_name": "NBA", "country": "USA", "sport_code": "BK"},
            {"id": "BK003", "name": "WKBL", "short_name": "WKBL", "country": "KOR", "sport_code": "BK"},
            
            # 배구
            {"id": "VL001", "name": "KOVO남", "short_name": "KOVO남", "country": "KOR", "sport_code": "VL"},
            {"id": "VL002", "name": "KOVO여", "short_name": "KOVO여", "country": "KOR", "sport_code": "VL"},
            
            # 야구
            {"id": "BB001", "name": "KBO", "short_name": "KBO", "country": "KOR", "sport_code": "BB"},
            {"id": "BB002", "name": "MLB", "short_name": "MLB", "country": "USA", "sport_code": "BB"},
            {"id": "BB003", "name": "NPB", "short_name": "NPB", "country": "JPN", "sport_code": "BB"},
        ]
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(default_leagues, f, ensure_ascii=False, indent=2)
        
        # 캐시에 로드
        for league_data in default_leagues:
            mapping = LeagueMapping(**league_data)
            self._league_mappings_cache[mapping.id] = mapping
    
    def _load_team_mappings(self):
        """팀 매핑 로드"""
        mappings_file = self.resource_dir / "team_mappings.json"
        
        if mappings_file.exists():
            try:
                with open(mappings_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for team_data in data:
                    mapping = TeamMapping(**team_data)
                    self._team_mappings_cache[f"{mapping.sport_code}_{mapping.id}"] = mapping
                    
            except Exception as e:
                logger.error(f"팀 매핑 로드 실패: {e}")
    
    def get_url_patterns(self, sport: str) -> Optional[URLPatterns]:
        """URL 패턴 조회"""
        return self._url_patterns_cache.get(sport)
    
    def get_league_mapping(self, league_id: str) -> Optional[LeagueMapping]:
        """리그 매핑 조회"""
        return self._league_mappings_cache.get(league_id)
    
    def get_team_mapping(self, sport_code: str, team_id: str) -> Optional[TeamMapping]:
        """팀 매핑 조회"""
        key = f"{sport_code}_{team_id}"
        return self._team_mappings_cache.get(key)
    
    def get_all_leagues_by_sport(self, sport_code: str) -> List[LeagueMapping]:
        """스포츠별 모든 리그 조회"""
        return [
            mapping for mapping in self._league_mappings_cache.values()
            if mapping.sport_code == sport_code
        ]
    
    def get_all_teams_by_league(self, league_id: str) -> List[TeamMapping]:
        """리그별 모든 팀 조회"""
        return [
            mapping for mapping in self._team_mappings_cache.values()
            if mapping.league_id == league_id
        ]
    
    def add_team_mapping(self, team_mapping: TeamMapping):
        """팀 매핑 추가"""
        key = f"{team_mapping.sport_code}_{team_mapping.id}"
        self._team_mappings_cache[key] = team_mapping
        self._save_team_mappings()
    
    def _save_team_mappings(self):
        """팀 매핑 저장"""
        try:
            mappings_file = self.resource_dir / "team_mappings.json"
            data = [asdict(mapping) for mapping in self._team_mappings_cache.values()]
            
            with open(mappings_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"팀 매핑 저장 실패: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """리소스 통계"""
        return {
            'url_patterns': len(self._url_patterns_cache),
            'league_mappings': len(self._league_mappings_cache),
            'team_mappings': len(self._team_mappings_cache),
            'sports': list(self._url_patterns_cache.keys()),
            'leagues_by_sport': {
                sport: len(self.get_all_leagues_by_sport(sport))
                for sport in ['SC', 'BK', 'VL', 'BB']
            }
        }


# 전역 리소스 관리자 인스턴스
resource_manager = StaticResourceManager()


def get_resource_manager() -> StaticResourceManager:
    """리소스 관리자 인스턴스 반환"""
    return resource_manager


# 편의 함수들
def get_url_patterns(sport: str) -> Optional[URLPatterns]:
    """URL 패턴 조회"""
    return resource_manager.get_url_patterns(sport)


def get_league_mapping(league_id: str) -> Optional[LeagueMapping]:
    """리그 매핑 조회"""
    return resource_manager.get_league_mapping(league_id)


def get_team_mapping(sport_code: str, team_id: str) -> Optional[TeamMapping]:
    """팀 매핑 조회"""
    return resource_manager.get_team_mapping(sport_code, team_id)
