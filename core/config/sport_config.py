"""
스포츠별 설정 관리
"""
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from datetime import time


@dataclass
class TimeSlot:
    """시간대 정보"""
    start: str  # "HH:MM" 형식
    end: str    # "HH:MM" 형식
    
    def to_time(self, time_str: str) -> time:
        """문자열을 time 객체로 변환"""
        hour, minute = map(int, time_str.split(':'))
        return time(hour, minute)
    
    @property
    def start_time(self) -> time:
        """시작 시간"""
        return self.to_time(self.start)
    
    @property
    def end_time(self) -> time:
        """종료 시간"""
        return self.to_time(self.end)


@dataclass
class SportConfig:
    """스포츠별 설정"""
    sport_name: str
    sport_code: str
    leagues: Dict[str, str]  # league_id -> league_name
    team_url_pattern: str
    player_url_pattern: str
    time_slots: List[Dict[str, str]]  # [{"start": "HH:MM", "end": "HH:MM"}]
    max_concurrent: int = 3
    delay_between_requests: float = 2.0
    
    def get_time_slots(self) -> List[TimeSlot]:
        """TimeSlot 객체 리스트 반환"""
        return [TimeSlot(slot["start"], slot["end"]) for slot in self.time_slots]
    
    def is_active_time(self, current_time: time) -> bool:
        """현재 시간이 활성 시간대인지 확인"""
        for slot in self.get_time_slots():
            if slot.start_time <= current_time <= slot.end_time:
                return True
        return False


# 스포츠별 기본 설정
SPORT_CONFIGS = {
    'soccer': SportConfig(
        sport_name="soccer",
        sport_code="SC",
        leagues={
            'SC001': 'K리그1',
            'SC003': 'K리그2',
            '52': 'EPL',
            '53': '프리메라리가',
            '54': '세리에A',
            '55': '분데스리가',
            '56': '프랑스리그',
            '57': '에레디비시',
            '58': 'J리그'
        },
        team_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}",
        player_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/scPlayerDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}&playerId={playerId}",
        time_slots=[
            {"start": "06:00", "end": "08:00"},
            {"start": "18:00", "end": "20:00"}
        ],
        max_concurrent=3,
        delay_between_requests=2.0
    ),
    
    'basketball': SportConfig(
        sport_name="basketball",
        sport_code="BK",
        leagues={
            'BK001': 'KBL',
            'BK002': 'NBA',
            'BK003': 'WKBL'
        },
        team_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/bkTeamDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}",
        player_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/bkPlayerDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}&playerId={playerId}",
        time_slots=[
            {"start": "08:00", "end": "10:00"},
            {"start": "20:00", "end": "22:00"}
        ],
        max_concurrent=3,
        delay_between_requests=2.0
    ),
    
    'volleyball': SportConfig(
        sport_name="volleyball",
        sport_code="VL",
        leagues={
            'VL001': 'KOVO남',
            'VL002': 'KOVO여'
        },
        team_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/vlTeamDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}",
        player_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/vlPlayerDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}&playerId={playerId}",
        time_slots=[
            {"start": "10:00", "end": "12:00"},
            {"start": "22:00", "end": "00:00"}
        ],
        max_concurrent=3,
        delay_between_requests=2.0
    ),
    
    'baseball': SportConfig(
        sport_name="baseball",
        sport_code="BB",
        leagues={
            'BB001': 'KBO',
            'BB002': 'MLB',
            'BB003': 'NPB'
        },
        team_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/bbTeamDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}",
        player_url_pattern="https://www.betman.co.kr/main/mainPage/gameinfo/bbPlayerDetail.do?item={item}&leagueId={leagueId}&teamId={teamId}&playerId={playerId}",
        time_slots=[
            {"start": "02:00", "end": "04:00"},
            {"start": "14:00", "end": "16:00"}
        ],
        max_concurrent=2,
        delay_between_requests=3.0
    )
}


def get_sport_config(sport_name: str) -> Optional[SportConfig]:
    """스포츠 설정 조회"""
    return SPORT_CONFIGS.get(sport_name)


def get_all_sport_configs() -> Dict[str, SportConfig]:
    """모든 스포츠 설정 조회"""
    return SPORT_CONFIGS.copy()


def register_sport_config(sport_name: str, config: SportConfig) -> None:
    """새로운 스포츠 설정 등록"""
    SPORT_CONFIGS[sport_name] = config


def get_active_sports(current_time: time) -> List[str]:
    """현재 시간에 활성화된 스포츠 목록"""
    active_sports = []
    for sport_name, config in SPORT_CONFIGS.items():
        if config.is_active_time(current_time):
            active_sports.append(sport_name)
    return active_sports
