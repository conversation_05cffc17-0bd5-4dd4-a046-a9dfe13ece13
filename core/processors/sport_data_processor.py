"""
스포츠 데이터 처리기 - SOLID 원칙 적용 구현
"""
import logging
import time
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

from core.interfaces.data_processor import (
    DataValidator, DataTransformer, DataEnricher, DataProcessor,
    ProcessingResult, ProcessingStatus, ProcessingMetrics,
    ObservableDataProcessor, ProcessingObserver
)

logger = logging.getLogger(__name__)


@dataclass
class SportData:
    """스포츠 데이터 모델"""
    sport_type: str
    team_name: str
    league: str
    league_id: str
    raw_data: Dict[str, Any]
    processed_data: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class SportDataValidator(DataValidator):
    """스포츠 데이터 검증기 - Single Responsibility Principle"""
    
    def __init__(self, required_fields: List[str]):
        self.required_fields = required_fields
        self.validation_errors: List[str] = []
    
    def validate(self, data: Any) -> bool:
        """데이터 유효성 검증"""
        self.validation_errors.clear()
        
        if not isinstance(data, dict):
            self.validation_errors.append("Data must be a dictionary")
            return False
        
        # 필수 필드 검증
        for field in self.required_fields:
            if field not in data:
                self.validation_errors.append(f"Missing required field: {field}")
            elif not data[field]:
                self.validation_errors.append(f"Empty required field: {field}")
        
        # 스포츠별 특수 검증
        if 'sport_type' in data:
            if data['sport_type'] not in ['soccer', 'basketball', 'volleyball', 'baseball']:
                self.validation_errors.append(f"Invalid sport type: {data['sport_type']}")
        
        return len(self.validation_errors) == 0
    
    def get_validation_errors(self, data: Any) -> List[str]:
        """검증 오류 목록 반환"""
        self.validate(data)
        return self.validation_errors.copy()


class SportDataTransformer(DataTransformer[SportData]):
    """스포츠 데이터 변환기 - Single Responsibility Principle"""
    
    def transform(self, raw_data: Any) -> SportData:
        """원시 데이터를 SportData로 변환"""
        if not isinstance(raw_data, dict):
            raise ValueError("Raw data must be a dictionary")
        
        return SportData(
            sport_type=raw_data.get('sport_type', ''),
            team_name=raw_data.get('team_name', ''),
            league=raw_data.get('league', ''),
            league_id=raw_data.get('league_id', ''),
            raw_data=raw_data,
            metadata={
                'transformed_at': time.time(),
                'transformer': self.__class__.__name__
            }
        )
    
    def can_transform(self, raw_data: Any) -> bool:
        """변환 가능 여부 확인"""
        return (
            isinstance(raw_data, dict) and
            'sport_type' in raw_data and
            'team_name' in raw_data
        )


class SportDataEnricher(DataEnricher[SportData]):
    """스포츠 데이터 보강기 - Single Responsibility Principle"""
    
    def __init__(self, team_mappings: Optional[Dict[str, str]] = None):
        self.team_mappings = team_mappings or {}
    
    def enrich(self, data: SportData) -> SportData:
        """데이터 보강"""
        # 팀 전체 이름 추가
        if data.team_name in self.team_mappings:
            data.metadata = data.metadata or {}
            data.metadata['full_team_name'] = self.team_mappings[data.team_name]
        
        # 스포츠별 추가 정보
        sport_info = self._get_sport_info(data.sport_type)
        if sport_info:
            data.metadata = data.metadata or {}
            data.metadata.update(sport_info)
        
        # 보강 메타데이터 추가
        enrichment_metadata = self.get_enrichment_metadata(data)
        data.metadata = data.metadata or {}
        data.metadata['enrichment'] = enrichment_metadata
        
        return data
    
    def get_enrichment_metadata(self, data: SportData) -> Dict[str, Any]:
        """보강 메타데이터 반환"""
        return {
            'enriched_at': time.time(),
            'enricher': self.__class__.__name__,
            'enrichments_applied': [
                'team_mapping',
                'sport_info',
                'metadata_enhancement'
            ]
        }
    
    def _get_sport_info(self, sport_type: str) -> Optional[Dict[str, Any]]:
        """스포츠별 추가 정보"""
        sport_info_map = {
            'soccer': {'max_players': 11, 'game_duration': 90},
            'basketball': {'max_players': 5, 'game_duration': 48},
            'volleyball': {'max_players': 6, 'game_duration': None},
            'baseball': {'max_players': 9, 'game_duration': None}
        }
        return sport_info_map.get(sport_type)


class SportDataProcessor(ObservableDataProcessor[SportData]):
    """
    스포츠 데이터 처리기 - Open/Closed Principle
    새로운 스포츠는 상속을 통해 확장
    """
    
    def __init__(self, 
                 validator: Optional[DataValidator] = None,
                 transformer: Optional[DataTransformer[SportData]] = None,
                 enricher: Optional[DataEnricher[SportData]] = None):
        super().__init__(validator, transformer, enricher)
        self.metrics = ProcessingMetrics()
    
    def process(self, raw_data: Any) -> ProcessingResult[SportData]:
        """데이터 처리 메인 로직"""
        start_time = time.time()
        
        try:
            # 관찰자에게 시작 알림
            self.notify_started(raw_data)
            
            # 1. 데이터 검증
            if not self.validate_data(raw_data):
                error_msg = "Data validation failed"
                if self.validator:
                    errors = self.validator.get_validation_errors(raw_data)
                    error_msg = f"Validation errors: {', '.join(errors)}"
                
                result = ProcessingResult(
                    status=ProcessingStatus.FAILED,
                    error=error_msg
                )
                self.notify_failed(error_msg)
                self.metrics.record_failure(time.time() - start_time)
                return result
            
            # 2. 데이터 변환
            transformed_data = self.transform_data(raw_data)
            if transformed_data is None:
                error_msg = "Data transformation failed"
                result = ProcessingResult(
                    status=ProcessingStatus.FAILED,
                    error=error_msg
                )
                self.notify_failed(error_msg)
                self.metrics.record_failure(time.time() - start_time)
                return result
            
            # 3. 데이터 보강
            enriched_data = self.enrich_data(transformed_data)
            
            # 4. 스포츠별 특수 처리
            final_data = self._apply_sport_specific_processing(enriched_data)
            
            # 성공 결과
            result = ProcessingResult(
                status=ProcessingStatus.SUCCESS,
                data=final_data,
                metadata={
                    'processing_time': time.time() - start_time,
                    'processor': self.__class__.__name__
                }
            )
            
            self.notify_completed(result)
            self.metrics.record_success(time.time() - start_time)
            return result
            
        except Exception as e:
            error_msg = f"Processing failed: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            result = ProcessingResult(
                status=ProcessingStatus.FAILED,
                error=error_msg
            )
            
            self.notify_failed(error_msg)
            self.metrics.record_failure(time.time() - start_time)
            return result
    
    def _apply_sport_specific_processing(self, data: SportData) -> SportData:
        """스포츠별 특수 처리 - Template Method Pattern"""
        # 기본 구현 - 하위 클래스에서 오버라이드 가능
        return data
    
    def get_processing_metrics(self) -> Dict[str, Any]:
        """처리 메트릭 반환"""
        return self.metrics.get_metrics_summary()


class SoccerDataProcessor(SportDataProcessor):
    """축구 데이터 처리기 - Liskov Substitution Principle"""
    
    def _apply_sport_specific_processing(self, data: SportData) -> SportData:
        """축구 특수 처리"""
        # 축구 특화 로직
        if data.processed_data is None:
            data.processed_data = {}
        
        data.processed_data['sport_specific'] = {
            'formation_analysis': True,
            'player_positions': True,
            'match_events': True
        }
        
        return data


class BasketballDataProcessor(SportDataProcessor):
    """농구 데이터 처리기 - Liskov Substitution Principle"""
    
    def _apply_sport_specific_processing(self, data: SportData) -> SportData:
        """농구 특수 처리"""
        if data.processed_data is None:
            data.processed_data = {}
        
        data.processed_data['sport_specific'] = {
            'quarter_analysis': True,
            'shooting_stats': True,
            'rebound_analysis': True
        }
        
        return data


class BaseballDataProcessor(SportDataProcessor):
    """야구 데이터 처리기 - Liskov Substitution Principle"""
    
    def _apply_sport_specific_processing(self, data: SportData) -> SportData:
        """야구 특수 처리"""
        if data.processed_data is None:
            data.processed_data = {}
        
        data.processed_data['sport_specific'] = {
            'pitcher_analysis': True,
            'batting_order': True,
            'inning_breakdown': True
        }
        
        return data


class ProcessingLogger(ProcessingObserver):
    """처리 로깅 관찰자 - Observer Pattern"""
    
    def __init__(self, logger_name: str = __name__):
        self.logger = logging.getLogger(logger_name)
    
    def on_processing_started(self, data: Any):
        """처리 시작 로깅"""
        sport_type = data.get('sport_type', 'unknown') if isinstance(data, dict) else 'unknown'
        self.logger.info(f"🔄 Processing started for {sport_type} data")
    
    def on_processing_completed(self, result: ProcessingResult):
        """처리 완료 로깅"""
        if result.is_success and result.data:
            sport_type = result.data.sport_type
            team_name = result.data.team_name
            self.logger.info(f"✅ Processing completed for {sport_type} - {team_name}")
        else:
            self.logger.info("✅ Processing completed")
    
    def on_processing_failed(self, error: str):
        """처리 실패 로깅"""
        self.logger.error(f"❌ Processing failed: {error}")


class SportDataProcessorFactory:
    """스포츠 데이터 처리기 팩토리 - Factory Pattern"""
    
    @staticmethod
    def create_processor(sport_type: str, **kwargs) -> SportDataProcessor:
        """스포츠별 처리기 생성"""
        # 기본 컴포넌트 생성
        validator = SportDataValidator(['sport_type', 'team_name', 'league'])
        transformer = SportDataTransformer()
        enricher = SportDataEnricher(kwargs.get('team_mappings'))
        
        # 스포츠별 처리기 생성
        processor_map = {
            'soccer': SoccerDataProcessor,
            'basketball': BasketballDataProcessor,
            'baseball': BaseballDataProcessor,
            'volleyball': SportDataProcessor  # 기본 처리기 사용
        }
        
        processor_class = processor_map.get(sport_type, SportDataProcessor)
        processor = processor_class(validator, transformer, enricher)
        
        # 로깅 관찰자 추가
        if kwargs.get('enable_logging', True):
            logger_observer = ProcessingLogger()
            processor.add_observer(logger_observer)
        
        return processor
    
    @staticmethod
    def get_supported_sports() -> List[str]:
        """지원하는 스포츠 목록"""
        return ['soccer', 'basketball', 'volleyball', 'baseball']
