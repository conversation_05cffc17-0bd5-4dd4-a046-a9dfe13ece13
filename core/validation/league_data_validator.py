"""
리그별 데이터 검증 및 품질 평가 시스템
실제 웹사이트에서 데이터를 수집하여 품질을 분석하고 검증
"""
import asyncio
import json
import logging
import statistics
from concurrent.futures import ThreadPoolExecutor
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple

from core.browser.browser_pool import BrowserPool
from core.config.sport_config import Sport, get_sport_config
from utils.logger import Logger

logger = Logger(__name__)


@dataclass
class DataQualityMetrics:
    """데이터 품질 지표"""
    completeness: float  # 완성도 (0-1)
    consistency: float   # 일관성 (0-1)
    accuracy: float      # 정확성 (0-1)
    timeliness: float    # 적시성 (0-1)
    total_records: int
    valid_records: int
    missing_fields: List[str]
    inconsistent_values: List[str]
    duplicate_records: int
    data_age_hours: float


@dataclass 
class LeagueValidationResult:
    """리그별 검증 결과"""
    league_id: str
    league_name: str
    sport: str
    collection_time: datetime
    data_count: int
    quality_metrics: DataQualityMetrics
    sample_data: List[Dict[str, Any]]
    validation_errors: List[str]
    validation_warnings: List[str]
    is_valid: bool


class LeagueDataValidator:
    """리그별 데이터 수집 및 검증기"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.browser_pool = BrowserPool(
            max_browsers=self.config.get('max_browsers', 3),
            headless=self.config.get('headless', True)
        )
        self.validation_results: Dict[str, LeagueValidationResult] = {}
        
        # 검증 임계값
        self.quality_thresholds = {
            'completeness': 0.8,  # 80% 이상 완성도
            'consistency': 0.9,   # 90% 이상 일관성 
            'accuracy': 0.85,     # 85% 이상 정확성
            'timeliness': 0.7     # 70% 이상 적시성
        }
    
    async def validate_all_leagues(
        self, 
        sports: Optional[List[str]] = None
    ) -> Dict[str, LeagueValidationResult]:
        """모든 리그 데이터 수집 및 검증"""
        logger.info("🔍 전체 리그 데이터 검증 시작")
        
        try:
            await self.browser_pool.initialize()
            
            # 검증할 스포츠 결정
            target_sports = sports or [
                'soccer', 'basketball', 'volleyball', 'baseball'
            ]
            
            # 병렬 검증 실행
            tasks = []
            for sport in target_sports:
                task = asyncio.create_task(self._validate_sport_leagues(sport))
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 결과 병합
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"❌ {target_sports[i]} 검증 실패: {result}")
                elif isinstance(result, dict):
                    self.validation_results.update(result)
            
            logger.info(f"✅ 리그 검증 완료: {len(self.validation_results)}개 리그")
            return self.validation_results
            
        except Exception as e:
            logger.error(f"❌ 리그 검증 중 오류: {e}")
            raise
        finally:
            await self.browser_pool.cleanup()
    
    async def _validate_sport_leagues(self, sport: str) -> Dict[str, LeagueValidationResult]:
        """특정 스포츠의 모든 리그 검증"""
        sport_results = {}
        
        try:
            # 스포츠 설정 로드
            sport_enum = getattr(Sport, sport.upper())
            sport_config = get_sport_config(sport_enum)
            
            logger.info(
                f"🏃‍♂️ {sport} 리그 검증 시작: {len(sport_config.leagues)}개 리그"
            )
            
            # 각 리그별 검증
            for league in sport_config.leagues:
                try:
                    result = await self._validate_league_data(league, sport)
                    sport_results[league.id] = result
                    
                except Exception as e:
                    logger.error(f"❌ {league.name} 검증 실패: {e}")
                    # 실패한 리그에 대한 기본 결과 생성
                    sport_results[league.id] = self._create_failed_result(
                        league, sport, str(e)
                    )
            
            return sport_results
            
        except Exception as e:
            logger.error(f"❌ {sport} 스포츠 검증 실패: {e}")
            return {}
    
    async def _validate_league_data(self, league, sport: str) -> LeagueValidationResult:
        """개별 리그 데이터 검증"""
        logger.info(f"📊 {league.name} 데이터 검증 시작")
        
        collection_time = datetime.now()
        validation_errors = []
        validation_warnings = []
        
        try:
            # 1. 실제 데이터 수집
            collected_data = await self._collect_league_data(league, sport)
            
            if not collected_data:
                validation_errors.append("데이터 수집 실패 - 빈 결과")
                return self._create_failed_result(league, sport, "No data collected")
            
            # 2. 데이터 품질 분석
            quality_metrics = self._analyze_data_quality(collected_data, league)
            
            # 3. 검증 규칙 적용
            errors, warnings = self._apply_validation_rules(collected_data, league, sport)
            validation_errors.extend(errors)
            validation_warnings.extend(warnings)
            
            # 4. 전체 검증 상태 결정
            is_valid = (
                len(validation_errors) == 0 and
                quality_metrics.completeness >= self.quality_thresholds['completeness'] and
                quality_metrics.consistency >= self.quality_thresholds['consistency']
            )
            
            # 5. 샘플 데이터 선택 (최대 5개)
            sample_data = collected_data[:5] if len(collected_data) >= 5 else collected_data
            
            result = LeagueValidationResult(
                league_id=league.id,
                league_name=league.name,
                sport=sport,
                collection_time=collection_time,
                data_count=len(collected_data),
                quality_metrics=quality_metrics,
                sample_data=sample_data,
                validation_errors=validation_errors,
                validation_warnings=validation_warnings,
                is_valid=is_valid
            )
            
            status = "✅ 유효" if is_valid else "⚠️ 문제"
            logger.info(f"{status} {league.name}: {len(collected_data)}건, 품질 {quality_metrics.completeness:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ {league.name} 검증 중 오류: {e}")
            return self._create_failed_result(league, sport, str(e))
    
    async def _collect_league_data(self, league, sport: str) -> List[Dict[str, Any]]:
        """리그별 실제 데이터 수집"""
        browser_session = None
        collected_data = []
        
        try:
            browser_session = await self.browser_pool.get_browser()
            
            # 스포츠별 플러그인 사용
            if sport == 'soccer':
                from sports.soccer.plugin import SoccerPlugin
                plugin = SoccerPlugin()
            elif sport == 'basketball':
                from sports.basketball.plugin import BasketballPlugin
                plugin = BasketballPlugin()
            elif sport == 'volleyball':
                from sports.volleyball.plugin import VolleyballPlugin
                plugin = VolleyballPlugin()
            elif sport == 'baseball':
                from sports.baseball.plugin import BaseballPlugin
                plugin = BaseballPlugin()
            else:
                raise ValueError(f"지원하지 않는 스포츠: {sport}")
            
            # 리그별 타겟 게임 생성 (임시)
            target_games = await self._generate_target_games(league, sport)
            
            if target_games:
                # 데이터 수집 (게임 정보만)
                games_data = await plugin.collect_games()
                
                # 리그 필터링
                league_games = [
                    game for game in games_data 
                    if game.get('league_id') == league.id
                ]
                
                collected_data = league_games
            
            return collected_data
            
        except Exception as e:
            logger.error(f"❌ {league.name} 데이터 수집 실패: {e}")
            return []
        finally:
            if browser_session:
                await self.browser_pool.return_browser(browser_session)
    
    async def _generate_target_games(self, league, sport: str) -> List[Dict[str, Any]]:
        """리그별 타겟 게임 생성 (검증용)"""
        # 실제 DB에서 가져오거나, 임시로 생성
        try:
            from database.database import connect_supabase
            
            client = connect_supabase()
            if client:
                query = client.table('target_games').select('*').eq(
                    'league_id', league.id
                )
                # 야구인 경우 W 게임 타입만 필터링
                if sport == 'baseball':
                    query = query.eq('game_type', 'W')
                response = query.limit(10).execute()
                
                if response.data:
                    return response.data
            
            # DB에 데이터가 없으면 임시 타겟 생성
            return [{
                'id': f'temp_{league.id}_001',
                'league_id': league.id,
                'sport': sport,
                'date': datetime.now().strftime('%Y-%m-%d'),
                'time': '19:00'
            }]
            
        except Exception as e:
            logger.debug(f"타겟 게임 생성 실패: {e}")
            return []
    
    def _analyze_data_quality(self, data: List[Dict[str, Any]], league) -> DataQualityMetrics:
        """데이터 품질 분석"""
        if not data:
            return DataQualityMetrics(
                completeness=0.0, consistency=0.0, accuracy=0.0, timeliness=0.0,
                total_records=0, valid_records=0, missing_fields=[],
                inconsistent_values=[], duplicate_records=0, data_age_hours=0.0
            )
        
        total_records = len(data)
        
        # 1. 완성도 분석 (필수 필드 존재율)
        required_fields = ['date', 'time', 'home_team', 'away_team', 'league_id']
        complete_records = 0
        missing_fields = set()
        
        for record in data:
            record_complete = True
            for field in required_fields:
                if not record.get(field):
                    missing_fields.add(field)
                    record_complete = False
            if record_complete:
                complete_records += 1
        
        completeness = complete_records / total_records if total_records > 0 else 0.0
        
        # 2. 일관성 분석 (중복 제거, 형식 통일성)
        unique_games = set()
        duplicate_count = 0
        consistent_records = 0
        inconsistent_values = []
        
        for record in data:
            # 게임 식별자 생성
            game_key = f"{record.get('date', '')}_{record.get('home_team', '')}_{record.get('away_team', '')}"
            
            if game_key in unique_games:
                duplicate_count += 1
            else:
                unique_games.add(game_key)
            
            # 날짜 형식 검증
            date_str = record.get('date', '')
            if date_str and self._is_valid_date_format(date_str):
                consistent_records += 1
            elif date_str:
                inconsistent_values.append(f"잘못된 날짜 형식: {date_str}")
        
        consistency = consistent_records / total_records if total_records > 0 else 0.0
        
        # 3. 정확성 분석 (팀명 매칭률)
        accurate_records = 0
        for record in data:
            home_team = record.get('home_team', '')
            away_team = record.get('away_team', '')
            
            # 팀명이 있고 비어있지 않으면 정확한 것으로 간주
            if home_team and away_team and home_team != away_team:
                accurate_records += 1
        
        accuracy = accurate_records / total_records if total_records > 0 else 0.0
        
        # 4. 적시성 분석 (데이터가 얼마나 최신인지)
        current_time = datetime.now()
        data_ages = []
        
        for record in data:
            date_str = record.get('date', '')
            if date_str and self._is_valid_date_format(date_str):
                try:
                    record_date = datetime.strptime(date_str, '%Y-%m-%d')
                    age_hours = abs((current_time - record_date).total_seconds() / 3600)
                    data_ages.append(age_hours)
                except:
                    pass
        
        avg_age_hours = statistics.mean(data_ages) if data_ages else 0.0
        # 1주일 이내면 1.0, 그 이후로는 점수 감소
        timeliness = max(0.0, 1.0 - (avg_age_hours / (7 * 24)))
        
        return DataQualityMetrics(
            completeness=completeness,
            consistency=consistency,
            accuracy=accuracy,
            timeliness=timeliness,
            total_records=total_records,
            valid_records=complete_records,
            missing_fields=list(missing_fields),
            inconsistent_values=inconsistent_values,
            duplicate_records=duplicate_count,
            data_age_hours=avg_age_hours
        )
    
    def _apply_validation_rules(self, data: List[Dict[str, Any]], 
                              league, sport: str) -> tuple[List[str], List[str]]:
        """검증 규칙 적용"""
        errors = []
        warnings = []
        
        # 1. 기본 데이터 존재 검증
        if not data:
            errors.append("수집된 데이터가 없습니다")
            return errors, warnings
        
        # 2. 데이터 양 검증
        if len(data) < 5:
            warnings.append(f"데이터가 너무 적습니다: {len(data)}건")
        
        # 3. 필수 필드 검증
        required_fields = ['home_team', 'away_team', 'league_id']
        for field in required_fields:
            missing_count = sum(1 for record in data if not record.get(field))
            if missing_count > len(data) * 0.1:  # 10% 이상 누락
                errors.append(f"'{field}' 필드가 {missing_count}건 누락됨")
        
        # 4. 리그 ID 일치성 검증
        wrong_league_count = sum(1 for record in data if record.get('league_id') != league.id)
        if wrong_league_count > 0:
            errors.append(f"잘못된 리그 ID: {wrong_league_count}건")
        
        # 5. 팀명 유효성 검증
        empty_team_count = sum(
            1 for record in data 
            if not record.get('home_team') or not record.get('away_team')
        )
        if empty_team_count > 0:
            warnings.append(f"팀명이 누락된 경기: {empty_team_count}건")
        
        return errors, warnings
    
    def _is_valid_date_format(self, date_str: str) -> bool:
        """날짜 형식 유효성 검증"""
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except:
            return False
    
    def _create_failed_result(self, league, sport: str, error_message: str) -> LeagueValidationResult:
        """실패한 검증 결과 생성"""
        return LeagueValidationResult(
            league_id=league.id,
            league_name=league.name,
            sport=sport,
            collection_time=datetime.now(),
            data_count=0,
            quality_metrics=DataQualityMetrics(
                completeness=0.0, consistency=0.0, accuracy=0.0, timeliness=0.0,
                total_records=0, valid_records=0, missing_fields=[],
                inconsistent_values=[], duplicate_records=0, data_age_hours=0.0
            ),
            sample_data=[],
            validation_errors=[error_message],
            validation_warnings=[],
            is_valid=False
        )
    
    async def get_validation_summary(self) -> Dict[str, Any]:
        """검증 결과 요약"""
        if not self.validation_results:
            return {"error": "검증 결과가 없습니다"}
        
        total_leagues = len(self.validation_results)
        valid_leagues = sum(1 for result in self.validation_results.values() if result.is_valid)
        
        # 스포츠별 집계
        sport_summary = {}
        for result in self.validation_results.values():
            sport = result.sport
            if sport not in sport_summary:
                sport_summary[sport] = {'total': 0, 'valid': 0, 'invalid': 0}
            
            sport_summary[sport]['total'] += 1
            if result.is_valid:
                sport_summary[sport]['valid'] += 1
            else:
                sport_summary[sport]['invalid'] += 1
        
        # 품질 점수 평균
        quality_scores = []
        for result in self.validation_results.values():
            avg_quality = (
                result.quality_metrics.completeness +
                result.quality_metrics.consistency +
                result.quality_metrics.accuracy +
                result.quality_metrics.timeliness
            ) / 4
            quality_scores.append(avg_quality)
        
        avg_quality = statistics.mean(quality_scores) if quality_scores else 0.0
        
        return {
            "summary": {
                "total_leagues": total_leagues,
                "valid_leagues": valid_leagues,
                "invalid_leagues": total_leagues - valid_leagues,
                "success_rate": valid_leagues / total_leagues if total_leagues > 0 else 0.0,
                "average_quality_score": avg_quality
            },
            "by_sport": sport_summary,
            "validation_time": datetime.now().isoformat()
        } 