"""
검증 결과 리포트 생성기
리그별 검증 결과와 크로스 리그 분석을 종합하여 상세한 리포트 생성
"""
import json
import os
from dataclasses import asdict
from datetime import datetime
from typing import Any, Dict, List, Optional

from utils.logger import Logger

from .cross_league_validator import CrossLeagueIssue, CrossLeagueReport
from .league_data_validator import DataQualityMetrics, LeagueValidationResult

logger = Logger(__name__)


class ValidationReporter:
    """검증 결과 리포트 생성기"""
    
    def __init__(self, output_dir: str = "results/validation"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
    def generate_comprehensive_report(
        self,
        league_results: Dict[str, LeagueValidationResult],
        cross_league_report: CrossLeagueReport,
        include_samples: bool = True
    ) -> Dict[str, Any]:
        """종합 검증 리포트 생성"""
        logger.info("📋 종합 검증 리포트 생성 시작")
        
        timestamp = datetime.now()
        
        # 1. 전체 요약
        summary = self._create_summary(league_results, cross_league_report)
        
        # 2. 리그별 상세 결과
        league_details = self._create_league_details(
            league_results, include_samples
        )
        
        # 3. 품질 분석
        quality_analysis = self._create_quality_analysis(league_results)
        
        # 4. 크로스 리그 분석
        cross_league_analysis = self._create_cross_league_analysis(
            cross_league_report
        )
        
        # 5. 권고사항 및 액션 아이템
        recommendations = self._create_recommendations(
            league_results, cross_league_report
        )
        
        # 6. 메트릭 트렌드 (향후 확장용)
        metrics = self._create_metrics_summary(league_results)
        
        comprehensive_report = {
            "metadata": {
                "report_type": "comprehensive_validation",
                "generated_at": timestamp.isoformat(),
                "version": "1.0",
                "total_leagues_analyzed": len(league_results)
            },
            "summary": summary,
            "league_details": league_details,
            "quality_analysis": quality_analysis,
            "cross_league_analysis": cross_league_analysis,
            "recommendations": recommendations,
            "metrics": metrics
        }
        
        # 파일로 저장
        report_filename = f"validation_report_{timestamp.strftime('%Y%m%d_%H%M%S')}.json"
        self._save_report(comprehensive_report, report_filename)
        
        logger.info(f"✅ 종합 리포트 생성 완료: {report_filename}")
        return comprehensive_report
    
    def generate_html_report(
        self,
        league_results: Dict[str, LeagueValidationResult],
        cross_league_report: CrossLeagueReport
    ) -> str:
        """HTML 형식 리포트 생성"""
        logger.info("🌐 HTML 리포트 생성 시작")
        
        timestamp = datetime.now()
        
        # HTML 템플릿 생성
        html_content = self._create_html_template(
            league_results, cross_league_report, timestamp
        )
        
        # 파일로 저장
        html_filename = f"validation_report_{timestamp.strftime('%Y%m%d_%H%M%S')}.html"
        html_path = os.path.join(self.output_dir, html_filename)
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"✅ HTML 리포트 생성 완료: {html_filename}")
        return html_path
    
    def _create_summary(
        self,
        league_results: Dict[str, LeagueValidationResult],
        cross_league_report: CrossLeagueReport
    ) -> Dict[str, Any]:
        """전체 요약 생성"""
        total_leagues = len(league_results)
        valid_leagues = sum(1 for r in league_results.values() if r.is_valid)
        total_data_count = sum(r.data_count for r in league_results.values())
        
        # 스포츠별 집계
        sports_summary = {}
        for result in league_results.values():
            sport = result.sport
            if sport not in sports_summary:
                sports_summary[sport] = {
                    'total_leagues': 0,
                    'valid_leagues': 0,
                    'total_data': 0
                }
            
            sports_summary[sport]['total_leagues'] += 1
            if result.is_valid:
                sports_summary[sport]['valid_leagues'] += 1
            sports_summary[sport]['total_data'] += result.data_count
        
        # 품질 점수 평균
        quality_scores = []
        for result in league_results.values():
            if result.quality_metrics:
                avg_quality = (
                    result.quality_metrics.completeness +
                    result.quality_metrics.consistency +
                    result.quality_metrics.accuracy +
                    result.quality_metrics.timeliness
                ) / 4
                quality_scores.append(avg_quality)
        
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
        
        return {
            "overall": {
                "total_leagues": total_leagues,
                "valid_leagues": valid_leagues,
                "invalid_leagues": total_leagues - valid_leagues,
                "success_rate": valid_leagues / total_leagues if total_leagues > 0 else 0.0,
                "total_data_records": total_data_count,
                "average_quality_score": avg_quality
            },
            "by_sport": sports_summary,
            "cross_league": {
                "compatibility_score": cross_league_report.compatibility_score,
                "total_issues": len(cross_league_report.issues),
                "critical_issues": len([
                    i for i in cross_league_report.issues 
                    if i.severity == 'critical'
                ])
            }
        }
    
    def _create_league_details(
        self,
        league_results: Dict[str, LeagueValidationResult],
        include_samples: bool
    ) -> Dict[str, Any]:
        """리그별 상세 결과 생성"""
        details = {}
        
        for league_id, result in league_results.items():
            league_detail = {
                "league_info": {
                    "id": result.league_id,
                    "name": result.league_name,
                    "sport": result.sport,
                    "collection_time": result.collection_time.isoformat(),
                    "is_valid": result.is_valid
                },
                "data_summary": {
                    "total_records": result.data_count,
                    "has_data": result.data_count > 0
                },
                "quality_metrics": asdict(result.quality_metrics) if result.quality_metrics else None,
                "validation_status": {
                    "errors": result.validation_errors,
                    "warnings": result.validation_warnings,
                    "error_count": len(result.validation_errors),
                    "warning_count": len(result.validation_warnings)
                }
            }
            
            # 샘플 데이터 포함 (옵션)
            if include_samples and result.sample_data:
                league_detail["sample_data"] = result.sample_data[:3]  # 최대 3개만
            
            details[league_id] = league_detail
        
        return details
    
    def _create_quality_analysis(
        self,
        league_results: Dict[str, LeagueValidationResult]
    ) -> Dict[str, Any]:
        """품질 분석 생성"""
        analysis = {
            "quality_distribution": {},
            "common_issues": {},
            "best_performing_leagues": [],
            "needs_improvement": []
        }
        
        # 품질 지표별 분포
        metrics = ['completeness', 'consistency', 'accuracy', 'timeliness']
        for metric in metrics:
            values = []
            for result in league_results.values():
                if result.quality_metrics:
                    value = getattr(result.quality_metrics, metric, 0.0)
                    values.append(value)
            
            if values:
                analysis["quality_distribution"][metric] = {
                    "average": sum(values) / len(values),
                    "min": min(values),
                    "max": max(values),
                    "count": len(values)
                }
        
        # 공통 문제 분석
        all_errors = []
        all_warnings = []
        for result in league_results.values():
            all_errors.extend(result.validation_errors)
            all_warnings.extend(result.validation_warnings)
        
        # 빈도수 계산
        error_counts = {}
        for error in all_errors:
            error_counts[error] = error_counts.get(error, 0) + 1
        
        warning_counts = {}
        for warning in all_warnings:
            warning_counts[warning] = warning_counts.get(warning, 0) + 1
        
        analysis["common_issues"] = {
            "frequent_errors": sorted(
                error_counts.items(), key=lambda x: x[1], reverse=True
            )[:5],
            "frequent_warnings": sorted(
                warning_counts.items(), key=lambda x: x[1], reverse=True
            )[:5]
        }
        
        # 최고 성능 리그
        league_scores = []
        for league_id, result in league_results.items():
            if result.is_valid and result.quality_metrics:
                avg_score = (
                    result.quality_metrics.completeness +
                    result.quality_metrics.consistency +
                    result.quality_metrics.accuracy +
                    result.quality_metrics.timeliness
                ) / 4
                league_scores.append((league_id, result.league_name, avg_score))
        
        league_scores.sort(key=lambda x: x[2], reverse=True)
        
        analysis["best_performing_leagues"] = [
            {"league_id": lid, "league_name": name, "score": score}
            for lid, name, score in league_scores[:3]
        ]
        
        analysis["needs_improvement"] = [
            {"league_id": lid, "league_name": name, "score": score}
            for lid, name, score in league_scores[-3:] if score < 0.7
        ]
        
        return analysis
    
    def _create_cross_league_analysis(
        self,
        cross_league_report: CrossLeagueReport
    ) -> Dict[str, Any]:
        """크로스 리그 분석 생성"""
        return {
            "compatibility_score": cross_league_report.compatibility_score,
            "total_issues": len(cross_league_report.issues),
            "issues_by_type": self._group_issues_by_type(cross_league_report.issues),
            "issues_by_severity": self._group_issues_by_severity(cross_league_report.issues),
            "affected_leagues": self._get_most_affected_leagues(cross_league_report.issues),
            "recommendations": cross_league_report.recommendations
        }
    
    def _create_recommendations(
        self,
        league_results: Dict[str, LeagueValidationResult],
        cross_league_report: CrossLeagueReport
    ) -> Dict[str, Any]:
        """권고사항 생성"""
        recommendations = {
            "immediate_actions": [],
            "short_term_improvements": [],
            "long_term_strategies": []
        }
        
        # 즉시 조치 필요 (Critical 이슈)
        critical_leagues = [
            result.league_name for result in league_results.values()
            if not result.is_valid
        ]
        
        if critical_leagues:
            recommendations["immediate_actions"].append(
                f"다음 리그의 데이터 수집 문제를 즉시 해결하세요: {', '.join(critical_leagues)}"
            )
        
        # 크로스 리그 권고사항 통합
        recommendations["short_term_improvements"].extend(
            cross_league_report.recommendations
        )
        
        # 장기 전략
        avg_quality = sum(
            (r.quality_metrics.completeness + r.quality_metrics.consistency + 
             r.quality_metrics.accuracy + r.quality_metrics.timeliness) / 4
            for r in league_results.values() 
            if r.quality_metrics
        ) / len(league_results) if league_results else 0.0
        
        if avg_quality < 0.8:
            recommendations["long_term_strategies"].append(
                "전체적인 데이터 품질 향상을 위한 표준화 프로세스 도입을 검토하세요."
            )
        
        return recommendations
    
    def _create_metrics_summary(
        self,
        league_results: Dict[str, LeagueValidationResult]
    ) -> Dict[str, Any]:
        """메트릭 요약 생성"""
        return {
            "data_volume": {
                "total_records": sum(r.data_count for r in league_results.values()),
                "average_per_league": sum(r.data_count for r in league_results.values()) / len(league_results) if league_results else 0
            },
            "quality_scores": {
                "completeness": [
                    r.quality_metrics.completeness for r in league_results.values()
                    if r.quality_metrics
                ],
                "consistency": [
                    r.quality_metrics.consistency for r in league_results.values()
                    if r.quality_metrics
                ]
            }
        }
    
    def _group_issues_by_type(self, issues: List[CrossLeagueIssue]) -> Dict[str, int]:
        """이슈를 유형별로 그룹화"""
        groups = {}
        for issue in issues:
            groups[issue.issue_type] = groups.get(issue.issue_type, 0) + 1
        return groups
    
    def _group_issues_by_severity(self, issues: List[CrossLeagueIssue]) -> Dict[str, int]:
        """이슈를 심각도별로 그룹화"""
        groups = {}
        for issue in issues:
            groups[issue.severity] = groups.get(issue.severity, 0) + 1
        return groups
    
    def _get_most_affected_leagues(self, issues: List[CrossLeagueIssue]) -> List[str]:
        """가장 많이 영향받은 리그 목록"""
        league_counts = {}
        for issue in issues:
            for league in issue.affected_leagues:
                league_counts[league] = league_counts.get(league, 0) + 1
        
        return sorted(league_counts.items(), key=lambda x: x[1], reverse=True)[:5]
    
    def _create_html_template(
        self,
        league_results: Dict[str, LeagueValidationResult],
        cross_league_report: CrossLeagueReport,
        timestamp: datetime
    ) -> str:
        """HTML 리포트 템플릿 생성"""
        
        # 간단한 HTML 템플릿 (실제로는 더 복잡한 템플릿 엔진 사용 가능)
        summary = self._create_summary(league_results, cross_league_report)
        
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>리그 데이터 검증 리포트</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .summary {{ background: #f5f5f5; padding: 15px; border-radius: 5px; }}
                .league {{ border: 1px solid #ddd; margin: 10px 0; padding: 10px; }}
                .valid {{ border-left: 5px solid #4CAF50; }}
                .invalid {{ border-left: 5px solid #f44336; }}
                .warning {{ color: #ff9800; }}
                .error {{ color: #f44336; }}
            </style>
        </head>
        <body>
            <h1>리그 데이터 검증 리포트</h1>
            <p>생성 시간: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <div class="summary">
                <h2>전체 요약</h2>
                <p>총 리그 수: {summary['overall']['total_leagues']}</p>
                <p>유효한 리그: {summary['overall']['valid_leagues']}</p>
                <p>성공률: {summary['overall']['success_rate']:.1%}</p>
                <p>평균 품질 점수: {summary['overall']['average_quality_score']:.2f}</p>
            </div>
            
            <h2>리그별 상세 결과</h2>
        """
        
        for league_id, result in league_results.items():
            status_class = "valid" if result.is_valid else "invalid"
            
            html += f"""
            <div class="league {status_class}">
                <h3>{result.league_name} ({result.sport})</h3>
                <p>상태: {'✅ 유효' if result.is_valid else '❌ 문제'}</p>
                <p>데이터 수: {result.data_count}건</p>
            """
            
            if result.validation_errors:
                html += f"<p class='error'>오류: {'; '.join(result.validation_errors)}</p>"
            
            if result.validation_warnings:
                html += f"<p class='warning'>경고: {'; '.join(result.validation_warnings)}</p>"
            
            html += "</div>"
        
        html += """
            </body>
        </html>
        """
        
        return html
    
    def _save_report(self, report: Dict[str, Any], filename: str) -> str:
        """리포트를 파일로 저장"""
        filepath = os.path.join(self.output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        return filepath 