"""
리그간 데이터 일관성 검증 시스템
여러 리그의 데이터를 비교하여 일관성과 호환성을 검증
"""
import asyncio
import difflib
from collections import Counter, defaultdict
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from utils.logger import Logger

from .league_data_validator import DataQualityMetrics, LeagueValidationResult

logger = Logger(__name__)


@dataclass
class CrossLeagueIssue:
    """리그간 일관성 문제"""
    issue_type: str  # 'team_name_mismatch', 'data_format_diff', 'missing_league'
    severity: str    # 'critical', 'warning', 'info'
    description: str
    affected_leagues: List[str]
    details: Dict[str, Any]


@dataclass
class CrossLeagueReport:
    """리그간 검증 보고서"""
    validation_time: datetime
    total_leagues: int
    comparable_leagues: int
    issues: List[CrossLeagueIssue]
    compatibility_score: float  # 0-1 점수
    recommendations: List[str]


class CrossLeagueValidator:
    """리그간 데이터 일관성 검증기"""
    
    def __init__(self):
        self.team_name_variations = defaultdict(set)
        self.data_format_patterns = {}
        
    def validate_cross_league_consistency(
        self, 
        validation_results: Dict[str, LeagueValidationResult]
    ) -> CrossLeagueReport:
        """리그간 일관성 검증"""
        logger.info("🔗 리그간 데이터 일관성 검증 시작")
        
        issues = []
        recommendations = []
        
        # 1. 팀명 일관성 검증
        team_issues = self._validate_team_name_consistency(validation_results)
        issues.extend(team_issues)
        
        # 2. 데이터 형식 일관성 검증
        format_issues = self._validate_data_format_consistency(validation_results)
        issues.extend(format_issues)
        
        # 3. 리그별 품질 차이 분석
        quality_issues = self._analyze_quality_differences(validation_results)
        issues.extend(quality_issues)
        
        # 4. 호환성 점수 계산
        compatibility_score = self._calculate_compatibility_score(
            validation_results, issues
        )
        
        # 5. 개선 권고사항 생성
        recommendations = self._generate_recommendations(issues)
        
        # 검증 가능한 리그 수 계산
        comparable_leagues = len([
            r for r in validation_results.values() 
            if r.is_valid and r.data_count > 0
        ])
        
        report = CrossLeagueReport(
            validation_time=datetime.now(),
            total_leagues=len(validation_results),
            comparable_leagues=comparable_leagues,
            issues=issues,
            compatibility_score=compatibility_score,
            recommendations=recommendations
        )
        
        logger.info(
            f"✅ 리그간 검증 완료: {len(issues)}개 이슈, "
            f"호환성 {compatibility_score:.2f}"
        )
        
        return report
    
    def _validate_team_name_consistency(
        self, 
        validation_results: Dict[str, LeagueValidationResult]
    ) -> List[CrossLeagueIssue]:
        """팀명 일관성 검증"""
        issues = []
        
        # 리그별 팀명 수집
        league_teams = {}
        for league_id, result in validation_results.items():
            if not result.is_valid or not result.sample_data:
                continue
                
            teams = set()
            for game in result.sample_data:
                home_team = game.get('home_team', '').strip()
                away_team = game.get('away_team', '').strip()
                if home_team:
                    teams.add(home_team)
                if away_team:
                    teams.add(away_team)
            
            league_teams[league_id] = teams
        
        # 비슷한 팀명 찾기
        all_teams = set()
        for teams in league_teams.values():
            all_teams.update(teams)
        
        similar_teams = self._find_similar_team_names(list(all_teams))
        
        if similar_teams:
            for group in similar_teams:
                if len(group) > 1:
                    affected_leagues = []
                    for league_id, teams in league_teams.items():
                        if any(team in teams for team in group):
                            affected_leagues.append(league_id)
                    
                    if len(affected_leagues) > 1:
                        issues.append(CrossLeagueIssue(
                            issue_type='team_name_mismatch',
                            severity='warning',
                            description=f"유사한 팀명 변형 발견: {', '.join(group)}",
                            affected_leagues=affected_leagues,
                            details={'team_variations': group}
                        ))
        
        return issues
    
    def _validate_data_format_consistency(
        self, 
        validation_results: Dict[str, LeagueValidationResult]
    ) -> List[CrossLeagueIssue]:
        """데이터 형식 일관성 검증"""
        issues = []
        
        # 날짜 형식 패턴 분석
        date_formats = defaultdict(list)
        time_formats = defaultdict(list)
        
        for league_id, result in validation_results.items():
            if not result.is_valid or not result.sample_data:
                continue
            
            for game in result.sample_data:
                date_str = game.get('date', '')
                time_str = game.get('time', '')
                
                if date_str:
                    date_pattern = self._detect_date_pattern(date_str)
                    date_formats[date_pattern].append(league_id)
                
                if time_str:
                    time_pattern = self._detect_time_pattern(time_str)
                    time_formats[time_pattern].append(league_id)
        
        # 형식 불일치 검증
        if len(date_formats) > 1:
            issues.append(CrossLeagueIssue(
                issue_type='data_format_diff',
                severity='warning',
                description="날짜 형식이 리그별로 다릅니다",
                affected_leagues=list(set().union(*date_formats.values())),
                details={'date_formats': dict(date_formats)}
            ))
        
        if len(time_formats) > 1:
            issues.append(CrossLeagueIssue(
                issue_type='data_format_diff',
                severity='warning',
                description="시간 형식이 리그별로 다릅니다",
                affected_leagues=list(set().union(*time_formats.values())),
                details={'time_formats': dict(time_formats)}
            ))
        
        return issues
    
    def _analyze_quality_differences(
        self, 
        validation_results: Dict[str, LeagueValidationResult]
    ) -> List[CrossLeagueIssue]:
        """리그별 품질 차이 분석"""
        issues = []
        
        # 품질 지표별 분석
        quality_metrics = ['completeness', 'consistency', 'accuracy', 'timeliness']
        
        for metric in quality_metrics:
            metric_values = {}
            for league_id, result in validation_results.items():
                if result.is_valid and result.quality_metrics:
                    value = getattr(result.quality_metrics, metric, 0.0)
                    metric_values[league_id] = value
            
            if len(metric_values) > 1:
                min_value = min(metric_values.values())
                max_value = max(metric_values.values())
                
                # 품질 차이가 30% 이상인 경우 이슈로 등록
                if max_value - min_value > 0.3:
                    worst_leagues = [
                        league_id for league_id, value in metric_values.items()
                        if value == min_value
                    ]
                    
                    issues.append(CrossLeagueIssue(
                        issue_type='quality_difference',
                        severity='warning',
                        description=f"{metric} 품질이 리그별로 차이납니다 "
                                  f"(최저 {min_value:.2f}, 최고 {max_value:.2f})",
                        affected_leagues=worst_leagues,
                        details={
                            'metric': metric,
                            'values': metric_values,
                            'difference': max_value - min_value
                        }
                    ))
        
        return issues
    
    def _find_similar_team_names(
        self, 
        team_names: List[str]
    ) -> List[List[str]]:
        """유사한 팀명 그룹 찾기"""
        similar_groups = []
        processed = set()
        
        for i, team1 in enumerate(team_names):
            if team1 in processed:
                continue
                
            similar_group = [team1]
            processed.add(team1)
            
            for j, team2 in enumerate(team_names[i+1:], i+1):
                if team2 in processed:
                    continue
                    
                # 유사도 계산 (0.8 이상이면 유사한 것으로 판단)
                similarity = difflib.SequenceMatcher(None, team1, team2).ratio()
                if similarity >= 0.8:
                    similar_group.append(team2)
                    processed.add(team2)
            
            if len(similar_group) > 1:
                similar_groups.append(similar_group)
        
        return similar_groups
    
    def _detect_date_pattern(self, date_str: str) -> str:
        """날짜 패턴 감지"""
        if '-' in date_str and len(date_str) == 10:
            return 'YYYY-MM-DD'
        elif '/' in date_str:
            return 'MM/DD/YYYY' if date_str.count('/') == 2 else 'unknown'
        elif '.' in date_str:
            return 'YYYY.MM.DD' if date_str.count('.') == 2 else 'unknown'
        else:
            return 'unknown'
    
    def _detect_time_pattern(self, time_str: str) -> str:
        """시간 패턴 감지"""
        if ':' in time_str:
            parts = time_str.split(':')
            if len(parts) == 2:
                return 'HH:MM'
            elif len(parts) == 3:
                return 'HH:MM:SS'
        return 'unknown'
    
    def _calculate_compatibility_score(
        self, 
        validation_results: Dict[str, LeagueValidationResult],
        issues: List[CrossLeagueIssue]
    ) -> float:
        """호환성 점수 계산"""
        if not validation_results:
            return 0.0
        
        # 기본 점수는 유효한 리그 비율
        valid_leagues = sum(1 for r in validation_results.values() if r.is_valid)
        base_score = valid_leagues / len(validation_results)
        
        # 이슈 심각도에 따른 점수 차감
        penalty = 0.0
        for issue in issues:
            if issue.severity == 'critical':
                penalty += 0.2
            elif issue.severity == 'warning':
                penalty += 0.1
            else:  # info
                penalty += 0.05
        
        # 최종 점수 (0-1 범위)
        final_score = max(0.0, base_score - penalty)
        return min(1.0, final_score)
    
    def _generate_recommendations(
        self, 
        issues: List[CrossLeagueIssue]
    ) -> List[str]:
        """개선 권고사항 생성"""
        recommendations = []
        
        # 이슈 유형별 권고사항
        issue_types = Counter(issue.issue_type for issue in issues)
        
        if 'team_name_mismatch' in issue_types:
            recommendations.append(
                "팀명 표준화 작업이 필요합니다. "
                "통일된 팀명 매핑 테이블을 구축하세요."
            )
        
        if 'data_format_diff' in issue_types:
            recommendations.append(
                "데이터 형식 통일이 필요합니다. "
                "날짜/시간 형식을 표준화하세요."
            )
        
        if 'quality_difference' in issue_types:
            recommendations.append(
                "품질이 낮은 리그의 데이터 수집 로직을 개선하세요. "
                "누락 필드와 일관성 문제를 해결하세요."
            )
        
        # 심각도별 우선순위 권고
        critical_issues = [i for i in issues if i.severity == 'critical']
        if critical_issues:
            recommendations.insert(0, 
                f"🚨 {len(critical_issues)}개의 심각한 문제를 우선 해결하세요."
            )
        
        return recommendations 