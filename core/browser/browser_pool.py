"""
Browser Pool Management - 브라우저 세션 최적화
"""
import asyncio
import gc
import logging
import time
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

import psutil
from playwright.async_api import Browser, async_playwright

from .platform_adapter import PlatformAdapter

logger = logging.getLogger(__name__)


class BrowserPool:
    """브라우저 풀 관리 클래스 (플랫폼 어댑터 사용)"""
    
    def __init__(self, platform_type: str = "auto"):
        # 플랫폼 어댑터 사용
        self.platform_adapter = PlatformAdapter(platform_type)
        self.platform_adapter.log_platform_info()
        
        # 플랫폼별 설정 적용
        browser_config = self.platform_adapter.get_browser_config()
        memory_config = self.platform_adapter.get_memory_config()
        performance_config = self.platform_adapter.get_performance_config()
        
        self.max_browsers = browser_config['max_browsers']
        self.browser_args = browser_config['args']
        self.browser_timeout = browser_config['timeout']
        self.memory_check_interval = memory_config['memory_check_interval']
        self.memory_thresholds = {
            'gc_threshold': memory_config['gc_threshold'],
            'emergency_threshold': memory_config['emergency_threshold']
        }
        self.max_memory_usage = memory_config['max_memory_usage']
        self.browser_restart_threshold = performance_config['browser_restart_threshold']
            
        self.playwright = None
        self.browsers: List[Browser] = []
        self.available_browsers: List[Browser] = []
        self.browser_usage_count: Dict[Browser, int] = {}
        self.last_memory_check = 0
        self._lock = asyncio.Lock()
        
    
    async def initialize(self):
        """브라우저 풀 초기화"""
        logger.info(f"🚀 브라우저 풀 초기화 중... (최대 {self.max_browsers}개)")
        
        self.playwright = await async_playwright().start()
        
        # 브라우저 생성
        for i in range(self.max_browsers):
            try:
                browser = await self._create_browser()
                self.browsers.append(browser)
                self.available_browsers.append(browser)
                self.browser_usage_count[browser] = 0
                logger.info(f"   브라우저 {i+1}/{self.max_browsers} 생성 완료")
                
                # 메모리 체크
                await self._check_memory_usage()
                
            except Exception as e:
                logger.error(f"❌ 브라우저 {i+1} 생성 실패: {e}")
                
        logger.info(f"✅ 브라우저 풀 초기화 완료 ({len(self.browsers)}개 활성)")
    
    async def _create_browser(self) -> Browser:
        """새 브라우저 인스턴스 생성"""
        return await self.playwright.chromium.launch(
            headless=True,
            args=self.browser_args,
            timeout=self.browser_timeout
        )
    
    @asynccontextmanager
    async def get_browser(self):
        """브라우저 컨텍스트 매니저"""
        browser = None
        page = None
        
        try:
            browser = await self._acquire_browser()
            context = await browser.new_context(
                viewport={'width': 1280, 'height': 720},
                user_agent=(
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                    'AppleWebKit/537.36'
                )
            )
            page = await context.new_page()
            
            # 메모리 최적화 설정
            await page.set_extra_http_headers({
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive'
            })
            
            yield page
            
        except Exception as e:
            logger.error(f"❌ 브라우저 사용 중 오류: {e}")
            raise
            
        finally:
            # 정리
            if page:
                try:
                    await page.close()
                except Exception:
                    pass
                    
            if browser:
                await self._release_browser(browser)
    
    async def _acquire_browser(self) -> Browser:
        """사용 가능한 브라우저 획득"""
        async with self._lock:
            # 메모리 체크
            await self._check_memory_usage()
            
            # 사용 가능한 브라우저가 없으면 대기
            while not self.available_browsers:
                logger.info("⏳ 사용 가능한 브라우저 대기 중...")
                await asyncio.sleep(1)
                
            browser = self.available_browsers.pop(0)
            
            # 사용 횟수 체크 및 재시작
            if (self.browser_usage_count[browser] >=
                    self.browser_restart_threshold):
                usage_count = self.browser_usage_count[browser]
                logger.info(
                    f"🔄 브라우저 재시작 (사용 횟수: {usage_count})"
                )
                await self._restart_browser(browser)
            
            self.browser_usage_count[browser] += 1
            return browser
    
    async def _release_browser(self, browser: Browser):
        """브라우저 반환"""
        async with self._lock:
            if browser in self.browsers:
                self.available_browsers.append(browser)
                
                # 메모리 정리
                if self.browser_usage_count[browser] % 10 == 0:
                    gc.collect()
    
    async def _restart_browser(self, old_browser: Browser):
        """브라우저 재시작"""
        try:
            # 기존 브라우저 종료
            await old_browser.close()
            
            # 새 브라우저 생성
            new_browser = await self._create_browser()
            
            # 브라우저 목록 업데이트
            index = self.browsers.index(old_browser)
            self.browsers[index] = new_browser
            
            # 사용 횟수 초기화
            del self.browser_usage_count[old_browser]
            self.browser_usage_count[new_browser] = 0
            
            logger.info("✅ 브라우저 재시작 완료")
            
        except Exception as e:
            logger.error(f"❌ 브라우저 재시작 실패: {e}")
    
    async def _check_memory_usage(self):
        """메모리 사용량 체크"""
        current_time = time.time()
        
        if current_time - self.last_memory_check < self.memory_check_interval:
            return
            
        memory = psutil.virtual_memory()
        
        # EC2 최적화 메모리 임계값 적용
        gc_threshold = self.memory_thresholds.get('gc_threshold', 75)
        emergency_threshold = self.memory_thresholds.get(
            'emergency_threshold', 85
        )
        
        if memory.percent > gc_threshold:
            logger.warning(f"⚠️ 높은 메모리 사용량: {memory.percent:.1f}%")
            gc.collect()
            
        if memory.percent > emergency_threshold:
            logger.warning("🧹 EC2 메모리 최적화: 강제 정리 실행")
            await self._emergency_cleanup()
            
        self.last_memory_check = current_time
    
    async def _emergency_cleanup(self):
        """응급 메모리 정리"""
        try:
            # 모든 브라우저의 불필요한 페이지/컨텍스트 정리
            for browser in self.browsers:
                contexts = browser.contexts
                for context in contexts:
                    pages = context.pages
                    # 첫 번째 페이지만 남기고 나머지 정리
                    for page in pages[1:]:
                        await page.close()
            
            # 가비지 컬렉션
            gc.collect()
            
            logger.info("🧹 응급 메모리 정리 완료")
            
        except Exception as e:
            logger.error(f"❌ 응급 메모리 정리 실패: {e}")
    
    async def close(self):
        """브라우저 풀 종료"""
        logger.info("🔚 브라우저 풀 종료 중...")
        
        # 브라우저들을 순차적으로 종료
        browsers_to_close = list(self.browsers)  # 복사본 생성
        for i, browser in enumerate(browsers_to_close):
            try:
                logger.debug(f"브라우저 {i+1}/{len(browsers_to_close)} 종료 중...")
                await asyncio.wait_for(browser.close(), timeout=5.0)
                logger.debug(f"브라우저 {i+1} 종료 완료")
            except asyncio.TimeoutError:
                logger.warning(f"브라우저 {i+1} 종료 시간 초과")
            except Exception as e:
                logger.warning(f"브라우저 {i+1} 종료 오류: {e}")
        
        # Playwright 종료
        if self.playwright:
            try:
                logger.debug("Playwright 종료 중...")
                await asyncio.wait_for(self.playwright.stop(), timeout=10.0)
                logger.debug("Playwright 종료 완료")
            except asyncio.TimeoutError:
                logger.warning("Playwright 종료 시간 초과")
            except Exception as e:
                logger.warning(f"Playwright 종료 오류: {e}")
            finally:
                self.playwright = None
                
        # 메모리 정리
        self.browsers.clear()
        self.available_browsers.clear()
        self.browser_usage_count.clear()
        gc.collect()
        
        logger.info("✅ 브라우저 풀 종료 완료")
    
    def get_status(self) -> Dict:
        """브라우저 풀 상태 정보"""
        memory = psutil.virtual_memory()
        
        return {
            "total_browsers": len(self.browsers),
            "available_browsers": len(self.available_browsers),
            "active_browsers": (
                len(self.browsers) - len(self.available_browsers)
            ),
            "memory_usage_percent": memory.percent,
            "memory_usage_mb": memory.used // 1024 // 1024,
            "memory_limit_mb": self.max_memory_usage // 1024 // 1024,
            "max_browsers": self.max_browsers,
            "platform_type": self.platform_adapter.platform_type,
            "memory_thresholds": self.memory_thresholds
        }


# 전역 브라우저 풀 인스턴스
_browser_pool: Optional[BrowserPool] = None


async def get_browser_pool() -> BrowserPool:
    """전역 브라우저 풀 인스턴스 반환"""
    global _browser_pool
    
    if _browser_pool is None:
        _browser_pool = BrowserPool()
        await _browser_pool.initialize()
        
    return _browser_pool


async def close_browser_pool():
    """전역 브라우저 풀 종료"""
    global _browser_pool
    
    if _browser_pool:
        try:
            await _browser_pool.close()
        except Exception as e:
            logger.error(f"전역 브라우저 풀 종료 오류: {e}")
        finally:
            _browser_pool = None
