"""
Browser Pool Management - 브라우저 세션 최적화
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import threading
from contextlib import asynccontextmanager

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>rowser<PERSON>ontext, <PERSON>
except ImportError:
    Browser = BrowserContext = Page = None
    async_playwright = None

logger = logging.getLogger(__name__)


@dataclass
class BrowserSession:
    """브라우저 세션 정보"""
    session_id: str
    browser: Optional[Browser]
    context: Optional[BrowserContext]
    page: Optional[Page]
    created_at: datetime
    last_used: datetime
    in_use: bool
    sport: Optional[str] = None


@dataclass
class BrowserPoolStats:
    """브라우저 풀 통계"""
    total_sessions: int
    active_sessions: int
    idle_sessions: int
    max_sessions: int
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_session_duration: float


class BrowserPool:
    """브라우저 세션 풀 관리자"""
    
    def __init__(self, 
                 max_browsers: int = 5,
                 headless: bool = True,
                 session_timeout: int = 300,  # 5분
                 cleanup_interval: int = 60):  # 1분
        """
        초기화
        
        Args:
            max_browsers: 최대 브라우저 수
            headless: 헤드리스 모드
            session_timeout: 세션 타임아웃 (초)
            cleanup_interval: 정리 주기 (초)
        """
        self.max_browsers = max_browsers
        self.headless = headless
        self.session_timeout = session_timeout
        self.cleanup_interval = cleanup_interval
        
        # 세션 관리
        self._sessions: Dict[str, BrowserSession] = {}
        self._available_sessions: asyncio.Queue = asyncio.Queue()
        self._lock = threading.Lock()
        
        # 통계
        self._stats = BrowserPoolStats(
            total_sessions=0,
            active_sessions=0,
            idle_sessions=0,
            max_sessions=max_browsers,
            total_requests=0,
            successful_requests=0,
            failed_requests=0,
            average_session_duration=0.0
        )
        
        # Playwright 인스턴스
        self._playwright = None
        self._browser_type = None
        
        # 정리 태스크
        self._cleanup_task = None
        self._initialized = False
        
        logger.info(f"BrowserPool 초기화: max={max_browsers}, headless={headless}")
    
    async def initialize(self):
        """브라우저 풀 초기화"""
        if self._initialized:
            return
        
        if async_playwright is None:
            raise ImportError("Playwright가 설치되지 않았습니다: pip install playwright")
        
        try:
            logger.info("🚀 브라우저 풀 초기화 시작")
            
            # Playwright 시작
            self._playwright = await async_playwright().start()
            self._browser_type = self._playwright.chromium
            
            # 초기 브라우저 세션 생성
            for i in range(min(2, self.max_browsers)):  # 초기에는 2개만 생성
                session = await self._create_session()
                if session:
                    await self._available_sessions.put(session.session_id)
            
            # 정리 태스크 시작
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            self._initialized = True
            logger.info(f"✅ 브라우저 풀 초기화 완료: {len(self._sessions)}개 세션")
            
        except Exception as e:
            logger.error(f"❌ 브라우저 풀 초기화 실패: {e}")
            raise
    
    async def get_browser(self, sport: Optional[str] = None) -> BrowserSession:
        """브라우저 세션 할당"""
        if not self._initialized:
            await self.initialize()
        
        self._stats.total_requests += 1
        
        try:
            # 사용 가능한 세션 대기 (최대 30초)
            session_id = await asyncio.wait_for(
                self._available_sessions.get(), 
                timeout=30.0
            )
            
            with self._lock:
                session = self._sessions.get(session_id)
                if session:
                    session.in_use = True
                    session.last_used = datetime.now()
                    session.sport = sport
                    self._stats.active_sessions += 1
                    self._stats.idle_sessions = max(0, self._stats.idle_sessions - 1)
                    self._stats.successful_requests += 1
                    
                    logger.debug(f"🔄 브라우저 세션 할당: {session_id} (스포츠: {sport})")
                    return session
            
            # 세션이 없으면 새로 생성
            session = await self._create_session()
            if session:
                session.in_use = True
                session.sport = sport
                self._stats.successful_requests += 1
                return session
            
            raise RuntimeError("브라우저 세션 생성 실패")
            
        except asyncio.TimeoutError:
            self._stats.failed_requests += 1
            logger.error("❌ 브라우저 세션 할당 타임아웃")
            raise RuntimeError("브라우저 세션 할당 타임아웃")
        except Exception as e:
            self._stats.failed_requests += 1
            logger.error(f"❌ 브라우저 세션 할당 실패: {e}")
            raise
    
    async def return_browser(self, session: BrowserSession):
        """브라우저 세션 반환"""
        try:
            with self._lock:
                if session.session_id in self._sessions:
                    session.in_use = False
                    session.sport = None
                    session.last_used = datetime.now()
                    self._stats.active_sessions = max(0, self._stats.active_sessions - 1)
                    self._stats.idle_sessions += 1
                    
                    # 세션을 사용 가능 큐에 다시 추가
                    await self._available_sessions.put(session.session_id)
                    
                    logger.debug(f"🔙 브라우저 세션 반환: {session.session_id}")
                else:
                    logger.warning(f"⚠️ 알 수 없는 세션 반환 시도: {session.session_id}")
                    
        except Exception as e:
            logger.error(f"❌ 브라우저 세션 반환 실패: {e}")
    
    @asynccontextmanager
    async def get_browser_context(self, sport: Optional[str] = None):
        """브라우저 세션 컨텍스트 매니저"""
        session = None
        try:
            session = await self.get_browser(sport)
            yield session
        finally:
            if session:
                await self.return_browser(session)
    
    async def _create_session(self) -> Optional[BrowserSession]:
        """새 브라우저 세션 생성"""
        if len(self._sessions) >= self.max_browsers:
            logger.warning(f"⚠️ 최대 브라우저 수 도달: {self.max_browsers}")
            return None
        
        try:
            session_id = f"session_{len(self._sessions)}_{datetime.now().timestamp()}"
            
            # 브라우저 시작
            browser = await self._browser_type.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # 컨텍스트 생성
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            
            # 페이지 생성
            page = await context.new_page()
            
            # 세션 객체 생성
            session = BrowserSession(
                session_id=session_id,
                browser=browser,
                context=context,
                page=page,
                created_at=datetime.now(),
                last_used=datetime.now(),
                in_use=False
            )
            
            with self._lock:
                self._sessions[session_id] = session
                self._stats.total_sessions += 1
                self._stats.idle_sessions += 1
            
            logger.debug(f"🆕 새 브라우저 세션 생성: {session_id}")
            return session
            
        except Exception as e:
            logger.error(f"❌ 브라우저 세션 생성 실패: {e}")
            return None
    
    async def _cleanup_loop(self):
        """주기적 정리 루프"""
        while self._initialized:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_expired_sessions()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ 정리 루프 오류: {e}")
    
    async def _cleanup_expired_sessions(self):
        """만료된 세션 정리"""
        current_time = datetime.now()
        expired_sessions = []
        
        with self._lock:
            for session_id, session in self._sessions.items():
                if (not session.in_use and 
                    current_time - session.last_used > timedelta(seconds=self.session_timeout)):
                    expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            await self._close_session(session_id)
            logger.debug(f"🗑️ 만료된 세션 정리: {session_id}")
    
    async def _close_session(self, session_id: str):
        """세션 종료"""
        try:
            with self._lock:
                session = self._sessions.pop(session_id, None)
                if session:
                    self._stats.total_sessions = max(0, self._stats.total_sessions - 1)
                    if session.in_use:
                        self._stats.active_sessions = max(0, self._stats.active_sessions - 1)
                    else:
                        self._stats.idle_sessions = max(0, self._stats.idle_sessions - 1)
            
            if session:
                # 브라우저 리소스 정리
                if session.page:
                    await session.page.close()
                if session.context:
                    await session.context.close()
                if session.browser:
                    await session.browser.close()
                    
        except Exception as e:
            logger.error(f"❌ 세션 종료 실패 {session_id}: {e}")
    
    async def get_status(self) -> Dict[str, Any]:
        """브라우저 풀 상태 조회"""
        with self._lock:
            return {
                'initialized': self._initialized,
                'stats': {
                    'total_sessions': self._stats.total_sessions,
                    'active_sessions': self._stats.active_sessions,
                    'idle_sessions': self._stats.idle_sessions,
                    'max_sessions': self._stats.max_sessions,
                    'total_requests': self._stats.total_requests,
                    'successful_requests': self._stats.successful_requests,
                    'failed_requests': self._stats.failed_requests,
                    'success_rate': (
                        self._stats.successful_requests / max(1, self._stats.total_requests) * 100
                    )
                },
                'sessions': [
                    {
                        'session_id': session.session_id,
                        'in_use': session.in_use,
                        'sport': session.sport,
                        'created_at': session.created_at.isoformat(),
                        'last_used': session.last_used.isoformat()
                    }
                    for session in self._sessions.values()
                ]
            }
    
    async def cleanup(self):
        """브라우저 풀 정리"""
        logger.info("🧹 브라우저 풀 정리 시작")
        
        self._initialized = False
        
        # 정리 태스크 중단
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 모든 세션 종료
        session_ids = list(self._sessions.keys())
        for session_id in session_ids:
            await self._close_session(session_id)
        
        # Playwright 종료
        if self._playwright:
            await self._playwright.stop()
        
        logger.info("✅ 브라우저 풀 정리 완료")
