"""
Browser Factory
Common browser initialization logic to eliminate duplication
"""
import async<PERSON>
from typing import Dict, Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>

from utils.logger import Logger

logger = Logger(__name__)


class BrowserFactory:
    """
    Centralized browser creation and management
    Eliminates duplicate browser initialization code across the system
    """
    
    _instance: Optional['BrowserFactory'] = None
    _browsers: Dict[str, <PERSON><PERSON><PERSON>] = {}
    
    def __new__(cls) -> 'BrowserFactory':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    async def get_browser(self, browser_type: str = 'chromium', headless: bool = True) -> Optional[Browser]:
        """
        Get or create a browser instance
        
        Args:
            browser_type: Type of browser ('chromium', 'firefox', 'webkit')
            headless: Whether to run in headless mode
            
        Returns:
            Browser instance or None if failed
        """
        browser_key = f"{browser_type}_{headless}"
        
        if browser_key in self._browsers:
            try:
                # Test if browser is still connected
                browser = self._browsers[browser_key]
                await browser.version()  # This will fail if browser is closed
                return browser
            except Exception:
                # <PERSON><PERSON><PERSON> is disconnected, remove it
                del self._browsers[browser_key]
        
        # Create new browser
        return await self._create_browser(browser_type, headless, browser_key)
    
    async def _create_browser(self, browser_type: str, headless: bool, browser_key: str) -> Optional[Browser]:
        """Create a new browser instance"""
        try:
            playwright = await async_playwright().start()
            
            browser_options = {
                'headless': headless,
                'args': [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-default-apps'
                ]
            }
            
            if browser_type == 'chromium':
                browser = await playwright.chromium.launch(**browser_options)
            elif browser_type == 'firefox':
                browser = await playwright.firefox.launch(**browser_options)
            elif browser_type == 'webkit':
                browser = await playwright.webkit.launch(**browser_options)
            else:
                raise ValueError(f"Unsupported browser type: {browser_type}")
            
            self._browsers[browser_key] = browser
            logger.info(f"✅ Browser created: {browser_type} (headless={headless})")
            
            return browser
            
        except Exception as e:
            logger.error(f"Failed to create {browser_type} browser: {e}")
            return None
    
    async def create_page(self, browser_type: str = 'chromium', headless: bool = True) -> Optional[Page]:
        """
        Create a new page with common settings
        
        Args:
            browser_type: Type of browser
            headless: Whether to run in headless mode
            
        Returns:
            Page instance or None if failed
        """
        browser = await self.get_browser(browser_type, headless)
        if not browser:
            return None
        
        try:
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            )
            
            page = await context.new_page()
            
            # Set common page settings
            await page.set_extra_http_headers({
                'Accept-Language': 'ko-KR,ko;q=0.9,en;q=0.8',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            })
            
            return page
            
        except Exception as e:
            logger.error(f"Failed to create page: {e}")
            return None
    
    async def cleanup_browser(self, browser_type: str = 'chromium', headless: bool = True):
        """Clean up a specific browser"""
        browser_key = f"{browser_type}_{headless}"
        
        if browser_key in self._browsers:
            try:
                await self._browsers[browser_key].close()
                del self._browsers[browser_key]
                logger.info(f"🧹 Browser cleaned up: {browser_type}")
            except Exception as e:
                logger.error(f"Failed to cleanup browser {browser_type}: {e}")
    
    async def cleanup_all(self):
        """Clean up all browsers"""
        for browser_key, browser in list(self._browsers.items()):
            try:
                await browser.close()
                logger.info(f"🧹 Browser closed: {browser_key}")
            except Exception as e:
                logger.error(f"Failed to close browser {browser_key}: {e}")
        
        self._browsers.clear()
        logger.info("🧹 All browsers cleaned up")


# Singleton instance
browser_factory = BrowserFactory()