"""
스포츠별 설정 정보 관리
기존 야구 시스템과 호환되는 멀티스포츠 설정
"""
from dataclasses import dataclass
from typing import List, Dict, Optional
from enum import Enum


class Sport(Enum):
    """지원 스포츠 목록"""
    BASEBALL = "baseball"
    SOCCER = "soccer"
    BASKETBALL = "basketball"
    VOLLEYBALL = "volleyball"


@dataclass
class LeagueInfo:
    """리그 정보"""
    id: str                    # 'SC001', 'BK002', 'VL001'
    name: str                  # 'K리그1', 'NBA', 'KOVO남'
    short_name: str           # 'K1', 'NBA', 'KOVO남'
    tab_id: str               # '#ui-id-1', '#ui-id-2'
    tab_order: int            # 탭 순서
    country: str              # 'KOR', 'USA', 'ENG'
    season_format: str        # '25', '24~25', '2024-25'


@dataclass
class SportConfig:
    """스포츠별 설정 정보"""
    sport: Sport                          # Sport enum
    sport_code: str                       # 'SC', 'BK', 'VL'  
    base_url: str                         # 스케줄 페이지 URL
    leagues: List[LeagueInfo]             # 리그 목록
    team_url_pattern: str                 # 팀 상세 URL 패턴
    player_url_pattern: str               # 선수 상세 URL 패턴
    
    def get_team_url(self, league_id: str, team_id: str) -> str:
        """팀 상세 URL 생성"""
        return self.team_url_pattern.format(
            sport_code=self.sport_code,
            league_id=league_id,
            team_id=team_id
        )
    
    def get_player_url(self, league_id: str, team_id: str, player_id: str, **kwargs) -> str:
        """선수 상세 URL 생성"""
        return self.player_url_pattern.format(
            sport_code=self.sport_code,
            league_id=league_id,
            team_id=team_id,
            player_id=player_id,
            **kwargs
        )
    
    def get_league_by_id(self, league_id: str) -> Optional[LeagueInfo]:
        """리그 ID로 리그 정보 조회"""
        for league in self.leagues:
            if league.id == league_id:
                return league
        return None


# 축구 설정
SOCCER_CONFIG = SportConfig(
    sport=Sport.SOCCER,
    sport_code="SC",
    base_url="/main/mainPage/gameinfo/dataOfSoccerSchedule.do",
    leagues=[
        LeagueInfo("SC001", "K리그1", "K1", "#ui-id-1", 1, "KOR", "25"),
        LeagueInfo("SC003", "K리그2", "K2", "#ui-id-2", 2, "KOR", "25"),
        LeagueInfo("52", "EPL", "EPL", "#ui-id-3", 3, "ENG", "24-25"),
        LeagueInfo("53", "프리메라리가", "La Liga", "#ui-id-4", 4, "ESP", "24-25"),
        LeagueInfo("54", "세리에A", "Serie A", "#ui-id-5", 5, "ITA", "24-25"),
        LeagueInfo("55", "분데스리가", "Bundesliga", "#ui-id-6", 6, "GER", "24-25"),
        LeagueInfo("56", "프랑스리그", "Ligue 1", "#ui-id-7", 7, "FRA", "24-25"),
        LeagueInfo("57", "에레디비시", "Eredivisie", "#ui-id-8", 8, "NED", "24-25"),
        LeagueInfo("58", "J리그", "J-League", "#ui-id-9", 9, "JPN", "25"),
    ],
    team_url_pattern="/main/mainPage/gameinfo/scTeamDetail.do?item={sport_code}&leagueId={league_id}&teamId={team_id}",
    player_url_pattern="/main/mainPage/gameinfo/scPlayerDetail.do?item={sport_code}&leagueId={league_id}&teamId={team_id}&playerId={player_id}"
)

# 농구 설정
BASKETBALL_CONFIG = SportConfig(
    sport=Sport.BASKETBALL,
    sport_code="BK",
    base_url="/main/mainPage/gameinfo/dataOfBasketballSchedule.do",
    leagues=[
        LeagueInfo("BK001", "KBL", "KBL", "#ui-id-1", 1, "KOR", "24-25"),
        LeagueInfo("BK003", "WKBL", "WKBL", "#ui-id-2", 2, "KOR", "24-25"),
        LeagueInfo("BK002", "NBA", "NBA", "#ui-id-3", 3, "USA", "24-25"),
    ],
    team_url_pattern="/main/mainPage/gameinfo/bkTeamDetail.do?item={sport_code}&leagueId={league_id}&teamId={team_id}",
    player_url_pattern="/main/mainPage/gameinfo/bkPlayerDetail.do?item={sport_code}&leagueId={league_id}&teamId={team_id}&playerId={player_id}"
)

# 배구 설정
VOLLEYBALL_CONFIG = SportConfig(
    sport=Sport.VOLLEYBALL,
    sport_code="VL",
    base_url="/main/mainPage/gameinfo/dataOfVolleyballSchedule.do",
    leagues=[
        LeagueInfo("VL001", "KOVO남", "KOVO남", "#ui-id-1", 1, "KOR", "24~25"),
        LeagueInfo("VL002", "KOVO여", "KOVO여", "#ui-id-2", 2, "KOR", "24~25"),
    ],
    team_url_pattern="/main/mainPage/gameinfo/vlTeamDetail.do?item={sport_code}&leagueId={league_id}&teamId={team_id}",
    player_url_pattern="/main/mainPage/gameinfo/vlPlayerDetail.do?item={sport_code}&leagueId={league_id}&teamId={team_id}&playerId={player_id}&id={gender_id}"
)

# 기존 야구 설정 (호환성 유지)
BASEBALL_CONFIG = SportConfig(
    sport=Sport.BASEBALL,
    sport_code="BS",
    base_url="/main/mainPage/gameinfo/dataOfBaseballSchedule.do",
    leagues=[
        LeagueInfo("BS001", "KBO", "KBO", "#ui-id-1", 1, "KOR", "25"),
        LeagueInfo("BS002", "MLB", "MLB", "#ui-id-2", 2, "USA", "2025"),
        LeagueInfo("BS004", "NPB", "NPB", "#ui-id-3", 3, "JPN", "25"),
    ],
    team_url_pattern="/main/mainPage/gameinfo/bsTeamDetail.do?item={sport_code}&leagueId={league_id}&teamId={team_id}",
    player_url_pattern="/main/mainPage/gameinfo/bsPlayerDetail.do?item={sport_code}&leagueId={league_id}&teamId={team_id}&playerId={player_id}"
)

# 스포츠별 설정 매핑
SPORT_CONFIGS: Dict[Sport, SportConfig] = {
    Sport.SOCCER: SOCCER_CONFIG,
    Sport.BASKETBALL: BASKETBALL_CONFIG,
    Sport.VOLLEYBALL: VOLLEYBALL_CONFIG,
    Sport.BASEBALL: BASEBALL_CONFIG,
}


def get_sport_config(sport: Sport) -> SportConfig:
    """스포츠별 설정 조회"""
    return SPORT_CONFIGS[sport]


def get_all_leagues() -> Dict[str, LeagueInfo]:
    """모든 리그 정보 조회 (league_id -> LeagueInfo 매핑)"""
    all_leagues = {}
    for config in SPORT_CONFIGS.values():
        for league in config.leagues:
            all_leagues[league.id] = league
    return all_leagues


def get_sport_by_league_id(league_id: str) -> Optional[Sport]:
    """리그 ID로 스포츠 종목 조회"""
    for sport, config in SPORT_CONFIGS.items():
        for league in config.leagues:
            if league.id == league_id:
                return sport
    return None 