"""
Refactored Multi-Sport Orchestrator - 관심사 분리 적용
SRP: 오케스트레이션 로직만 담당
DIP: 구체적인 구현체가 아닌 추상화에 의존
"""
import asyncio
import logging
import threading
from typing import Dict, List, Optional, Any

from core.cache.cache_manager import get_global_cache_manager
from core.resources.resource_manager import ResourceManager
from core.browser.browser_pool import BrowserPool
from core.scheduler.time_slot_scheduler import TimeSlotScheduler
from .result_collector import ResultCollector, OrchestrationSummary

logger = logging.getLogger(__name__)


class MultiSportOrchestrator:
    """멀티스포츠 데이터 수집 조율 시스템 (리팩토링됨)"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        초기화 (관심사 분리 적용)
        
        Args:
            config: 설정 딕셔너리
        """
        self.config = config or {}
        self._apply_default_config()
        
        # 의존성 주입을 위한 컴포넌트들
        self.scheduler = TimeSlotScheduler()
        self.cache_manager = get_global_cache_manager()
        self.resource_manager = ResourceManager(self.config)
        self.result_collector = ResultCollector()
        
        # 브라우저 풀 (플랫폼 어댑터 사용)
        platform_type = self.config.get('platform_type', 'auto')
        self.browser_pool = BrowserPool(platform_type)
        
        # 스포츠 플러그인 (팩토리 패턴 사용 권장)
        self.plugins = self._initialize_plugins()
        
        # 실행 상태
        self._running = False
        self._lock = threading.Lock()
        
        # logger.info("MultiSportOrchestrator 초기화 완료 (리팩토링 버전)")
    
    def _apply_default_config(self) -> None:
        """기본 설정 적용"""
        defaults = {
            'max_browsers': 1,
            'batch_size': 5,
            'worker_timeout': 600,
            'memory_limit': 1024**3,
            'headless': True,
            'enable_cache': True,
            'sport_delay': 5
        }
        
        for key, value in defaults.items():
            if key not in self.config:
                self.config[key] = value
    
    def _initialize_plugins(self) -> Dict[str, Any]:
        """플러그인 초기화 (팩토리 패턴 사용 권장)"""
        # 현재는 기존 방식 유지, 추후 팩토리 패턴으로 변경 권장
        from sports.baseball.plugin import BaseballPlugin
        from sports.basketball.plugin import BasketballPlugin
        from sports.soccer.plugin import SoccerPlugin
        from sports.volleyball.plugin import VolleyballPlugin
        
        return {
            'soccer': SoccerPlugin(),
            'basketball': BasketballPlugin(),
            'volleyball': VolleyballPlugin(),
            'baseball': BaseballPlugin(),
        }
    
    async def start_orchestration(
        self,
        sports: Optional[List[str]] = None,
        force_run: bool = False
    ) -> OrchestrationSummary:
        """
        멀티스포츠 데이터 수집 시작
        
        Args:
            sports: 실행할 스포츠 목록
            force_run: 스케줄 무시하고 강제 실행
            
        Returns:
            OrchestrationSummary: 실행 결과 요약
        """
        try:
            # 실행 상태 확인
            with self._lock:
                if self._running:
                    raise RuntimeError("이미 실행 중입니다")
                self._running = True
            
            # logger.info("🚀 멀티스포츠 데이터 수집 시작 (리팩토링 버전)")
            
            # 컴포넌트 초기화
            await self._initialize_components()
            
            # 결과 수집 시작
            self.result_collector.start_collection()
            
            # 스포츠별 데이터 수집
            await self._execute_sports_collection(sports, force_run)
            
            # 결과 요약 생성
            summary = self.result_collector.get_summary()
            # self.result_collector.log_summary()
            
            # logger.info(f"🏁 멀티스포츠 수집 완료: {summary.message}")
            return summary
            
        except Exception as e:
            error_msg = f"오케스트레이션 실패: {str(e)}"
            logger.error(f"💥 {error_msg}")
            
            # 에러 결과 반환
            self.result_collector.add_error_result("system", e)
            return self.result_collector.get_summary()
            
        finally:
            with self._lock:
                self._running = False
            await self._cleanup_components()
    
    async def _initialize_components(self) -> None:
        """컴포넌트 초기화"""
        # logger.info("🔧 컴포넌트 초기화 시작")
        
        # 캐시 초기화
        cache_enabled = self.config.get('enable_cache', True)
        if cache_enabled:
            await self.cache_manager.initialize()
        
        # 리소스 관리자 초기화
        await self.resource_manager.initialize(self.browser_pool)
        
        # logger.info("✅ 컴포넌트 초기화 완료")
    
    async def _execute_sports_collection(
        self,
        sports: Optional[List[str]],
        force_run: bool
    ) -> None:
        """스포츠별 데이터 수집 실행"""
        target_sports = sports or list(self.plugins.keys())
        
        for sport in target_sports:
            if sport not in self.plugins:
                logger.warning(f"⚠️ 알 수 없는 스포츠: {sport}")
                continue
            
            # 스케줄 체크
            if not force_run and not self._should_run_sport(sport):
                # logger.info(f"⏭️ {sport} 스케줄 시간이 아님 - 스킵")
                continue
            
            # 스포츠 데이터 수집
            await self._collect_sport_data(sport)
            
            # 스포츠 간 대기 시간
            await asyncio.sleep(self.config.get('sport_delay', 5))
    
    async def _collect_sport_data(self, sport: str) -> None:
        """개별 스포츠 데이터 수집"""
        try:
            # logger.info(f"🎯 {sport} 데이터 수집 시작")
            
            plugin = self.plugins[sport]
            
            # 캐시를 플러그인에 주입
            if hasattr(plugin, 'set_cache'):
                duplicate_cache = self.cache_manager.get_duplicate_cache()
                plugin.set_cache(duplicate_cache)
            
            # 데이터 수집 실행
            result = await plugin.collect_data()
            
            # 결과 수집
            self.result_collector.add_sport_result(
                sport=sport,
                success=result.get('success', False),
                message=result.get('message', ''),
                data_count=result.get('count', 0),
                duration=result.get('duration', 0),
                errors=result.get('errors', []),
                teams_processed=result.get('teams_processed', 0),
                players_processed=result.get('players_processed', 0)
            )
            
            # logger.info(f"✅ {sport} 완료: {result.get('message', '')}")
            
        except Exception as e:
            logger.error(f"❌ {sport} 실행 실패: {e}")
            self.result_collector.add_error_result(sport, e)
    
    async def _cleanup_components(self) -> None:
        """컴포넌트 정리"""
        try:
            # logger.info("🧹 컴포넌트 정리 시작")
            await self.resource_manager.cleanup()
            # logger.info("✅ 컴포넌트 정리 완료")
        except Exception as e:
            logger.warning(f"컴포넌트 정리 중 오류: {e}")
    
    def _should_run_sport(self, sport: str) -> bool:
        """스포츠 실행 여부 결정"""
        # 현재는 항상 실행, 추후 스케줄러 로직 추가
        return True
    
    async def get_status(self) -> Dict[str, Any]:
        """현재 실행 상태 조회"""
        with self._lock:
            running = self._running
        
        # 리소스 상태 조회
        resource_status = await self.resource_manager.get_resource_summary()
        
        return {
            'running': running,
            'available_sports': list(self.plugins.keys()),
            'resources': resource_status,
            'last_results': self.result_collector.get_statistics()
        }
    
    async def stop_orchestration(self) -> None:
        """실행 중인 조율 중단"""
        logger.info("🛑 멀티스포츠 조율 중단 요청")
        with self._lock:
            self._running = False
        await self._cleanup_components()
    
    async def cleanup(self) -> None:
        """전체 정리"""
        await self.stop_orchestration()
        self.result_collector.clear_results()
        logger.info("🧹 MultiSportOrchestrator 정리 완료")
    
    def get_result_collector(self) -> ResultCollector:
        """결과 수집기 반환 (테스트용)"""
        return self.result_collector
    
    def get_cache_manager(self):
        """캐시 관리자 반환"""
        return self.cache_manager
    
    def get_resource_manager(self) -> ResourceManager:
        """리소스 관리자 반환"""
        return self.resource_manager