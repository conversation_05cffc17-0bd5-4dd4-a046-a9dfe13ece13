"""
MultiSportOrchestrator - 전체 스포츠 조율 시스템
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import threading

from core.config.sport_config import SportConfig
from core.scheduler.time_slot_scheduler import TimeSlotScheduler
from core.browser.browser_pool import BrowserPool
from sports.soccer.plugin import SoccerPlugin
from sports.basketball.plugin import BasketballPlugin
from sports.volleyball.plugin import VolleyballPlugin

logger = logging.getLogger(__name__)


@dataclass
class OrchestrationResult:
    """조율 결과 데이터"""
    sport: str
    success: bool
    teams_processed: int
    players_processed: int
    errors: List[str]
    start_time: datetime
    end_time: datetime
    duration: timedelta


@dataclass
class OrchestrationSummary:
    """전체 조율 요약"""
    total_sports: int
    successful_sports: int
    total_teams: int
    total_players: int
    total_duration: timedelta
    results: List[OrchestrationResult]
    errors: List[str]


class MultiSportOrchestrator:
    """멀티스포츠 데이터 수집 조율 시스템"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        초기화
        
        Args:
            config: 설정 딕셔너리
        """
        self.config = config or {}
        self.scheduler = TimeSlotScheduler()
        self.browser_pool = BrowserPool(
            max_browsers=self.config.get('max_browsers', 5),
            headless=self.config.get('headless', True)
        )
        
        # 스포츠 플러그인 등록
        self.plugins = {
            'soccer': SoccerPlugin(),
            'basketball': BasketballPlugin(),
            'volleyball': VolleyballPlugin()
        }
        
        # 실행 상태
        self._running = False
        self._results: List[OrchestrationResult] = []
        self._lock = threading.Lock()
        
        logger.info("MultiSportOrchestrator 초기화 완료")
    
    async def start_orchestration(self, 
                                sports: Optional[List[str]] = None,
                                force_run: bool = False) -> OrchestrationSummary:
        """
        멀티스포츠 데이터 수집 시작
        
        Args:
            sports: 실행할 스포츠 목록 (None이면 모든 스포츠)
            force_run: 스케줄 무시하고 강제 실행
            
        Returns:
            OrchestrationSummary: 실행 결과 요약
        """
        start_time = datetime.now()
        
        try:
            with self._lock:
                if self._running:
                    raise RuntimeError("이미 실행 중입니다")
                self._running = True
                self._results = []
            
            logger.info("🚀 멀티스포츠 데이터 수집 시작")
            
            # 브라우저 풀 초기화
            await self.browser_pool.initialize()
            
            # 실행할 스포츠 결정
            target_sports = sports or list(self.plugins.keys())
            
            # 스케줄 확인 (force_run이 아닌 경우)
            if not force_run:
                target_sports = self._filter_by_schedule(target_sports)
            
            if not target_sports:
                logger.info("현재 시간에 실행할 스포츠가 없습니다")
                return self._create_summary(start_time, [])
            
            logger.info(f"실행 대상 스포츠: {target_sports}")
            
            # 병렬 실행
            tasks = []
            for sport in target_sports:
                if sport in self.plugins:
                    task = asyncio.create_task(
                        self._process_sport(sport, self.plugins[sport])
                    )
                    tasks.append(task)
            
            # 모든 스포츠 처리 완료 대기
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 결과 처리
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    sport = target_sports[i]
                    error_result = OrchestrationResult(
                        sport=sport,
                        success=False,
                        teams_processed=0,
                        players_processed=0,
                        errors=[str(result)],
                        start_time=start_time,
                        end_time=datetime.now(),
                        duration=datetime.now() - start_time
                    )
                    processed_results.append(error_result)
                    logger.error(f"❌ {sport} 처리 실패: {result}")
                else:
                    processed_results.append(result)
            
            return self._create_summary(start_time, processed_results)
            
        except Exception as e:
            logger.error(f"❌ 멀티스포츠 조율 실패: {e}")
            raise
        finally:
            with self._lock:
                self._running = False
            await self.browser_pool.cleanup()
    
    async def _process_sport(self, sport_name: str, plugin) -> OrchestrationResult:
        """개별 스포츠 처리"""
        start_time = datetime.now()
        teams_processed = 0
        players_processed = 0
        errors = []
        
        try:
            logger.info(f"🏃‍♂️ {sport_name} 데이터 수집 시작")
            
            # 브라우저 세션 할당
            browser_session = await self.browser_pool.get_browser()
            
            try:
                # 플러그인을 통한 데이터 수집
                result = await plugin.collect_all_data(browser_session)
                
                teams_processed = result.get('teams_processed', 0)
                players_processed = result.get('players_processed', 0)
                errors = result.get('errors', [])
                
                logger.info(f"✅ {sport_name} 완료: 팀 {teams_processed}개, 선수 {players_processed}명")
                
            finally:
                # 브라우저 세션 반환
                await self.browser_pool.return_browser(browser_session)
            
            return OrchestrationResult(
                sport=sport_name,
                success=len(errors) == 0,
                teams_processed=teams_processed,
                players_processed=players_processed,
                errors=errors,
                start_time=start_time,
                end_time=datetime.now(),
                duration=datetime.now() - start_time
            )
            
        except Exception as e:
            logger.error(f"❌ {sport_name} 처리 중 오류: {e}")
            return OrchestrationResult(
                sport=sport_name,
                success=False,
                teams_processed=teams_processed,
                players_processed=players_processed,
                errors=[str(e)] + errors,
                start_time=start_time,
                end_time=datetime.now(),
                duration=datetime.now() - start_time
            )
    
    def _filter_by_schedule(self, sports: List[str]) -> List[str]:
        """스케줄에 따라 실행할 스포츠 필터링"""
        current_time = datetime.now()
        filtered_sports = []
        
        for sport in sports:
            if sport in self.plugins:
                plugin = self.plugins[sport]
                sport_config = plugin.get_sport_config()
                
                # 시간대 확인
                if self.scheduler.should_run_now(sport_config, current_time):
                    filtered_sports.append(sport)
                else:
                    logger.debug(f"⏰ {sport}는 현재 시간대가 아님")
        
        return filtered_sports
    
    def _create_summary(self, start_time: datetime, 
                       results: List[OrchestrationResult]) -> OrchestrationSummary:
        """실행 결과 요약 생성"""
        end_time = datetime.now()
        total_duration = end_time - start_time
        
        successful_sports = sum(1 for r in results if r.success)
        total_teams = sum(r.teams_processed for r in results)
        total_players = sum(r.players_processed for r in results)
        
        all_errors = []
        for result in results:
            all_errors.extend(result.errors)
        
        summary = OrchestrationSummary(
            total_sports=len(results),
            successful_sports=successful_sports,
            total_teams=total_teams,
            total_players=total_players,
            total_duration=total_duration,
            results=results,
            errors=all_errors
        )
        
        # 결과 로깅
        logger.info("=" * 60)
        logger.info("🎉 멀티스포츠 데이터 수집 완료")
        logger.info(f"   총 스포츠: {summary.total_sports}개")
        logger.info(f"   성공: {summary.successful_sports}개")
        logger.info(f"   총 팀: {summary.total_teams}개")
        logger.info(f"   총 선수: {summary.total_players}명")
        logger.info(f"   소요 시간: {summary.total_duration}")
        
        if summary.errors:
            logger.warning(f"   오류: {len(summary.errors)}개")
        
        logger.info("=" * 60)
        
        return summary
    
    async def get_status(self) -> Dict[str, Any]:
        """현재 실행 상태 조회"""
        with self._lock:
            return {
                'running': self._running,
                'available_sports': list(self.plugins.keys()),
                'browser_pool_status': await self.browser_pool.get_status(),
                'last_results': [asdict(r) for r in self._results[-5:]]  # 최근 5개 결과
            }
    
    async def stop_orchestration(self):
        """실행 중인 조율 중단"""
        logger.info("🛑 멀티스포츠 조율 중단 요청")
        with self._lock:
            self._running = False
        await self.browser_pool.cleanup()
    
    async def cleanup(self):
        """리소스 정리"""
        await self.stop_orchestration()
        logger.info("🧹 MultiSportOrchestrator 정리 완료")
