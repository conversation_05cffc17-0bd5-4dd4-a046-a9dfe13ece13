"""
데이터 파싱 인터페이스 정의
Strategy 패턴을 활용한 유연한 파싱 시스템
"""
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum

# 제네릭 타입 변수
T = TypeVar('T')


class ParseStatus(Enum):
    """파싱 상태 열거형"""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"
    INVALID_FORMAT = "invalid_format"


@dataclass
class ParseResult(Generic[T]):
    """파싱 결과 데이터 클래스"""
    status: ParseStatus
    data: Optional[T] = None
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class DataValidator(ABC):
    """데이터 검증자 추상 클래스 (SRP 원칙 적용)"""
    
    @abstractmethod
    def validate(self, data: Any) -> bool:
        """데이터 유효성 검증"""
        pass
    
    @abstractmethod
    def get_validation_errors(self, data: Any) -> List[str]:
        """검증 오류 목록 반환"""
        pass


class DataTransformer(ABC):
    """데이터 변환자 추상 클래스 (SRP 원칙 적용)"""
    
    @abstractmethod
    def transform(self, raw_data: Any) -> Any:
        """원시 데이터를 목적에 맞게 변환"""
        pass


class SportParser(Generic[T], ABC):
    """
    스포츠 데이터 파싱 추상 클래스
    Strategy 패턴 적용으로 다양한 파싱 전략 지원
    """
    
    def __init__(
        self, 
        validator: Optional[DataValidator] = None,
        transformer: Optional[DataTransformer] = None
    ):
        self._validator = validator
        self._transformer = transformer
    
    # Template Method 패턴 구현
    async def parse(self, raw_data: str, **kwargs) -> ParseResult[T]:
        """
        파싱 템플릿 메서드
        공통 파싱 흐름을 정의하고 하위 클래스에서 세부 구현
        """
        try:
            # 전처리
            processed_data = await self._preprocess(raw_data, **kwargs)
            
            # 실제 파싱
            result = await self._do_parse(processed_data, **kwargs)
            
            # 데이터 검증
            if self._validator and result.data is not None:
                if not self._validator.validate(result.data):
                    validation_errors = self._validator.get_validation_errors(result.data)
                    result.errors = (result.errors or []) + validation_errors
                    result.status = ParseStatus.INVALID_FORMAT
            
            # 데이터 변환
            if self._transformer and result.data is not None and result.status == ParseStatus.SUCCESS:
                result.data = self._transformer.transform(result.data)
            
            # 후처리
            result = await self._postprocess(result, **kwargs)
            
            return result
            
        except Exception as e:
            return ParseResult(
                status=ParseStatus.FAILED,
                errors=[f"파싱 중 오류 발생: {str(e)}"]
            )
    
    # 하위 클래스에서 구현할 추상 메서드들
    @abstractmethod
    async def _do_parse(self, data: str, **kwargs) -> ParseResult[T]:
        """실제 파싱 로직 구현 (하위 클래스에서 구현)"""
        pass
    
    @abstractmethod
    async def parse_team_data(self, html: str, **kwargs) -> ParseResult[T]:
        """팀 데이터 파싱"""
        pass
    
    @abstractmethod
    async def parse_player_data(self, html: str, **kwargs) -> ParseResult[T]:
        """선수 데이터 파싱"""
        pass
    
    @abstractmethod
    async def parse_game_data(self, html: str, **kwargs) -> ParseResult[T]:
        """경기 데이터 파싱"""
        pass
    
    # 훅 메서드들 (선택적 구현)
    async def _preprocess(self, raw_data: str, **kwargs) -> str:
        """파싱 전 데이터 전처리 (선택적 구현)"""
        return raw_data
    
    async def _postprocess(self, result: ParseResult[T], **kwargs) -> ParseResult[T]:
        """파싱 후 데이터 후처리 (선택적 구현)"""
        return result
    
    # 헬퍼 메서드들
    @staticmethod
    def _safe_extract_text(element: Any, default: str = "") -> str:
        """안전한 텍스트 추출"""
        if element is None:
            return default
        
        # BeautifulSoup 요소인 경우
        if hasattr(element, 'get_text'):
            return element.get_text(strip=True)
        elif hasattr(element, 'text'):
            return element.text.strip()
        else:
            return str(element).strip()
    
    @staticmethod
    def _safe_extract_int(text: str, default: int = 0) -> int:
        """안전한 정수 변환"""
        try:
            # 숫자가 아닌 문자 제거
            cleaned = ''.join(c for c in str(text) if c.isdigit() or c in '.-')
            return int(float(cleaned)) if cleaned else default
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def _safe_extract_float(text: str, default: float = 0.0) -> float:
        """안전한 실수 변환"""
        try:
            # 숫자가 아닌 문자 제거 (소수점 포함)
            cleaned = ''.join(c for c in str(text) if c.isdigit() or c in '.-')
            return float(cleaned) if cleaned else default
        except (ValueError, TypeError):
            return default


class BulkParser(SportParser[T], ABC):
    """
    대량 데이터 파싱을 위한 특수 파서
    메모리 효율적인 스트리밍 파싱 지원
    """
    
    @abstractmethod
    async def parse_bulk(
        self, 
        data_sources: List[str], 
        chunk_size: int = 100
    ) -> List[ParseResult[T]]:
        """대량 데이터 청크 단위 파싱"""
        pass
    
    @abstractmethod
    async def parse_stream(
        self, 
        data_stream: Any, 
        buffer_size: int = 1024
    ) -> ParseResult[List[T]]:
        """스트리밍 데이터 파싱"""
        pass


class SchemaParser(SportParser[T], ABC):
    """
    스키마 기반 파싱을 위한 특수 파서
    데이터 구조 검증 및 타입 안전성 보장
    """
    
    @property
    @abstractmethod
    def schema(self) -> Dict[str, Any]:
        """파싱 대상 데이터의 스키마 정의"""
        pass
    
    @abstractmethod
    async def validate_schema(self, data: Dict[str, Any]) -> bool:
        """스키마 유효성 검증"""
        pass 