"""
데이터 처리 인터페이스 - SOLID 원칙 적용
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, TypeVar, Generic
from dataclasses import dataclass
from enum import Enum

T = TypeVar('T')


class ProcessingStatus(Enum):
    """처리 상태"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class ProcessingResult(Generic[T]):
    """처리 결과"""
    status: ProcessingStatus
    data: Optional[T] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    @property
    def is_success(self) -> bool:
        """성공 여부"""
        return self.status == ProcessingStatus.SUCCESS
    
    @property
    def is_failed(self) -> bool:
        """실패 여부"""
        return self.status == ProcessingStatus.FAILED


class DataValidator(ABC):
    """데이터 검증 인터페이스 - Single Responsibility Principle"""
    
    @abstractmethod
    def validate(self, data: Any) -> bool:
        """데이터 유효성 검증"""
        pass
    
    @abstractmethod
    def get_validation_errors(self, data: Any) -> List[str]:
        """검증 오류 목록 반환"""
        pass


class DataTransformer(ABC, Generic[T]):
    """데이터 변환 인터페이스 - Single Responsibility Principle"""
    
    @abstractmethod
    def transform(self, raw_data: Any) -> T:
        """원시 데이터를 목표 형식으로 변환"""
        pass
    
    @abstractmethod
    def can_transform(self, raw_data: Any) -> bool:
        """변환 가능 여부 확인"""
        pass


class DataEnricher(ABC, Generic[T]):
    """데이터 보강 인터페이스 - Single Responsibility Principle"""
    
    @abstractmethod
    def enrich(self, data: T) -> T:
        """데이터 보강"""
        pass
    
    @abstractmethod
    def get_enrichment_metadata(self, data: T) -> Dict[str, Any]:
        """보강 메타데이터 반환"""
        pass


class DataProcessor(ABC, Generic[T]):
    """
    데이터 처리 인터페이스 - Open/Closed Principle
    새로운 처리 로직은 상속을 통해 확장
    """
    
    def __init__(self, 
                 validator: Optional[DataValidator] = None,
                 transformer: Optional[DataTransformer[T]] = None,
                 enricher: Optional[DataEnricher[T]] = None):
        """
        의존성 주입 - Dependency Inversion Principle
        """
        self.validator = validator
        self.transformer = transformer
        self.enricher = enricher
    
    @abstractmethod
    def process(self, raw_data: Any) -> ProcessingResult[T]:
        """데이터 처리 메인 로직"""
        pass
    
    def validate_data(self, data: Any) -> bool:
        """데이터 검증 - 검증기가 있는 경우에만"""
        if self.validator:
            return self.validator.validate(data)
        return True
    
    def transform_data(self, raw_data: Any) -> Optional[T]:
        """데이터 변환 - 변환기가 있는 경우에만"""
        if self.transformer and self.transformer.can_transform(raw_data):
            return self.transformer.transform(raw_data)
        return None
    
    def enrich_data(self, data: T) -> T:
        """데이터 보강 - 보강기가 있는 경우에만"""
        if self.enricher:
            return self.enricher.enrich(data)
        return data


class BatchDataProcessor(ABC, Generic[T]):
    """
    배치 데이터 처리 인터페이스
    """
    
    @abstractmethod
    def process_batch(self, raw_data_list: List[Any]) -> List[ProcessingResult[T]]:
        """배치 데이터 처리"""
        pass
    
    @abstractmethod
    def get_batch_size(self) -> int:
        """배치 크기 반환"""
        pass


class DataProcessorFactory(ABC):
    """
    데이터 처리기 팩토리 - Factory Pattern
    """
    
    @abstractmethod
    def create_processor(self, processor_type: str, **kwargs) -> DataProcessor:
        """처리기 생성"""
        pass
    
    @abstractmethod
    def get_supported_types(self) -> List[str]:
        """지원하는 처리기 타입 목록"""
        pass


class ProcessingPipeline(Generic[T]):
    """
    처리 파이프라인 - Chain of Responsibility Pattern
    """
    
    def __init__(self):
        self.processors: List[DataProcessor[T]] = []
    
    def add_processor(self, processor: DataProcessor[T]) -> 'ProcessingPipeline[T]':
        """처리기 추가"""
        self.processors.append(processor)
        return self
    
    def process(self, raw_data: Any) -> ProcessingResult[T]:
        """파이프라인 실행"""
        current_data = raw_data
        
        for processor in self.processors:
            result = processor.process(current_data)
            
            if result.is_failed:
                return result
            
            if result.data is not None:
                current_data = result.data
        
        return ProcessingResult(
            status=ProcessingStatus.SUCCESS,
            data=current_data
        )


class ProcessingStrategy(ABC, Generic[T]):
    """
    처리 전략 인터페이스 - Strategy Pattern
    """
    
    @abstractmethod
    def execute(self, data: Any) -> ProcessingResult[T]:
        """전략 실행"""
        pass
    
    @abstractmethod
    def can_handle(self, data: Any) -> bool:
        """처리 가능 여부"""
        pass


class ProcessingContext(Generic[T]):
    """
    처리 컨텍스트 - Strategy Pattern
    """
    
    def __init__(self):
        self.strategies: List[ProcessingStrategy[T]] = []
    
    def add_strategy(self, strategy: ProcessingStrategy[T]):
        """전략 추가"""
        self.strategies.append(strategy)
    
    def process(self, data: Any) -> ProcessingResult[T]:
        """적절한 전략으로 처리"""
        for strategy in self.strategies:
            if strategy.can_handle(data):
                return strategy.execute(data)
        
        return ProcessingResult(
            status=ProcessingStatus.FAILED,
            error="No suitable strategy found"
        )


class ProcessingObserver(ABC):
    """
    처리 관찰자 인터페이스 - Observer Pattern
    """
    
    @abstractmethod
    def on_processing_started(self, data: Any):
        """처리 시작 시 호출"""
        pass
    
    @abstractmethod
    def on_processing_completed(self, result: ProcessingResult):
        """처리 완료 시 호출"""
        pass
    
    @abstractmethod
    def on_processing_failed(self, error: str):
        """처리 실패 시 호출"""
        pass


class ObservableDataProcessor(DataProcessor[T]):
    """
    관찰 가능한 데이터 처리기 - Observer Pattern
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.observers: List[ProcessingObserver] = []
    
    def add_observer(self, observer: ProcessingObserver):
        """관찰자 추가"""
        self.observers.append(observer)
    
    def remove_observer(self, observer: ProcessingObserver):
        """관찰자 제거"""
        if observer in self.observers:
            self.observers.remove(observer)
    
    def notify_started(self, data: Any):
        """처리 시작 알림"""
        for observer in self.observers:
            observer.on_processing_started(data)
    
    def notify_completed(self, result: ProcessingResult[T]):
        """처리 완료 알림"""
        for observer in self.observers:
            observer.on_processing_completed(result)
    
    def notify_failed(self, error: str):
        """처리 실패 알림"""
        for observer in self.observers:
            observer.on_processing_failed(error)


class ProcessingMetrics:
    """
    처리 메트릭 수집 - Single Responsibility Principle
    """
    
    def __init__(self):
        self.total_processed = 0
        self.successful_processed = 0
        self.failed_processed = 0
        self.processing_times: List[float] = []
    
    def record_success(self, processing_time: float):
        """성공 기록"""
        self.total_processed += 1
        self.successful_processed += 1
        self.processing_times.append(processing_time)
    
    def record_failure(self, processing_time: float):
        """실패 기록"""
        self.total_processed += 1
        self.failed_processed += 1
        self.processing_times.append(processing_time)
    
    def get_success_rate(self) -> float:
        """성공률 계산"""
        if self.total_processed == 0:
            return 0.0
        return self.successful_processed / self.total_processed
    
    def get_average_processing_time(self) -> float:
        """평균 처리 시간"""
        if not self.processing_times:
            return 0.0
        return sum(self.processing_times) / len(self.processing_times)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """메트릭 요약"""
        return {
            'total_processed': self.total_processed,
            'successful_processed': self.successful_processed,
            'failed_processed': self.failed_processed,
            'success_rate': self.get_success_rate(),
            'average_processing_time': self.get_average_processing_time()
        }
