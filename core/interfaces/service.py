"""
비즈니스 로직 서비스 인터페이스 정의
서비스 계층의 공통 인터페이스와 패턴 제공
"""
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Dict, Any, Optional, List, Protocol
from dataclasses import dataclass
from enum import Enum

# 제네릭 타입 변수
T = TypeVar('T')
R = TypeVar('R')


class ServiceStatus(Enum):
    """서비스 처리 상태 열거형"""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"
    TIMEOUT = "timeout"
    UNAUTHORIZED = "unauthorized"
    NOT_FOUND = "not_found"


@dataclass
class ServiceResult(Generic[T]):
    """서비스 처리 결과 데이터 클래스"""
    status: ServiceStatus
    data: Optional[T] = None
    message: Optional[str] = None
    errors: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class Repository(Protocol, Generic[T]):
    """데이터 저장소 인터페이스 (DIP 원칙 적용)"""
    
    async def save(self, entity: T) -> ServiceResult[T]:
        """엔티티 저장"""
        ...
    
    async def find_by_id(self, entity_id: str) -> ServiceResult[T]:
        """ID로 엔티티 조회"""
        ...
    
    async def find_all(self, **filters) -> ServiceResult[List[T]]:
        """조건에 맞는 모든 엔티티 조회"""
        ...
    
    async def update(self, entity: T) -> ServiceResult[T]:
        """엔티티 업데이트"""
        ...
    
    async def delete(self, entity_id: str) -> ServiceResult[bool]:
        """엔티티 삭제"""
        ...


class CacheService(Protocol):
    """캐시 서비스 인터페이스"""
    
    async def get(self, key: str) -> Optional[Any]:
        """캐시에서 값 조회"""
        ...
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """캐시에 값 저장"""
        ...
    
    async def delete(self, key: str) -> bool:
        """캐시에서 값 삭제"""
        ...
    
    async def clear(self) -> bool:
        """전체 캐시 삭제"""
        ...


class NotificationService(Protocol):
    """알림 서비스 인터페이스"""
    
    async def send_notification(
        self, 
        message: str, 
        recipient: str, 
        notification_type: str = "info"
    ) -> bool:
        """알림 발송"""
        ...


class SportService(Generic[T, R], ABC):
    """
    스포츠 비즈니스 로직 서비스 추상 클래스
    Domain Driven Design 원칙을 적용한 서비스 계층
    """
    
    def __init__(
        self,
        repository: Repository[T],
        cache_service: Optional[CacheService] = None,
        notification_service: Optional[NotificationService] = None
    ):
        self._repository = repository
        self._cache_service = cache_service
        self._notification_service = notification_service
    
    # Template Method 패턴 구현
    async def process(self, input_data: R, **kwargs) -> ServiceResult[T]:
        """
        비즈니스 로직 처리 템플릿 메서드
        공통 처리 흐름을 정의하고 하위 클래스에서 세부 구현
        """
        try:
            # 전처리
            validated_data = await self._validate_input(input_data, **kwargs)
            if not validated_data:
                return ServiceResult(
                    status=ServiceStatus.FAILED,
                    errors=["입력 데이터 검증 실패"]
                )
            
            # 캐시 확인
            cached_result = await self._check_cache(input_data, **kwargs)
            if cached_result:
                return cached_result
            
            # 실제 비즈니스 로직 실행
            result = await self._execute_business_logic(input_data, **kwargs)
            
            # 결과 캐싱
            await self._cache_result(input_data, result, **kwargs)
            
            # 후처리
            await self._post_process(result, **kwargs)
            
            return result
            
        except Exception as e:
            error_msg = f"서비스 처리 중 오류 발생: {str(e)}"
            await self._handle_error(e, input_data, **kwargs)
            return ServiceResult(
                status=ServiceStatus.FAILED,
                errors=[error_msg]
            )
    
    # 하위 클래스에서 구현할 추상 메서드들
    @abstractmethod
    async def _execute_business_logic(self, input_data: R, **kwargs) -> ServiceResult[T]:
        """핵심 비즈니스 로직 실행 (하위 클래스에서 구현)"""
        pass
    
    @abstractmethod
    async def save_stats(self, stats_data: T, **kwargs) -> ServiceResult[T]:
        """통계 데이터 저장"""
        pass
    
    @abstractmethod
    async def get_stats(self, query_params: Dict[str, Any]) -> ServiceResult[List[T]]:
        """통계 데이터 조회"""
        pass
    
    @abstractmethod
    async def update_stats(self, stats_id: str, update_data: T) -> ServiceResult[T]:
        """통계 데이터 업데이트"""
        pass
    
    # 훅 메서드들 (선택적 구현)
    async def _validate_input(self, input_data: R, **kwargs) -> bool:
        """입력 데이터 검증 (선택적 구현)"""
        return True
    
    async def _check_cache(self, input_data: R, **kwargs) -> Optional[ServiceResult[T]]:
        """캐시 확인 (선택적 구현)"""
        if not self._cache_service:
            return None
        
        cache_key = await self._generate_cache_key(input_data, **kwargs)
        cached_data = await self._cache_service.get(cache_key)
        
        if cached_data:
            return ServiceResult(
                status=ServiceStatus.SUCCESS,
                data=cached_data,
                metadata={"from_cache": True}
            )
        
        return None
    
    async def _cache_result(self, input_data: R, result: ServiceResult[T], **kwargs) -> None:
        """결과 캐싱 (선택적 구현)"""
        if not self._cache_service or result.status != ServiceStatus.SUCCESS:
            return
        
        cache_key = await self._generate_cache_key(input_data, **kwargs)
        await self._cache_service.set(cache_key, result.data, ttl=43200)  # 12시간 TTL
    
    async def _post_process(self, result: ServiceResult[T], **kwargs) -> None:
        """후처리 (선택적 구현)"""
        # 성공 시 알림 발송
        if (self._notification_service and 
            result.status == ServiceStatus.SUCCESS):
            await self._notification_service.send_notification(
                "데이터 처리 완료",
                "system",
                "info"
            )
    
    async def _handle_error(self, error: Exception, input_data: R, **kwargs) -> None:
        """에러 처리 (선택적 구현)"""
        # 에러 알림 발송
        if self._notification_service:
            await self._notification_service.send_notification(
                f"처리 중 오류 발생: {str(error)}",
                "admin",
                "error"
            )
    
    async def _generate_cache_key(self, input_data: R, **kwargs) -> str:
        """캐시 키 생성"""
        import hashlib
        data_str = str(input_data) + str(sorted(kwargs.items()))
        return hashlib.md5(data_str.encode()).hexdigest()


class PlayerStatsService(SportService[T, R], ABC):
    """선수 통계 서비스 특화 인터페이스"""
    
    @abstractmethod
    async def get_player_stats(
        self, 
        player_id: str, 
        season: Optional[str] = None
    ) -> ServiceResult[T]:
        """선수 통계 조회"""
        pass
    
    @abstractmethod
    async def get_player_recent_games(
        self, 
        player_id: str, 
        game_count: int = 5
    ) -> ServiceResult[List[T]]:
        """선수 최근 경기 기록 조회"""
        pass


class TeamStatsService(SportService[T, R], ABC):
    """팀 통계 서비스 특화 인터페이스"""
    
    @abstractmethod
    async def get_team_stats(
        self, 
        team_id: str, 
        season: Optional[str] = None
    ) -> ServiceResult[T]:
        """팀 통계 조회"""
        pass
    
    @abstractmethod
    async def get_team_rankings(
        self, 
        league: str, 
        season: Optional[str] = None
    ) -> ServiceResult[List[T]]:
        """팀 순위 조회"""
        pass


class GameStatsService(SportService[T, R], ABC):
    """경기 통계 서비스 특화 인터페이스"""
    
    @abstractmethod
    async def get_game_stats(self, game_id: str) -> ServiceResult[T]:
        """경기 통계 조회"""
        pass
    
    @abstractmethod
    async def get_upcoming_games(
        self, 
        team_id: Optional[str] = None,
        days: int = 7
    ) -> ServiceResult[List[T]]:
        """예정된 경기 조회"""
        pass 