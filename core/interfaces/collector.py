"""
데이터 수집기 인터페이스 정의
SOLID 원칙을 준수하여 확장 가능하고 유연한 구조 제공
"""
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, List, Dict, Any, Optional, Protocol
from dataclasses import dataclass
from enum import Enum

# 제네릭 타입 변수
T = TypeVar('T')


class CollectionStatus(Enum):
    """수집 상태 열거형"""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"
    TIMEOUT = "timeout"


@dataclass
class CollectorResult(Generic[T]):
    """수집 결과 데이터 클래스"""
    status: CollectionStatus
    data: Optional[List[T]] = None
    errors: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    total_count: int = 0
    success_count: int = 0


class ProgressObserver(Protocol):
    """진행상황 관찰자 프로토콜 (ISP 원칙 적용)"""
    
    def on_progress(self, current: int, total: int, message: str) -> None:
        """진행상황 업데이트"""
        ...
    
    def on_complete(self, message: str) -> None:
        """완료 알림"""
        ...
    
    def on_error(self, error: str) -> None:
        """에러 알림"""
        ...


class ResourceManager(Protocol):
    """리소스 관리자 프로토콜 (DIP 원칙 적용)"""
    
    async def acquire(self) -> Any:
        """리소스 획득"""
        ...
    
    async def release(self, resource: Any) -> None:
        """리소스 해제"""
        ...
    
    async def cleanup(self) -> None:
        """전체 리소스 정리"""
        ...


class SportCollector(Generic[T], ABC):
    """
    스포츠 데이터 수집기 추상 클래스
    
    모든 스포츠 수집기가 구현해야 할 공통 인터페이스 정의
    Strategy 패턴과 Template Method 패턴 결합
    """
    
    def __init__(self, resource_manager: Optional[ResourceManager] = None):
        self._observers: List[ProgressObserver] = []
        self._resource_manager = resource_manager
    
    # Observer 패턴 구현
    def add_observer(self, observer: ProgressObserver) -> None:
        """관찰자 추가"""
        self._observers.append(observer)
    
    def remove_observer(self, observer: ProgressObserver) -> None:
        """관찰자 제거"""
        if observer in self._observers:
            self._observers.remove(observer)
    
    def _notify_progress(self, current: int, total: int, message: str) -> None:
        """진행상황 알림"""
        for observer in self._observers:
            observer.on_progress(current, total, message)
    
    def _notify_complete(self, message: str) -> None:
        """완료 알림"""
        for observer in self._observers:
            observer.on_complete(message)
    
    def _notify_error(self, error: str) -> None:
        """에러 알림"""
        for observer in self._observers:
            observer.on_error(error)
    
    # Template Method 패턴 구현
    async def collect(self, **kwargs) -> CollectorResult[T]:
        """
        데이터 수집 템플릿 메서드
        공통 수집 흐름을 정의하고 하위 클래스에서 세부 구현
        """
        try:
            await self._pre_collect(**kwargs)
            result = await self._do_collect(**kwargs)
            await self._post_collect(result, **kwargs)
            return result
        except Exception as e:
            self._notify_error(f"수집 중 오류 발생: {str(e)}")
            return CollectorResult(
                status=CollectionStatus.FAILED,
                errors=[str(e)]
            )
        finally:
            await self._cleanup()
    
    # 하위 클래스에서 구현할 추상 메서드들
    @abstractmethod
    async def _do_collect(self, **kwargs) -> CollectorResult[T]:
        """실제 수집 로직 구현 (하위 클래스에서 구현)"""
        pass
    
    @abstractmethod
    async def collect_team_stats(self, teams: List[str], **kwargs) -> CollectorResult[T]:
        """팀 통계 수집"""
        pass
    
    @abstractmethod 
    async def collect_player_stats(self, players: List[str], **kwargs) -> CollectorResult[T]:
        """선수 통계 수집"""
        pass
    
    # 훅 메서드들 (선택적 구현)
    async def _pre_collect(self, **kwargs) -> None:
        """수집 전 처리 (선택적 구현)"""
        pass
    
    async def _post_collect(self, result: CollectorResult[T], **kwargs) -> None:
        """수집 후 처리 (선택적 구현)"""
        pass
    
    async def _cleanup(self) -> None:
        """리소스 정리"""
        if self._resource_manager:
            await self._resource_manager.cleanup()


class BatchCollector(SportCollector[T], ABC):
    """
    배치 처리 기능이 있는 수집기
    대용량 데이터 처리를 위한 최적화된 인터페이스
    """
    
    @abstractmethod
    async def collect_batch(
        self, 
        items: List[str], 
        batch_size: int = 10,
        max_concurrent: int = 3,
        **kwargs
    ) -> CollectorResult[T]:
        """배치 단위로 데이터 수집"""
        pass
    
    @abstractmethod
    async def estimate_collection_time(self, items: List[str]) -> int:
        """수집 예상 시간 계산 (초)"""
        pass 