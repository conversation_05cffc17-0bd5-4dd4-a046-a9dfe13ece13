"""
스포츠 팩토리 및 플러그인 매니저
Factory 패턴과 Plugin 패턴을 결합한 확장 가능한 아키텍처
"""
from abc import ABC, abstractmethod
from typing import Dict, Type, List, Optional, Any, Protocol
from enum import Enum

from core.interfaces.collector import SportCollector
from core.interfaces.parser import SportParser
from core.interfaces.service import SportService
from core.exceptions import ConfigurationException


class SportType(Enum):
    """지원하는 스포츠 타입"""
    BASEBALL = "baseball"
    BASKETBALL = "basketball"
    SOCCER = "soccer"
    VOLLEYBALL = "volleyball"


class SportPlugin(ABC):
    """
    스포츠 플러그인 추상 클래스
    새로운 스포츠를 시스템에 추가하기 위한 플러그인 인터페이스
    """
    
    @property
    @abstractmethod
    def sport_type(self) -> SportType:
        """스포츠 타입 반환"""
        pass
    
    @property
    @abstractmethod
    def display_name(self) -> str:
        """표시용 스포츠 이름"""
        pass
    
    @property
    @abstractmethod
    def supported_leagues(self) -> List[str]:
        """지원하는 리그 목록"""
        pass
    
    @abstractmethod
    def create_collector(self, **kwargs) -> SportCollector:
        """데이터 수집기 생성"""
        pass
    
    @abstractmethod
    def create_parser(self, **kwargs) -> SportParser:
        """데이터 파서 생성"""
        pass
    
    @abstractmethod
    def create_service(self, **kwargs) -> SportService:
        """비즈니스 서비스 생성"""
        pass
    
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """플러그인 설정 검증 (선택적 구현)"""
        return True
    
    def get_default_configuration(self) -> Dict[str, Any]:
        """기본 설정 반환 (선택적 구현)"""
        return {}


class ComponentFactory(Protocol):
    """컴포넌트 팩토리 프로토콜"""
    
    def create(self, sport_type: SportType, **kwargs) -> Any:
        """컴포넌트 생성"""
        ...


class SportFactory:
    """
    스포츠별 객체 생성을 위한 팩토리 클래스
    Factory Method 패턴과 Abstract Factory 패턴 결합
    """
    
    def __init__(self):
        self._plugins: Dict[SportType, SportPlugin] = {}
        self._configurations: Dict[SportType, Dict[str, Any]] = {}
    
    def register_plugin(self, plugin: SportPlugin) -> None:
        """스포츠 플러그인 등록"""
        if not isinstance(plugin, SportPlugin):
            raise TypeError("SportPlugin 인스턴스여야 합니다.")
        
        sport_type = plugin.sport_type
        self._plugins[sport_type] = plugin
        
        # 기본 설정 로드
        default_config = plugin.get_default_configuration()
        self._configurations[sport_type] = default_config
        
        # 설정 검증
        if not plugin.validate_configuration(default_config):
            raise ConfigurationException(
                config_key=f"{sport_type.value}_plugin",
                context={"sport_type": sport_type.value}
            )
    
    def unregister_plugin(self, sport_type: SportType) -> None:
        """스포츠 플러그인 등록 해제"""
        if sport_type in self._plugins:
            del self._plugins[sport_type]
        if sport_type in self._configurations:
            del self._configurations[sport_type]
    
    def get_supported_sports(self) -> List[SportType]:
        """지원하는 스포츠 목록 반환"""
        return list(self._plugins.keys())
    
    def is_sport_supported(self, sport_type: SportType) -> bool:
        """스포츠 지원 여부 확인"""
        return sport_type in self._plugins
    
    def get_sport_info(self, sport_type: SportType) -> Dict[str, Any]:
        """스포츠 정보 반환"""
        if sport_type not in self._plugins:
            raise ConfigurationException(
                config_key=f"unsupported_sport",
                context={"sport_type": sport_type.value}
            )
        
        plugin = self._plugins[sport_type]
        return {
            "sport_type": sport_type,
            "display_name": plugin.display_name,
            "supported_leagues": plugin.supported_leagues,
            "configuration": self._configurations.get(sport_type, {})
        }
    
    def create_collector(self, sport_type: SportType, **kwargs) -> SportCollector:
        """데이터 수집기 생성"""
        plugin = self._get_plugin(sport_type)
        config = self._configurations.get(sport_type, {})
        
        # 설정과 런타임 파라미터 병합
        merged_kwargs = {**config, **kwargs}
        
        return plugin.create_collector(**merged_kwargs)
    
    def create_parser(self, sport_type: SportType, **kwargs) -> SportParser:
        """데이터 파서 생성"""
        plugin = self._get_plugin(sport_type)
        config = self._configurations.get(sport_type, {})
        
        # 설정과 런타임 파라미터 병합
        merged_kwargs = {**config, **kwargs}
        
        return plugin.create_parser(**merged_kwargs)
    
    def create_service(self, sport_type: SportType, **kwargs) -> SportService:
        """비즈니스 서비스 생성"""
        plugin = self._get_plugin(sport_type)
        config = self._configurations.get(sport_type, {})
        
        # 설정과 런타임 파라미터 병합
        merged_kwargs = {**config, **kwargs}
        
        return plugin.create_service(**merged_kwargs)
    
    def create_complete_stack(
        self, 
        sport_type: SportType, 
        **kwargs
    ) -> Dict[str, Any]:
        """전체 스택 생성 (수집기, 파서, 서비스)"""
        return {
            "collector": self.create_collector(sport_type, **kwargs),
            "parser": self.create_parser(sport_type, **kwargs),
            "service": self.create_service(sport_type, **kwargs)
        }
    
    def update_configuration(
        self, 
        sport_type: SportType, 
        config: Dict[str, Any]
    ) -> None:
        """스포츠별 설정 업데이트"""
        plugin = self._get_plugin(sport_type)
        
        # 설정 검증
        if not plugin.validate_configuration(config):
            raise ConfigurationException(
                config_key=f"{sport_type.value}_configuration",
                context={"sport_type": sport_type.value, "config": config}
            )
        
        self._configurations[sport_type] = config
    
    def get_configuration(self, sport_type: SportType) -> Dict[str, Any]:
        """스포츠별 설정 반환"""
        self._get_plugin(sport_type)  # 플러그인 존재 여부 확인
        return self._configurations.get(sport_type, {}).copy()
    
    def _get_plugin(self, sport_type: SportType) -> SportPlugin:
        """플러그인 반환 (내부 헬퍼 메서드)"""
        if sport_type not in self._plugins:
            raise ConfigurationException(
                config_key="unsupported_sport",
                context={
                    "sport_type": sport_type.value,
                    "supported_sports": [s.value for s in self._plugins.keys()]
                }
            )
        
        return self._plugins[sport_type]


class PluginManager:
    """
    플러그인 생명주기 관리
    플러그인의 로드, 초기화, 종료를 담당
    """
    
    def __init__(self, factory: SportFactory):
        self._factory = factory
        self._initialized_plugins: Dict[SportType, bool] = {}
    
    def load_plugin(self, plugin: SportPlugin) -> None:
        """플러그인 로드 및 초기화"""
        try:
            self._factory.register_plugin(plugin)
            self._initialized_plugins[plugin.sport_type] = True
            
        except Exception as e:
            raise ConfigurationException(
                config_key="plugin_load_failed",
                context={
                    "sport_type": plugin.sport_type.value,
                    "plugin_class": plugin.__class__.__name__
                },
                original_exception=e
            )
    
    def unload_plugin(self, sport_type: SportType) -> None:
        """플러그인 언로드"""
        if sport_type in self._initialized_plugins:
            self._factory.unregister_plugin(sport_type)
            del self._initialized_plugins[sport_type]
    
    def reload_plugin(self, plugin: SportPlugin) -> None:
        """플러그인 재로드"""
        sport_type = plugin.sport_type
        if sport_type in self._initialized_plugins:
            self.unload_plugin(sport_type)
        self.load_plugin(plugin)
    
    def get_plugin_status(self) -> Dict[SportType, bool]:
        """플러그인 상태 조회"""
        return self._initialized_plugins.copy()
    
    def shutdown_all(self) -> None:
        """모든 플러그인 종료"""
        for sport_type in list(self._initialized_plugins.keys()):
            self.unload_plugin(sport_type)


# 전역 팩토리 인스턴스 (싱글톤 패턴)
_global_factory: Optional[SportFactory] = None
_global_plugin_manager: Optional[PluginManager] = None


def get_sport_factory() -> SportFactory:
    """전역 스포츠 팩토리 인스턴스 반환"""
    global _global_factory
    if _global_factory is None:
        _global_factory = SportFactory()
    return _global_factory


def get_plugin_manager() -> PluginManager:
    """전역 플러그인 매니저 인스턴스 반환"""
    global _global_plugin_manager, _global_factory
    if _global_plugin_manager is None:
        if _global_factory is None:
            _global_factory = SportFactory()
        _global_plugin_manager = PluginManager(_global_factory)
    return _global_plugin_manager


def reset_factory() -> None:
    """팩토리 인스턴스 리셋 (테스트용)"""
    global _global_factory, _global_plugin_manager
    _global_factory = None
    _global_plugin_manager = None 