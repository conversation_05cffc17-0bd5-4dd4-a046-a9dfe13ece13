"""
경기 시간 기반 스마트 분산 스케줄러
메모리/CPU 최적화를 위한 시간대별 스포츠 처리
"""
from datetime import datetime, time
from typing import List, Dict, Set, Optional
from enum import Enum
from dataclasses import dataclass

from core.sport_config import Sport, SportConfig, get_sport_config


class TimeSlot(Enum):
    """시간대별 분류"""
    AFTERNOON = "afternoon"        # 14:00-17:00 (해외 스포츠)
    EVENING = "evening"           # 17:00-20:00 (한국 스포츠 피크)
    NIGHT = "night"               # 20:00-24:00 (일본/미국 스포츠)
    LATE_NIGHT = "late_night"     # 00:00-02:00 (NPB 야구)


@dataclass
class SportPriority:
    """스포츠별 우선순위 정보"""
    sport: Sport
    leagues: List[str]
    expected_games: int
    memory_usage_mb: int
    processing_time_sec: int


@dataclass
class TimeSlotConfig:
    """시간대별 설정"""
    slot: TimeSlot
    start_time: time
    end_time: time
    max_concurrent_sports: int
    max_memory_mb: int
    priority_sports: List[SportPriority]


class TimeSlotScheduler:
    """경기 시간 기반 스마트 스케줄러"""
    
    def __init__(self):
        self.time_slot_configs = self._initialize_time_slots()
        self.current_slot = None
        self.active_sports = set()
        self.memory_usage = 0
        
    def _initialize_time_slots(self) -> Dict[TimeSlot, TimeSlotConfig]:
        """시간대별 설정 초기화"""
        return {
            TimeSlot.AFTERNOON: TimeSlotConfig(
                slot=TimeSlot.AFTERNOON,
                start_time=time(14, 0),
                end_time=time(17, 0),
                max_concurrent_sports=2,
                max_memory_mb=256,
                priority_sports=[
                    SportPriority(
                        sport=Sport.SOCCER,
                        leagues=["52", "53", "54"],  # EPL, 프리메라, 세리에A
                        expected_games=15,
                        memory_usage_mb=120,
                        processing_time_sec=45
                    ),
                    SportPriority(
                        sport=Sport.BASKETBALL,
                        leagues=["BK002"],  # NBA
                        expected_games=8,
                        memory_usage_mb=80,
                        processing_time_sec=25
                    )
                ]
            ),
            
            TimeSlot.EVENING: TimeSlotConfig(
                slot=TimeSlot.EVENING,
                start_time=time(17, 0),
                end_time=time(20, 0),
                max_concurrent_sports=4,  # 피크 시간 - 모든 스포츠 동시 처리
                max_memory_mb=384,
                priority_sports=[
                    SportPriority(
                        sport=Sport.SOCCER,
                        leagues=["SC001", "SC003", "55", "56"],  # K리그1/2, 분데스, 프랑스
                        expected_games=12,
                        memory_usage_mb=100,
                        processing_time_sec=35
                    ),
                    SportPriority(
                        sport=Sport.BASKETBALL,
                        leagues=["BK001", "BK003"],  # KBL, WKBL
                        expected_games=6,
                        memory_usage_mb=70,
                        processing_time_sec=20
                    ),
                    SportPriority(
                        sport=Sport.VOLLEYBALL,
                        leagues=["VL001", "VL002"],  # KOVO남/여
                        expected_games=4,
                        memory_usage_mb=50,
                        processing_time_sec=15
                    ),
                    SportPriority(
                        sport=Sport.BASEBALL,
                        leagues=["BS001"],  # KBO
                        expected_games=10,
                        memory_usage_mb=120,
                        processing_time_sec=40
                    )
                ]
            ),
            
            TimeSlot.NIGHT: TimeSlotConfig(
                slot=TimeSlot.NIGHT,
                start_time=time(20, 0),
                end_time=time(23, 59),
                max_concurrent_sports=3,
                max_memory_mb=320,
                priority_sports=[
                    SportPriority(
                        sport=Sport.SOCCER,
                        leagues=["58", "54", "57"],  # J리그, 세리에A, 에레디비시
                        expected_games=10,
                        memory_usage_mb=90,
                        processing_time_sec=30
                    ),
                    SportPriority(
                        sport=Sport.BASKETBALL,
                        leagues=["BK001", "BK002"],  # KBL, NBA
                        expected_games=12,
                        memory_usage_mb=110,
                        processing_time_sec=35
                    ),
                    SportPriority(
                        sport=Sport.BASEBALL,
                        leagues=["BS002"],  # MLB
                        expected_games=15,
                        memory_usage_mb=130,
                        processing_time_sec=45
                    )
                ]
            ),
            
            TimeSlot.LATE_NIGHT: TimeSlotConfig(
                slot=TimeSlot.LATE_NIGHT,
                start_time=time(0, 0),
                end_time=time(2, 0),
                max_concurrent_sports=1,
                max_memory_mb=128,
                priority_sports=[
                    SportPriority(
                        sport=Sport.BASEBALL,
                        leagues=["BS004"],  # NPB
                        expected_games=6,
                        memory_usage_mb=80,
                        processing_time_sec=25
                    )
                ]
            )
        }
    
    def get_current_time_slot(self, current_time: Optional[datetime] = None) -> TimeSlot:
        """현재 시간대 결정"""
        if current_time is None:
            current_time = datetime.now()
        
        current_time_only = current_time.time()
        
        for slot, config in self.time_slot_configs.items():
            if self._is_time_in_slot(current_time_only, config.start_time, config.end_time):
                return slot
        
        # 기본값은 EVENING (가장 활성화된 시간대)
        return TimeSlot.EVENING
    
    def _is_time_in_slot(self, current: time, start: time, end: time) -> bool:
        """시간이 슬롯 범위 내에 있는지 확인"""
        if start <= end:
            return start <= current <= end
        else:
            # 자정을 넘나드는 경우 (예: 23:00 - 02:00)
            return current >= start or current <= end
    
    def get_prioritized_sports(self, time_slot: Optional[TimeSlot] = None) -> List[SportPriority]:
        """시간대별 우선순위 스포츠 목록"""
        if time_slot is None:
            time_slot = self.get_current_time_slot()
        
        config = self.time_slot_configs[time_slot]
        
        # 메모리 사용량 기준으로 정렬 (적은 것부터)
        return sorted(config.priority_sports, key=lambda x: x.memory_usage_mb)
    
    def can_process_sport(self, sport: Sport, time_slot: Optional[TimeSlot] = None) -> bool:
        """해당 시간대에 스포츠 처리 가능 여부 확인"""
        if time_slot is None:
            time_slot = self.get_current_time_slot()
        
        config = self.time_slot_configs[time_slot]
        
        # 1. 동시 처리 가능한 스포츠 수 확인
        if len(self.active_sports) >= config.max_concurrent_sports:
            return False
        
        # 2. 해당 스포츠가 이 시간대 우선순위에 있는지 확인
        sport_priorities = [sp.sport for sp in config.priority_sports]
        if sport not in sport_priorities:
            return False
        
        # 3. 메모리 사용량 확인
        sport_priority = next(sp for sp in config.priority_sports if sp.sport == sport)
        if self.memory_usage + sport_priority.memory_usage_mb > config.max_memory_mb:
            return False
        
        return True
    
    def start_sport_processing(self, sport: Sport) -> bool:
        """스포츠 처리 시작"""
        time_slot = self.get_current_time_slot()
        
        if not self.can_process_sport(sport, time_slot):
            return False
        
        config = self.time_slot_configs[time_slot]
        sport_priority = next(sp for sp in config.priority_sports if sp.sport == sport)
        
        self.active_sports.add(sport)
        self.memory_usage += sport_priority.memory_usage_mb
        
        return True
    
    def finish_sport_processing(self, sport: Sport) -> None:
        """스포츠 처리 완료"""
        if sport not in self.active_sports:
            return
        
        time_slot = self.get_current_time_slot()
        config = self.time_slot_configs[time_slot]
        sport_priority = next(sp for sp in config.priority_sports if sp.sport == sport)
        
        self.active_sports.remove(sport)
        self.memory_usage -= sport_priority.memory_usage_mb
    
    def get_resource_status(self) -> Dict[str, any]:
        """현재 리소스 사용 상태"""
        time_slot = self.get_current_time_slot()
        config = self.time_slot_configs[time_slot]
        
        return {
            "current_time_slot": time_slot.value,
            "active_sports": [sport.value for sport in self.active_sports],
            "memory_usage": {
                "current_mb": self.memory_usage,
                "max_mb": config.max_memory_mb,
                "usage_percent": round((self.memory_usage / config.max_memory_mb) * 100, 1)
            },
            "concurrent_sports": {
                "current": len(self.active_sports),
                "max": config.max_concurrent_sports
            }
        }
    
    def get_processing_schedule(self, target_time: Optional[datetime] = None) -> Dict[str, any]:
        """특정 시간의 처리 스케줄 생성"""
        if target_time is None:
            target_time = datetime.now()
        
        time_slot = self.get_current_time_slot(target_time)
        config = self.time_slot_configs[time_slot]
        
        schedule = {
            "time_slot": time_slot.value,
            "time_range": f"{config.start_time.strftime('%H:%M')}-{config.end_time.strftime('%H:%M')}",
            "max_memory_mb": config.max_memory_mb,
            "max_concurrent_sports": config.max_concurrent_sports,
            "sports_schedule": []
        }
        
        for priority in config.priority_sports:
            sport_config = get_sport_config(priority.sport)
            schedule["sports_schedule"].append({
                "sport": priority.sport.value,
                "sport_code": sport_config.sport_code,
                "leagues": priority.leagues,
                "expected_games": priority.expected_games,
                "memory_usage_mb": priority.memory_usage_mb,
                "processing_time_sec": priority.processing_time_sec,
                "league_names": [
                    sport_config.get_league_by_id(league_id).name 
                    for league_id in priority.leagues
                    if sport_config.get_league_by_id(league_id)
                ]
            })
        
        return schedule
    
    def simulate_full_day_schedule(self) -> List[Dict[str, any]]:
        """하루 전체 스케줄 시뮬레이션"""
        schedules = []
        
        # 각 시간대별 스케줄 생성
        test_times = [
            datetime.now().replace(hour=15, minute=0),  # AFTERNOON
            datetime.now().replace(hour=18, minute=30), # EVENING
            datetime.now().replace(hour=22, minute=0),  # NIGHT
            datetime.now().replace(hour=1, minute=0),   # LATE_NIGHT
        ]
        
        for test_time in test_times:
            schedule = self.get_processing_schedule(test_time)
            schedule["sample_time"] = test_time.strftime("%H:%M")
            schedules.append(schedule)
        
        return schedules


# 전역 스케줄러 인스턴스
scheduler = TimeSlotScheduler()


def get_current_processing_sports() -> List[Sport]:
    """현재 시간대에 처리할 스포츠 목록"""
    priorities = scheduler.get_prioritized_sports()
    return [sp.sport for sp in priorities]


def is_peak_time() -> bool:
    """현재가 피크 시간(EVENING)인지 확인"""
    return scheduler.get_current_time_slot() == TimeSlot.EVENING


def get_memory_budget() -> int:
    """현재 시간대 메모리 예산"""
    time_slot = scheduler.get_current_time_slot()
    config = scheduler.time_slot_configs[time_slot]
    return config.max_memory_mb 