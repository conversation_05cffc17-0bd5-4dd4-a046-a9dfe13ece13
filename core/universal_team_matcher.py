"""
멀티스포츠 통합 팀 매칭 시스템
기존 team_info 테이블을 활용한 확장 가능한 팀 매칭
"""
import sqlite3
import re
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass
from difflib import SequenceMatcher
import asyncio
from functools import lru_cache

from core.sport_config import Sport, get_sport_config, get_all_leagues


@dataclass
class TeamMatch:
    """팀 매칭 결과"""
    team_id: str
    team_name: str
    sport: str
    league: str
    league_id: str
    match_score: float        # 매칭 점수 (0.0 ~ 1.0)
    match_type: str          # 'exact', 'fuzzy', 'partial'
    original_name: str       # 원본 팀명


@dataclass
class LeagueMatch:
    """리그 매칭 결과"""
    league_id: str
    league_name: str
    sport: str
    match_score: float
    tab_order: int


class UniversalTeamMatcher:
    """통합 팀 매칭 시스템"""
    
    def __init__(self, db_path: str = "database/stats.db"):
        self.db_path = db_path
        self._team_cache = {}
        self._league_cache = {}
        self._fuzzy_cache = {}
        self.similarity_threshold = 0.7
        
    @lru_cache(maxsize=1000)
    def _clean_team_name(self, name: str) -> str:
        """팀명 정규화"""
        if not name:
            return ""
        
        # 공통 정규화 패턴
        cleaned = name.strip()
        
        # FC, CF, SC 등 축구 접미사 제거
        soccer_suffixes = [
            r'\s+FC$', r'\s+CF$', r'\s+SC$', r'\s+AC$',
            r'^FC\s+', r'^CF\s+', r'^SC\s+', r'^AC\s+'
        ]
        for pattern in soccer_suffixes:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        # 농구 접미사 (Galaxy, Eagles 등은 유지)
        basketball_patterns = [
            r'\s+Basketball$', r'\s+BC$'
        ]
        for pattern in basketball_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        # 배구 접미사
        volleyball_patterns = [
            r'\s+Volleyball$', r'\s+VC$'
        ]
        for pattern in volleyball_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        # 공통 정리
        cleaned = re.sub(r'\s+', ' ', cleaned)  # 중복 공백 제거
        cleaned = cleaned.strip()
        
        return cleaned
    
    def _calculate_similarity(self, name1: str, name2: str) -> float:
        """문자열 유사도 계산"""
        clean1 = self._clean_team_name(name1).lower()
        clean2 = self._clean_team_name(name2).lower()
        
        if clean1 == clean2:
            return 1.0
        
        # SequenceMatcher를 사용한 유사도 계산
        return SequenceMatcher(None, clean1, clean2).ratio()
    
    async def load_team_cache(self, sport: Optional[Sport] = None) -> None:
        """팀 정보 캐시 로드"""
        cache_key = sport.value if sport else "all"
        
        if cache_key in self._team_cache:
            return
        
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            if sport:
                query = """
                SELECT team_id, team_name, team_full_name, sports, league, 
                       sportic_league_id, sportic_team_id, stadium_name
                FROM team_info 
                WHERE sports = ?
                """
                rows = conn.execute(query, (sport.value,)).fetchall()
            else:
                query = """
                SELECT team_id, team_name, team_full_name, sports, league,
                       sportic_league_id, sportic_team_id, stadium_name
                FROM team_info
                """
                rows = conn.execute(query).fetchall()
        
        teams = {}
        for row in rows:
            team_data = {
                'team_id': row['team_id'],
                'team_name': row['team_name'],
                'team_full_name': row['team_full_name'],
                'sports': row['sports'],
                'league': row['league'],
                'sportic_league_id': row['sportic_league_id'],
                'sportic_team_id': row['sportic_team_id'],
                'stadium_name': row['stadium_name']
            }
            teams[row['team_id']] = team_data
        
        self._team_cache[cache_key] = teams
    
    async def load_league_cache(self) -> None:
        """리그 정보 캐시 로드"""
        if self._league_cache:
            return
        
        # sport_config에서 리그 정보 로드
        all_leagues = get_all_leagues()
        self._league_cache = all_leagues
    
    async def find_team_exact_match(self, team_name: str, sport: Sport, 
                                   league_id: Optional[str] = None) -> Optional[TeamMatch]:
        """정확한 팀명 매칭"""
        await self.load_team_cache(sport)
        cache_key = sport.value
        
        if cache_key not in self._team_cache:
            return None
        
        teams = self._team_cache[cache_key]
        
        for team_id, team_data in teams.items():
            # 팀명 정확 매칭
            if (self._clean_team_name(team_data['team_name']) == self._clean_team_name(team_name) or
                self._clean_team_name(team_data['team_full_name']) == self._clean_team_name(team_name)):
                
                # 리그 필터 적용
                if league_id and team_data['sportic_league_id'] != league_id:
                    continue
                
                return TeamMatch(
                    team_id=team_id,
                    team_name=team_data['team_name'],
                    sport=team_data['sports'],
                    league=team_data['league'],
                    league_id=team_data['sportic_league_id'],
                    match_score=1.0,
                    match_type='exact',
                    original_name=team_name
                )
        
        return None
    
    async def find_team_fuzzy_match(self, team_name: str, sport: Sport,
                                   league_id: Optional[str] = None) -> List[TeamMatch]:
        """유사도 기반 팀명 매칭"""
        cache_key = f"{sport.value}_{team_name}_{league_id or 'all'}"
        
        if cache_key in self._fuzzy_cache:
            return self._fuzzy_cache[cache_key]
        
        await self.load_team_cache(sport)
        sport_cache_key = sport.value
        
        if sport_cache_key not in self._team_cache:
            return []
        
        teams = self._team_cache[sport_cache_key]
        matches = []
        
        for team_id, team_data in teams.items():
            # 리그 필터 적용
            if league_id and team_data['sportic_league_id'] != league_id:
                continue
            
            # 팀명 유사도 계산
            score1 = self._calculate_similarity(team_name, team_data['team_name'])
            score2 = self._calculate_similarity(team_name, team_data['team_full_name'])
            max_score = max(score1, score2)
            
            if max_score >= self.similarity_threshold:
                matches.append(TeamMatch(
                    team_id=team_id,
                    team_name=team_data['team_name'],
                    sport=team_data['sports'],
                    league=team_data['league'],
                    league_id=team_data['sportic_league_id'],
                    match_score=max_score,
                    match_type='fuzzy',
                    original_name=team_name
                ))
        
        # 점수 순으로 정렬
        matches.sort(key=lambda x: x.match_score, reverse=True)
        
        # 캐시 저장
        self._fuzzy_cache[cache_key] = matches
        
        return matches
    
    async def match_team(self, team_name: str, sport: Sport,
                        league_id: Optional[str] = None) -> Optional[TeamMatch]:
        """통합 팀 매칭 (정확 매칭 → 유사도 매칭)"""
        
        # 1. 정확 매칭 시도
        exact_match = await self.find_team_exact_match(team_name, sport, league_id)
        if exact_match:
            return exact_match
        
        # 2. 유사도 매칭 시도
        fuzzy_matches = await self.find_team_fuzzy_match(team_name, sport, league_id)
        if fuzzy_matches:
            return fuzzy_matches[0]  # 가장 높은 점수
        
        return None
    
    async def match_teams_batch(self, team_names: List[str], sport: Sport,
                               league_id: Optional[str] = None) -> List[Optional[TeamMatch]]:
        """배치 팀 매칭"""
        tasks = [
            self.match_team(team_name, sport, league_id)
            for team_name in team_names
        ]
        
        return await asyncio.gather(*tasks)
    
    async def get_league_teams(self, sport: Sport, league_id: str) -> List[TeamMatch]:
        """특정 리그의 모든 팀 조회"""
        await self.load_team_cache(sport)
        cache_key = sport.value
        
        if cache_key not in self._team_cache:
            return []
        
        teams = self._team_cache[cache_key]
        league_teams = []
        
        for team_id, team_data in teams.items():
            if team_data['sportic_league_id'] == league_id:
                league_teams.append(TeamMatch(
                    team_id=team_id,
                    team_name=team_data['team_name'],
                    sport=team_data['sports'],
                    league=team_data['league'],
                    league_id=league_id,
                    match_score=1.0,
                    match_type='exact',
                    original_name=team_data['team_name']
                ))
        
        return league_teams
    
    async def add_new_team(self, team_id: str, team_name: str, team_full_name: str,
                          sport: Sport, league_id: str, **kwargs) -> bool:
        """새 팀 정보 추가"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = """
                INSERT OR REPLACE INTO team_info 
                (team_id, team_name, team_full_name, sports, sportic_league_id, 
                 sportic_team_id, stadium_name, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
                """
                
                conn.execute(query, (
                    team_id,
                    team_name,
                    team_full_name,
                    sport.value,
                    league_id,
                    kwargs.get('sportic_team_id', team_id),
                    kwargs.get('stadium_name', '')
                ))
                
                conn.commit()
            
            # 캐시 무효화
            if sport.value in self._team_cache:
                del self._team_cache[sport.value]
            if "all" in self._team_cache:
                del self._team_cache["all"]
            
            return True
            
        except Exception as e:
            print(f"팀 정보 추가 실패: {e}")
            return False
    
    def get_match_statistics(self) -> Dict[str, any]:
        """매칭 통계 정보"""
        total_teams = sum(len(teams) for teams in self._team_cache.values())
        total_cache_hits = len(self._fuzzy_cache)
        
        sport_stats = {}
        for sport_key, teams in self._team_cache.items():
            if sport_key != "all":
                sport_stats[sport_key] = len(teams)
        
        return {
            "total_teams_cached": total_teams,
            "fuzzy_cache_hits": total_cache_hits,
            "sport_breakdown": sport_stats,
            "similarity_threshold": self.similarity_threshold
        }
    
    def clear_cache(self, sport: Optional[Sport] = None) -> None:
        """캐시 정리"""
        if sport:
            cache_key = sport.value
            if cache_key in self._team_cache:
                del self._team_cache[cache_key]
        else:
            self._team_cache.clear()
            self._fuzzy_cache.clear()


# 전역 매처 인스턴스
team_matcher = UniversalTeamMatcher()


async def match_teams_for_sport(team_names: List[str], sport: Sport,
                               league_id: Optional[str] = None) -> List[Optional[TeamMatch]]:
    """스포츠별 팀 매칭 (편의 함수)"""
    return await team_matcher.match_teams_batch(team_names, sport, league_id)


async def get_sport_teams(sport: Sport, league_id: str) -> List[TeamMatch]:
    """스포츠별 리그 팀 목록 조회 (편의 함수)"""
    return await team_matcher.get_league_teams(sport, league_id)


def create_team_info_migration() -> str:
    """team_info 테이블 확장을 위한 마이그레이션 SQL"""
    return """
    -- 기존 team_info 테이블 확장 (기존 데이터 보존)
    
    -- 새 컬럼 추가 (이미 존재하면 무시)
    ALTER TABLE team_info ADD COLUMN league_id VARCHAR(50);
    ALTER TABLE team_info ADD COLUMN city VARCHAR(100);
    ALTER TABLE team_info ADD COLUMN coach VARCHAR(100);
    ALTER TABLE team_info ADD COLUMN homepage VARCHAR(200);
    
    -- 새 인덱스 생성
    CREATE INDEX IF NOT EXISTS idx_team_info_sport_league 
    ON team_info(sports, sportic_league_id);
    
    CREATE INDEX IF NOT EXISTS idx_team_info_team_name 
    ON team_info(team_name);
    
    -- league_info 테이블 생성
    CREATE TABLE IF NOT EXISTS league_info (
        league_id VARCHAR(50) PRIMARY KEY,
        league_name VARCHAR(100) NOT NULL,
        short_name VARCHAR(50),
        sport VARCHAR(20) NOT NULL,
        sport_code VARCHAR(10) NOT NULL,
        country VARCHAR(10),
        season_format VARCHAR(20),
        tab_order INTEGER,
        tab_id VARCHAR(20),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
    
    -- league_info 초기 데이터 삽입
    INSERT OR IGNORE INTO league_info VALUES
    -- 축구 리그
    ('SC001', 'K리그1', 'K1', 'soccer', 'SC', 'KOR', '25', 1, '#ui-id-1', TRUE, datetime('now'), datetime('now')),
    ('SC003', 'K리그2', 'K2', 'soccer', 'SC', 'KOR', '25', 2, '#ui-id-2', TRUE, datetime('now'), datetime('now')),
    ('52', 'EPL', 'EPL', 'soccer', 'SC', 'ENG', '24-25', 3, '#ui-id-3', TRUE, datetime('now'), datetime('now')),
    ('53', '프리메라리가', 'La Liga', 'soccer', 'SC', 'ESP', '24-25', 4, '#ui-id-4', TRUE, datetime('now'), datetime('now')),
    ('54', '세리에A', 'Serie A', 'soccer', 'SC', 'ITA', '24-25', 5, '#ui-id-5', TRUE, datetime('now'), datetime('now')),
    ('55', '분데스리가', 'Bundesliga', 'soccer', 'SC', 'GER', '24-25', 6, '#ui-id-6', TRUE, datetime('now'), datetime('now')),
    ('56', '프랑스리그', 'Ligue 1', 'soccer', 'SC', 'FRA', '24-25', 7, '#ui-id-7', TRUE, datetime('now'), datetime('now')),
    ('57', '에레디비시', 'Eredivisie', 'soccer', 'SC', 'NED', '24-25', 8, '#ui-id-8', TRUE, datetime('now'), datetime('now')),
    ('58', 'J리그', 'J-League', 'soccer', 'SC', 'JPN', '25', 9, '#ui-id-9', TRUE, datetime('now'), datetime('now')),
    
    -- 농구 리그
    ('BK001', 'KBL', 'KBL', 'basketball', 'BK', 'KOR', '24-25', 1, '#ui-id-1', TRUE, datetime('now'), datetime('now')),
    ('BK003', 'WKBL', 'WKBL', 'basketball', 'BK', 'KOR', '24-25', 2, '#ui-id-2', TRUE, datetime('now'), datetime('now')),
    ('BK002', 'NBA', 'NBA', 'basketball', 'BK', 'USA', '24-25', 3, '#ui-id-3', TRUE, datetime('now'), datetime('now')),
    
    -- 배구 리그
    ('VL001', 'KOVO남', 'KOVO남', 'volleyball', 'VL', 'KOR', '24~25', 1, '#ui-id-1', TRUE, datetime('now'), datetime('now')),
    ('VL002', 'KOVO여', 'KOVO여', 'volleyball', 'VL', 'KOR', '24~25', 2, '#ui-id-2', TRUE, datetime('now'), datetime('now'));
    
    -- 기존 야구 데이터 호환성 유지 (league_info에 추가)
    INSERT OR IGNORE INTO league_info VALUES
    ('BS001', 'KBO', 'KBO', 'baseball', 'BS', 'KOR', '25', 1, '#ui-id-1', TRUE, datetime('now'), datetime('now')),
    ('BS002', 'MLB', 'MLB', 'baseball', 'BS', 'USA', '2025', 2, '#ui-id-2', TRUE, datetime('now'), datetime('now')),
    ('BS004', 'NPB', 'NPB', 'baseball', 'BS', 'JPN', '25', 3, '#ui-id-3', TRUE, datetime('now'), datetime('now'));
    """ 