"""
통합 팀 매칭 시스템
"""
import json
import logging
import os
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Set

logger = logging.getLogger(__name__)


@dataclass
class TeamMatch:
    """팀 매칭 결과"""
    team_id: str
    team_name: str
    league_id: str
    confidence: float = 1.0


class UniversalTeamMatcher:
    """통합 팀 매칭 시스템"""
    
    def __init__(self, db_client=None):
        self.mappings: Dict[str, Dict[str, str]] = {}
        self.reverse_mappings: Dict[str, Dict[str, str]] = {}
        self.db_client = db_client
        self._initialize_database_mappings()
        logger.info("UniversalTeamMatcher 초기화 완료")
    
    def _initialize_database_mappings(self):
        try:
            self.mappings = {}
            self.reverse_mappings = {}
        except Exception as e:
            logger.error(f"데이터베이스 매핑 초기화 실패: {e}")
    
    def add_team_mapping(self, sport: str, team_id: str, team_name: str):
        logger.warning("add_team_mapping은 사용하지 않습니다. team_info 테이블을 직접 수정하세요.")
    
    def get_team_name(self, sport: str, team_id: str) -> Optional[str]:
        try:
            if self.db_client:
                return self._get_team_name_from_db(sport, team_id)
            return self.mappings.get(sport, {}).get(team_id)
        except Exception as e:
            logger.error(f"팀명 조회 실패 {sport}-{team_id}: {e}")
            return None
    
    def _get_team_name_from_db(self, sport: str, team_id: str) -> Optional[str]:
        try:
            result = self.db_client.table("team_info").select(
                "team_name"
            ).eq("team_id", team_id).execute()
            
            if result.data:
                return result.data[0].get('team_name')
            return None
        except Exception as e:
            logger.error(f"DB 팀명 조회 실패 {sport}-{team_id}: {e}")
            return None
    
    def get_team_id(self, sport: str, team_name: str) -> Optional[str]:
        """팀명으로 팀 ID 조회"""
        try:
            return self.reverse_mappings.get(sport, {}).get(team_name)
        except Exception as e:
            logger.error(f"팀 ID 조회 실패 {sport}-{team_name}: {e}")
            return None
    
    def get_website_team_id(self, db_team_id: str) -> str:
        """NPB 팀의 DB team_id를 website teamId로 변환 - 데이터베이스 기반으로 변경"""
        # 하드코딩 제거 - team_info 테이블에서 조회하도록 변경
        return db_team_id
    
    def get_all_teams(self, sport: str, league: str = None) -> Dict[str, str]:
        try:
            if self.db_client:
                return self._get_all_teams_from_db(sport, league)
            return self.mappings.get(sport, {}).copy()
        except Exception as e:
            logger.error(f"전체 팀 조회 실패 {sport}: {e}")
            return {}
    
    def _get_all_teams_from_db(self, sport: str, league: str = None) -> Dict[str, str]:
        try:
            if not self.db_client:
                return {}
                
            # Define the columns to select
            columns = (
                "team_id, team_name, eng_name, league_id"
            )
            
            query = self.db_client.table("team_info").select(
                columns
            )
            
            if league:
                query = query.eq("league_id", league.upper())
            
            result = query.execute()
            
            teams = {}
            if result.data:
                for team in result.data:
                    # Extract team information
                    team_info = {
                        'team_id': team.get('team_id'),
                        'team_name': team.get('team_name'),
                        'eng_name': team.get('eng_name'),
                        'league_id': team.get('league_id'),
                    }
                    
                    # Use team_id as the key for consistency
                    team_id = team_info['team_id']
                    team_name = team_info['team_name']
                    
                    # Add to mappings based on sport
                    league_id = team_info['league_id']
                    team_sport = self._get_sport_from_league(league_id)
                    
                    if team_sport == sport:
                        teams[team_id] = team_name
            
            return teams
            
        except Exception as e:
            logger.error(f"DB 전체 팀 조회 실패 {sport}: {e}")
            return {}
    
    def _get_sport_from_league(self, league_id: str) -> str:
        try:
            if not self.db_client:
                return 'unknown'
                
            result = self.db_client.table("league_info").select(
                "sports"
            ).eq("league_id", league_id).execute()
            
            if result.data:
                return result.data[0].get('sports', 'unknown')
            
            return 'unknown'
            
        except Exception as e:
            logger.error(f"리그 스포츠 조회 실패 {league_id}: {e}")
            return 'unknown'
    
    def get_supported_sports(self) -> List[str]:
        """지원하는 스포츠 목록"""
        return list(self.mappings.keys())
    
    def search_teams(self, sport: str, query: str) -> List[Dict[str, str]]:
        """팀 검색"""
        try:
            results = []
            sport_mappings = self.mappings.get(sport, {})
            
            query_lower = query.lower()
            
            for team_id, team_name in sport_mappings.items():
                if (query_lower in team_id.lower() or 
                    query_lower in team_name.lower()):
                    results.append({
                        'team_id': team_id,
                        'team_name': team_name,
                        'sport': sport
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"팀 검색 실패 {sport}-{query}: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """매핑 통계 정보"""
        try:
            stats = {
                'total_sports': len(self.mappings),
                'sports': {},
                'total_teams': 0
            }
            
            for sport, mappings in self.mappings.items():
                team_count = len(mappings)
                stats['sports'][sport] = {
                    'team_count': team_count,
                    'teams': list(mappings.values())
                }
                stats['total_teams'] += team_count
            
            return stats
            
        except Exception as e:
            logger.error(f"통계 생성 실패: {e}")
            return {'error': str(e)}
    
    def validate_mapping(self, sport: str, team_id: str, team_name: str) -> bool:
        """매핑 유효성 검증"""
        try:
            # 기본 검증
            if not sport or not team_id or not team_name:
                return False
            
            # 중복 검증
            existing_name = self.get_team_name(sport, team_id)
            if existing_name and existing_name != team_name:
                logger.warning(f"팀 ID 중복: {sport}-{team_id} ({existing_name} vs {team_name})")
                return False
            
            existing_id = self.get_team_id(sport, team_name)
            if existing_id and existing_id != team_id:
                logger.warning(f"팀명 중복: {sport}-{team_name} ({existing_id} vs {team_id})")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"매핑 검증 실패: {e}")
            return False
    
    async def match_team(self, team_name: str, sport, league_id: str = None) -> Optional[TeamMatch]:
        """팀 매칭"""
        try:
            sport_str = sport.value if hasattr(sport, 'value') else str(sport)
            
            # 팀 ID 검색
            team_id = self.get_team_id(sport_str, team_name)
            if team_id:
                return TeamMatch(
                    team_id=team_id,
                    team_name=team_name,
                    league_id=league_id or '',
                    confidence=1.0
                )
            
            # 부분 매칭 시도
            for tid, tname in self.mappings.get(sport_str, {}).items():
                if team_name in tname or tname in team_name:
                    return TeamMatch(
                        team_id=tid,
                        team_name=tname,
                        league_id=league_id or '',
                        confidence=0.8
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"팀 매칭 실패 {team_name}: {e}")
            return None


# 전역 인스턴스
team_matcher = UniversalTeamMatcher()
