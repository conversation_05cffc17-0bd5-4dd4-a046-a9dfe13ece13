"""
Common validation utilities for baseball statistics and data integrity.

Provides centralized validation functions to reduce code duplication
and ensure consistent data quality checks across all processors.
"""
from typing import Dict, Any, Optional, List, Union
import re
from utils.logger import Logger

logger = Logger(__name__)


class DataValidator:
    """
    Centralized data validation utility for baseball statistics.
    
    Provides consistent validation methods for team stats, pitcher data,
    and general data integrity checks.
    """
    
    @staticmethod
    def is_valid_team_stats(team_stats: Optional[Dict[str, Any]]) -> bool:
        """
        Validate team statistics data for completeness.
        
        Args:
            team_stats: Team statistics dictionary
            
        Returns:
            bool: True if valid and complete, False otherwise
        """
        if not team_stats or not isinstance(team_stats, dict):
            return False
            
        if 'error' in team_stats:
            return False
        
        # Check for required fields
        required_fields = ['season_summary', 'recent_games_summary']
        for field in required_fields:
            field_data = team_stats.get(field)
            if not field_data or not isinstance(field_data, dict) or len(field_data) == 0:
                return False
                
        return True
    
    @staticmethod
    def is_valid_pitcher_data(pitcher_data: Optional[Dict[str, Any]]) -> bool:
        """
        Validate pitcher data for completeness.
        
        Args:
            pitcher_data: Pitcher data dictionary
            
        Returns:
            bool: True if valid and complete, False otherwise
        """
        if not pitcher_data or not isinstance(pitcher_data, dict):
            return False
        
        # Check for required fields
        profile = pitcher_data.get('pitcher_profile', {})
        stats = pitcher_data.get('pitcher_stats', {})
        
        # At least profile or stats should have some data
        return bool(profile or stats)
    
    @staticmethod
    def is_valid_pitcher_name(pitcher_name: Optional[str]) -> bool:
        """
        Validate pitcher name format and content.
        
        Args:
            pitcher_name: Pitcher name string
            
        Returns:
            bool: True if valid pitcher name, False otherwise
        """
        if not pitcher_name or not isinstance(pitcher_name, str):
            return False
            
        name = pitcher_name.strip()
        if not name or len(name) < 2:
            return False
            
        # Check for invalid placeholder values
        invalid_names = {'none', 'null', '없음', '-', 'n/a', 'unknown'}
        return name.lower() not in invalid_names
    
    @staticmethod
    def is_valid_team_id(team_id: Optional[str]) -> bool:
        """
        Validate team ID format.
        
        Args:
            team_id: Team identifier string
            
        Returns:
            bool: True if valid team ID, False otherwise
        """
        if not team_id or not isinstance(team_id, str):
            return False
            
        # Team IDs should be alphanumeric and not empty
        return bool(team_id.strip() and re.match(r'^[A-Za-z0-9_-]+$', team_id.strip()))
    
    @staticmethod
    def clean_numeric_value(value: Union[str, int, float, Any]) -> str:
        """
        Clean and standardize numeric values for consistent storage.
        
        Args:
            value: Numeric value in any format
            
        Returns:
            str: Clean string representation of the numeric value
        """
        try:
            if isinstance(value, str):
                value = value.strip()
                if not value or value.lower() in ['none', 'null', '없음', '-', 'n/a']:
                    return "0"
            
            # Convert to float first, then to clean string
            float_val = float(value)
            if float_val.is_integer():
                return str(int(float_val))
            else:
                # Remove trailing zeros after decimal point
                return f"{float_val:.2f}".rstrip('0').rstrip('.')
                
        except (ValueError, TypeError, AttributeError):
            return "0"
    
    @staticmethod
    def clean_text_value(value: Any) -> str:
        """
        Clean and standardize text values.
        
        Args:
            value: Text value in any format
            
        Returns:
            str: Clean string value or empty string
        """
        if value is None:
            return ""
            
        text = str(value).strip()
        if text.lower() in ['none', 'null', '없음', '-', 'n/a']:
            return ""
            
        return text
    
    @staticmethod
    def validate_required_fields(
        data: Dict[str, Any], 
        required_fields: List[str],
        context: str = "data"
    ) -> bool:
        """
        Validate that required fields are present and valid.
        
        Args:
            data: Data dictionary to validate
            required_fields: List of required field names
            context: Context description for logging
            
        Returns:
            bool: True if all required fields are valid, False otherwise
        """
        if not isinstance(data, dict):
            logger.debug(f"Invalid {context}: not a dictionary")
            return False
        
        missing_fields = []
        for field in required_fields:
            if field not in data or not data[field]:
                missing_fields.append(field)
        
        if missing_fields:
            logger.debug(f"Missing required fields in {context}: {missing_fields}")
            return False
            
        return True
    
    @staticmethod
    def sanitize_dict(
        data: Dict[str, Any], 
        keep_numeric: bool = True,
        keep_empty: bool = False
    ) -> Dict[str, Any]:
        """
        Sanitize dictionary by removing invalid entries.
        
        Args:
            data: Dictionary to sanitize
            keep_numeric: Whether to keep numeric values
            keep_empty: Whether to keep empty values
            
        Returns:
            Dict with sanitized data
        """
        if not isinstance(data, dict):
            return {}
        
        sanitized = {}
        for key, value in data.items():
            # Skip None values
            if value is None and not keep_empty:
                continue
            
            # Handle nested dictionaries
            if isinstance(value, dict):
                nested_clean = DataValidator.sanitize_dict(value, keep_numeric, keep_empty)
                if nested_clean or keep_empty:
                    sanitized[key] = nested_clean
            
            # Handle strings
            elif isinstance(value, str):
                clean_value = DataValidator.clean_text_value(value)
                if clean_value or keep_empty:
                    sanitized[key] = clean_value
            
            # Handle numeric values
            elif isinstance(value, (int, float)) and keep_numeric:
                sanitized[key] = DataValidator.clean_numeric_value(value)
            
            # Keep other types as-is if they're not empty
            elif value or keep_empty:
                sanitized[key] = value
        
        return sanitized


class SecurityValidator:
    """
    Security-focused validation utilities for production stability.
    
    Provides validation methods to prevent security issues and
    ensure data integrity in production environments.
    """
    
    @staticmethod
    def validate_url(url: Optional[str]) -> bool:
        """
        Validate URL format and security.
        
        Args:
            url: URL string to validate
            
        Returns:
            bool: True if URL is valid and safe, False otherwise
        """
        if not url or not isinstance(url, str):
            return False
        
        url = url.strip()
        
        # Check for valid URL patterns
        valid_patterns = [
            r'^https://www\.betman\.co\.kr/',
            r'^https://betman\.co\.kr/',
        ]
        
        return any(re.match(pattern, url) for pattern in valid_patterns)
    
    @staticmethod
    def sanitize_log_data(data: Any, max_length: int = 1000) -> str:
        """
        Sanitize data for safe logging (prevent log injection).
        
        Args:
            data: Data to sanitize for logging
            max_length: Maximum length for log entry
            
        Returns:
            str: Safe string for logging
        """
        if data is None:
            return "None"
        
        # Convert to string and limit length
        text = str(data)[:max_length]
        
        # Remove potential log injection characters
        text = re.sub(r'[\r\n\t]', ' ', text)
        
        # Remove ANSI escape sequences
        text = re.sub(r'\x1b\[[0-9;]*m', '', text)
        
        return text
    
    @staticmethod
    def validate_team_name(team_name: Optional[str]) -> bool:
        """
        Validate team name for security and format.
        
        Args:
            team_name: Team name string
            
        Returns:
            bool: True if valid team name, False otherwise
        """
        if not team_name or not isinstance(team_name, str):
            return False
        
        name = team_name.strip()
        
        # Check length constraints
        if len(name) < 2 or len(name) > 50:
            return False
        
        # Check for valid characters (Korean, English, numbers, common punctuation)
        if not re.match(r'^[가-힣a-zA-Z0-9\s\-_.()]+$', name):
            return False
        
        return True