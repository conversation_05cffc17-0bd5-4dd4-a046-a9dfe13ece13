"""
브라우저 초기화 공통 유틸리티
"""
import logging
from typing import Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Playwright

logger = logging.getLogger(__name__)


class BrowserManager:
    """브라우저 초기화 및 관리 공통 클래스"""
    
    def __init__(self):
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
    
    async def ensure_browser_ready(self) -> bool:
        """브라우저 초기화 상태 확인 및 초기화"""
        if not self.browser:
            try:
                await self.initialize_browser()
                return self.browser is not None
            except Exception as e:
                logger.error(f"브라우저 초기화 실패: {e}")
                return False
        return True
    
    async def initialize_browser(self):
        """브라우저 초기화"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(headless=True)
            logger.debug("✅ 브라우저 초기화 완료")
        except Exception as e:
            logger.error(f"❌ 브라우저 초기화 오류: {e}")
            await self.cleanup()
            raise
    
    async def cleanup(self):
        """브라우저 리소스 정리"""
        errors = []
        
        try:
            if self.browser:
                try:
                    await self.browser.close()
                except Exception as e:
                    errors.append(f"브라우저 종료 실패: {e}")
                finally:
                    self.browser = None
            
            if self.playwright:
                try:
                    await self.playwright.stop()
                except Exception as e:
                    errors.append(f"Playwright 종료 실패: {e}")
                finally:
                    self.playwright = None
            
            if errors:
                logger.warning(f"브라우저 정리 중 일부 오류 발생: {errors}")
                
        except Exception as e:
            logger.error(f"브라우저 정리 중 예상치 못한 오류: {e}")
            # 강제 정리
            self.browser = None
            self.playwright = None


async def ensure_browser_ready(crawler_instance) -> bool:
    """
    크롤러 인스턴스의 브라우저 초기화 상태 확인
    
    Args:
        crawler_instance: 브라우저 속성을 가진 크롤러 인스턴스
        
    Returns:
        bool: 브라우저 준비 상태
    """
    if not hasattr(crawler_instance, 'browser') or not crawler_instance.browser:
        try:
            if hasattr(crawler_instance, 'initialize_browser'):
                await crawler_instance.initialize_browser()
                return crawler_instance.browser is not None
            else:
                logger.error("크롤러에 initialize_browser 메서드가 없습니다")
                return False
        except Exception as e:
            logger.error(f"브라우저 초기화 실패: {e}")
            return False
    return True