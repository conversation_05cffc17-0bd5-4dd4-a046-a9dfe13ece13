"""
Memory monitoring utilities for production stability and debugging.

Provides centralized memory tracking and logging capabilities
to help identify memory leaks and performance bottlenecks.
"""
import psutil
from typing import Dict, Any, Optional
from utils.logger import Logger

logger = Logger(__name__)


class MemoryMonitor:
    """
    Centralized memory monitoring and logging utility.
    
    Provides consistent memory usage tracking across all services
    with formatted logging and threshold monitoring.
    """
    
    def __init__(self, threshold_mb: float = 500.0) -> None:
        """
        Initialize memory monitor with warning threshold.
        
        Args:
            threshold_mb: Memory usage threshold in MB for warnings
        """
        self.threshold_mb = threshold_mb
        self.process = self._get_process()
        
    def _get_process(self) -> Optional[psutil.Process]:
        """Get current process handle safely."""
        try:
            return psutil.Process()
        except Exception as e:
            logger.error(f"Failed to get process handle: {e}")
            return None
    
    def get_memory_usage(self) -> float:
        """
        Get current memory usage in MB.
        
        Returns:
            float: Memory usage in MB, or 0.0 if unavailable
        """
        try:
            if self.process:
                return self.process.memory_info().rss / 1024 / 1024
        except Exception as e:
            logger.debug(f"Memory usage check failed: {e}")
        return 0.0
    
    def get_memory_info(self) -> Dict[str, Any]:
        """
        Get comprehensive memory information.
        
        Returns:
            Dictionary with memory statistics
        """
        try:
            if not self.process:
                return {"error": "Process handle unavailable"}
                
            memory_info = self.process.memory_info()
            return {
                "rss_mb": memory_info.rss / 1024 / 1024,
                "vms_mb": memory_info.vms / 1024 / 1024,
                "percent": self.process.memory_percent(),
                "available_mb": psutil.virtual_memory().available / 1024 / 1024,
                "system_percent": psutil.virtual_memory().percent
            }
        except Exception as e:
            return {"error": str(e)}
    
    def log_memory_usage(
        self, 
        operation: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> float:
        """
        Log current memory usage with context.
        
        Args:
            operation: Description of current operation
            context: Additional context information
            
        Returns:
            float: Current memory usage in MB
        """
        memory_mb = self.get_memory_usage()
        context_str = f" | {context}" if context else ""
        
        if memory_mb > self.threshold_mb:
            logger.warning(f"🔥 HIGH MEMORY [{operation}]: {memory_mb:.1f}MB{context_str}")
        else:
            logger.debug(f"📊 MEMORY [{operation}]: {memory_mb:.1f}MB{context_str}")
            
        return memory_mb
    
    def log_operation_start(
        self, 
        operation_name: str, 
        item_name: str, 
        index: int, 
        total: int
    ) -> float:
        """
        Log operation start with memory tracking.
        
        Args:
            operation_name: Name of the operation
            item_name: Name of item being processed
            index: Current item index (1-based)
            total: Total number of items
            
        Returns:
            float: Memory usage at operation start
        """
        memory_mb = self.get_memory_usage()
        logger.info(
            f"🔄 START [{operation_name}] {item_name} "
            f"({index}/{total}) | {memory_mb:.1f}MB"
        )
        return memory_mb
    
    def log_operation_end(
        self, 
        operation_name: str, 
        item_name: str, 
        index: int, 
        total: int,
        success: bool = True,
        additional_info: Optional[str] = None
    ) -> float:
        """
        Log operation end with memory tracking.
        
        Args:
            operation_name: Name of the operation
            item_name: Name of item processed
            index: Current item index (1-based) 
            total: Total number of items
            success: Whether operation succeeded
            additional_info: Extra information to log
            
        Returns:
            float: Memory usage at operation end
        """
        memory_mb = self.get_memory_usage()
        status = "✅ SUCCESS" if success else "❌ FAILED"
        info_str = f" | {additional_info}" if additional_info else ""
        
        logger.info(
            f"{status} [{operation_name}] {item_name} "
            f"({index}/{total}) | {memory_mb:.1f}MB{info_str}"
        )
        return memory_mb
    
    def log_detailed_error(
        self,
        error_type: str,
        context_name: str,
        error: Exception,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Log detailed error information with memory context.
        
        Args:
            error_type: Type of error (e.g., "TEAM_COLLECTION", "PITCHER_DATA")
            context_name: Name of item/context where error occurred
            error: Exception that occurred
            additional_context: Extra context information
        """
        import traceback
        
        error_info = {
            'error_type': error_type,
            'context': context_name,
            'error_message': str(error),
            'error_class': type(error).__name__,
            'memory_mb': self.get_memory_usage(),
        }
        
        if additional_context:
            error_info.update(additional_context)
        
        logger.error(
            f"💥 ERROR_DETAIL [{error_type}] {context_name}: {error_info}"
        )
        logger.debug(f"Traceback for {error_type}: {traceback.format_exc()}")


# Global memory monitor instance
_global_monitor: Optional[MemoryMonitor] = None


def get_memory_monitor() -> MemoryMonitor:
    """
    Get global memory monitor instance (singleton).
    
    Returns:
        MemoryMonitor: Global memory monitor instance
    """
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = MemoryMonitor()
    return _global_monitor


def log_memory_usage(operation: str, context: Optional[Dict[str, Any]] = None) -> float:
    """
    Convenience function to log memory usage.
    
    Args:
        operation: Description of current operation
        context: Additional context information
        
    Returns:
        float: Current memory usage in MB
    """
    return get_memory_monitor().log_memory_usage(operation, context)