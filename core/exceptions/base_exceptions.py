"""
기본 예외 클래스 정의
시스템 전반에 사용되는 기본 예외 타입들
"""
from typing import Optional, Dict, Any


class SystemException(Exception):
    """시스템 전체에 사용되는 기본 예외 클래스"""
    
    def __init__(
        self, 
        message: str = "시스템 오류가 발생했습니다.",
        error_code: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        self.message = message
        self.error_code = error_code
        self.context = context or {}
        self.original_exception = original_exception
        super().__init__(self.message)
    
    def __str__(self) -> str:
        base_msg = self.message
        if self.error_code:
            base_msg = f"[{self.error_code}] {base_msg}"
        
        if self.context:
            context_str = ", ".join(f"{k}={v}" for k, v in self.context.items())
            base_msg = f"{base_msg} (Context: {context_str})"
        
        return base_msg
    
    def to_dict(self) -> Dict[str, Any]:
        """예외 정보를 딕셔너리로 변환"""
        return {
            "type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "context": self.context,
            "original_exception": str(self.original_exception) if self.original_exception else None
        }


class ValidationException(SystemException):
    """데이터 검증 관련 예외"""
    
    def __init__(
        self, 
        field_name: str, 
        invalid_value: Any = None,
        expected_format: Optional[str] = None,
        **kwargs
    ):
        self.field_name = field_name
        self.invalid_value = invalid_value
        self.expected_format = expected_format
        
        message = f"데이터 검증 실패: {field_name}"
        if expected_format:
            message += f" (예상 형식: {expected_format})"
        
        kwargs.setdefault("error_code", "VALIDATION_ERROR")
        kwargs.setdefault("context", {}).update({
            "field": field_name,
            "value": invalid_value,
            "expected_format": expected_format
        })
        
        super().__init__(message, **kwargs)


class ConfigurationException(SystemException):
    """설정 관련 예외"""
    
    def __init__(
        self, 
        config_key: str, 
        config_file: Optional[str] = None,
        **kwargs
    ):
        self.config_key = config_key
        self.config_file = config_file
        
        message = f"설정 오류: {config_key}"
        if config_file:
            message += f" (파일: {config_file})"
        
        kwargs.setdefault("error_code", "CONFIG_ERROR")
        kwargs.setdefault("context", {}).update({
            "config_key": config_key,
            "config_file": config_file
        })
        
        super().__init__(message, **kwargs) 