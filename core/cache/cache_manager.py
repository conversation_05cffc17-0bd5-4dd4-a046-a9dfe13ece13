"""
Cache Management System – 캐시 전용 클래스 (SRP)
"""
# flake8: noqa
import asyncio
import logging
import time
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Set

logger = logging.getLogger(__name__)


class CacheProvider(ABC):
    """캐시 제공자 인터페이스 (DIP 원칙 적용)"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """캐시에서 값 조회"""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """캐시에 값 저장"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """캐시에서 값 삭제"""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """전체 캐시 삭제"""
        pass


class InMemoryCacheProvider(CacheProvider):
    """인메모리 캐시 제공자"""
    
    def __init__(self):
        self._cache: Dict[str, Any] = {}
        self._ttl_cache: Dict[str, float] = {}
    
    async def get(self, key: str) -> Optional[Any]:
        """캐시에서 값 조회"""
        if key in self._ttl_cache:
            if time.time() > self._ttl_cache[key]:
                await self.delete(key)
                return None
        
        return self._cache.get(key)
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """캐시에 값 저장"""
        self._cache[key] = value
        if ttl:
            self._ttl_cache[key] = time.time() + ttl
        return True
    
    async def delete(self, key: str) -> bool:
        """캐시에서 값 삭제"""
        self._cache.pop(key, None)
        self._ttl_cache.pop(key, None)
        return True
    
    async def clear(self) -> bool:
        """전체 캐시 삭제"""
        self._cache.clear()
        self._ttl_cache.clear()
        return True


class DuplicateCheckCache:
    """중복 체크 전용 캐시 (SRP 적용)"""
    
    def __init__(self, cache_provider: CacheProvider):
        self._cache_provider = cache_provider
        self._baseball_cache: Set[str] = set()
        self._soccer_cache: Set[str] = set()
        self._initialized = False
    
    async def initialize(self) -> None:
        """캐시 초기화"""
        if self._initialized:
            logger.info("🚀 캐시 이미 초기화됨")
            return
        
        try:
            logger.info("🚀 중복 체크 캐시 초기화 시작...")
            start_time = time.time()
            
            # 데이터베이스에서 최근 데이터 로드
            await self._load_recent_data()
            
            elapsed = time.time() - start_time
            self._initialized = True
            
            logger.info(f"✅ 캐시 초기화 완료 ({elapsed:.2f}초)")
            
        except Exception as e:
            logger.error(f"❌ 캐시 초기화 실패: {e}")
            self._initialized = True
    
    async def _load_recent_data(self) -> None:
        """최근 데이터 로드"""
        try:
            from database.database import connect_supabase
            client = connect_supabase()
            if not client:
                logger.warning("DB 연결 실패 - 캐시 없이 진행")
                return
            
            # 병렬로 야구/축구 데이터 로드
            baseball_count, soccer_count = await asyncio.gather(
                self._load_baseball_data(client),
                self._load_soccer_data(client),
                return_exceptions=True
            )
            
            # 예외 처리
            if isinstance(baseball_count, Exception):
                logger.warning(f"야구 캐시 로드 실패: {baseball_count}")
                baseball_count = 0
            if isinstance(soccer_count, Exception):
                logger.warning(f"축구 캐시 로드 실패: {soccer_count}")
                soccer_count = 0
            
            logger.info(f"캐시 로드 완료: 야구 {baseball_count}개, 축구 {soccer_count}개")
            
        except Exception as e:
            logger.error(f"데이터 로드 실패: {e}")
    
    async def _load_baseball_data(self, client) -> int:
        """야구 데이터 로드"""
        try:
            # 캐시 기간 1일(기존 3일)
            one_day_ago = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            
            response = client.table('baseball_stats').select(
                'match_id,team_id,match_date'
            ).gte('match_date', one_day_ago).limit(1000).execute()
            
            count = 0
            for row in response.data or []:
                if all(row.get(k) for k in ['match_id', 'team_id', 'match_date']):
                    # 기본 키
                    key_base = (
                        f"{row['match_id']}_{row['match_date']}"
                        f"_{row['team_id']}"
                    )
                    self._baseball_cache.add(key_base)

                    # 날짜 형식 문제는 무시
                    count += 1
            
            return count
            
        except Exception as e:
            logger.warning(f"야구 캐시 로드 실패: {e}")
            return 0
    
    async def _load_soccer_data(self, client) -> int:
        """축구 데이터 로드"""
        try:
            # 캐시 기간 1일(기존 3일)
            one_day_ago = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            
            response = client.table('soccer_stats').select(
                'match_id,team_id,match_date'
            ).gte('match_date', one_day_ago).limit(1000).execute()
            
            count = 0
            for row in response.data or []:
                if all(row.get(k) for k in ['match_id', 'team_id', 'match_date']):
                    key_base = (
                        f"{row['match_id']}_{row['match_date']}"
                        f"_{row['team_id']}"
                    )
                    self._soccer_cache.add(key_base)

                    # 날짜 형식 문제는 무시
                    count += 1
            
            return count
            
        except Exception as e:
            logger.warning(f"축구 캐시 로드 실패: {e}")
            return 0
    
    def is_duplicate_baseball(self, match_id: str, team_id: str, match_date: str) -> bool:
        """야구 중복 체크"""
        key_base = f"{match_id}_{match_date}_{team_id}"
        if key_base in self._baseball_cache:
            return True

        # ±1일 키도 확인(KST/UTC 차이 보정)
        try:
            base_dt = datetime.strptime(match_date, '%Y-%m-%d')
            for delta in (-1, 1):
                alt_date = (base_dt + timedelta(days=delta)).strftime('%Y-%m-%d')
                key_alt = f"{match_id}_{alt_date}_{team_id}"
                if key_alt in self._baseball_cache:
                    return True
        except ValueError:
            pass
        return False
    
    def is_duplicate_soccer(self, match_id: str, team_id: str, match_date: str) -> bool:
        """축구 중복 체크"""
        key_base = f"{match_id}_{match_date}_{team_id}"
        if key_base in self._soccer_cache:
            return True

        try:
            base_dt = datetime.strptime(match_date, '%Y-%m-%d')
            for delta in (-1, 1):
                alt_date = (base_dt + timedelta(days=delta)).strftime('%Y-%m-%d')
                key_alt = f"{match_id}_{alt_date}_{team_id}"
                if key_alt in self._soccer_cache:
                    return True
        except ValueError:
            pass
        return False
    
    def add_baseball(self, match_id: str, team_id: str, match_date: str) -> None:
        """야구 새 데이터 캐시 추가"""
        key_base = f"{match_id}_{match_date}_{team_id}"
        self._baseball_cache.add(key_base)
    
    def add_soccer(self, match_id: str, team_id: str, match_date: str) -> None:
        """축구 새 데이터 캐시 추가"""
        key_base = f"{match_id}_{match_date}_{team_id}"
        self._soccer_cache.add(key_base)


class CacheManager:
    """캐시 관리자 - 모든 캐시 관련 작업을 담당"""
    
    def __init__(self, cache_provider: Optional[CacheProvider] = None):
        self._cache_provider = cache_provider or InMemoryCacheProvider()
        self._duplicate_cache = DuplicateCheckCache(self._cache_provider)
        self._initialized = False
    
    async def initialize(self) -> None:
        """캐시 시스템 초기화"""
        if not self._initialized:
            await self._duplicate_cache.initialize()
            self._initialized = True
    
    def get_duplicate_cache(self) -> DuplicateCheckCache:
        """중복 체크 캐시 반환"""
        return self._duplicate_cache
    
    async def get(self, key: str) -> Optional[Any]:
        """일반 캐시에서 값 조회"""
        return await self._cache_provider.get(key)
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """일반 캐시에 값 저장"""
        return await self._cache_provider.set(key, value, ttl)
    
    async def delete(self, key: str) -> bool:
        """일반 캐시에서 값 삭제"""
        return await self._cache_provider.delete(key)
    
    async def clear_all(self) -> bool:
        """모든 캐시 삭제"""
        return await self._cache_provider.clear()


# 전역 캐시 관리자 인스턴스
_global_cache_manager: Optional[CacheManager] = None


def get_global_cache_manager() -> CacheManager:
    """전역 캐시 관리자 인스턴스 반환"""
    global _global_cache_manager
    if _global_cache_manager is None:
        _global_cache_manager = CacheManager()
    return _global_cache_manager