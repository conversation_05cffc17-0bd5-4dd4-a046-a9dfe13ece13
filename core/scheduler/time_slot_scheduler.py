"""
시간대별 스케줄러
"""
import logging
from datetime import datetime, time
from typing import List, Dict, Optional, Any

from core.config.sport_config import SportConfig, get_active_sports

logger = logging.getLogger(__name__)


class TimeSlotScheduler:
    """시간대별 스케줄러"""
    
    def __init__(self):
        """초기화"""
        self.last_check_time: Optional[datetime] = None
        self.active_sports_cache: Dict[str, List[str]] = {}
        
        logger.info("TimeSlotScheduler 초기화 완료")
    
    def should_run_now(self, sport_config: SportConfig, current_time: datetime) -> bool:
        """
        현재 시간에 해당 스포츠가 실행되어야 하는지 확인
        
        Args:
            sport_config: 스포츠 설정
            current_time: 현재 시간
            
        Returns:
            bool: 실행 여부
        """
        try:
            current_time_only = current_time.time()
            return sport_config.is_active_time(current_time_only)
            
        except Exception as e:
            logger.error(f"스케줄 확인 실패 {sport_config.sport_name}: {e}")
            return False
    
    def get_active_sports_now(self, current_time: Optional[datetime] = None) -> List[str]:
        """
        현재 시간에 활성화된 스포츠 목록 조회
        
        Args:
            current_time: 확인할 시간 (None이면 현재 시간)
            
        Returns:
            List[str]: 활성화된 스포츠 목록
        """
        if current_time is None:
            current_time = datetime.now()
        
        try:
            current_time_only = current_time.time()
            active_sports = get_active_sports(current_time_only)
            
            logger.debug(f"현재 시간 {current_time_only} 활성 스포츠: {active_sports}")
            return active_sports
            
        except Exception as e:
            logger.error(f"활성 스포츠 조회 실패: {e}")
            return []
    
    def get_next_schedule(self, sport_name: str, current_time: Optional[datetime] = None) -> Optional[datetime]:
        """
        다음 실행 예정 시간 조회
        
        Args:
            sport_name: 스포츠 이름
            current_time: 기준 시간 (None이면 현재 시간)
            
        Returns:
            Optional[datetime]: 다음 실행 시간
        """
        if current_time is None:
            current_time = datetime.now()
        
        try:
            from core.config.sport_config import get_sport_config
            
            config = get_sport_config(sport_name)
            if not config:
                return None
            
            current_time_only = current_time.time()
            current_date = current_time.date()
            
            # 오늘의 남은 시간대 확인
            for slot in config.get_time_slots():
                if slot.start_time > current_time_only:
                    return datetime.combine(current_date, slot.start_time)
            
            # 오늘 남은 시간대가 없으면 내일 첫 번째 시간대
            if config.get_time_slots():
                from datetime import timedelta
                next_date = current_date + timedelta(days=1)
                first_slot = config.get_time_slots()[0]
                return datetime.combine(next_date, first_slot.start_time)
            
            return None
            
        except Exception as e:
            logger.error(f"다음 스케줄 조회 실패 {sport_name}: {e}")
            return None
    
    def get_schedule_summary(self, current_time: Optional[datetime] = None) -> Dict[str, Any]:
        """
        전체 스케줄 요약 정보
        
        Args:
            current_time: 기준 시간 (None이면 현재 시간)
            
        Returns:
            Dict[str, Any]: 스케줄 요약
        """
        if current_time is None:
            current_time = datetime.now()
        
        try:
            from core.config.sport_config import get_all_sport_configs
            
            summary = {
                'current_time': current_time.isoformat(),
                'active_sports': self.get_active_sports_now(current_time),
                'all_sports': {},
                'next_activities': []
            }
            
            all_configs = get_all_sport_configs()
            
            for sport_name, config in all_configs.items():
                is_active = self.should_run_now(config, current_time)
                next_schedule = self.get_next_schedule(sport_name, current_time)
                
                summary['all_sports'][sport_name] = {
                    'is_active': is_active,
                    'next_schedule': next_schedule.isoformat() if next_schedule else None,
                    'time_slots': [
                        {'start': slot.start, 'end': slot.end} 
                        for slot in config.time_slots
                    ]
                }
                
                if next_schedule:
                    summary['next_activities'].append({
                        'sport': sport_name,
                        'time': next_schedule.isoformat(),
                        'timestamp': next_schedule.timestamp()
                    })
            
            # 다음 활동들을 시간순으로 정렬
            summary['next_activities'].sort(key=lambda x: x['timestamp'])
            
            return summary
            
        except Exception as e:
            logger.error(f"스케줄 요약 생성 실패: {e}")
            return {
                'current_time': current_time.isoformat(),
                'active_sports': [],
                'all_sports': {},
                'next_activities': [],
                'error': str(e)
            }
    
    def is_business_hours(self, current_time: Optional[datetime] = None) -> bool:
        """
        업무 시간인지 확인 (전체 시스템 활성화 시간)
        
        Args:
            current_time: 확인할 시간 (None이면 현재 시간)
            
        Returns:
            bool: 업무 시간 여부
        """
        if current_time is None:
            current_time = datetime.now()
        
        try:
            # 전체 시스템 활성화 시간: 02:00 ~ 23:59
            current_time_only = current_time.time()
            start_time = time(2, 0)  # 02:00
            end_time = time(23, 59)  # 23:59
            
            return start_time <= current_time_only <= end_time
            
        except Exception as e:
            logger.error(f"업무 시간 확인 실패: {e}")
            return False
    
    def get_optimal_sports_order(self, current_time: Optional[datetime] = None) -> List[str]:
        """
        현재 시간 기준 최적 스포츠 실행 순서
        
        Args:
            current_time: 기준 시간 (None이면 현재 시간)
            
        Returns:
            List[str]: 실행 순서대로 정렬된 스포츠 목록
        """
        if current_time is None:
            current_time = datetime.now()
        
        try:
            active_sports = self.get_active_sports_now(current_time)
            
            # 우선순위: 야구 > 축구 > 농구 > 배구
            priority_order = ['baseball', 'soccer', 'basketball', 'volleyball']
            
            ordered_sports = []
            for sport in priority_order:
                if sport in active_sports:
                    ordered_sports.append(sport)
            
            # 우선순위에 없는 스포츠들 추가
            for sport in active_sports:
                if sport not in ordered_sports:
                    ordered_sports.append(sport)
            
            return ordered_sports
            
        except Exception as e:
            logger.error(f"최적 순서 계산 실패: {e}")
            return []
