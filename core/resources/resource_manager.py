"""
Resource Management System - 리소스 관리 전용 클래스
SRP: 브라우저 풀과 메모리 관리를 담당하는 단일 책임 클래스
"""
import asyncio
import gc
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, Optional, Any
from contextlib import asynccontextmanager

import psutil

logger = logging.getLogger(__name__)


class ResourceProvider(ABC):
    """리소스 제공자 인터페이스 (DIP 원칙 적용)"""
    
    @abstractmethod
    async def initialize(self) -> None:
        """리소스 초기화"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """리소스 정리"""
        pass
    
    @abstractmethod
    async def get_status(self) -> Dict[str, Any]:
        """리소스 상태 조회"""
        pass


class MemoryMonitor:
    """메모리 모니터링 클래스 (SRP 적용)"""
    
    def __init__(self, config: Dict[str, Any]):
        self.memory_thresholds = config.get('memory_thresholds', {
            'gc_threshold': 75,
            'emergency_threshold': 85
        })
        self.check_interval = config.get('memory_check_interval', 15)
        self.max_memory_usage = config.get('max_memory_usage', 1024**3)
        self.last_memory_check = 0
    
    async def check_memory_usage(self) -> Dict[str, Any]:
        """메모리 사용량 체크"""
        current_time = time.time()
        
        if current_time - self.last_memory_check < self.check_interval:
            return {"status": "skipped", "reason": "too_soon"}
        
        memory = psutil.virtual_memory()
        
        gc_threshold = self.memory_thresholds.get('gc_threshold', 75)
        emergency_threshold = self.memory_thresholds.get('emergency_threshold', 85)
        
        result = {
            "memory_percent": memory.percent,
            "memory_used_mb": memory.used // 1024 // 1024,
            "memory_limit_mb": self.max_memory_usage // 1024 // 1024,
            "actions_taken": []
        }
        
        if memory.percent > gc_threshold:
            logger.warning(f"⚠️ 높은 메모리 사용량: {memory.percent:.1f}%")
            gc.collect()
            result["actions_taken"].append("garbage_collection")
        
        if memory.percent > emergency_threshold:
            logger.warning("🧹 메모리 최적화: 응급 정리 필요")
            result["actions_taken"].append("emergency_cleanup_needed")
        
        self.last_memory_check = current_time
        return result
    
    def get_memory_status(self) -> Dict[str, Any]:
        """현재 메모리 상태 반환"""
        memory = psutil.virtual_memory()
        return {
            "memory_percent": memory.percent,
            "memory_used_mb": memory.used // 1024 // 1024,
            "memory_available_mb": memory.available // 1024 // 1024,
            "memory_limit_mb": self.max_memory_usage // 1024 // 1024,
            "thresholds": self.memory_thresholds
        }


class BrowserResourceManager(ResourceProvider):
    """브라우저 리소스 관리자"""
    
    def __init__(self, browser_pool, memory_monitor: MemoryMonitor):
        self.browser_pool = browser_pool
        self.memory_monitor = memory_monitor
        self._initialized = False
    
    async def initialize(self) -> None:
        """브라우저 리소스 초기화"""
        if self._initialized:
            return
        
        logger.info("🚀 브라우저 리소스 관리자 초기화")
        await self.browser_pool.initialize()
        self._initialized = True
    
    async def cleanup(self) -> None:
        """브라우저 리소스 정리"""
        if not self._initialized:
            return
        
        logger.info("🧹 브라우저 리소스 정리")
        try:
            await asyncio.wait_for(self.browser_pool.close(), timeout=15.0)
        except asyncio.TimeoutError:
            logger.warning("브라우저 풀 종료 시간 초과")
        except Exception as e:
            logger.error(f"브라우저 풀 종료 오류: {e}")
        finally:
            self._initialized = False
    
    @asynccontextmanager
    async def get_browser_context(self):
        """브라우저 컨텍스트 관리자"""
        async with self.browser_pool.get_browser() as browser:
            yield browser
    
    async def get_status(self) -> Dict[str, Any]:
        """브라우저 리소스 상태 조회"""
        browser_status = self.browser_pool.get_status()
        memory_status = self.memory_monitor.get_memory_status()
        
        return {
            "browser_pool": browser_status,
            "memory": memory_status,
            "initialized": self._initialized
        }
    
    async def perform_memory_check(self) -> Dict[str, Any]:
        """메모리 체크 수행"""
        memory_result = await self.memory_monitor.check_memory_usage()
        
        if "emergency_cleanup_needed" in memory_result.get("actions_taken", []):
            await self._emergency_cleanup()
        
        return memory_result
    
    async def _emergency_cleanup(self) -> None:
        """응급 메모리 정리"""
        try:
            logger.info("🧹 응급 메모리 정리 시작")
            
            # 브라우저 풀의 응급 정리 수행
            if hasattr(self.browser_pool, '_emergency_cleanup'):
                await self.browser_pool._emergency_cleanup()
            
            # 가비지 컬렉션
            gc.collect()
            
            logger.info("✅ 응급 메모리 정리 완료")
            
        except Exception as e:
            logger.error(f"❌ 응급 메모리 정리 실패: {e}")


class ResourceManager:
    """전체 리소스 관리자 - 모든 리소스를 통합 관리"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.memory_monitor = MemoryMonitor(config)
        self.browser_resource_manager: Optional[BrowserResourceManager] = None
        self._initialized = False
    
    async def initialize(self, browser_pool) -> None:
        """리소스 관리자 초기화"""
        if self._initialized:
            return
        
        logger.info("🚀 리소스 관리자 초기화")
        
        # 브라우저 리소스 관리자 생성
        self.browser_resource_manager = BrowserResourceManager(
            browser_pool, self.memory_monitor
        )
        
        # 초기화
        await self.browser_resource_manager.initialize()
        
        self._initialized = True
        logger.info("✅ 리소스 관리자 초기화 완료")
    
    async def cleanup(self) -> None:
        """모든 리소스 정리"""
        if not self._initialized:
            return
        
        logger.info("🧹 리소스 관리자 정리")
        
        if self.browser_resource_manager:
            try:
                await asyncio.wait_for(self.browser_resource_manager.cleanup(), timeout=20.0)
            except asyncio.TimeoutError:
                logger.warning("브라우저 리소스 관리자 정리 시간 초과")
            except Exception as e:
                logger.error(f"브라우저 리소스 관리자 정리 오류: {e}")
        
        self._initialized = False
        logger.info("✅ 리소스 관리자 정리 완료")
    
    @asynccontextmanager
    async def get_browser_context(self):
        """브라우저 컨텍스트 제공"""
        if not self.browser_resource_manager:
            raise RuntimeError("리소스 관리자가 초기화되지 않았습니다")
        
        async with self.browser_resource_manager.get_browser_context() as browser:
            yield browser
    
    async def perform_health_check(self) -> Dict[str, Any]:
        """전체 리소스 상태 체크"""
        if not self._initialized:
            return {"status": "not_initialized"}
        
        try:
            # 메모리 체크
            memory_result = await self.browser_resource_manager.perform_memory_check()
            
            # 브라우저 상태 체크
            browser_status = await self.browser_resource_manager.get_status()
            
            return {
                "status": "healthy",
                "memory_check": memory_result,
                "browser_status": browser_status,
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"리소스 상태 체크 실패: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
    
    async def get_resource_summary(self) -> Dict[str, Any]:
        """리소스 요약 정보"""
        if not self._initialized or not self.browser_resource_manager:
            return {"status": "not_initialized"}
        
        status = await self.browser_resource_manager.get_status()
        
        return {
            "initialized": self._initialized,
            "browser_pool_active": status["browser_pool"]["total_browsers"],
            "memory_usage_percent": status["memory"]["memory_percent"],
            "memory_used_mb": status["memory"]["memory_used_mb"],
            "config": {
                "max_browsers": self.config.get("max_browsers", 1),
                "memory_limit_mb": self.config.get("max_memory_usage", 1024**3) // 1024 // 1024
            }
        }