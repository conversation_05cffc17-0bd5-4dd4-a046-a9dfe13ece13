"""
공통 유틸리티 모듈: 다양한 유틸리티 함수 제공
"""
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from utils.logger import Logger

# 로거 초기화
logger = Logger(__name__)


def extract_league_id(href: str) -> str:
    """URL에서 리그 ID 추출"""
    match = re.search(r'leagueId=([^&]+)', href)
    return match.group(1) if match else ""


def extract_team_id(href: str) -> str:
    """URL에서 팀 ID 추출"""
    match = re.search(r'teamId=([^&]+)', href)
    return match.group(1) if match else ""


def extract_iso_date(date_text: str) -> str:
    """한글 날짜 텍스트에서 ISO 형식 날짜 추출 (YYYY-MM-DD)"""
    try:
        # '2023년 04월 12일 (수)' 형식을 '2023-04-12'로 변환
        pattern = r'(\d{4})년\s*(\d{1,2})월\s*(\d{1,2})일'
        match = re.search(pattern, date_text)
        if match:
            year, month, day = match.groups()
            return f"{year}-{int(month):02d}-{int(day):02d}"
        return ""
    except Exception:
        return ""


def safe_float(value: Any, default: float = 0.0) -> float:
    """안전하게 부동소수점으로 변환, 실패 시 기본값 반환"""
    if not value:
        return default
        
    try:
        if isinstance(value, str):
            # 쉼표 제거 및 숫자만 추출
            clean_value = re.sub(r'[^\d.+-]', '', value.replace(',', ''))
            if clean_value:
                return float(clean_value)
        elif isinstance(value, (int, float)):
            return float(value)
    except (ValueError, TypeError):
        pass
        
    return default


def to_json_safe(data: Union[Dict, List, Any]) -> Union[Dict, List, Any]:
    """데이터를 JSON 직렬화 가능한 형식으로 변환"""
    if isinstance(data, dict):
        return {k: to_json_safe(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [to_json_safe(item) for item in data]
    elif isinstance(data, (int, float, str, bool, type(None))):
        return data
    elif pd.isna(data):
        return None
    else:
        # 기타 객체는 문자열로 변환
        return str(data)


def format_date(date_str: Optional[str] = None,
                format_str: str = '%Y-%m-%d') -> str:
    """날짜 문자열 포맷팅, None이면 현재 날짜 사용"""
    if date_str:
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            return date_obj.strftime(format_str)
        except ValueError:
            pass
    
    # 날짜가 없거나 유효하지 않으면 현재 날짜 사용
    return datetime.now().strftime(format_str)


def create_file_name(prefix: str, date_str: Optional[str] = None,
                     suffix: str = 'json') -> str:
    """날짜를 포함한 파일명 생성"""
    date_part = format_date(date_str, '%Y%m%d')
    return f"{prefix}_{date_part}.{suffix}"


def format_pitcher_name(name: str) -> str:
    """투수 이름 형식 정리 (공백 제거, 일관된 형식으로 변환)"""
    if not name:
        return ""
        
    # 공백 제거 및 이름 정리
    cleaned = re.sub(r'\s+', ' ', name).strip()
    
    return cleaned 