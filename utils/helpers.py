"""
HTML 파싱 및 데이터 변환 헬퍼 함수
SOLID 원칙 적용을 통해 각 기능을 전용 파서 모듈로 분리했습니다.
"""

# Import from new SOLID-compliant parser modules
from utils.parsers.game_result_parser import map_result, map_pitcher_stats
from utils.parsers.text_processor import (
    remove_duplication, get_td_text, 
    log_duplicate_check_stats, get_duplicate_check_summary
)
from utils.parsers.date_parser import (
    convert_date_str, parse_date_from_string, 
    convert_korean_date_to_ymd, extract_date_from_korean
)
from utils.parsers.innings_parser import (
    normalize_innings, normalize_innings_in_dict, parse_innings_to_float
)