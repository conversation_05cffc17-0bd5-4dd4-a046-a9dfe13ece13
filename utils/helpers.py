"""
HTML 파싱 및 데이터 변환 헬퍼 함수
"""
import logging
import os
import re
from datetime import datetime
from typing import Any, Dict, List, Optional

from utils.logger import Logger

# 로거 초기화
logger = logging.getLogger(__name__)


def map_result(result_text: str) -> str:
    """
    경기 결과 텍스트를 표준 코드로 변환
    
    Args:
        result_text: 원본 경기 결과 텍스트
        
    Returns:
        str: 변환된 결과 코드 (W, L, D)
    """
    # 한 글자 단위로 변환 (승->W, 패->L, 무->D)
    result_mapping = {
        "승": "W",
        "패": "L",
        "무": "D",
    }
    
    # 이미 W, L, D 형식이면 그대로 반환
    if result_text in ["W", "L", "D"]:
        return result_text
        
    # "1승 0무 2패" 같은 형식 처리
    if "승" in result_text or "패" in result_text or "무" in result_text:
        for k, v in result_mapping.items():
            result_text = result_text.replace(k, v)
    
    return result_text


def map_pitcher_stats(stats_text: str) -> str:
    """
    투수 기록 텍스트에서 특수 문자 제거 및 승패 표기 변환
    
    Args:
        stats_text: 원본 투수 기록 텍스트
        
    Returns:
        str: 정리된 투수 기록 텍스트
    """
    # 특수 문자 제거
    cleaned_text = stats_text.replace("●", "").strip()
    
    # 팀명 제거 (콜론 뒤의 내용만 유지)
    if ":" in cleaned_text:
        cleaned_text = cleaned_text.split(":", 1)[1].strip()
    
    # 승패 표기 변환 (승->W, 패->L, 세->S, 홀->H)
    return (cleaned_text
            .replace("(승)", "(W)")
            .replace("(패)", "(L)")
            .replace("(세)", "(S)")
            .replace("(홀)", "(H)"))


def remove_duplication(text: str) -> str:
    """
    중복된 내용 제거
    
    Args:
        text: 원본 텍스트
        
    Returns:
        str: 중복이 제거된 텍스트
    """
    # 콜론 뒤의 내용만 추출
    if ":" in text:
        text = text.split(":", 1)[1].strip()
    
    # 동일한 텍스트가 반복되는 경우 (예: "오티즈오티즈" -> "오티즈")
    text_len = len(text)
    if text_len % 2 == 0:
        half_len = text_len // 2
        first_half = text[:half_len]
        second_half = text[half_len:]
        if first_half == second_half:
            return first_half
    
    return text


def get_td_text(tds: List, index: int) -> str:
    """
    HTML td 요소에서 텍스트 추출
    
    Args:
        tds: td 요소 목록
        index: 가져올 요소의 인덱스
        
    Returns:
        str: 추출된 텍스트
    """
    if index < len(tds):
        return tds[index].get_text(strip=True)
    return ""


def convert_date_str(date_str, year=None):
    """ '05.25(일)' -> '2025-05-25' 형식으로 변환 """
    if year is None:
        year = datetime.now().year
    match = re.match(r'(\d{2})\.(\d{2})', date_str)
    if not match:
        raise ValueError(f"날짜 형식이 올바르지 않습니다: {date_str}")
    month, day = match.groups()
    return f"{year}-{month}-{day}"


def parse_date_from_string(date_string: str) -> str:
    """날짜 문자열을 파싱하여 YYYY-MM-DD 형태로 반환"""
    if not date_string:
        return ""
    
    # 일반적인 형식들을 처리
    patterns = [
        r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
        r'(\d{4})\.(\d{1,2})\.(\d{1,2})',  # YYYY.MM.DD
        r'(\d{4})/(\d{1,2})/(\d{1,2})',  # YYYY/MM/DD
        r'(\d{1,2})-(\d{1,2})-(\d{4})',  # MM-DD-YYYY
        r'(\d{1,2})\.(\d{1,2})\.(\d{4})',  # MM.DD.YYYY
        r'(\d{1,2})/(\d{1,2})/(\d{4})',  # MM/DD/YYYY
    ]
    
    for pattern in patterns:
        match = re.search(pattern, date_string)
        if match:
            groups = match.groups()
            if len(groups[0]) == 4:  # YYYY 형식
                year, month, day = groups
            else:  # MM/DD/YYYY 형식
                month, day, year = groups
            
            # 월과 일을 두 자리로 맞춤
            month = month.zfill(2)
            day = day.zfill(2)
            
            return f"{year}-{month}-{day}"
    
    # 파싱 실패 시 원본 반환
    return date_string


def convert_korean_date_to_ymd(date_str: str) -> str:
    """한국어 날짜 문자열을 YYYY-MM-DD 형식으로 변환"""
    if not date_str:
        return ""
    
    # "2024년 3월 15일" 형태를 파싱
    match = re.search(r'(\d{4})년\s*(\d{1,2})월\s*(\d{1,2})일', date_str)
    if match:
        year, month, day = match.groups()
        month = month.zfill(2)
        day = day.zfill(2)
        return f"{year}-{month}-{day}"
    
    # 파싱 실패 시 원본 반환
    return date_str


def extract_date_from_korean(text: str) -> str:
    """한국어 텍스트에서 날짜 추출 후 YYYY-MM-DD 형식으로 변환"""
    if not text:
        return ""
    
    # 다양한 한국어 날짜 패턴들
    patterns = [
        r'(\d{4})년\s*(\d{1,2})월\s*(\d{1,2})일',
        r'(\d{2,4})\.(\d{1,2})\.(\d{1,2})',
        r'(\d{2,4})-(\d{1,2})-(\d{1,2})',
        r'(\d{2,4})/(\d{1,2})/(\d{1,2})',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            year, month, day = match.groups()
            
            # 2자리 년도를 4자리로 변환 (20xx 가정)
            if len(year) == 2:
                year = f"20{year}"
            
            month = month.zfill(2)
            day = day.zfill(2)
            
            return f"{year}-{month}-{day}"
    
    return ""


def normalize_innings(innings_value: str) -> str:
    """
    이닝 값을 정규화합니다.
    "25 2" -> "25 2/3" 형태로 변환
    기존 파서에서 사용하던 로직을 재활용
    
    Args:
        innings_value: 원본 이닝 값 ("25 2", "7.1", "6", etc.)
        
    Returns:
        str: 정규화된 이닝 값 ("25 2/3", "7 1/3", "6", etc.)
    """
    if not innings_value or not isinstance(innings_value, str):
        return str(innings_value) if innings_value else ""
    
    innings_value = innings_value.strip()
    if not innings_value:
        return ""
    
    try:
        # "25 2" 형태 처리 (공백으로 분리된 경우)
        if ' ' in innings_value and '/' not in innings_value:
            parts = innings_value.split(' ')
            if len(parts) == 2 and parts[1].isdigit():
                whole_part = parts[0]
                remainder = int(parts[1])
                if remainder == 1:
                    return f"{whole_part} 1/3"
                elif remainder == 2:
                    return f"{whole_part} 2/3"
                # remainder가 3 이상이면 추가 이닝으로 처리
                elif remainder >= 3:
                    additional_innings = remainder // 3
                    remaining = remainder % 3
                    new_whole = int(whole_part) + additional_innings
                    if remaining == 0:
                        return str(new_whole)
                    elif remaining == 1:
                        return f"{new_whole} 1/3"
                    elif remaining == 2:
                        return f"{new_whole} 2/3"
        
        # "7.1", "7.2" 형태 처리 (소수점 형태)
        elif '.' in innings_value:
            whole, fraction = innings_value.split('.')
            if fraction == '0':
                return whole
            elif fraction == '1':
                return f"{whole} 1/3"
            elif fraction == '2':
                return f"{whole} 2/3"
            else:
                # 다른 소수점은 그대로 반환
                return innings_value
        
        # 이미 분수 형태이거나 정수인 경우 그대로 반환
        else:
            return innings_value
            
    except (ValueError, IndexError) as e:
        logger.debug(
            f"이닝 정규화 실패 - 원본값: {innings_value}, 오류: {e}"
        )
        return innings_value


def normalize_innings_in_dict(data_dict: Dict[str, Any], 
                             innings_fields: List[str] = None) -> Dict[str, Any]:
    """
    딕셔너리 내의 이닝 필드들을 정규화합니다.
    
    Args:
        data_dict: 정규화할 데이터 딕셔너리
        innings_fields: 이닝 필드명 리스트 (기본값: ['innings'])
        
    Returns:
        Dict[str, Any]: 이닝이 정규화된 딕셔너리
    """
    if not isinstance(data_dict, dict):
        return data_dict
    
    if innings_fields is None:
        innings_fields = ['innings']
    
    normalized_dict = data_dict.copy()
    
    for field in innings_fields:
        if field in normalized_dict:
            normalized_dict[field] = normalize_innings(normalized_dict[field])
    
    return normalized_dict


def parse_innings_to_float(innings_str: str) -> float:
    """
    이닝 문자열을 float로 변환합니다.
    "25 2/3" -> 25.67, "7 1/3" -> 7.33 등
    
    Args:
        innings_str: 이닝 문자열
        
    Returns:
        float: 이닝의 float 값
    """
    if not innings_str or not isinstance(innings_str, str):
        return 0.0
    
    innings_str = innings_str.strip()
    if not innings_str:
        return 0.0
    
    try:
        # "25 2/3" 형태 처리
        if ' ' in innings_str and '/' in innings_str:
            parts = innings_str.split(' ')
            whole = float(parts[0])
            fraction_part = parts[1]
            if '/' in fraction_part:
                numerator, denominator = fraction_part.split('/')
                fraction = float(numerator) / float(denominator)
                return whole + fraction
        
        # "7 1/3" 형태 (공백이 있지만 분수 형태)
        elif ' ' in innings_str:
            parts = innings_str.split(' ')
            whole = float(parts[0])
            if '/' in parts[1]:
                numerator, denominator = parts[1].split('/')
                fraction = float(numerator) / float(denominator)
                return whole + fraction
            else:
                # "25 2" 형태는 "25 2/3"로 해석
                remainder = int(parts[1])
                fraction = remainder / 3.0
                return whole + fraction
        
        # 일반 숫자 형태
        else:
            return float(innings_str)
            
    except (ValueError, ZeroDivisionError) as e:
        logger.debug(
            f"이닝 float 변환 실패 - 원본값: {innings_str}, 오류: {e}"
        )
        return 0.0


# expand_team_name 함수는 DB 기반 팀 매핑 시스템(TeamMappingService)으로 완전히 대체되어 제거됨 