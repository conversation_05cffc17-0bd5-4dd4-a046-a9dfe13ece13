import glob
import logging
import os
from datetime import datetime, timedelta
from typing import Callable, List, Optional, Protocol

# ===============================
# INTERFACES (ISP - Interface Segregation Principle)
# ===============================
# I add this line for github action test !! 

class LogFilter(Protocol):
    """로그 필터링 전략을 위한 프로토콜"""
    def should_log(self, record: logging.LogRecord) -> bool:
        """로그 메시지를 출력할지 결정"""
        ...


class LogHandler(Protocol):
    """로그 핸들러 전략을 위한 프로토콜"""
    def handle(self, level: str, message: str) -> None:
        """로그 메시지를 처리"""
        ...


class LogCleaner(Protocol):
    """로그 파일 정리 전략을 위한 프로토콜"""
    def cleanup(self) -> None:
        """오래된 로그 파일 정리"""
        ...


class CallbackNotifier(Protocol):
    """콜백 알림 전략을 위한 프로토콜"""
    def add_callback(self, callback: Callable[[str], None]) -> None:
        """콜백 추가"""
        ...
        
    def notify(self, level: str, message: str) -> None:
        """콜백들에게 로그 이벤트 알림"""
        ...


# ===============================
# CONCRETE IMPLEMENTATIONS (SRP - Single Responsibility Principle)
# ===============================

class DuplicateLogFilter(logging.Filter):
    """같은 메시지가 짧은 시간 내에 중복 출력되는 것을 방지하는 필터"""
    
    def __init__(self, interval: int = 3):
        super().__init__()
        self.interval = interval
        self.last_log = {}
        self.duplicate_count = {}

    def filter(self, record):
        current_time = datetime.now()
        msg_key = record.getMessage()
        
        last_log_time = self.last_log.get(msg_key)
        
        if last_log_time is None:
            self.last_log[msg_key] = current_time
            self.duplicate_count[msg_key] = 0
            return True
            
        time_diff = (current_time - last_log_time).total_seconds()
        
        if time_diff < self.interval:
            self.duplicate_count[msg_key] += 1
            return False
            
        if self.duplicate_count[msg_key] > 0:
            self.duplicate_count[msg_key] = 0
            
        self.last_log[msg_key] = current_time
        return True


class StandardLogCleaner:
    """표준 로그 파일 정리 구현체"""
    
    def __init__(self, log_directory: str, retention_days: int):
        self.log_directory = log_directory
        self.retention_days = retention_days

    def cleanup(self) -> None:
        """보관 기간 초과 로그 정리"""
        try:
            cutoff_date = datetime.now() - timedelta(
                days=self.retention_days
            )

            for log_path in glob.glob(
                os.path.join(self.log_directory, "*.log")
            ):
                filename = os.path.basename(log_path)
                date_str = filename.split('.')[0]

                try:
                    file_date = datetime.strptime(date_str, '%Y%m%d')
                    if file_date < cutoff_date:
                        os.remove(log_path)
                except ValueError:
                    continue

        except Exception as e:
            logging.getLogger(__name__).error(f"로그 정리 실패: {str(e)}")


class FileLogHandler:
    """파일 로그 핸들러"""
    
    def __init__(self, log_path: str, level: int = logging.ERROR):
        self.log_path = log_path
        self.level = level
        self._handler = None

    def get_handler(self) -> logging.FileHandler:
        """파일 핸들러 생성 및 반환"""
        if self._handler is None:
            os.makedirs(os.path.dirname(self.log_path), exist_ok=True)
            self._handler = logging.FileHandler(
                self.log_path, encoding='utf-8'
            )
            self._handler.setLevel(self.level)
            
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            self._handler.setFormatter(formatter)
            
        return self._handler


class ConsoleLogHandler:
    """콘솔 로그 핸들러"""
    
    def __init__(self, level: int = logging.INFO):
        self.level = level
        self._handler = None

    def get_handler(self) -> logging.StreamHandler:
        """콘솔 핸들러 생성 및 반환"""
        if self._handler is None:
            self._handler = logging.StreamHandler()
            self._handler.setLevel(self.level)
            
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            self._handler.setFormatter(formatter)
            
        return self._handler


class StandardCallbackNotifier:
    """표준 콜백 알림 구현체"""
    
    def __init__(self):
        self.callbacks: List[Callable[[str], None]] = []

    def add_callback(self, callback: Callable[[str], None]) -> None:
        """콜백 추가"""
        if not callable(callback):
            raise TypeError("호출 가능한 함수만 등록 가능")
        self.callbacks.append(callback)

    def notify(self, level: str, message: str) -> None:
        """콜백들에게 알림"""
        formatted = f"[{datetime.now().isoformat()}] {level}: {message}"
        for callback in self.callbacks:
            try:
                callback(formatted)
            except Exception as e:
                logging.getLogger(__name__).error(f"콜백 실행 오류: {str(e)}")


# ===============================
# MAIN LOGGER CLASS (DIP - Dependency Inversion Principle)
# ===============================

class Logger:
    """
    [통합 로그 관리 시스템]

    ▣ 시스템 목적:
      - 애플리케이션 이벤트의 체계적 기록 및 분석 지원
      - 다중 출력 채널(콘솔/파일) 관리
      - 실시간 로그 모니터링을 위한 콜백 시스템 제공
      - 중복 로그 메시지 제거 기능(단기간 동일 메시지 중복 방지)

    ▣ 핵심 기능 매트릭스:
      ┌───────────────┬─────────────────────────────────────────┐
      │ 기능          │ 상세                                    │
      ├───────────────┼─────────────────────────────────────────┤
      │ 로그 수준     │ DEBUG, INFO, WARNING, ERROR             │
      │ 출력 대상      │ 콘솔 + 파일(ERROR 이상)                │
      │ 실시간 스트림 │ 콜백 기반 이벤트 전파                    │
      │ 중복 제거     │ 단기간 동일 메시지 필터링                │
      └───────────────┴─────────────────────────────────────────┘

    ▣ 아키텍처 연계:
      +-------------+     +------------+     +---------------+
      │ Application │ ◄──►│   Logger   │ ◄──►│ External      │
      +-------------+     +─────┬──────+     +---------------+
                                │
                                ▼
                          +------------+
                          │ Log Files  │
                          +------------+
    """

    # 클래스 상수
    LOG_RETENTION_DAYS = 1
    # 프로젝트 루트의 logs 폴더 사용
    LOG_DIR = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
        "logs"
    )
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    # 중복 로그 방지를 위한 로거 관리
    _LOGGERS = {}

    def __init__(
        self, 
        name: str,
        level: int = logging.INFO,
        log_cleaner: Optional[LogCleaner] = None,
        callback_notifier: Optional[CallbackNotifier] = None,
        log_dir: Optional[str] = None
    ):
        """
        Logger 생성자

        Args:
            name: 로거명 (일반적으로 __name__ 사용)
            level: 로그 레벨 (기본값: INFO)
            log_cleaner: 로그 정리 전략 (기본값: StandardLogCleaner)
            callback_notifier: 콜백 알림 전략 (기본값: StandardCallbackNotifier)
            log_dir: 로그 디렉토리 (기본값: Stats/logs)
        """
        self.name = name
        self.log_directory = log_dir or Logger.LOG_DIR
        
        # 종속성 주입 (DIP)
        self.log_cleaner = log_cleaner or StandardLogCleaner(
            self.log_directory, Logger.LOG_RETENTION_DAYS
        )
        self.callback_notifier = (
            callback_notifier or StandardCallbackNotifier()
        )

        # 내부 로거 설정
        self._logger = logging.getLogger(name)
        self._logger.setLevel(level)  # 생성자에서 받은 로그 레벨 설정
        
        # 기존 핸들러 모두 제거 후 새로 설정
        for handler in self._logger.handlers[:]:
            self._logger.removeHandler(handler)
        
        # 부모 로거 전파 방지로 중복 로깅 차단
        self._logger.propagate = False
        
        self._setup_handlers()

    def _setup_handlers(self):
        """핸들러 설정 (콘솔 + 파일)"""
        
        # Lambda 환경 체크
        if os.environ.get('AWS_LAMBDA_FUNCTION_NAME'):
            self._setup_lambda_handlers()
            return

        # 콘솔 핸들러 설정 - 외부에서 설정한 로그 레벨 사용
        # 기본 레벨은 logging.INFO
        current_level = self._logger.getEffectiveLevel()
        console_handler = ConsoleLogHandler(current_level).get_handler()
        self._logger.addHandler(console_handler)

        # 파일 핸들러 설정 (ERROR 이상)
        file_handler = FileLogHandler(
            self._current_log_path(), logging.ERROR
        ).get_handler()
        self._logger.addHandler(file_handler)

        # 기존 로그 정리
        self.log_cleaner.cleanup()

    def _current_log_path(self) -> str:
        """현재 날짜 기준 로그 파일 경로"""
        date_str = datetime.now().strftime('%Y%m%d')
        return os.path.join(self.log_directory, f"{date_str}.log")

    def _setup_lambda_handlers(self):
        """Lambda 환경용 핸들러 설정 (콘솔만)"""
        console_handler = ConsoleLogHandler(logging.INFO).get_handler()
        self._logger.addHandler(console_handler)

    def info(self, message: str):
        """정보 레벨 로그 출력"""
        self._logger.info(message)
        self.callback_notifier.notify("INFO", message)

    def error(self, message: str):
        """오류 레벨 로그 출력"""
        self._logger.error(message)
        self.callback_notifier.notify("ERROR", message)

    def warning(self, message: str):
        """경고 레벨 로그 출력"""
        self._logger.warning(message)
        self.callback_notifier.notify("WARNING", message)

    def debug(self, message: str, *args, **kwargs):
        """디버그 레벨 로그 출력 (개발용)"""
        self._logger.debug(message, *args, **kwargs)

    def add_callback(self, callback: Callable[[str], None]):
        """로그 이벤트 콜백 추가"""
        self.callback_notifier.add_callback(callback)


def setup_logger(name: str, level: Optional[int] = None) -> logging.Logger:
    """
    기본 로거 설정 (하위 호환성)
    
    Args:
        name: 로거명
        level: 로그 레벨 (기본값: INFO)
    
    Returns:
        logging.Logger: 설정된 로거
    """
    logger = logging.getLogger(name)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(Logger.LOG_FORMAT)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
    logger.setLevel(level or logging.INFO)
    return logger 