"""
utils/process_utils.py - 서브프로세스 실행 및 운영시간 체크 유틸리티
"""
import subprocess
import sys
from datetime import datetime

START_HOUR = 8
END_HOUR = 22

def is_operating_hour():
    now = datetime.now()
    return START_HOUR <= now.hour < END_HOUR

def run_worker(worker_path='worker_crawl.py'):
    """worker_crawl.py 등 워커 서브프로세스 실행"""
    result = subprocess.run([sys.executable, worker_path])
    return result.returncode