"""
범용 리그 및 팀 서비스 모듈
모든 스포츠(야구, 축구, 농구, 배구 등)에 적용 가능한 통합 서비스
"""
import logging
from typing import Dict, Optional

from utils.logger import Logger

logger = Logger(__name__, level=logging.DEBUG)


class UniversalLeagueService:
    """범용 리그 정보 서비스 - DB 기반 동적 매핑"""
    
    def get_league_from_team_name(self, team_name: str) -> str:
        """팀명으로부터 리그 결정 - team_info 테이블에서 직접 조회"""
        if not team_name:
            return ""
        try:
            from database.database import connect_supabase
            client = connect_supabase()
            if not client:
                return ""
            
            # 🚀 team_info에서 sportic_league_id 조회
            response = client.table('team_info').select(
                'sportic_league_id'
            ).eq('team_name', team_name).execute()
            
            if response.data and len(response.data) > 0:
                # sportic_league_id를 리그명으로 변환
                league_id = response.data[0].get('sportic_league_id', '')
                if league_id:
                    # 🚀 데이터베이스에서 리그 매핑 동적 조회
                    league_name = self._get_league_name_from_id(client, league_id)
                    if league_name:
                        logger.debug(f"팀명으로 리그 매핑: {team_name} -> {league_id} -> {league_name}")
                        return league_name
                    
        except Exception as e:
            logger.debug(f"리그 조회 실패: {e}")
        return ""
    
    def _get_league_name_from_id(self, client, league_id: str) -> str:
        """league_info 테이블에서 리그명 조회"""
        try:
            response = client.table('league_info').select(
                'league_name'
            ).eq('sportic_league_id', league_id).execute()
            
            if response.data and len(response.data) > 0:
                return response.data[0].get('league_name', '')
        except Exception as e:
            logger.debug(f"리그명 조회 실패: {e}")
        return ""
    
    def get_sport_from_league(self, league: str) -> str:
        """리그로부터 스포츠 종목 결정 - 데이터베이스에서 조회"""
        if not league:
            return "unknown"
        try:
            from database.database import connect_supabase
            client = connect_supabase()
            if not client:
                return "unknown"
            
            # 🚀 league_info에서 스포츠 종목 조회
            response = client.table('league_info').select(
                'sport_type'
            ).eq('league_name', league).execute()
            
            if response.data and len(response.data) > 0:
                sport_type = response.data[0].get('sport_type', '')
                if sport_type:
                    return sport_type
        except Exception as e:
            logger.debug(f"스포츠 종목 조회 실패: {e}")
        return "unknown"


class UniversalTeamMapper:
    """범용 팀명 매핑 서비스 - 모든 스포츠에 적용"""
    
    def __init__(
        self,
        team_mappings: Optional[Dict[str, str]] = None,
        league: str = "",
        use_dynamic_mapping: bool = True,
    ):
        """UniversalTeamMapper

        Args:
            team_mappings: 외부에서 주입한 매핑 딕셔너리(테스트용/오버라이드용)
            league: 호출 컨텍스트의 리그 (MLB, K-League 등)
            use_dynamic_mapping: True 이면 Supabase `team_info` 테이블에서 매핑을 즉시 로드.
        """

        # 1) 우선 주입받은 매핑 사용
        self.team_mappings = team_mappings or {}
        self.league = league
        self.use_dynamic_mapping = use_dynamic_mapping

        # 2) 동적 매핑 활성화 & 아직 비어 있다면 Supabase 조회
        if use_dynamic_mapping and not self.team_mappings:
            try:
                # 지연 로딩(import cycle 방지)
                from database.database import (connect_supabase,
                                               get_team_mappings)

                client = connect_supabase()
                if client:
                    self.team_mappings = get_team_mappings(client)
            except Exception as exc:
                logger.debug(
                    "UniversalTeamMapper 동적 매핑 로드 실패: %s", exc
                )

        # 3) 역매핑(full_name -> short_name) 생성
        self._reverse_mappings: Dict[str, str] = {
            full_name: short_name
            for short_name, full_name in self.team_mappings.items()
        }
    
    def map_team_name(self, original_team_name: str) -> str:
        """범용 팀명 매핑 전략 (데이터베이스 기반)"""
        # 🎯 데이터베이스 기반 팀명 매핑 사용
        if self.use_dynamic_mapping and self.league:
            try:
                from database.database import (connect_supabase,
                                               get_team_display_name)
                client = connect_supabase()
                if client:
                    mapped_name = get_team_display_name(
                        client, original_team_name, self.league
                    )
                    if mapped_name != original_team_name:
                        logger.debug(
                            f"🔄 팀명 매핑 ({self.league}): "
                            f"{original_team_name} → {mapped_name}"
                        )
                        return mapped_name
            except Exception as e:
                logger.debug(f"데이터베이스 팀명 매핑 실패: {e}")
        
        # 🚀 기존 로직 유지 (백업용)
        # 1) 한국/일본 리그: 역매핑 우선 적용 (full_name → short_name)
        if (self.league in ["KBO", "NPB", "K-League", "J-League", "KBL", 
                           "B-League", "V-League", "V1-League"] and 
            original_team_name in self._reverse_mappings):
            return self._reverse_mappings[original_team_name]

        # 2) 해외 리그: 짧은 → 긴 매핑 (기존 로직)
        if (self.league in ["MLB", "Premier League", "La Liga", "Bundesliga", 
                           "Serie A", "Ligue 1", "NBA"] and self.team_mappings):
            return self._apply_team_mapping(original_team_name)

        # 3) 매핑 불가 시 기존 명칭 유지
        return original_team_name
    
    def _apply_team_mapping(self, original_team_name: str) -> str:
        """해외 리그용 팀명 매핑 적용"""
        # 정확한 매칭 우선
        if original_team_name in self.team_mappings:
            return self.team_mappings[original_team_name]
        
        # 부분 매칭 시도
        for key, value in self.team_mappings.items():
            if original_team_name in key or key in original_team_name:
                return value
        
        return original_team_name


class UniversalSportService:
    """범용 스포츠 서비스 - 리그와 팀 매핑 통합 관리"""
    
    def __init__(self):
        self.league_service = UniversalLeagueService()
        self.team_mapper = UniversalTeamMapper()
    
    def get_league_and_map_team(self, team_name: str) -> tuple[str, str]:
        """팀명으로부터 리그를 결정하고 매핑된 팀명 반환"""
        league = self.league_service.get_league_from_team_name(team_name)
        if league:
            self.team_mapper.league = league
            mapped_team = self.team_mapper.map_team_name(team_name)
            return league, mapped_team
        return "", team_name
    
    def get_sport_info(self, team_name: str) -> dict:
        """팀명으로부터 스포츠 정보 전체 반환"""
        league = self.league_service.get_league_from_team_name(team_name)
        sport = self.league_service.get_sport_from_league(league)
        
        if league:
            self.team_mapper.league = league
            mapped_team = self.team_mapper.map_team_name(team_name)
        else:
            mapped_team = team_name
        
        return {
            'original_team': team_name,
            'mapped_team': mapped_team,
            'league': league,
            'sport': sport
        } 