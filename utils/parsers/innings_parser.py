"""
Innings Parser - Single Responsibility Principle (SRP)
Focused solely on parsing and normalizing innings data
"""
import logging
from typing import Any, Dict, List

logger = logging.getLogger(__name__)


def normalize_innings(innings_value: str) -> str:
    """
    이닝 값을 정규화합니다.
    "25 2" -> "25 2/3" 형태로 변환
    기존 파서에서 사용하던 로직을 재활용
    
    Args:
        innings_value: 원본 이닝 값 ("25 2", "7.1", "6", etc.)
        
    Returns:
        str: 정규화된 이닝 값 ("25 2/3", "7 1/3", "6", etc.)
    """
    if not innings_value or not isinstance(innings_value, str):
        return str(innings_value) if innings_value else ""
    
    innings_value = innings_value.strip()
    if not innings_value:
        return ""
    
    try:
        # "25 2" 형태 처리 (공백으로 분리된 경우)
        if ' ' in innings_value and '/' not in innings_value:
            parts = innings_value.split(' ')
            if len(parts) == 2 and parts[1].isdigit():
                whole_part = parts[0]
                remainder = int(parts[1])
                if remainder == 1:
                    return f"{whole_part} 1/3"
                elif remainder == 2:
                    return f"{whole_part} 2/3"
                # remainder가 3 이상이면 추가 이닝으로 처리
                elif remainder >= 3:
                    additional_innings = remainder // 3
                    remaining = remainder % 3
                    new_whole = int(whole_part) + additional_innings
                    if remaining == 0:
                        return str(new_whole)
                    elif remaining == 1:
                        return f"{new_whole} 1/3"
                    elif remaining == 2:
                        return f"{new_whole} 2/3"
        
        # "7.1", "7.2" 형태 처리 (소수점 형태)
        elif '.' in innings_value:
            whole, fraction = innings_value.split('.')
            if fraction == '0':
                return whole
            elif fraction == '1':
                return f"{whole} 1/3"
            elif fraction == '2':
                return f"{whole} 2/3"
            else:
                # 다른 소수점은 그대로 반환
                return innings_value
        
        # 이미 분수 형태이거나 정수인 경우 그대로 반환
        else:
            return innings_value
            
    except (ValueError, IndexError) as e:
        logger.debug(
            f"이닝 정규화 실패 - 원본값: {innings_value}, 오류: {e}"
        )
        return innings_value


def parse_innings_to_float(innings_str: str) -> float:
    """
    이닝 문자열을 float로 변환합니다.
    "25 2/3" -> 25.67, "7 1/3" -> 7.33 등
    
    Args:
        innings_str: 이닝 문자열
        
    Returns:
        float: 이닝의 float 값
    """
    if not innings_str or not isinstance(innings_str, str):
        return 0.0
    
    innings_str = innings_str.strip()
    if not innings_str:
        return 0.0
    
    try:
        # "25 2/3" 형태 처리
        if ' ' in innings_str and '/' in innings_str:
            parts = innings_str.split(' ')
            whole = float(parts[0])
            fraction_part = parts[1]
            if '/' in fraction_part:
                numerator, denominator = fraction_part.split('/')
                fraction = float(numerator) / float(denominator)
                return whole + fraction
        
        # "7 1/3" 형태 (공백이 있지만 분수 형태)
        elif ' ' in innings_str:
            parts = innings_str.split(' ')
            whole = float(parts[0])
            if '/' in parts[1]:
                numerator, denominator = parts[1].split('/')
                fraction = float(numerator) / float(denominator)
                return whole + fraction
            else:
                # "25 2" 형태는 "25 2/3"로 해석
                remainder = int(parts[1])
                fraction = remainder / 3.0
                return whole + fraction
        
        # 일반 숫자 형태
        else:
            return float(innings_str)
            
    except (ValueError, ZeroDivisionError) as e:
        logger.debug(
            f"이닝 float 변환 실패 - 원본값: {innings_str}, 오류: {e}"
        )
        return 0.0


def normalize_innings_in_dict(data_dict: Dict[str, Any], 
                             innings_fields: List[str] = None) -> Dict[str, Any]:
    """
    딕셔너리 내의 이닝 필드들을 정규화합니다.
    
    Args:
        data_dict: 정규화할 데이터 딕셔너리
        innings_fields: 이닝 필드명 리스트 (기본값: ['innings'])
        
    Returns:
        Dict[str, Any]: 이닝이 정규화된 딕셔너리
    """
    if not isinstance(data_dict, dict):
        return data_dict
    
    if innings_fields is None:
        innings_fields = ['innings']
    
    normalized_dict = data_dict.copy()
    
    for field in innings_fields:
        if field in normalized_dict:
            normalized_dict[field] = normalize_innings(normalized_dict[field])
    
    return normalized_dict