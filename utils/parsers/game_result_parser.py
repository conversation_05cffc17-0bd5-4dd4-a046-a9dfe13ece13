"""
Game Result Parser - Single Responsibility Principle (SRP)
Focused solely on parsing game results across different sports
"""


def map_result(result_text: str) -> str:
    """
    경기 결과 텍스트를 표준 코드로 변환
    
    Args:
        result_text: 원본 경기 결과 텍스트
        
    Returns:
        str: 변환된 결과 코드 (W, L, D)
    """
    # 한 글자 단위로 변환 (승->W, 패->L, 무->D)
    result_mapping = {
        "승": "W",
        "패": "L",
        "무": "D",
    }
    
    # 이미 W, L, D 형식이면 그대로 반환
    if result_text in ["W", "L", "D"]:
        return result_text
        
    # "1승 0무 2패" 같은 형식 처리
    if "승" in result_text or "패" in result_text or "무" in result_text:
        for k, v in result_mapping.items():
            result_text = result_text.replace(k, v)
    
    return result_text


def map_pitcher_stats(stats_text: str) -> str:
    """
    투수 기록 텍스트에서 특수 문자 제거 및 승패 표기 변환
    
    Args:
        stats_text: 원본 투수 기록 텍스트
        
    Returns:
        str: 정리된 투수 기록 텍스트
    """
    # 특수 문자 제거
    cleaned_text = stats_text.replace("●", "").strip()
    
    # 팀명 제거 (콜론 뒤의 내용만 유지)
    if ":" in cleaned_text:
        cleaned_text = cleaned_text.split(":", 1)[1].strip()
    
    # 승패 표기 변환 (승->W, 패->L, 세->S, 홀->H)
    return (cleaned_text
            .replace("(승)", "(W)")
            .replace("(패)", "(L)")
            .replace("(세)", "(S)")
            .replace("(홀)", "(H)"))