#!/usr/bin/env python3
"""
간단한 데이터 파싱 테스트 - 순환 import 없이
"""
import asyncio
import sys
import os
from bs4 import BeautifulSoup

# 프로젝트 루트를 Python path에 추가
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def test_soccer_team_direct():
    """축구 팀 데이터 직접 파싱 테스트"""
    print("=" * 60)
    print("🚀 축구 팀 데이터 직접 파싱 테스트")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        
        # 울산 HD FC 팀 페이지 크롤링
        team_url = "https://www.betman.co.kr/main/mainPage/gameinfo/scTeamDetail.do?item=SC&leagueId=SC001&teamId=K01"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print(f"📄 팀 페이지 로딩: {team_url}")
            await page.goto(team_url, wait_until='networkidle')
            await asyncio.sleep(3)  # 데이터 로딩 대기
            
            html = await page.content()
            await browser.close()
            
            print(f"✅ HTML 크기: {len(html)} bytes")
            
            # BeautifulSoup으로 직접 파싱
            soup = BeautifulSoup(html, 'html.parser')
            
            # 팀 정보 추출
            print(f"\n📊 팀 정보 추출:")
            
            # 팀명 추출
            team_name_elem = soup.find('h2')
            if team_name_elem:
                team_name = team_name_elem.get_text(strip=True)
                print(f"   팀명: {team_name}")
            
            # 팀 정보 리스트 추출
            info_list = soup.find('ul')
            if info_list:
                print(f"   팀 정보:")
                items = info_list.find_all('li')
                for item in items:
                    text = item.get_text(strip=True)
                    if text:
                        print(f"     - {text}")
            
            # 테이블 찾기
            tables = soup.find_all('table')
            print(f"\n📈 테이블 수: {len(tables)}개")
            
            for i, table in enumerate(tables):
                caption = table.find('caption')
                caption_text = caption.get_text(strip=True) if caption else f"테이블 {i+1}"
                print(f"   {i+1}. {caption_text}")
                
                # 테이블 행 수 확인
                rows = table.find_all('tr')
                print(f"      행 수: {len(rows)}")
                
                if len(rows) > 0:
                    # 첫 번째 행 (헤더) 확인
                    first_row = rows[0].find_all(['th', 'td'])
                    print(f"      컬럼 수: {len(first_row)}")
                    
                    # 헤더 출력
                    headers = [cell.get_text(strip=True) for cell in first_row]
                    print(f"      헤더: {headers[:5]}{'...' if len(headers) > 5 else ''}")
                    
                    # 데이터 행이 있으면 첫 번째 데이터 행 출력
                    if len(rows) > 1:
                        data_row = rows[1].find_all(['th', 'td'])
                        data = [cell.get_text(strip=True) for cell in data_row]
                        print(f"      첫 데이터: {data[:5]}{'...' if len(data) > 5 else ''}")
            
            # 선수 링크 찾기
            player_links = soup.find_all('a', href=lambda x: x and 'scPlayerDetail.do' in x)
            print(f"\n👥 선수 링크: {len(player_links)}개")
            
            for i, link in enumerate(player_links[:5]):
                player_name = link.get_text(strip=True)
                player_url = link.get('href', '')
                print(f"   {i+1}. {player_name}: {player_url}")
            
            return True
            
    except Exception as e:
        print(f"❌ 축구 팀 직접 파싱 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_soccer_player_direct():
    """축구 선수 데이터 직접 파싱 테스트"""
    print("\n" + "=" * 60)
    print("🚀 축구 선수 데이터 직접 파싱 테스트")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        
        # 류성민 선수 페이지 크롤링
        player_url = "https://www.betman.co.kr/main/mainPage/gameinfo/scPlayerDetail.do?item=SC&leagueId=SC001&teamId=K01&playerId=20250023"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print(f"📄 선수 페이지 로딩: {player_url}")
            await page.goto(player_url, wait_until='networkidle')
            await asyncio.sleep(3)  # 데이터 로딩 대기
            
            html = await page.content()
            await browser.close()
            
            print(f"✅ HTML 크기: {len(html)} bytes")
            
            # BeautifulSoup으로 직접 파싱
            soup = BeautifulSoup(html, 'html.parser')
            
            # 선수 정보 추출
            print(f"\n📊 선수 정보 추출:")
            
            # 선수명 추출
            player_name_elem = soup.find('h2')
            if player_name_elem:
                player_name = player_name_elem.get_text(strip=True)
                print(f"   선수명: {player_name}")
            
            # 선수 정보 리스트 추출
            info_list = soup.find('ul')
            if info_list:
                print(f"   선수 정보:")
                items = info_list.find_all('li')
                for item in items:
                    text = item.get_text(strip=True)
                    if text:
                        print(f"     - {text}")
            
            # 테이블 찾기
            tables = soup.find_all('table')
            print(f"\n📈 테이블 수: {len(tables)}개")
            
            for i, table in enumerate(tables):
                caption = table.find('caption')
                caption_text = caption.get_text(strip=True) if caption else f"테이블 {i+1}"
                print(f"   {i+1}. {caption_text}")
                
                # 테이블 행 수 확인
                rows = table.find_all('tr')
                print(f"      행 수: {len(rows)}")
                
                if len(rows) > 0:
                    # 첫 번째 행 (헤더) 확인
                    first_row = rows[0].find_all(['th', 'td'])
                    print(f"      컬럼 수: {len(first_row)}")
                    
                    # 헤더 출력
                    headers = [cell.get_text(strip=True) for cell in first_row]
                    print(f"      헤더: {headers}")
                    
                    # 데이터 행이 있으면 첫 번째 데이터 행 출력
                    if len(rows) > 1:
                        data_row = rows[1].find_all(['th', 'td'])
                        data = [cell.get_text(strip=True) for cell in data_row]
                        print(f"      첫 데이터: {data}")
            
            return True
            
    except Exception as e:
        print(f"❌ 축구 선수 직접 파싱 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_basketball_team_direct():
    """농구 팀 데이터 직접 파싱 테스트"""
    print("\n" + "=" * 60)
    print("🚀 농구 팀 데이터 직접 파싱 테스트")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        
        # 휴스턴 로켓츠 팀 페이지 크롤링 (NBA)
        team_url = "https://www.betman.co.kr/main/mainPage/gameinfo/bkTeamDetail.do?item=BK&leagueId=BK002&teamId=HOU"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print(f"📄 팀 페이지 로딩: {team_url}")
            await page.goto(team_url, wait_until='networkidle')
            await asyncio.sleep(3)  # 데이터 로딩 대기
            
            html = await page.content()
            await browser.close()
            
            print(f"✅ HTML 크기: {len(html)} bytes")
            
            # 농구 시즌이 아닐 수 있으므로 데이터 확인
            if "데이터가 없습니다" in html or len(html) < 5000:
                print("⚠️ 농구 시즌이 아니거나 데이터가 없습니다")
                return True
            
            # BeautifulSoup으로 직접 파싱
            soup = BeautifulSoup(html, 'html.parser')
            
            # 팀 정보 추출
            print(f"\n📊 농구 팀 정보 추출:")
            
            # 팀명 추출
            team_name_elem = soup.find('h2')
            if team_name_elem:
                team_name = team_name_elem.get_text(strip=True)
                print(f"   팀명: {team_name}")
            
            # 테이블 찾기
            tables = soup.find_all('table')
            print(f"\n📈 테이블 수: {len(tables)}개")
            
            for i, table in enumerate(tables):
                caption = table.find('caption')
                caption_text = caption.get_text(strip=True) if caption else f"테이블 {i+1}"
                print(f"   {i+1}. {caption_text}")
            
            return True
            
    except Exception as e:
        print(f"❌ 농구 팀 직접 파싱 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """메인 테스트 함수"""
    print("🚀 간단한 데이터 파싱 테스트 시작")
    print("=" * 80)
    
    tests = [
        ("축구 팀 직접", test_soccer_team_direct),
        ("축구 선수 직접", test_soccer_player_direct),
        ("농구 팀 직접", test_basketball_team_direct),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 {test_name} 테스트 실행 중...")
            if await test_func():
                passed += 1
                print(f"✅ {test_name} 테스트 통과")
            else:
                print(f"❌ {test_name} 테스트 실패")
        except Exception as e:
            print(f"❌ {test_name} 테스트 실행 실패: {e}")
    
    print("\n" + "=" * 80)
    print(f"🎉 간단한 데이터 파싱 테스트 완료: {passed}/{total} 통과")
    print("=" * 80)
    
    if passed == total:
        print("✅ 모든 테스트 통과! 웹사이트 구조 파악 성공")
    else:
        print(f"⚠️ {total - passed}개 테스트 실패")


if __name__ == "__main__":
    asyncio.run(main())
