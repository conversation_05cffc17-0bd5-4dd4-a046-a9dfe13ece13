# 📋 베트맨 멀티스포츠 시스템 개발 계획서

## 📊 목차
1. [🎯 프로젝트 개요](#-프로젝트-개요)
2. [⚡ 즉시 적용 가능한 개선사항](#-즉시-적용-가능한-개선사항)
3. [🏗️ 멀티스포츠 시스템 확장 계획](#-멀티스포츠-시스템-확장-계획)
4. [📅 단계별 구현 일정](#-단계별-구현-일정)
5. [🧪 테스트 및 성능 최적화](#-테스트-및-성능-최적화)
6. [📈 성공 지표 및 검증](#-성공-지표-및-검증)

---

## 🎯 프로젝트 개요

### 🏆 핵심 목표
- **베트맨 사이트에서 야구 외 3개 스포츠(축구, 농구, 배구) 확장**
- **기존 야구 시스템 100% 보호하면서 점진적 확장**
- **경기 시간 기반 스마트 분산으로 메모리/CPU 최적화**
- **SOLID 원칙과 플러그인 아키텍처 적용**
- **코드 품질 및 개발 환경 혁신적 개선**

### 🏅 기존 야구 시스템 현황 (완벽 동작)
```
URL: dataOfBaseballSchedule.do
리그: KBO(BS001), MLB(BS002), NPB(BS004) - 3개
현재 경기: 119개 (활성 시즌)
핵심 구성요소:
├── UnifiedDataCollector (스케줄 크롤링 + 팀 매칭)
├── BaseStatsParser (KBO/MLB/NPB 통계 파싱)
├── PitcherService (투수 통계 수집/저장)
└── 팀 ID 기반 빠른 매칭 시스템
상태: ✅ 100% 정상 동작 (보호 필수!)
```

---

## ⚡ 즉시 적용 가능한 개선사항

### 🚀 Quick Wins (30분 이내 적용 가능)

#### 1. 메모리 누수 방지 (30분)
```python
# 모든 async 함수에 try-finally 블록 추가
async def collect_data(self):
    browser = None
    try:
        browser = await self.get_browser()
        return await self._do_collect(browser)
    finally:
        if browser:
            await browser.close()
```

#### 2. 환경변수 설정 표준화 (15분)
```bash
# .env.example 파일 생성
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
LOG_LEVEL=INFO
BATCH_SIZE=10
MAX_CONCURRENT_BROWSERS=3
```

#### 3. 구조화된 로깅 (20분)
```python
import structlog
logger = structlog.get_logger(__name__)

try:
    result = await collect_data()
except Exception as e:
    logger.error(
        "데이터 수집 실패",
        sport="baseball",
        team="KIA", 
        error=str(e),
        exc_info=True
    )
```

### 🛠️ 개발 환경 개선

#### requirements.txt → pyproject.toml
```toml
[tool.poetry]
name = "sports-stats-collector"
version = "1.0.0"
description = "Multi-sport statistics collection system"

[tool.poetry.dependencies]
python = "^3.11"
playwright = "^1.40.0"
beautifulsoup4 = "^4.12.2"
supabase = "^2.0.0"
structlog = "^23.2.0"
tenacity = "^8.2.0"
cachetools = "^5.3.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
black = "^23.11.0"
mypy = "^1.7.0"
```

### 💎 타입 안정성 강화

#### 완전한 타입 힌트 적용
```python
from typing import TypedDict, Literal, List, Dict, Union

class TeamStatsDict(TypedDict):
    team_id: str
    league: Literal["KBO", "MLB", "NPB"]
    stats: Dict[str, Union[int, float, str]]

async def collect_team_stats(
    self,
    teams: List[str],
    league: Literal["KBO", "MLB", "NPB"]
) -> List[TeamStatsDict]:
    """팀 통계 수집 - 완전한 타입 안정성"""
    pass
```

### 🧪 테스트 프레임워크

#### 기본 테스트 구조
```python
# tests/unit/test_baseball_collector.py
import pytest
from unittest.mock import AsyncMock

class TestBaseballCollector:
    @pytest.fixture
    async def collector(self):
        mock_client = AsyncMock()
        return BaseballCollector(client=mock_client)
    
    async def test_collect_team_stats_success(self, collector):
        # Given
        teams = ["KIA", "LG", "SSG"]
        
        # When  
        result = await collector.collect_team_stats(teams)
        
        # Then
        assert len(result) == 3
        assert all(isinstance(stat, TeamStatsDict) for stat in result)
```

### 🎯 성능 최적화

#### 브라우저 풀링
```python
class BrowserPool:
    def __init__(self, max_instances: int = 3):
        self._pool: Queue[BrowserSession] = Queue(maxsize=max_instances)
    
    async def acquire(self) -> BrowserSession:
        if self._pool.empty():
            return await self._create_session()
        return await self._pool.get()
    
    async def release(self, session: BrowserSession):
        await self._pool.put(session)
```

#### 적응형 배치 처리
```python
@dataclass
class BatchConfig:
    size: int = 10
    max_concurrent: int = 3
    retry_attempts: int = 3
    backoff_factor: float = 2.0

class AdaptiveBatchProcessor:
    async def process(self, items: List[T]) -> List[ProcessedResult[T]]:
        optimal_size = self._calculate_optimal_batch_size()
        batches = self._create_batches(items, optimal_size)
        return await self._process_batches_concurrently(batches)
```

---

## 🏗️ 멀티스포츠 시스템 확장 계획

### 🔄 확장 대상 스포츠

#### 1. 축구 ⚽ (최우선)
```
URL: dataOfSoccerSchedule.do
리그 9개: K리그1(SC001), K리그2(SC003), EPL(52), 프리메라리가(53), 
         세리에A(54), 분데스리가(55), 프랑스리그(56), 에레디비시(57), J리그(58)
팀 URL: scTeamDetail.do?item=SC&leagueId=SC001&teamId=K01
선수 URL: scPlayerDetail.do?item=SC&leagueId=SC001&teamId=K29&playerId=20230346
현재 상태: 7월 12일부터 K리그1 활성화
우선순위: 최고 (국내외 인기 리그)
```

#### 2. 농구 🏀 (중우선)
```
URL: dataOfBasketballSchedule.do
리그 3개: KBL(BK001), WKBL(BK003), NBA(BK002)
팀 URL: bkTeamDetail.do?item=BK&leagueId=BK002&teamId=HOU
선수 URL: bkPlayerDetail.do?item=BK&leagueId=BK002&teamId=HOU&playerId=6959
현재 상태: 시즌 오프 (31개 행)
우선순위: 중간
```

#### 3. 배구 🏐 (저우선)
```
URL: dataOfVolleyballSchedule.do
리그 2개: KOVO남(VL001), KOVO여(VL002)
팀 URL: vlTeamDetail.do?item=VL&leagueId=VL001&teamId=1005
선수 URL: vlPlayerDetail.do?item=VL&leagueId=VL001&teamId=1005&playerId=0000853&id=1
현재 상태: 시즌 오프 (31개 행)
우선순위: 낮음
```

### 🏗️ 시스템 아키텍처 설계

#### 1. 핵심 설계 원리
- **기존 야구 시스템 100% 보호**: 래퍼 패턴으로 기존 코드 수정 없음
- **경기 시간 기반 분산**: "경기 시간에 맞춰서 하면 분산될거야" 구현
- **HTML 구조 통일성**: 모든 스포츠가 동일한 vsDIv 패턴 사용
- **team_info/league_info 확장**: 기존 구조 호환성 유지하면서 확장

#### 2. 최상위 아키텍처
```
MultiSportOrchestrator (최상위 조정자)
├── TimeSlotScheduler (경기 시간 기반 분산 스케줄러)
├── SportConfigManager (스포츠별 설정 관리)
├── UniversalTeamMatcher (통합 팀 매칭 시스템)
├── BrowserPoolManager (브라우저 세션 재사용)
└── SportCollectorFactory (스포츠별 수집기 생성)
    ├── BaseballCollector (기존 시스템 래핑)
    ├── SoccerCollector (축구 - 9리그)
    ├── BasketballCollector (농구 - 3리그)
    └── VolleyballCollector (배구 - 2리그)
```

#### 3. 시간대별 스마트 분산 시스템
```
시간대별 리소스 할당:
14:00-17:00 AFTERNOON │ 해외축구(EPL,프리메라) + NBA        → 256MB
17:00-20:00 EVENING   │ 한국스포츠(K리그,KBL,KOVO) + KBO    → 384MB (피크)
20:00-24:00 NIGHT     │ J리그 + MLB + 세리에A              → 320MB
00:00-02:00 LATE_NIGHT│ NPB                               → 128MB

메모리 절약 효과: 70% (1.2GB → 384MB)
```

### 📊 데이터베이스 설계 (기존 Supabase Proto 확장)

#### ⚠️ 중요: 기존 데이터 100% 보호
- **기존 Proto 데이터베이스의 team_info, target_games 테이블 활용**
- **새로 생성하지 않고 기존 테이블에 데이터 추가**
- **야구 데이터 절대 손상 없도록 보장**

#### 1. 기존 team_info 테이블 확장 (야구 데이터 보존)
```sql
-- 기존 team_info 테이블에 새 스포츠 데이터 INSERT
-- 야구 데이터는 그대로 유지, 새 컬럼만 추가

-- 축구 팀 데이터 추가 예시
INSERT INTO team_info (team_id, team_name, team_full_name, sportic_league_id, 
                       sportic_team_id, sports, league, league_id) VALUES
('K01_SC', '울산', '울산 HD FC', 'SC001', 'K01', 'soccer', 'K리그1', 'SC001'),
('K02_SC', '김천', '김천 상무 FC', 'SC001', 'K02', 'soccer', 'K리그1', 'SC001'),
-- ... 축구 팀들

-- 농구 팀 데이터 추가 예시  
('HOU_BK', '휴스턴', 'Houston Rockets', 'BK002', 'HOU', 'basketball', 'NBA', 'BK002'),
('LAL_BK', '레이커스', 'LA Lakers', 'BK002', 'LAL', 'basketball', 'NBA', 'BK002'),
-- ... 농구 팀들

-- 배구 팀 데이터 추가 예시
('1005_VL', '현대캐피탈', '현대캐피탈 스카이워커스', 'VL001', '1005', 'volleyball', 'KOVO남', 'VL001');
-- ... 배구 팀들
```

### 🔧 핵심 구현 클래스

#### 1. SportConfig 시스템
```python
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict

class Sport(Enum):
    BASEBALL = "baseball"
    SOCCER = "soccer"
    BASKETBALL = "basketball"
    VOLLEYBALL = "volleyball"

@dataclass
class LeagueInfo:
    id: str                 # 'SC001', 'BK002'
    name: str               # 'K리그1', 'NBA'
    short_name: str         # 'K1', 'NBA'
    country: str            # 'KOR', 'USA'
    tab_id: str             # '#ui-id-1'
    tab_order: int          # 1, 2, 3...

@dataclass  
class SportConfig:
    sport: Sport
    sport_code: str                  # 'SC', 'BK', 'VL'
    base_url: str                    # 스케줄 페이지 URL
    leagues: List[LeagueInfo]        # 리그 목록
    team_url_pattern: str            # 팀 상세 URL 패턴
    player_url_pattern: str          # 선수 상세 URL 패턴
```

#### 2. TimeSlotScheduler (핵심 혁신)
```python
from enum import Enum
from datetime import datetime, time
from dataclasses import dataclass

class TimeSlot(Enum):
    AFTERNOON = "afternoon"      # 14:00-17:00
    EVENING = "evening"          # 17:00-20:00
    NIGHT = "night"              # 20:00-24:00
    LATE_NIGHT = "late_night"    # 00:00-02:00

@dataclass
class SportPriority:
    sport: Sport
    priority: int               # 우선순위 (1=최고)
    expected_games: int         # 예상 경기 수
    memory_mb: int              # 예상 메모리 사용량

class TimeSlotScheduler:
    """경기 시간 기반 스마트 분산 스케줄러"""
    
    TIMESLOT_MAPPING = {
        TimeSlot.AFTERNOON: {
            "time_range": (time(14, 0), time(17, 0)),
            "sports": [
                SportPriority(Sport.SOCCER, 1, 15, 128),  # EPL, 프리메라
                SportPriority(Sport.BASKETBALL, 2, 5, 128)  # NBA
            ],
            "max_memory_mb": 256
        },
        TimeSlot.EVENING: {
            "time_range": (time(17, 0), time(20, 0)),
            "sports": [
                SportPriority(Sport.SOCCER, 1, 20, 128),     # K리그
                SportPriority(Sport.BASKETBALL, 2, 8, 128),  # KBL, WKBL
                SportPriority(Sport.VOLLEYBALL, 3, 6, 64),   # KOVO
                SportPriority(Sport.BASEBALL, 4, 15, 128)    # KBO
            ],
            "max_memory_mb": 384  # 피크 시간
        }
        # ... 다른 시간대
    }
```

#### 3. UniversalSportCollector (통합 수집기)
```python
from abc import ABC, abstractmethod
from typing import List, Dict, Optional

class BaseSportCollector(ABC):
    """모든 스포츠 수집기의 기본 인터페이스"""
    
    @abstractmethod
    async def collect_schedule(self, target_games: List[Dict]) -> List[Dict]:
        """스포츠별 스케줄 수집"""
        pass
    
    @abstractmethod
    async def match_target_games(self, web_games: List[Dict], target_games: List[Dict]) -> List[Dict]:
        """타겟 경기 매칭"""
        pass
    
    @abstractmethod
    async def extract_team_info(self, game_row) -> Dict[str, str]:
        """팀 정보 추출 (홈팀, 어웨이팀, URL)"""
        pass

class UniversalSportCollector(BaseSportCollector):
    """범용 스포츠 데이터 수집기 (축구, 농구, 배구)"""
    
    def __init__(self, sport_config: SportConfig):
        self.config = sport_config
        self.sport_name = sport_config.sport.value
        
    async def collect_schedule(self, target_games: List[Dict]) -> List[Dict]:
        """HTML 구조가 동일하므로 통합 처리 가능"""
        all_games = []
        
        for league in self.config.leagues:
            # 리그별 탭 클릭
            await self._click_league_tab(league.tab_id)
            
            # 경기 테이블 파싱 (야구와 동일한 vsDIv 구조)
            web_games = await self._parse_game_table()
            
            # 타겟 경기 매칭
            matched_games = await self.match_target_games(web_games, target_games)
            all_games.extend(matched_games)
            
        return all_games
```

---

## 📅 단계별 구현 일정

### Phase 1: 핵심 인터페이스 구현 (2-3일)

#### Day 1-2: 기본 구조 설계
- [ ] **core/interfaces/** 디렉토리 생성
  - [ ] `collector.py`: SportCollector 추상 클래스
  - [ ] `parser.py`: SportParser 추상 클래스  
  - [ ] `service.py`: SportService 추상 클래스
- [ ] **core/exceptions/** 강화
  - [ ] `collection_exceptions.py`: 수집 관련 예외
  - [ ] `service_exceptions.py`: 서비스 관련 예외
- [ ] **core/factories/** 팩토리 패턴
  - [ ] `sport_factory.py`: 스포츠별 컴포넌트 생성

#### Day 2-3: SportConfig 시스템 구축
- [ ] **core/sport_config.py** 완성
  - [ ] Sport, TimeSlot enum 정의
  - [ ] LeagueInfo, SportConfig 데이터클래스
  - [ ] 축구, 농구, 배구 전체 설정 정의
- [ ] **core/time_scheduler.py** 완성  
  - [ ] TimeSlotScheduler 클래스
  - [ ] 시간대별 리소스 할당 로직
  - [ ] 동적 메모리 관리 시스템

### Phase 2: 야구 시스템 보호 및 래핑 (1-2일)

#### Day 3-4: 기존 시스템 래퍼 구현
- [ ] **sports/baseball/plugin.py** 생성
  - [ ] BaseballCollector 클래스 (UnifiedDataCollector 래핑)
  - [ ] BaseballParser 클래스 (기존 파서 래핑)
  - [ ] BaseballService 클래스 (기존 서비스 래핑)
- [ ] **기존 코드 100% 보호**
  - [ ] UnifiedDataCollector 그대로 유지
  - [ ] PitcherService 그대로 유지
  - [ ] 모든 야구 로직 기존 방식으로 동작 보장

### Phase 3: 축구 시스템 구현 (3-4일)

#### Day 4-6: 축구 기본 구조
- [ ] **sports/soccer/** 디렉토리 생성
- [ ] **sports/soccer/collectors/soccer_collector.py**
  - [ ] SoccerCollector 클래스 구현
  - [ ] 9개 리그 탭 처리 로직
  - [ ] vsDIv 구조 파싱 (야구와 동일 패턴)
- [ ] **sports/soccer/parsers/soccer_parser.py**
  - [ ] SoccerParser 클래스
  - [ ] HTML 파싱 로직 (기존 야구 패턴 재사용)
- [ ] **sports/soccer/services/soccer_service.py**
  - [ ] SoccerService 클래스  
  - [ ] 축구 팀/선수 데이터 저장 로직

#### Day 6-7: 축구 특화 기능
- [ ] **축구 팀 매칭 시스템**
  - [ ] K01, K17 등 축구 팀 ID 매핑
  - [ ] 유사도 기반 팀명 매칭
- [ ] **URL 패턴 생성**
  - [ ] 팀 URL: `scTeamDetail.do?item=SC&leagueId=SC001&teamId=K01`
  - [ ] 선수 URL: `scPlayerDetail.do?item=SC&leagueId=SC001&teamId=K29&playerId=20230346`
- [ ] **축구 리그 데이터 초기화**
  - [ ] league_info 테이블에 9개 축구 리그 추가
  - [ ] team_info 테이블에 축구 팀 데이터 추가

### Phase 4: 농구/배구 시스템 구현 (2-3일)

#### Day 7-8: 농구 시스템
- [ ] **sports/basketball/** 디렉토리 생성
- [ ] **BasketballCollector 구현**
  - [ ] 축구 패턴 재사용 (HTML 구조 동일)
  - [ ] KBL, WKBL, NBA 3개 리그 처리
- [ ] **농구 URL 패턴**
  - [ ] 팀: `bkTeamDetail.do?item=BK&leagueId=BK002&teamId=HOU`
  - [ ] 선수: `bkPlayerDetail.do?item=BK&leagueId=BK002&teamId=HOU&playerId=6959`
- [ ] **농구 데이터 초기화**
  - [ ] 3개 농구 리그 설정
  - [ ] NBA 팀 데이터 추가

#### Day 8-9: 배구 시스템  
- [ ] **sports/volleyball/** 디렉토리 생성
- [ ] **VolleyballCollector 구현**
  - [ ] KOVO 남녀 2개 리그 처리
  - [ ] 최소 구현으로 패턴 완성
- [ ] **배구 URL 패턴**
  - [ ] 팀: `vlTeamDetail.do?item=VL&leagueId=VL001&teamId=1005`
  - [ ] 선수: `vlPlayerDetail.do?item=VL&leagueId=VL001&teamId=1005&playerId=0000853&id=1`

### Phase 5: 통합 시스템 구축 (2-3일)

#### Day 9-10: 전체 시스템 통합
- [ ] **core/universal_team_matcher.py** 완성
  - [ ] 4개 스포츠 통합 팀 매칭
  - [ ] 캐시 시스템으로 성능 최적화
  - [ ] 정확 매칭 → 유사도 매칭 fallback
- [ ] **MultiSportOrchestrator 구현**
  - [ ] 4개 스포츠 조정자 역할
  - [ ] 시간대별 분산 처리
  - [ ] 오류 격리 및 복구
- [ ] **demo_multi_sport_system.py** 완성
  - [ ] 전체 시스템 시연 스크립트
  - [ ] 6개 섹션 완전 구현

#### Day 10-12: 최적화 및 테스트
- [ ] **infrastructure/web/browser_pool.py**
  - [ ] BrowserPool 클래스 구현
  - [ ] 브라우저 세션 재사용으로 리소스 절약
- [ ] **application/orchestrators/adaptive_batch_processor.py**
  - [ ] AdaptiveBatchProcessor 클래스
  - [ ] 동적 배치 크기 조정
- [ ] **infrastructure/monitoring/** 모니터링
  - [ ] 로깅 시스템 구축
  - [ ] 성능 메트릭 수집
  - [ ] 실시간 대시보드

---

## 🧪 테스트 및 성능 최적화

### 🧩 단위 테스트 (pytest)
- [ ] **tests/unit/core/** 
  - [ ] test_sport_config.py
  - [ ] test_time_scheduler.py
  - [ ] test_universal_team_matcher.py
- [ ] **tests/unit/sports/**
  - [ ] test_soccer_collector.py  
  - [ ] test_basketball_collector.py
  - [ ] test_volleyball_collector.py

### 🔗 통합 테스트
- [ ] **tests/integration/**
  - [ ] test_multi_sport_orchestrator.py
  - [ ] test_browser_pool_integration.py
  - [ ] test_database_integration.py

### ⚡ 성능 테스트  
- [ ] **메모리 사용량 < 512MB** 검증
- [ ] **전체 처리 시간 < 60초** 검증
- [ ] **매칭 정확도 > 95%** 검증

---

## 📈 성공 지표 및 검증

### 🎯 코드 품질 목표
- [ ] 테스트 커버리지 80% 이상
- [ ] 타입 체크 통과율 100%  
- [ ] 메모리 사용량 30% 감소
- [ ] 에러율 90% 감소

### 📊 핵심 성능 지표
```
✅ 전체 처리 시간: < 60초 (4개 스포츠 병렬)
✅ 메모리 사용량: < 512MB (피크 시간 기준)
✅ 매칭 정확도: > 95% (시간, 날짜, 팀명 기준)
✅ 브라우저 세션 재사용률: > 80%
✅ 메모리 절약: 70% (1.2GB → 384MB)
```

### 🛡️ 위험 관리
- **기존 야구 시스템 영향도: 0%** (래퍼 패턴으로 완전 격리)
- **개별 스포츠 실패 격리**: 한 스포츠 실패가 다른 스포츠에 영향 없음
- **롤백 가능성**: 언제든 기존 시스템으로 완전 복원 가능

### ✅ 즉시 측정 가능한 개선
- [x] 환경변수 표준화 → 설정 관리 개선
- [x] 구조화된 로깅 → 디버깅 효율성 향상
- [x] 타입 힌트 → IDE 지원 개선
- [x] 테스트 프레임워크 → 안정성 확보 

### 🎯 최종 검증 체크리스트

#### 기능 검증
- [ ] 야구 시스템 100% 정상 동작 (기존과 동일)
- [ ] 축구 9개 리그 모든 탭 처리 가능
- [ ] 농구/배구 시즌 오프 상태 정상 처리
- [ ] 타겟 경기 매칭 정확도 95% 이상
- [ ] 팀 URL, 선수 URL 정상 생성

#### 성능 검증  
- [ ] 시간대별 분산 처리 정상 동작
- [ ] 메모리 사용량 384MB 이하 유지
- [ ] 브라우저 세션 재사용 80% 이상
- [ ] 전체 처리 시간 60초 이하

#### 시스템 검증
- [ ] 4개 스포츠 동시 실행 안정성
- [ ] 오류 발생시 격리 및 복구 정상
- [ ] 데이터베이스 호환성 100% 유지
- [ ] 모니터링 및 로깅 시스템 정상

---

## 🎉 결론

이 통합 개발 계획서는 **기존 야구 시스템을 100% 보호**하면서도 **3개 스포츠를 효율적으로 확장**하는 완전한 로드맵을 제공합니다. 

**"경기 시간에 맞춰서 하면 분산될거야"**라는 핵심 아이디어를 바탕으로 메모리/CPU 문제를 근본적으로 해결하는 혁신적인 설계와 함께, **즉시 적용 가능한 코드 품질 개선사항**을 통해 개발 효율성과 시스템 안정성을 동시에 달성할 수 있습니다.

**Made with ❤️ by MoneyPick-KO Team** 