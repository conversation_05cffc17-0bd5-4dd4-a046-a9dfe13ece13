#!/usr/bin/env python3
"""
Debug script to check why team batting/pitching data is not being collected
"""
import asyncio
from sports.baseball.services.processors.team_data_processor import TeamDataProcessor
from sports.baseball.collectors.browser_manager import TeamStatsBrowser
from sports.baseball.services.stats_collector import TeamStatsCollector
from utils.service_utils import get_team_mappings
from database.database import connect_supabase

async def debug_team_collection():
    """Debug team data collection process"""
    print("🔍 Debugging team data collection...")
    
    try:
        # Get a sample KBO team task
        client = connect_supabase()
        response = client.table('target_games').select('*').eq('league', 'KBO').limit(1).execute()
        
        if not response.data:
            print("No KBO games found")
            return
        
        game = response.data[0]
        print(f"Testing with game: {game.get('match_id')} - {game.get('home_team')} vs {game.get('away_team')}")
        
        # Create a test task for home team
        from config.config import League
        task = {
            'team_id': game.get('home_team_id'),
            'team_name': game.get('home_team'),
            'league': League.KBO,
            'match_id': game.get('match_id'),
            'match_date': game.get('match_date'),
        }
        
        print(f"Task: {task}")
        
        # Initialize components
        browser = TeamStatsBrowser()
        await browser.initialize()
        
        team_mappings = await get_team_mappings()
        team_collector = TeamStatsCollector(browser, team_mappings)
        team_processor = TeamDataProcessor(team_collector)
        
        # Collect team stats
        print("\n🔄 Starting team data collection...")
        team_stats = await team_processor.collect_team_stats(task)
        
        if team_stats:
            print("\n✅ Team stats collected:")
            for key, value in team_stats.items():
                if value:
                    if isinstance(value, dict):
                        print(f"  {key}: PRESENT ({len(value)} keys)")
                        # Show a few keys if small dict
                        if len(value) <= 5:
                            print(f"    Keys: {list(value.keys())}")
                    elif isinstance(value, list):
                        print(f"  {key}: PRESENT ({len(value)} items)")
                    else:
                        print(f"  {key}: PRESENT (type: {type(value).__name__})")
                else:
                    print(f"  {key}: EMPTY")
        else:
            print("❌ No team stats returned")
        
        await browser.cleanup()
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_team_collection())