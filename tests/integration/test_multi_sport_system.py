#!/usr/bin/env python3
"""
멀티스포츠 시스템 종합 테스트
"""
import asyncio
import pytest
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any

# 테스트 환경 설정
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from core.orchestrator.multi_sport_orchestrator import MultiSportOrchestrator
from core.browser.browser_pool import BrowserPool
from sports.soccer.plugin import SoccerPlugin
from sports.basketball.plugin import BasketballPlugin
from sports.volleyball.plugin import VolleyballPlugin
from sports.baseball.plugin import BaseballPlugin

logger = logging.getLogger(__name__)


class TestMultiSportSystem:
    """멀티스포츠 시스템 종합 테스트"""
    
    @pytest.fixture
    async def orchestrator(self):
        """오케스트레이터 픽스처"""
        config = {
            'max_browsers': 3,
            'headless': True
        }
        orchestrator = MultiSportOrchestrator(config)
        yield orchestrator
        await orchestrator.cleanup()
    
    @pytest.fixture
    async def browser_pool(self):
        """브라우저 풀 픽스처"""
        pool = BrowserPool(max_browsers=2, headless=True)
        await pool.initialize()
        yield pool
        await pool.cleanup()
    
    async def test_orchestrator_initialization(self, orchestrator):
        """오케스트레이터 초기화 테스트"""
        assert orchestrator is not None
        assert len(orchestrator.plugins) == 4  # 4개 스포츠
        assert 'soccer' in orchestrator.plugins
        assert 'basketball' in orchestrator.plugins
        assert 'volleyball' in orchestrator.plugins
        assert 'baseball' in orchestrator.plugins
    
    async def test_browser_pool_management(self, browser_pool):
        """브라우저 풀 관리 테스트"""
        # 상태 확인
        status = await browser_pool.get_status()
        assert status['initialized'] is True
        assert status['stats']['max_sessions'] == 2
        
        # 브라우저 세션 할당/반환
        session = await browser_pool.get_browser('test_sport')
        assert session is not None
        assert session.in_use is True
        assert session.sport == 'test_sport'
        
        await browser_pool.return_browser(session)
        assert session.in_use is False
        assert session.sport is None
    
    async def test_soccer_plugin_functionality(self):
        """축구 플러그인 기능 테스트"""
        plugin = SoccerPlugin()
        
        # 설정 확인
        config = plugin.get_sport_config()
        assert config.sport_name == "soccer"
        assert config.sport_code == "SC"
        assert len(config.leagues) == 9  # 9개 리그
        
        # 시간대 확인
        assert len(config.time_slots) > 0
    
    async def test_basketball_plugin_functionality(self):
        """농구 플러그인 기능 테스트"""
        plugin = BasketballPlugin()
        
        # 설정 확인
        config = plugin.get_sport_config()
        assert config.sport_name == "basketball"
        assert config.sport_code == "BK"
        assert len(config.leagues) == 3  # 3개 리그
    
    async def test_volleyball_plugin_functionality(self):
        """배구 플러그인 기능 테스트"""
        plugin = VolleyballPlugin()
        
        # 설정 확인
        config = plugin.get_sport_config()
        assert config.sport_name == "volleyball"
        assert config.sport_code == "VL"
        assert len(config.leagues) == 2  # 2개 리그
    
    async def test_baseball_plugin_functionality(self):
        """야구 플러그인 기능 테스트"""
        plugin = BaseballPlugin()
        
        # 설정 확인
        config = plugin.get_sport_config()
        assert config.sport_name == "baseball"
        assert config.sport_code == "BB"
        assert len(config.leagues) == 3  # 3개 리그
    
    @pytest.mark.slow
    async def test_single_sport_orchestration(self, orchestrator):
        """단일 스포츠 조율 테스트"""
        # 축구만 실행
        result = await orchestrator.start_orchestration(
            sports=['soccer'], 
            force_run=True
        )
        
        assert result.total_sports == 1
        assert len(result.results) == 1
        assert result.results[0].sport == 'soccer'
        
        # 결과 검증
        soccer_result = result.results[0]
        assert soccer_result.teams_processed >= 0
        assert soccer_result.players_processed >= 0
        assert soccer_result.duration > timedelta(0)
    
    @pytest.mark.slow
    async def test_multi_sport_orchestration(self, orchestrator):
        """멀티스포츠 조율 테스트"""
        # 모든 스포츠 실행
        result = await orchestrator.start_orchestration(
            sports=['soccer', 'basketball'], 
            force_run=True
        )
        
        assert result.total_sports == 2
        assert len(result.results) == 2
        
        # 각 스포츠 결과 확인
        sports_processed = [r.sport for r in result.results]
        assert 'soccer' in sports_processed
        assert 'basketball' in sports_processed
    
    async def test_orchestrator_status(self, orchestrator):
        """오케스트레이터 상태 조회 테스트"""
        status = await orchestrator.get_status()
        
        assert 'running' in status
        assert 'available_sports' in status
        assert 'browser_pool_status' in status
        assert 'last_results' in status
        
        assert len(status['available_sports']) == 4
        assert status['running'] is False  # 초기 상태
    
    async def test_error_handling(self, orchestrator):
        """오류 처리 테스트"""
        # 존재하지 않는 스포츠로 테스트
        result = await orchestrator.start_orchestration(
            sports=['invalid_sport'], 
            force_run=True
        )
        
        # 오류가 있어도 정상적으로 결과 반환
        assert result.total_sports == 0
        assert len(result.results) == 0
    
    @pytest.mark.slow
    async def test_concurrent_execution(self, orchestrator):
        """동시 실행 테스트"""
        # 여러 스포츠 동시 실행
        start_time = datetime.now()
        
        result = await orchestrator.start_orchestration(
            sports=['soccer', 'basketball', 'volleyball'], 
            force_run=True
        )
        
        end_time = datetime.now()
        total_duration = end_time - start_time
        
        # 병렬 처리로 인해 개별 처리 시간의 합보다 짧아야 함
        individual_durations = sum(r.duration for r in result.results)
        assert total_duration < individual_durations * 0.8  # 20% 이상 단축
    
    async def test_browser_session_isolation(self, browser_pool):
        """브라우저 세션 격리 테스트"""
        # 여러 세션 동시 할당
        session1 = await browser_pool.get_browser('sport1')
        session2 = await browser_pool.get_browser('sport2')
        
        # 세션이 서로 다른지 확인
        assert session1.session_id != session2.session_id
        assert session1.sport != session2.sport
        
        # 세션 반환
        await browser_pool.return_browser(session1)
        await browser_pool.return_browser(session2)
    
    async def test_memory_cleanup(self, orchestrator):
        """메모리 정리 테스트"""
        # 여러 번 실행하여 메모리 누수 확인
        for i in range(3):
            result = await orchestrator.start_orchestration(
                sports=['soccer'], 
                force_run=True
            )
            assert result.total_sports == 1
        
        # 상태 확인 (메모리 누수가 없다면 정상 상태 유지)
        status = await orchestrator.get_status()
        assert status['running'] is False


class TestDataParsing:
    """데이터 파싱 테스트"""
    
    async def test_soccer_data_parsing(self):
        """축구 데이터 파싱 테스트"""
        from sports.soccer.parsers.soccer_parser import SoccerParser
        
        # 샘플 HTML로 파싱 테스트
        sample_html = """
        <html>
            <body>
                <table>
                    <caption>시즌 전체성적</caption>
                    <tr><th>순위</th><th>경기수</th><th>승</th></tr>
                    <tr><td>1</td><td>30</td><td>20</td></tr>
                </table>
            </body>
        </html>
        """
        
        parser = SoccerParser()
        data = parser.parse_team_data(sample_html, "테스트팀")
        
        assert data.team_name == "테스트팀"
        assert data.league == "K리그1"  # 기본값
    
    async def test_basketball_data_parsing(self):
        """농구 데이터 파싱 테스트"""
        from sports.basketball.parsers.basketball_parser import BasketballParser
        
        # 샘플 HTML로 파싱 테스트
        sample_html = """
        <html>
            <body>
                <table>
                    <caption>시즌 전체성적</caption>
                    <tr><th>순위</th><th>경기수</th><th>승</th></tr>
                    <tr><td>1</td><td>30</td><td>20</td></tr>
                </table>
            </body>
        </html>
        """
        
        parser = BasketballParser()
        data = parser.parse_team_data(sample_html, "테스트팀")
        
        assert data.team_name == "테스트팀"
        assert data.league == "KBL"  # 기본값


# 테스트 실행 함수
async def run_tests():
    """테스트 실행"""
    print("🚀 멀티스포츠 시스템 종합 테스트 시작")
    print("=" * 80)
    
    # 기본 테스트
    test_system = TestMultiSportSystem()
    test_parsing = TestDataParsing()
    
    # 오케스트레이터 테스트
    orchestrator = MultiSportOrchestrator({'max_browsers': 2, 'headless': True})
    
    try:
        print("🔧 오케스트레이터 초기화 테스트...")
        await test_system.test_orchestrator_initialization(orchestrator)
        print("✅ 오케스트레이터 초기화 테스트 통과")
        
        print("🔧 플러그인 기능 테스트...")
        await test_system.test_soccer_plugin_functionality()
        await test_system.test_basketball_plugin_functionality()
        await test_system.test_volleyball_plugin_functionality()
        await test_system.test_baseball_plugin_functionality()
        print("✅ 플러그인 기능 테스트 통과")
        
        print("🔧 상태 조회 테스트...")
        await test_system.test_orchestrator_status(orchestrator)
        print("✅ 상태 조회 테스트 통과")
        
        print("🔧 데이터 파싱 테스트...")
        await test_parsing.test_soccer_data_parsing()
        await test_parsing.test_basketball_data_parsing()
        print("✅ 데이터 파싱 테스트 통과")
        
        print("\n🎉 모든 기본 테스트 통과!")
        
    except Exception as e:
        print(f"❌ 테스트 실패: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await orchestrator.cleanup()
    
    print("=" * 80)
    print("✅ 멀티스포츠 시스템 종합 테스트 완료")


if __name__ == "__main__":
    asyncio.run(run_tests())
