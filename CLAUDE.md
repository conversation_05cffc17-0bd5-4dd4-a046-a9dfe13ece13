# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive sports statistics collection and analysis platform, primarily focused on baseball data from KBO (Korean Baseball Organization), MLB (Major League Baseball), and NPB (Nippon Professional Baseball). The system uses web scraping with <PERSON>wright to collect real-time sports statistics and stores them in a Supabase PostgreSQL database.

## Key Architecture (2025 Refactor)

The system follows a scheduler-worker pattern designed for long-term stability and memory safety:

- **main.py**: Lightweight scheduler that runs 24/7, executing worker processes at scheduled intervals (10-minute intervals during operating hours: 8-10, 11-13, 14-16, 18-20)
- **worker_crawl.py**: Heavy-duty worker process that performs all data collection, processing, and database operations. Each execution is a complete batch job that terminates after completion to prevent memory leaks
- **Memory Safety**: Each worker execution is a separate subprocess that terminates after completion, ensuring no memory accumulation during long-term operation

## Development Commands

### Running the Application
```bash
# Run the main scheduler (production mode)
python main.py

# Run a single worker cycle (development/testing)
python worker_crawl.py

# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium
```

### Testing
```bash
# Run tests (if test files exist)
pytest

# Test database connection
python -c "from database.database import connect_supabase; print('✅ DB OK' if connect_supabase() else '❌ DB Error')"

# Test Playwright setup
python -c "from playwright.async_api import async_playwright; print('✅ Playwright OK')"
```

## Core Architecture Components

### Data Collection Layer (`collectors/`)
- **UnifiedDataCollector**: Main orchestrator for data collection across multiple leagues
- **TeamStatsBrowser**: Browser automation for team statistics
- **PitcherStatsBrowser**: Specialized browser for pitcher data collection
- **BatchProcessor**: Handles batch processing with memory management (10 teams per batch)

### Data Parsing Layer (`parsers/baseball/`)
- **BaseStatsParser**: Abstract base class for all parsers
- **KBOStatsParser**: Korean Baseball Organization specific parser
- **MLBStatsParser**: Major League Baseball specific parser
- **NPBStatsParser**: Nippon Professional Baseball specific parser
- **PitcherParser**: Specialized parser for pitcher statistics

### Business Logic Layer (`services/baseball/`)
- **TeamService**: Team statistics management and database operations
- **PitcherService**: Pitcher data processing and storage
- **StatsCollector**: Coordinates collection across different data types

### Database Layer (`database/`)
- **database.py**: Supabase client management and all CRUD operations
- Main tables: `team_info`, `target_games`, `team_stats`, `pitcher_individual_stats`

## Configuration & Environment

### Required Environment Variables (.env)
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key
```

### Key Configuration Files
- **config/config.py**: League definitions, URL mappings, and application settings
- **requirements.txt**: Runtime dependencies only (no development packages)

## Database Schema Overview

### Primary Tables
- **team_info**: Team metadata and league associations
- **target_games**: Game schedule and basic match information
- **team_stats**: Comprehensive team statistics with JSONB columns for complex data structures
- **pitcher_individual_stats**: Detailed pitcher performance metrics

### Data Structure Pattern
The system uses a hybrid approach:
- Structured columns for searchable/indexable data (dates, IDs, basic metrics)
- JSONB columns for complex nested statistics (season_summary, recent_games, pitcher_stats)

## Data Flow Architecture

1. **Scheduler (main.py)** triggers worker execution
2. **Worker (worker_crawl.py)** loads target games from database
3. **UnifiedDataCollector** orchestrates parallel data collection
4. **League-specific parsers** extract and structure data
5. **Database services** handle upsert operations with conflict resolution
6. **Cleanup routines** ensure proper resource deallocation

## Development Patterns

### Adding New Leagues
1. Add league to `League` enum in `config/config.py`
2. Create league-specific parser in `parsers/baseball/`
3. Update URL mappings and team configurations
4. Add league-specific tests

### Memory Management
- Use context managers for browser sessions
- Implement batch processing for large datasets
- Call cleanup methods explicitly in finally blocks
- Avoid keeping large data structures in memory between batches

### Error Handling Strategy
- Graceful degradation: continue processing other teams if one fails
- Detailed logging with structured data for debugging
- Database transaction rollback on critical failures
- Retry logic with exponential backoff for network issues

## Performance Considerations

- **Batch Processing**: Process teams in groups of 10 to balance performance and memory usage
- **Connection Pooling**: Reuse browser contexts within batches
- **Parallel Processing**: Use asyncio for concurrent data collection
- **Database Optimization**: Use upsert operations to handle duplicates efficiently

## Troubleshooting Common Issues

### RuntimeError: Event loop is closed
This occurs during cleanup of asyncio subprocesses. The codebase includes specific handling in worker_crawl.py to properly close browser sessions before event loop termination.

### Playwright Browser Issues
```bash
# Reinstall browsers if issues occur
playwright uninstall
playwright install chromium
```

### Database Connection Issues
Verify Supabase credentials and network connectivity. The system includes connection validation utilities.

## Code Style Guidelines

- Use type hints for all function parameters and return types
- Follow PEP 8 naming conventions (snake_case for functions/variables, PascalCase for classes)
- Include docstrings for public methods
- Use structured logging with contextual information
- Implement proper error handling with specific exception types