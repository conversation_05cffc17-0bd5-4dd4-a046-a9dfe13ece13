# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive **multi-sport** statistics collection and analysis platform supporting **4 sports across 17 leagues**:
- **Baseball (3 leagues)**: KBO, MLB, NPB 
- **Soccer (9 leagues)**: K리그1/2, EPL, 프리메라리가, 세리에A, 분데스리가, 프랑스리그, 에레디비시, J리그
- **Basketball (3 leagues)**: KBL, WKBL, NBA
- **Volleyball (2 leagues)**: KOVO남, KOVO여

The system uses a **plugin architecture** with **betman.co.kr web scraping only** via Playwright to collect real-time sports statistics and stores them in a Supabase PostgreSQL database.

## Key Architecture (2025 TimeSlot Optimization)

The system follows a scheduler-worker pattern with TimeSlot precision and 72% memory optimization:

- **main.py**: Ultra-lightweight TimeSlot scheduler (19MB standby, 72% memory savings), executing worker processes at precise times (08:12, 11:12, 12:12, 14:12, 14:32, 16:12, 17:12)
- **worker_crawl.py**: Heavy-duty worker process that performs all data collection, processing, and database operations. Each execution is a complete batch job that terminates after completion to prevent memory leaks
- **TimeSlot Precision**: 1-minute check interval with exact execution times, 30x more precise than previous 30-minute intervals
- **Memory Safety**: Complete process separation with library isolation - heavy libraries moved to worker, scheduler kept minimal

## Development Commands

### Running the Application
```bash
# Run the TimeSlot scheduler (production mode - 19MB standby memory)
python main.py

# Run a single worker cycle (development/testing)
python worker_crawl.py

# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium

# Test TimeSlot scheduler functionality
python -c "
from core.scheduler.time_slot_scheduler import TimeSlotScheduler
from datetime import datetime
scheduler = TimeSlotScheduler()
test_time = datetime.now().replace(hour=8, minute=12)
print(f'Active sports at 08:12: {scheduler.get_active_sports_now(test_time)}')
"
```

### Multi-Sport Commands
```bash
# Run multi-sport orchestrator (all sports)
python -c "
import asyncio
from core.orchestrator.multi_sport_orchestrator import MultiSportOrchestrator
async def run():
    orchestrator = MultiSportOrchestrator({'max_browsers': 3})
    result = await orchestrator.start_orchestration(force_run=True)
    print(f'Teams: {result.total_teams}, Players: {result.total_players}')
asyncio.run(run())
"

# Run specific sports only
python demo_multi_sport_system.py

# Test individual sport plugins
python -c "
import asyncio
from sports.soccer.plugin import SoccerPlugin
async def test(): 
    plugin = SoccerPlugin()
    games = await plugin.collect_games()
    print(f'Soccer games: {len(games)}')
asyncio.run(test())
"
```

### Testing
```bash
# Run comprehensive multi-sport tests
python test_system_integration.py

# Test individual sport implementations
python test_soccer_implementation.py
python test_basketball_volleyball_players.py

# Test database connection
python -c "from database.database import connect_supabase; print('✅ DB OK' if connect_supabase() else '❌ DB Error')"

# Test Playwright setup
python -c "from playwright.async_api import async_playwright; print('✅ Playwright OK')"
```

## Multi-Sport Architecture Components

### Core Orchestration Layer (`core/`)
- **MultiSportOrchestrator**: Central coordinator for all 4 sports with error isolation
- **TimeSlotScheduler**: Time-based distribution to prevent system overload
- **BrowserPool**: Efficient browser session management and resource pooling
- **StaticResourceManager**: URL patterns, league mappings, and team configurations

### Plugin System (`sports/`)
- **Baseball Plugin**: 3 leagues with betman.co.kr crawling for teams and pitchers
- **Soccer Plugin**: 9 leagues with draw-handling and goal statistics
- **Basketball Plugin**: 3 leagues with quarter-based scoring and position-specific stats
- **Volleyball Plugin**: 2 leagues with set-based scoring and attack efficiency metrics

### Data Collection Layer (`collectors/`)
- **TeamPitcherUnifiedService**: Main baseball orchestrator for team stats and pitcher data
- **PitcherCrawler**: Enhanced with pitcher.json configuration support for direct URLs
- **SoccerCollector**: Soccer-specific data collection with 9-league support
- **BasketballCollector**: Basketball data with quarter scoring
- **VolleyballCollector**: Volleyball data with set-based metrics

### Data Parsing Layer (Sport-Specific)
- **Baseball Parsers** (`sports/baseball/parsers/`): KBO, MLB, NPB specialized parsers
- **Soccer Parsers** (`sports/soccer/parsers/`): Soccer-specific with 3-outcome results (W/D/L)
- **Basketball Parsers** (`sports/basketball/parsers/`): Quarter-based scoring and rebounds/assists
- **Volleyball Parsers** (`sports/volleyball/parsers/`): Set-based scoring and attack efficiency

### Database Layer (`database/`)
- **database.py**: Multi-sport Supabase client with sport_type differentiation
- **baseball_stats table**: Primary table for all baseball team and pitcher statistics
- **team_info, target_games**: Supporting tables for team metadata and game schedules

## Configuration & Environment

### Required Environment Variables (.env)
```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key
```

### Key Configuration Files
- **config/config.py**: League definitions, URL mappings, and application settings
- **core/config/resources/pitcher.json**: Direct URL configuration for specific pitchers
- **requirements.txt**: Runtime dependencies only (no development packages)

## Database Schema Overview

### Primary Tables
- **baseball_stats**: All baseball data including team stats, pitcher profiles, season summaries
- **team_info**: Team metadata and league associations
- **target_games**: Game schedule and basic match information

### Data Structure Pattern
The system uses a hybrid approach:
- Structured columns for searchable/indexable data (dates, IDs, basic metrics)
- JSONB columns for complex nested statistics (season_summary, recent_games, pitcher_stats)

## Data Flow Architecture (Betman-Only)

1. **Scheduler (main.py)** triggers worker execution
2. **Worker (worker_crawl.py)** loads target games from database
3. **TeamPitcherUnifiedService** orchestrates parallel data collection from betman.co.kr only
4. **League-specific parsers** extract and structure betman data
5. **Database services** handle upsert operations with conflict resolution
6. **Cleanup routines** ensure proper resource deallocation

## Development Patterns

### Adding New Sports
1. Create sport plugin in `sports/new_sport/plugin.py`
2. Implement collectors, parsers, and services for the sport
3. Add sport configuration to `core/config/sport_config.py`
4. Update `core/orchestrator/multi_sport_orchestrator.py` to include new plugin
5. Add comprehensive tests in `test_new_sport_implementation.py`

### Adding New Leagues to Existing Sports
1. Update league mappings in `core/config/static_resources.py`
2. Add league-specific parsing logic if needed
3. Update URL patterns for the new league
4. Test with demo script: `python demo_multi_sport_system.py`

### Multi-Sport Memory Management
- **Browser Pool**: Shared across all sports with automatic cleanup
- **Time-based Distribution**: Each sport runs in different time slots to prevent overlap
- **Plugin Isolation**: Each sport plugin manages its own resources independently
- **Worker Process**: Heavy operations run in separate process that terminates after completion

### Multi-Sport Error Handling Strategy
- **Sport Isolation**: Failure in one sport doesn't affect others
- **Graceful Degradation**: Continue with remaining sports if one fails
- **Detailed Logging**: Sport-specific error context for debugging
- **Resource Recovery**: Browser pool automatically recovers from failures
- **Retry Logic**: Sport-specific retry strategies with exponential backoff

## Performance Considerations

- **Batch Processing**: Process teams in groups of 10 to balance performance and memory usage
- **Connection Pooling**: Reuse browser contexts within batches
- **Parallel Processing**: Use asyncio for concurrent data collection
- **Database Optimization**: Use upsert operations to handle duplicates efficiently
- **Quick Skip Logic**: Silent database checks to avoid redundant crawling

## Troubleshooting Common Issues

### RuntimeError: Event loop is closed
This occurs during cleanup of asyncio subprocesses. The codebase includes specific handling in worker_crawl.py to properly close browser sessions before event loop termination.

### Playwright Browser Issues
```bash
# Reinstall browsers if issues occur
playwright uninstall
playwright install chromium
```

### Database Connection Issues
Verify Supabase credentials and network connectivity. The system includes connection validation utilities.

## Special Configurations

### Pitcher.json Configuration System
For specific pitchers requiring direct URL access (e.g., NPB players like 이토):

```json
{
  "manual_pitchers": {
    "이토": {
      "name": "이토",
      "league": "NPB",
      "direct_url": "https://www.betman.co.kr/main/mainPage/gameinfo/bsPlayerDetail.do?item=BS&leagueId=BS004&teamId=C1&playerId=2004715",
      "priority": "high",
      "active": true
    }
  }
}
```

The PitcherCrawler automatically:
1. Checks pitcher.json for direct URLs first
2. Falls back to schedule-based name matching if not found
3. Provides extensible configuration for difficult-to-find players

## Important Notes

- **Data Source**: ALL data collection is from betman.co.kr only - no external APIs
- **Skip Logic**: Silent database checks prevent unnecessary crawling when data exists
- **Memory Optimization**: Process separation ensures no memory leaks during long runs
- **Error Isolation**: Sport-specific error handling prevents system-wide failures

## Code Style Guidelines

- Use type hints for all function parameters and return types
- Follow PEP 8 naming conventions (snake_case for functions/variables, PascalCase for classes)
- Include docstrings for public methods
- Use structured logging with contextual information
- Implement proper error handling with specific exception types