import asyncio
import atexit
import gc
import logging
import time
import tracemalloc
from collections import defaultdict

from collectors.unified_crawler import UnifiedDataCollector
from database.database import connect_supabase
from processors.game_processor import GameProcessor
from services.baseball.pitcher_service import PitcherService
from utils.logger import Logger

# from datetime import datetime  # (미사용 시 제거)


logger = Logger(__name__, level=logging.INFO)

INTERVAL_SECONDS = 1200  # 20 minutes


def group_games_by_league(games):
    league_games = defaultdict(list)
    for game in games:
        if 'date' in game and 'match_date' not in game:
            game['match_date'] = game['date']
        if 'time' in game and 'match_time' not in game:
            game['match_time'] = game['time']
        league = game.get('league') or game.get('league_id')
        league_games[league].append(game)
    return league_games


async def process_league_batches(league, games_in_league, game_processor, client, start_time, logger):
    for i in range(0, len(games_in_league), 10):
        batch = games_in_league[i:i+10]
        logger.info(f"  - {league} {i+1}~{i+len(batch)}번째 배치 처리")
        valid_games = await game_processor.filter_games_with_match_id(batch)
        if not valid_games:
            continue
        games_to_process = await game_processor.filter_existing_team_stats(valid_games)
        if not games_to_process:
            continue
        teams_saved = 0
        pitcher_data_list = []
        try:
            collector = UnifiedDataCollector(client, wait_time=5.0)
            teams_saved = await collector.save_team_stats(games_to_process)
            if teams_saved > 0:
                pitcher_service = PitcherService()
                pitcher_data_list = await pitcher_service.save_multiple_pitcher_stats(games_to_process, client)
        finally:
            if 'collector' in locals() and hasattr(collector, 'close'):
                await collector.close()
            gc.collect()
        elapsed = time.time() - start_time
        logger.info(
            f"  ✅ {league} 배치 처리 완료: 팀 {len(batch) * 2}개, "
            f"투수 {len(pitcher_data_list)}명 ({elapsed:.1f}초)"
        )


async def main():
    tracemalloc.start()
    start_time = time.time()
    logger.info("🚀 수집 시작")
    try:
        collector: UnifiedDataCollector | None = None  # 명시적 타입 힌트로 가독성 향상

        client = connect_supabase()
        if not client:
            elapsed = time.time() - start_time
            logger.error(f"❌ 실패: DB 연결 실패 ({elapsed:.1f}초)")
            return

        game_processor = GameProcessor(client)
        collector = UnifiedDataCollector(client, wait_time=5.0)

        # ── 경기 목록 수집 ────────────────────────────────────────────────
        games = await collector.collect_games()
        if not games:
            elapsed = time.time() - start_time
            logger.error(f"⚠️ 경기 없음 - 스킵 ({elapsed:.1f}초)")
            return

        league_games = group_games_by_league(games)
        for league, games_in_league in league_games.items():
            logger.info(f"리그 {league} 처리 시작 ({len(games_in_league)}경기)")
            await process_league_batches(
                league,
                games_in_league,
                game_processor,
                client,
                start_time,
                logger,
            )

    except Exception as e:
        elapsed = time.time() - start_time
        logger.error(f"❌ 실패: {str(e)} ({elapsed:.1f}초)")
    finally:
        # collector가 남아 있다면 이벤트 루프가 종료되기 전에 명시적으로 정리
        if collector is not None and hasattr(collector, "close"):
            try:
                await collector.close()
            except Exception as close_err:
                logger.warning(f"collector.close() 중 예외 무시: {close_err}")
    total_elapsed = time.time() - start_time
    current, peak = tracemalloc.get_traced_memory()
    logger.info(f"🏁 전체 처리 완료: 총 {total_elapsed:.1f}초 소요")
    logger.info(f"[메모리] 현재 사용량: {current / 1024 / 1024:.2f}MB, 피크: {peak / 1024 / 1024:.2f}MB")
    tracemalloc.stop()


# ---- 이벤트 루프 종료 전 잔여 태스크 정리 ----


def _cleanup_pending_tasks():
    """atexit 훅: 닫히기 전 루프의 미처리 태스크/리소스 정리"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        # 이벤트 루프가 이미 소멸된 경우
        return

    if loop.is_closed():
        return

    pending = [t for t in asyncio.all_tasks(loop) if not t.done()]

    for task in pending:
        task.cancel()

    if pending:
        gathered = asyncio.gather(*pending, return_exceptions=True)
        loop.run_until_complete(gathered)

    # async generator / default executor 정리 (Python 3.9+)
    try:
        loop.run_until_complete(loop.shutdown_asyncgens())
        loop.run_until_complete(
            loop.shutdown_default_executor()
        )
    except (AttributeError, RuntimeError):
        pass


atexit.register(_cleanup_pending_tasks)


if __name__ == "__main__":
    asyncio.run(main())