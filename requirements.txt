# Production dependencies - Core packages only
# All transitive dependencies will be installed automatically

# Web scraping and parsing
playwright
beautifulsoup4
lxml
requests

# Database
supabase

# Data processing
pandas
numpy

# Utilities
python-dotenv
pytz
psutil

# Development and testing dependencies
# These are not needed for production but help with code quality

# Testing framework (minimal set)
pytest
pytest-asyncio
pytest-cov

# Essential code quality tools only
black
flake8

# Type checking (optional)
mypy