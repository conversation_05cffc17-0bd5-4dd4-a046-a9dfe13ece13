"""
통합 데이터 수집기 설정 모듈
"""
import os
from enum import Enum
from typing import Dict, List

from dotenv import load_dotenv

# .env 파일 로드
load_dotenv()

# Supabase 설정
SUPABASE_URL = os.environ.get("SUPABASE_URL")
SUPABASE_KEY = os.environ.get("SUPABASE_KEY")


class League(Enum):
    """지원하는 리그"""
    KBO = "KBO"
    MLB = "MLB"
    NPB = "NPB"


class Sports(Enum):
    """지원하는 스포츠"""
    BASEBALL = "BS"
    BASKETBALL = "BK"
    SOCCER = "SC"
    VOLLEYBALL = "VL"


# 지원 리그 목록
SUPPORTED_LEAGUES = [League.KBO, League.MLB, League.NPB]

# 리그 ID 매핑
LEAGUE_ID_MAPPING = {
    League.KBO: "BS001",
    League.MLB: "BS002", 
    League.NPB: "BS004"
}

# Sportic 리그 ID 매핑
SPORTIC_LEAGUE_MAPPING = {
    League.KBO: "bs001",
    League.MLB: "bs002",
    League.NPB: "bs004"
}

# 기본 URL 포맷
BASE_URL_FORMAT = (
    "https://www.betman.co.kr/main/mainPage/gameinfo/{sports}TeamDetail.do"
    "?item={item}&leagueId={league_id}&teamId={team_id}"
)

# 스케줄 URL
SCHEDULE_URL = (
    'https://www.betman.co.kr/main/mainPage/gameinfo/'
    'dataOfBaseballSchedule.do'
)

# 스포츠별 URL 경로 매핑
SPORTS_URL_MAPPING: Dict[str, str] = {
    "BS": "bs",  # 야구
    "BK": "bk",  # 농구
    "SC": "sc",  # 축구
    "VL": "vl",  # 배구
}

# 출력 설정
OUTPUT_CONFIG = {
    'print_summary': True,
    'save_to_file': False,
    'file_format': 'json'
}


def create_team_url(sports: str, league_id: str, team_id: str) -> str:
    """
    스포츠, 리그 ID, 팀 ID를 기반으로 팀 통계 URL을 생성합니다.
    
    Args:
        sports: 스포츠 코드 (예: 'BS', 'BK')
        league_id: 리그 ID (예: 'BS002')
        team_id: 팀 ID (예: 'CL')
        
    Returns:
        str: 생성된 팀 통계 URL
    """
    sports_path = SPORTS_URL_MAPPING.get(sports, sports.lower())
    return BASE_URL_FORMAT.format(
        sports=sports_path, 
        item=sports, 
        league_id=league_id, 
        team_id=team_id
    )


def get_league_id(league: League) -> str:
    """리그 열거형을 리그 ID로 변환"""
    return LEAGUE_ID_MAPPING.get(league, "BS001")


def get_sportic_league_id(league: League) -> str:
    """리그 열거형을 Sportic 리그 ID로 변환"""
    return SPORTIC_LEAGUE_MAPPING.get(league, "bs001") 