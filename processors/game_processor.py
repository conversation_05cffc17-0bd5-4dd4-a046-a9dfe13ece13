"""
게임 처리 로직 - 필터링, 검증, 데이터 체크
"""
from typing import Dict, List

from supabase import Client

from utils.logger import Logger

logger = Logger(__name__)


class GameProcessor:
    """게임 데이터 처리 담당 클래스"""
    
    def __init__(self, client: Client):
        self.client = client

    async def filter_games_with_match_id(
        self, games: List[Dict]
    ) -> List[Dict]:
        """target_games에서 match_id가 있는 경기만 필터링"""
        valid_games = []
        
        for game in games:
            try:
                # target_games에서 해당 경기의 match_id 조회
                response = (
                    self.client.table('target_games')
                    .select('match_id')
                    .eq('match_date', game['date'])
                    .eq('match_time', game['time'])
                    .eq('home_team_id', game['home_team_id'])
                    .eq('away_team_id', game['away_team_id'])
                    .eq('sports', 'baseball')
                    .eq('game_type', 'W')
                    .execute()
                )
                
                if response.data and response.data[0].get('match_id'):
                    # match_id를 게임 데이터에 추가
                    game['match_id'] = response.data[0]['match_id']
                    valid_games.append(game)
                else:
                    logger.info(
                        f"❌ match_id 없음: {game.get('match_date', '?')} "
                        f"{game.get('match_time', '?')} | "
                        f"홈팀: {game.get('home_team', '?')}"
                        f"({game.get('home_team_id', '?')}) vs "
                        f"원정팀: {game.get('away_team', '?')}"
                        f"({game.get('away_team_id', '?')})"
                    )
                    
            except Exception as e:
                logger.debug(f"❌ match_id 조회 실패: {e}")
                continue
        
        return valid_games

    async def filter_existing_team_stats(
        self, games: List[Dict]
    ) -> List[Dict]:
        """팀별로 pitcher_profile이나 pitcher_stats가 하나라도 없는 경기 찾기"""
        games_to_process = []
        existing_games = []
        
        for game in games:
            try:
                match_id = game.get('match_id')
                if not match_id:
                    continue
                    
                # team_stats에서 pitcher_profile, pitcher_stats 확인
                response = (
                    self.client.table('team_stats')
                    .select('team_role, pitcher_profile, pitcher_stats')
                    .eq('match_id', match_id)
                    .execute()
                )
                
                # 홈팀과 원정팀의 투수 데이터 상태 확인
                home_pitcher_exists = False
                away_pitcher_exists = False
                
                for row in response.data:
                    team_role = row.get('team_role')
                    has_profile = row.get('pitcher_profile') is not None
                    has_stats = row.get('pitcher_stats') is not None
                    
                    if team_role == 'home' and has_profile and has_stats:
                        home_pitcher_exists = True
                    elif team_role == 'away' and has_profile and has_stats:
                        away_pitcher_exists = True
                
                # 홈팀이나 원정팀 중 하나라도 투수 데이터가 없으면 처리 대상
                if not (home_pitcher_exists and away_pitcher_exists):
                    games_to_process.append(game)
                else:
                    existing_games.append(game)
                    
            except Exception as e:
                logger.debug(f"❌ team_stats 조회 실패: {e}")
                # 오류 시 처리하도록 포함
                games_to_process.append(game)
        
        # 모든 게임 처리 후 중복 게임 수를 한 번만 로그로 출력
        if existing_games:
            logger.debug(f"이미 존재하는 경기: {len(existing_games)}개")
        
        return games_to_process

    def is_pitcher_data_exists(self, match_id: str, team_role: str) -> bool:
        """team_stats에서 pitcher_profile이 이미 있는 경기 제외"""
        try:
            if not self.client or not match_id or not team_role:
                return False

            # 팀 롤에 따라 팀 아이디 가져오기
            response = self.client.table('team_stats').select('team_id').eq(
                'match_id', match_id
            ).eq('team_role', team_role).execute()

            if not response.data:
                return False

            team_id = response.data[0].get('team_id')
            if not team_id:
                return False

            # team_stats에서 해당 match_id의 pitcher_profile 확인
            response = self.client.table('team_stats').select(
                'pitcher_profile'
            ).eq('match_id', match_id).eq('team_id', team_id).execute()

            if not response.data:
                return False

            row = response.data[0]
            if row.get('pitcher_profile') is not None:
                # pitcher_profile이 이미 있으면 존재하는 것으로 판단
                return True

            return False
        except Exception as e:
            logger.error(f"투수 데이터 존재 여부 확인 중 오류: {e}")
            return False