# EC2 Deployment Guide for Betman Crawler

This guide helps you deploy the betman crawler application on an EC2 instance with proper Playwright Chromium setup.

## Prerequisites

- Ubuntu 20.04+ EC2 instance
- Python 3.11+
- At least 2GB RAM (4GB recommended)
- 20GB+ disk space

## Quick Setup

### 1. Connect to EC2 Instance
```bash
ssh -i your-key.pem ubuntu@your-ec2-ip
```

### 2. Update System
```bash
sudo apt-get update && sudo apt-get upgrade -y
```

### 3. Install Python Dependencies
```bash
# Install Python and pip
sudo apt-get install -y python3 python3-pip python3-venv

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. Install Playwright Dependencies
```bash
# Install system dependencies for Playwright
sudo apt-get install -y \
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    libatspi2.0-0 \
    libgtk-3-0 \
    libxshmfence1 \
    libgconf-2-4 \
    libxcursor1 \
    libxi6 \
    libxrender1 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgcc1 \
    libglib2.0-0 \
    libnspr4 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    xvfb
```

### 5. Install Playwright Browsers
```bash
# Install Chromium browser
playwright install chromium
```

### 6. Environment Configuration
Create `.env` file:
```bash
# Betman Crawler Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_key_here

# Database Configuration
DATABASE_URL=your_database_url_here

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Browser Configuration
HEADLESS=true
MAX_BROWSERS=5
BROWSER_TIMEOUT=30000

# Scheduler Configuration
SCHEDULE_TIMES=08:12,11:12,12:12,14:12,14:32,16:12,17:12
WORKING_HOURS_START=8
WORKING_HOURS_END=23

# Sports Configuration
ENABLED_SPORTS=baseball,soccer,basketball,volleyball
```

### 7. Test Installation
```bash
# Test Playwright
python3 -c "from playwright.sync_api import sync_playwright; print('✅ Playwright OK')"

# Test main application
python3 main.py --help

# Test individual sport
python3 worker_crawl.py soccer
```

## Production Deployment

### Using PM2 (Recommended)
```bash
# Install PM2
npm install -g pm2

# Start the scheduler
pm2 start "python3 main.py" --name betman-scheduler

# Start individual sport workers
pm2 start "python3 worker_crawl.py baseball" --name baseball-worker
pm2 start "python3 worker_crawl.py soccer" --name soccer-worker

# Monitor logs
pm2 logs betman-scheduler
pm2 logs baseball-worker

# Restart all processes
pm2 restart all

# Save PM2 configuration
pm2 save
pm2 startup
```

### Using Systemd Service
Create `/etc/systemd/system/betman-crawler.service`:
```ini
[Unit]
Description=Betman Crawler Service
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/home/<USER>/Stats
Environment=PATH=/home/<USER>/Stats/venv/bin
ExecStart=/home/<USER>/Stats/venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start the service:
```bash
sudo systemctl daemon-reload
sudo systemctl enable betman-crawler
sudo systemctl start betman-crawler
sudo systemctl status betman-crawler
```

## Monitoring and Logs

### View Logs
```bash
# PM2 logs
pm2 logs

# Systemd logs
sudo journalctl -u betman-crawler -f

# Application logs
tail -f logs/app.log
```

### Monitor Resources
```bash
# Check memory usage
free -h

# Check disk usage
df -h

# Check running processes
ps aux | grep python
```

## Troubleshooting

### Playwright Issues
```bash
# Reinstall Playwright
pip uninstall playwright
pip install playwright
playwright install chromium

# Test browser
python3 -c "
from playwright.sync_api import sync_playwright
with sync_playwright() as p:
    browser = p.chromium.launch(headless=True)
    browser.close()
    print('✅ Browser OK')
"
```

### Memory Issues
```bash
# Check memory usage
htop

# Restart PM2 processes
pm2 restart all

# Clear PM2 logs
pm2 flush
```

### Database Connection Issues
```bash
# Test database connection
python3 -c "
import os
from dotenv import load_dotenv
load_dotenv()
print(f'SUPABASE_URL: {os.getenv(\"SUPABASE_URL\")}')
print(f'SUPABASE_KEY: {os.getenv(\"SUPABASE_KEY\")[:10]}...')
"
```

## Security Considerations

1. **Firewall**: Configure security groups to allow only necessary ports
2. **SSH**: Use key-based authentication only
3. **Updates**: Regularly update system packages
4. **Monitoring**: Set up CloudWatch alarms for resource usage
5. **Backups**: Regular database backups

## Performance Optimization

1. **Memory**: Monitor memory usage, restart if needed
2. **CPU**: Use appropriate instance size
3. **Network**: Ensure stable internet connection
4. **Storage**: Use SSD for better I/O performance

## Maintenance

### Regular Tasks
```bash
# Update system packages
sudo apt-get update && sudo apt-get upgrade -y

# Update Python packages
pip install --upgrade -r requirements.txt

# Restart services
pm2 restart all
```

### Log Rotation
```bash
# Create logrotate configuration
sudo tee /etc/logrotate.d/betman-crawler << EOF
/home/<USER>/Stats/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
}
EOF
```