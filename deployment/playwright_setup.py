"""
Playwright setup utility for EC2 server deployment.
Handles Chromium installation and browser setup.
"""

import os
import subprocess
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import logging

from src.utils.path_setup import setup_project_paths

# Standardized path setup
setup_project_paths(__file__)
from utils.logger import Logger


class PlaywrightSetup:
    """Handles Playwright browser installation and setup for EC2 deployment."""
    
    def __init__(self, logger: Optional[Logger] = None):
        """
        Initialize PlaywrightSetup.
        
        Args:
            logger: Optional logger instance. If not provided, creates a new one.
        """
        self.logger = logger or Logger(__name__)
        self.is_ec2 = self._detect_ec2_environment()
        
    def _detect_ec2_environment(self) -> bool:
        """
        Detect if running on EC2 instance.
        
        Returns:
            True if running on EC2, False otherwise
        """
        try:
            # Check for EC2 metadata service
            result = subprocess.run(
                ['curl', '-s', '--max-time', '2', 'http://169.254.169.254/latest/meta-data/'],
                capture_output=True,
                text=True,
                timeout=3
            )
            if result.returncode == 0 and result.stdout:
                self.logger.info("EC2 environment detected")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        # Check for common EC2 indicators
        ec2_indicators = [
            '/sys/hypervisor/uuid',
            '/sys/devices/virtual/dmi/id/product_uuid'
        ]
        
        for indicator in ec2_indicators:
            if os.path.exists(indicator):
                try:
                    with open(indicator, 'r') as f:
                        content = f.read().strip()
                        if content.startswith('ec2') or content.startswith('EC2'):
                            self.logger.info(f"EC2 environment detected via {indicator}")
                            return True
                except (IOError, OSError):
                    continue
        
        # Check environment variables
        if os.environ.get('AWS_EXECUTION_ENV') or os.environ.get('AWS_REGION'):
            self.logger.info("EC2 environment detected via environment variables")
            return True
        
        return False
    
    def check_playwright_installation(self) -> bool:
        """
        Check if Playwright is installed and available.
        
        Returns:
            True if Playwright is installed, False otherwise
        """
        try:
            import playwright
            self.logger.info("Playwright package is installed")
            return True
        except ImportError:
            self.logger.warning("Playwright package is not installed")
            return False
    
    def check_chromium_installation(self) -> bool:
        """
        Check if Chromium browser is installed for Playwright.
        
        Returns:
            True if Chromium is installed, False otherwise
        """
        try:
            from playwright.sync_api import sync_playwright
            
            with sync_playwright() as p:
                try:
                    browser = p.chromium.launch(headless=True)
                    browser.close()
                    self.logger.info("Chromium browser is installed and working")
                    return True
                except Exception as e:
                    self.logger.warning(f"Chromium browser check failed: {e}")
                    return False
        except ImportError:
            self.logger.error("Playwright not available for browser check")
            return False
    
    def install_chromium(self) -> bool:
        """
        Install Chromium browser for Playwright with retry mechanism.
        
        Returns:
            True if installation successful, False otherwise
        """
        max_retries = 3
        retry_delay = 10  # seconds
        
        for attempt in range(1, max_retries + 1):
            try:
                self.logger.info(f"Installing Chromium browser for Playwright (attempt {attempt}/{max_retries})...")
                
                # Set environment variables for better installation
                env = os.environ.copy()
                env['PLAYWRIGHT_BROWSERS_PATH'] = '/tmp/playwright'
                env['PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD'] = '0'
                
                # Try to install dependencies first if on EC2, but don't fail if it doesn't work
                if self.is_ec2 and attempt == 1:
                    self.logger.info("Attempting to install browser dependencies...")
                    try:
                        deps_result = subprocess.run(
                            [sys.executable, '-m', 'playwright', 'install-deps', 'chromium'],
                            capture_output=True,
                            text=True,
                            timeout=300,  # 5 minutes for dependencies
                            env=env
                        )
                        
                        if deps_result.returncode == 0:
                            self.logger.info("Browser dependencies installed successfully")
                        else:
                            self.logger.warning(f"Dependencies installation failed (continuing anyway): {deps_result.stderr}")
                    except Exception as e:
                        self.logger.warning(f"Dependencies installation failed (continuing anyway): {e}")
                
                # Install Chromium browser with multiple approaches
                install_commands = [
                    # Standard installation
                    [sys.executable, '-m', 'playwright', 'install', 'chromium'],
                    # Force installation ignoring dependencies
                    [sys.executable, '-m', 'playwright', 'install', 'chromium', '--with-deps'],
                    # Installation without dependencies 
                    [sys.executable, '-m', 'playwright', 'install', 'chromium', '--skip-deps']
                ]
                
                result = None
                for i, cmd in enumerate(install_commands):
                    try:
                        self.logger.info(f"Trying installation method {i+1}/{len(install_commands)}")
                        result = subprocess.run(
                            cmd,
                            capture_output=True,
                            text=True,
                            timeout=600,  # 10 minutes timeout
                            env=env
                        )
                        
                        if result.returncode == 0:
                            self.logger.info(f"Chromium installation successful with method {i+1}")
                            break
                        else:
                            self.logger.warning(f"Installation method {i+1} failed: {result.stderr}")
                    except Exception as e:
                        self.logger.warning(f"Installation method {i+1} error: {e}")
                        continue
                
                if result and result.returncode == 0:
                    self.logger.info("Chromium installation completed successfully")
                    return True
                else:
                    error_msg = result.stderr if result else "All installation methods failed"
                    self.logger.error(f"Chromium installation failed (attempt {attempt}): {error_msg}")
                    
                    # If not the last attempt, wait before retry
                    if attempt < max_retries:
                        self.logger.info(f"Waiting {retry_delay} seconds before retry...")
                        import time
                        time.sleep(retry_delay)
                    
            except subprocess.TimeoutExpired:
                self.logger.error(f"Chromium installation timed out (attempt {attempt})")
                if attempt < max_retries:
                    self.logger.info(f"Waiting {retry_delay} seconds before retry...")
                    import time
                    time.sleep(retry_delay)
            except Exception as e:
                self.logger.error(f"Chromium installation error (attempt {attempt}): {e}")
                if attempt < max_retries:
                    self.logger.info(f"Waiting {retry_delay} seconds before retry...")
                    import time
                    time.sleep(retry_delay)
        
        # If all attempts failed, try alternative installation method
        self.logger.info("Trying alternative installation method...")
        return self._install_chromium_alternative()
    
    def _install_chromium_alternative(self) -> bool:
        """
        Alternative method to install Chromium - try multiple approaches.
        
        Returns:
            True if installation successful, False otherwise
        """
        try:
            self.logger.info("Attempting alternative Chromium installation approaches...")
            
            # Method 1: Try downloading portable Chromium directly
            if self._try_portable_chromium():
                return True
            
            # Method 2: Try using existing system browser if available
            if self._try_system_browser():
                return True
            
            # Method 3: Try manual download (as last resort)
            if self._try_manual_download():
                return True
            
            self.logger.error("All alternative installation methods failed")
            return False
                
        except Exception as e:
            self.logger.error(f"Alternative installation error: {e}")
            return False
    
    def _try_portable_chromium(self) -> bool:
        """Try installing portable Chromium without system dependencies."""
        try:
            self.logger.info("Trying portable Chromium installation...")
            
            # Set minimal environment
            env = os.environ.copy()
            env['PLAYWRIGHT_BROWSERS_PATH'] = '/tmp/playwright-browsers'
            env['PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD'] = '0'
            
            # Create directory
            os.makedirs('/tmp/playwright-browsers', exist_ok=True)
            
            # Try installation with minimal dependencies
            result = subprocess.run(
                [sys.executable, '-m', 'playwright', 'install', 'chromium', '--skip-deps'],
                capture_output=True,
                text=True,
                timeout=300,
                env=env
            )
            
            if result.returncode == 0:
                self.logger.info("Portable Chromium installation successful")
                return True
            else:
                self.logger.warning(f"Portable Chromium installation failed: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.warning(f"Portable Chromium installation error: {e}")
            return False
    
    def _try_system_browser(self) -> bool:
        """Try to use existing system browser."""
        try:
            self.logger.info("Looking for existing system browsers...")
            
            # Common Chrome/Chromium paths
            browser_paths = [
                '/usr/bin/chromium-browser',
                '/usr/bin/chromium',
                '/usr/bin/google-chrome',
                '/usr/bin/google-chrome-stable',
                '/opt/google/chrome/chrome',
                '/snap/bin/chromium'
            ]
            
            for path in browser_paths:
                if os.path.exists(path) and os.access(path, os.X_OK):
                    self.logger.info(f"Found system browser at {path}")
                    os.environ['PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH'] = path
                    return True
            
            self.logger.warning("No usable system browser found")
            return False
            
        except Exception as e:
            self.logger.warning(f"System browser detection error: {e}")
            return False
    
    def _try_manual_download(self) -> bool:
        """Try manual Chromium download as last resort."""
        try:
            self.logger.info("Attempting manual Chromium download...")
            
            # This is a simplified approach - in production you might want
            # to implement a more robust download mechanism
            self.logger.warning("Manual download not implemented - consider installing Chromium manually")
            return False
            
        except Exception as e:
            self.logger.warning(f"Manual download error: {e}")
            return False
    
    def install_system_dependencies(self) -> bool:
        """
        Install system dependencies required for Chromium on EC2.
        
        Returns:
            True if installation successful, False otherwise
        """
        if not self.is_ec2:
            self.logger.info("Not on EC2, skipping system dependencies installation")
            return True
        
        try:
            self.logger.info("Installing system dependencies for Chromium on EC2...")
            
            # Install required system packages
            system_packages = [
                'libnss3',
                'libatk-bridge2.0-0',
                'libdrm2',
                'libxkbcommon0',
                'libxcomposite1',
                'libxdamage1',
                'libxrandr2',
                'libgbm1',
                'libxss1',
                'libasound2',
                'libatspi2.0-0',
                'libgtk-3-0'
            ]
            
            # Update package list
            subprocess.run(['sudo', 'apt-get', 'update'], check=True)
            
            # Install packages
            cmd = ['sudo', 'apt-get', 'install', '-y'] + system_packages
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                self.logger.info("System dependencies installed successfully")
                return True
            else:
                self.logger.error(f"System dependencies installation failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.error("System dependencies installation timed out")
            return False
        except Exception as e:
            self.logger.error(f"System dependencies installation error: {e}")
            return False
    
    def setup_playwright_for_ec2(self) -> bool:
        """
        Complete Playwright setup for EC2 deployment with improved error handling.
        
        Returns:
            True if setup successful, False otherwise
        """
        self.logger.info("Starting Playwright setup for EC2 deployment...")
        
        # Check if Playwright is installed
        if not self.check_playwright_installation():
            self.logger.error("Playwright package is not installed. Please install it first:")
            self.logger.error("  pip install playwright")
            return False
        
        # Check if Chromium is already installed and working
        if self.check_chromium_installation():
            self.logger.info("Chromium is already installed and working")
            return True
        
        # Try to install Chromium with graceful degradation
        self.logger.info("Chromium not found, attempting installation...")
        
        if self.install_chromium():
            # Verify installation worked
            if self.check_chromium_installation():
                self.logger.info("✅ Playwright Chromium setup completed successfully")
                return True
            else:
                self.logger.warning("Installation completed but verification failed")
        
        # If installation failed, provide helpful guidance
        self.logger.error("❌ Failed to install Chromium automatically")
        self.logger.info("Manual installation options:")
        self.logger.info("1. Install system dependencies manually:")
        if self.is_ec2:
            self.logger.info("   sudo apt-get update && sudo apt-get install -y libnss3 libatk-bridge2.0-0 libgtk-3-0 libxss1 libasound2")
            self.logger.info("2. Install Chromium manually:")
            self.logger.info("   sudo apt-get install -y chromium-browser")
            self.logger.info("3. Or try: python -m playwright install chromium --with-deps")
        else:
            self.logger.info("   python -m playwright install-deps chromium")
            self.logger.info("   python -m playwright install chromium")
        
        return False
    
    def get_browser_info(self) -> Dict[str, Any]:
        """
        Get information about installed browsers.
        
        Returns:
            Dictionary containing browser information
        """
        info = {
            'playwright_installed': self.check_playwright_installation(),
            'chromium_installed': self.check_chromium_installation(),
            'is_ec2': self.is_ec2,
            'system_info': {
                'platform': sys.platform,
                'python_version': sys.version
            }
        }
        
        if info['playwright_installed']:
            try:
                import playwright
                info['playwright_version'] = playwright.__version__
            except (ImportError, AttributeError):
                info['playwright_version'] = 'unknown'
        
        return info


def setup_playwright_for_deployment() -> bool:
    """
    Main function to setup Playwright for deployment.
    
    Returns:
        True if setup successful, False otherwise
    """
    logger = Logger(__name__)
    setup = PlaywrightSetup(logger)
    
    logger.info("=== Playwright Setup for Deployment ===")
    
    # Get current browser info
    browser_info = setup.get_browser_info()
    logger.info(f"Browser info: {browser_info}")
    
    # Perform setup
    success = setup.setup_playwright_for_ec2()
    
    if success:
        logger.info("=== Playwright Setup Completed Successfully ===")
    else:
        logger.error("=== Playwright Setup Failed ===")
    
    return success


def test_playwright_installation() -> bool:
    """
    Test function to verify Playwright Chromium installation.
    
    Returns:
        True if Chromium is working, False otherwise
    """
    setup = PlaywrightSetup()
    
    
    # Check if Playwright package is available
    if not setup.check_playwright_installation():
        return False
    
    # Check if Chromium browser is working
    if setup.check_chromium_installation():
        return True
    else:
        print("💡 Run setup_playwright_for_deployment() to install Chromium")
        return False


if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        success = test_playwright_installation()
    else:
        success = setup_playwright_for_deployment()
    
    sys.exit(0 if success else 1)