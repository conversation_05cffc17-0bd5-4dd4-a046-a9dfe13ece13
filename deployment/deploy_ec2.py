#!/usr/bin/env python3
"""
Deployment script for betman crawler application on EC2.
Handles Playwright setup, requirements installation, and environment setup.
"""

import logging
import os
import subprocess
import sys
from pathlib import Path
from typing import Any, Dict, List

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('deploy.log')
    ]
)

logger = logging.getLogger(__name__)


class EC2Deployer:
    """Handles EC2 deployment for betman crawler application."""
    
    def __init__(self, project_root: Path):
        """
        Initialize deployer.
        
        Args:
            project_root: Path to project root directory
        """
        self.project_root = project_root
        self.is_ec2 = self._detect_ec2()
        
    def _detect_ec2(self) -> bool:
        """Detect if running on EC2."""
        try:
            result = subprocess.run(
                ['curl', '-s', '--max-time', '2', 'http://***************/latest/meta-data/'],
                capture_output=True,
                text=True,
                timeout=3
            )
            return result.returncode == 0 and result.stdout
        except:
            return False
    
    def install_system_dependencies(self) -> bool:
        """Install system dependencies for EC2."""
        if not self.is_ec2:
            logger.info("Not on EC2, skipping system dependencies")
            return True
            
        logger.info("Installing system dependencies...")
        
        try:
            # Update package list
            subprocess.run(['sudo', 'apt-get', 'update', '-y'], check=True)
            
            # Install essential packages
            packages = [
                'python3-pip',
                'python3-venv',
                'curl',
                'wget',
                'git',
                'build-essential',
                'libpq-dev',
                'python3-dev',
                # Playwright dependencies
                'libnss3',
                'libatk-bridge2.0-0',
                'libdrm2',
                'libxkbcommon0',
                'libxcomposite1',
                'libxdamage1',
                'libxrandr2',
                'libgbm1',
                'libxss1',
                'libasound2',
                'libatspi2.0-0',
                'libgtk-3-0',
                'libxshmfence1',
                'libgconf-2-4',
                'libxcursor1',
                'libxi6',
                'libxrender1',
                'libcairo2',
                'libcups2',
                'libdbus-1-3',
                'libexpat1',
                'libfontconfig1',
                'libgcc1',
                'libglib2.0-0',
                'libnspr4',
                'libnss3',
                'libpango-1.0-0',
                'libpangocairo-1.0-0',
                'libstdc++6',
                'libx11-6',
                'libx11-xcb1',
                'libxcb1',
                'libxcomposite1',
                'libxcursor1',
                'libxdamage1',
                'libxext6',
                'libxfixes3',
                'libxi6',
                'libxrandr2',
                'libxrender1',
                'libxss1',
                'libxtst6',
                'xvfb'
            ]
            
            for package in packages:
                try:
                    subprocess.run(['sudo', 'apt-get', 'install', '-y', package], check=True)
                    logger.info(f"✅ Installed {package}")
                except subprocess.CalledProcessError as e:
                    logger.warning(f"⚠️ Failed to install {package}: {e}")
                    continue
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to install system dependencies: {e}")
            return False
    
    def create_requirements_file(self) -> bool:
        """Create requirements.txt file."""
        logger.info("Creating requirements.txt...")
        
        requirements_content = """annotated-types==0.7.0
anyio==4.9.0
attrs==25.3.0
beautifulsoup4==4.13.4
certifi==2025.6.15
charset-normalizer==3.4.2
deprecation==2.1.0
frozenlist==1.7.0
gotrue==2.12.3
greenlet==3.2.3
hpack==4.1.0
hyperframe==6.1.0
idna==3.10
lxml==6.0.0
multidict==6.6.3
numpy==2.3.1
packaging==25.0
pandas==2.3.1
playwright==1.53.0
postgrest==1.1.1
propcache==0.3.2
psutil==7.0.0
pydantic==2.11.7
pydantic_core==2.33.2
pyee==13.0.0
PyJWT==2.10.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
pytz==2025.2
realtime==2.5.3
requests==2.32.4
schedule==1.2.2
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
storage3==0.12.0
StrEnum==0.4.15
supabase==2.16.0
supafunc==0.10.1
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
urllib3==2.5.0
yarl==1.20.1
"""
        
        try:
            with open(self.project_root / 'requirements.txt', 'w') as f:
                f.write(requirements_content)
            logger.info("✅ Created requirements.txt")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to create requirements.txt: {e}")
            return False
    
    def install_python_dependencies(self) -> bool:
        """Install Python dependencies."""
        logger.info("Installing Python dependencies...")
        
        try:
            # Upgrade pip
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], check=True)
            
            # Install requirements
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ], check=True)
            
            logger.info("✅ Installed Python dependencies")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install Python dependencies: {e}")
            return False
    
    def setup_playwright(self) -> bool:
        """Setup Playwright browsers."""
        logger.info("Setting up Playwright...")
        
        try:
            subprocess.run([sys.executable, '-m', 'playwright', 'install', 'chromium'], check=True)
            logger.info("✅ Installed Playwright Chromium")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install Playwright: {e}")
            return False
    
    def setup_environment(self) -> bool:
        """Setup environment variables."""
        logger.info("Setting up environment...")
        
        env_template = """# Betman Crawler Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_KEY=your_supabase_key_here

# Database Configuration
DATABASE_URL=your_database_url_here

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Browser Configuration
HEADLESS=true
MAX_BROWSERS=5
BROWSER_TIMEOUT=30000

# Scheduler Configuration
SCHEDULE_TIMES=08:12,11:12,12:12,14:12,14:32,16:12,17:12
WORKING_HOURS_START=8
WORKING_HOURS_END=23

# Sports Configuration
ENABLED_SPORTS=baseball,soccer,basketball,volleyball
"""
        
        try:
            env_file = self.project_root / '.env'
            if not env_file.exists():
                with open(env_file, 'w') as f:
                    f.write(env_template)
                logger.info("✅ Created .env template")
            else:
                logger.info("ℹ️ .env file already exists")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup environment: {e}")
            return False
    
    def run_tests(self) -> bool:
        """Run basic tests to verify installation."""
        logger.info("Running basic tests...")
        
        test_commands = [
            'python -c "import playwright; print(\'✅ Playwright OK\')"',
            'python -c "from playwright.sync_api import sync_playwright; print(\'✅ Playwright API OK\')"',
            'python -c "import supabase; print(\'✅ Supabase OK\')"',
            'python -c "import pandas; print(\'✅ Pandas OK\')"',
            'python -c "import psutil; print(\'✅ Psutil OK\')"',
            'python -c "import schedule; print(\'✅ Schedule OK\')"',
            'python -c "import requests; print(\'✅ Requests OK\')"',
            'python -c "import beautifulsoup4; print(\'✅ BeautifulSoup OK\')"',
        ]
        
        for cmd in test_commands:
            try:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info(f"✅ {cmd}")
                else:
                    logger.warning(f"⚠️ {cmd}: {result.stderr}")
            except Exception as e:
                logger.warning(f"⚠️ {cmd}: {e}")
        
        return True
    
    def deploy(self) -> bool:
        """Run complete deployment process."""
        logger.info("=== Starting betman crawler EC2 deployment ===")
        
        steps = [
            ("Install system dependencies", self.install_system_dependencies),
            ("Create requirements file", self.create_requirements_file),
            ("Install Python dependencies", self.install_python_dependencies),
            ("Setup Playwright", self.setup_playwright),
            ("Setup environment", self.setup_environment),
            ("Run tests", self.run_tests),
        ]
        
        for step_name, step_func in steps:
            logger.info(f"🔄 {step_name}...")
            if not step_func():
                logger.error(f"❌ Failed at: {step_name}")
                return False
            logger.info(f"✅ Completed: {step_name}")
        
        logger.info("=== Deployment completed successfully ===")
        logger.info("Next steps:")
        logger.info("1. Configure your .env file with actual credentials")
        logger.info("2. Test the application: python3 main.py --help")
        logger.info("3. Run the application: python3 main.py")
        
        return True


def main():
    """Main deployment function."""
    project_root = Path(__file__).parent.parent
    deployer = EC2Deployer(project_root)
    
    if not deployer.deploy():
        sys.exit(1)
    
    logger.info("🎉 Deployment completed successfully!")


if __name__ == "__main__":
    main()